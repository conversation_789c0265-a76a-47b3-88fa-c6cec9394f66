<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.stock</groupId>
    <artifactId>capital-lcab-manage</artifactId>
    <packaging>war</packaging>
    <version>0.0.1-SNAPSHOT</version>
    <name>capital-lcab-manage</name>

    <properties>
        <!-- Plugin的属性定义 -->
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 主要依赖库的版本定义 -->
        <tika.version>1.18</tika.version>
        <opencc4j.version>1.0.2</opencc4j.version>
        <phantomjsdriver.version>1.4.4</phantomjsdriver.version>
        <stock-core>1.0.0-SNAPSHOT</stock-core>
    </properties>

    <repositories>
        <repository>
            <id>stock-nexus</id>
            <url>https://nexus.valueonline.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>HuaweiCloudSDK</id>
            <url>https://mirrors.huaweicloud.com/repository/maven/huaweicloudsdk/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- 依赖项定义 -->
    <dependencies>
        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-all-deps</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.codeborne</groupId>
            <artifactId>phantomjsdriver</artifactId>
            <version>${phantomjsdriver.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-core</artifactId>
            <version>3.1.98</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.tomakehurst</groupId>
                    <artifactId>wiremock-jre8</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-vod</artifactId>
            <version>3.0.49</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-mpc</artifactId>
            <version>3.0.39-rc</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.sis</groupId>
            <artifactId>huaweicloud-java-sdk-sis</artifactId>
            <version>1.8.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-oms</artifactId>
            <version>3.1.116</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>1.8.0-beta2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>5.2.13.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jms</artifactId>
            <version>5.2.13.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
            <version>5.2.13.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>5.2.13.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>5.2.13.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
            <version>5.2.13.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-acl</artifactId>
            <version>5.3.9.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
            <version>5.3.9.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <version>5.3.9.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-taglibs</artifactId>
            <version>5.3.9.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
            <version>5.3.9.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.61</version>
        </dependency>
        <dependency>
            <groupId>org.asynchttpclient</groupId>
            <artifactId>async-http-client</artifactId>
            <version>2.0.35</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.17.1</version>
        </dependency>
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.21.8</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.4</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.squareup.okhttp3</groupId>-->
<!--            <artifactId>okhttp</artifactId>-->
<!--            <version>4.12.0</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.squareup.okio</groupId>-->
<!--                    <artifactId>okio</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.jetbrains.kotlin</groupId>-->
<!--                    <artifactId>kotlin-stdlib-jdk8</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.squareup.okio</groupId>-->
<!--            <artifactId>okio</artifactId>-->
<!--            <version>3.9.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.5</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
        </dependency>
        <dependency>
            <groupId>org.htmlunit</groupId>
            <artifactId>htmlunit</artifactId>
            <version>3.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-rs-client</artifactId>
            <version>3.5.6</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.101.Final</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.1-jre</version>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-server</artifactId>
            <version>3.9.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>htmlunit</artifactId>
                    <groupId>net.sourceforge.htmlunit</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>htmlunit-core-js</artifactId>
                    <groupId>net.sourceforge.htmlunit</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>neko-htmlunit</artifactId>
                    <groupId>net.sourceforge.htmlunit</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>htmlunit-driver</artifactId>
                    <groupId>org.seleniumhq.selenium</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.stock</groupId>
            <artifactId>stock-core-search</artifactId>
            <version>${stock-core}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.stock</groupId>
            <artifactId>stock-core-push</artifactId>
            <version>${stock-core}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.stock</groupId>
                    <artifactId>stock-core-dao</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.stock</groupId>
            <artifactId>capital-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.amqp</groupId>
                    <artifactId>spring-rabbit</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.birt.runtime.3_7_1</groupId>
            <artifactId>com.lowagie.text</artifactId>
            <version>2.1.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-parsers</artifactId>
            <version>${tika.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.james</groupId>
                    <artifactId>apache-mime4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.james</groupId>
                    <artifactId>apache-mime4j-dom</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcmail-jdk15on</artifactId>
                </exclusion>
                <!--                 <exclusion> -->
                <!--                     <groupId>org.bouncycastle</groupId> -->
                <!--                     <artifactId>bcprov-jdk15on</artifactId> -->
                <!--                 </exclusion> -->
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm-debug-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.googlecode.mp4parser</groupId>
                    <artifactId>isoparser</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>de.l3s.boilerpipe</groupId>
                    <artifactId>boilerpipe</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.gagravarr</groupId>
                    <artifactId>vorbis-java-tika</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.gagravarr</groupId>
                    <artifactId>vorbis-java-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>rome</groupId>
                    <artifactId>rome</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codelibs</groupId>
                    <artifactId>jhighlight</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.pff</groupId>
                    <artifactId>java-libpst</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.sourceforge.jmatio</groupId>
                    <artifactId>jmatio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.healthmarketscience.jackcess</groupId>
                    <artifactId>jackcess</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.healthmarketscience.jackcess</groupId>
                    <artifactId>jackcess-encrypt</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.opennlp</groupId>
                    <artifactId>opennlp-tools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-exec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.googlecode.json-simple</groupId>
                    <artifactId>json-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>edu.ucar</groupId>
                    <artifactId>netcdf4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>edu.ucar</groupId>
                    <artifactId>grib</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>edu.ucar</groupId>
                    <artifactId>cdm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>edu.ucar</groupId>
                    <artifactId>httpservices</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.opengis</groupId>
                    <artifactId>geoapi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.sis.core</groupId>
                    <artifactId>sis-utility</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.sis.storage</groupId>
                    <artifactId>sis-netcdf</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.sis.core</groupId>
                    <artifactId>sis-metadata</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.junrar</groupId>
                    <artifactId>junrar</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-csv</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cxf-rt-rs-client</artifactId>
                    <groupId>org.apache.cxf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.houbb/opencc4j -->
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>opencc4j</artifactId>
            <version>${opencc4j.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>client-sdk.api</groupId>
            <artifactId>client-sdk.api</artifactId>
            <version>1.0.2</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/client-sdk.api-1.0.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>client-sdk.common</groupId>
            <artifactId>client-sdk.common</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/client-sdk.common-1.0.0-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>client-sdk.core</groupId>
            <artifactId>client-sdk.core</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/client-sdk.core-1.0.0-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>client-sdk.example</groupId>
            <artifactId>client-sdk.example</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/client-sdk.example-1.0.0-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>client-sdk.spring</groupId>
            <artifactId>client-sdk.spring</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/client-sdk.spring-1.0.0-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.stock</groupId>
            <artifactId>stock-core-os</artifactId>
            <version>${stock-core}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.stock</groupId>
                    <artifactId>stock-core-dao</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huaweicloud</groupId>
                    <artifactId>esdk-obs-java-bundle</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>lippi-oapi-encrpt</groupId>
            <artifactId>lippi-oapi-encrpt</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/lippi-oapi-encrpt.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.batik.pdf</groupId>
            <artifactId>org.apache.batik.pdf</artifactId>
            <version>1.6.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/org.apache.batik.pdf-1.6.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>taobao-sdk-java-auto_1479188381469-20191130</groupId>
            <artifactId>taobao-sdk-java-auto_1479188381469-20191130</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/taobao-sdk-java-auto_1479188381469-20191130.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>taobao-sdk-java-auto_1479188381469-20191130-source</groupId>
            <artifactId>taobao-sdk-java-auto_1479188381469-20191130-source</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/taobao-sdk-java-auto_1479188381469-20191130-source.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>viewservlets</groupId>
            <artifactId>viewservlets</artifactId>
            <version>4.4.1</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/viewservlets-4.4.1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>Ipin</groupId>
            <artifactId>Ipin</artifactId>
            <version>0.0.19</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/enhance-api-0.2.16-jar-with-dependencies.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.2.13.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.7.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--			<groupId>com.huaweicloud</groupId>-->
        <!--			<artifactId>esdk-obs-java</artifactId>-->
        <!--			<version>[3.21.11,)</version>-->
        <!--		</dependency>-->
        <dependency>
            <groupId>esdk-obs-java</groupId>
            <artifactId>esdk-obs-java</artifactId>
            <version>3.19.7</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/webapp/WEB-INF/lib/esdk-obs-java-3.19.7.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.huawei.vod</groupId>
            <artifactId>cloud-java-sdk-vod</artifactId>
            <version>2.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.stock</groupId>
            <artifactId>stock-core-message</artifactId>
            <version>${stock-core}</version>
        </dependency>
        <!--		<dependency>-->
        <!--		    <groupId>com.ning</groupId>-->
        <!--		    <artifactId>async-http-client</artifactId>-->
        <!--		    <version>1.9.40</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>netty</artifactId>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.3.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>capital-lcab-manage</finalName>
        <outputDirectory>${project.basedir}/target/classes</outputDirectory>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp/WEB-INF</directory>
                <includes>
                    <include>*.xml</include>
                </includes>
                <targetPath>../webapp/WEB-INF</targetPath>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- compiler插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArguments>
                        <extdirs>${basedir}/src/main/webapp/WEB-INF/lib</extdirs>
                    </compilerArguments>
                </configuration>
            </plugin>
            <!-- resources插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- war打包插件, 设定war包名称不带版本号 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <webResources>
                        <webResource>
                            <directory>src/main/webapp/WEB-INF</directory>
                            <includes>
                                <include>*.xml</include>
                            </includes>
                            <targetPath>WEB-INF</targetPath>
                            <filtering>true</filtering>
                        </webResource>
                    </webResources>
                </configuration>
            </plugin>
            <!-- jetty插件 -->
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.8.v20171121</version>
                <configuration>
                    <httpConnector>
                        <port>8081</port>
                    </httpConnector>
                    <scanIntervalSeconds>0</scanIntervalSeconds>
                    <webApp>
                        <contextPath>/capital-lcab-manage</contextPath>
                        <descriptor>${project.basedir}/target/webapp/WEB-INF/web.xml</descriptor>
                        <webInfIncludeJarPattern>.*/.*jsp-api-[^/]\.jar$|.*/.*jsp-[^/]\.jar$|.*/.*taglibs[^/]*\.jar$|.*/.*spring-[^/]*\.jar$</webInfIncludeJarPattern>
                    </webApp>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
                <stock-core>0.3.6.RELEASE</stock-core>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>src/main/resources/env/dev.properties</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>prepub-sz</id>
            <properties>
                <profiles.active>prepub-sz</profiles.active>
                <stock-core>0.3.6.RELEASE</stock-core>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/env/prepub-sz.properties</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <stock-core>0.3.6.RELEASE</stock-core>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/env/prod.properties</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>sim</id>
            <properties>
                <profiles.active>sim</profiles.active>
                <stock-core>1.0.0-SNAPSHOT</stock-core>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/env/sim.properties</filter>
                </filters>
            </build>
        </profile>
    </profiles>
</project>
