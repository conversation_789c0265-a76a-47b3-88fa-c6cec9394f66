/*!
 * jQuery Validation Bootstrap Tooltip extention v0.8.0
 *
 * https://github.com/Thrilleratplay/jQuery-Validation-Bootstrap-tooltip
 *
 * Copyright 2015 <PERSON>
 * Released under the MIT license:
 *   http://www.opensource.org/licenses/mit-license.php
 */
!function(t) {
	t.extend(!0, t.validator, {
		prototype : {
			defaultShowErrors : function() {
				var s = this;
				t.each(this.errorList, function(i, e) {
					t(e.element).removeClass(s.settings.validClass).addClass(
							s.settings.errorClass).tooltip("destroy").tooltip(
							s.applyTooltipOptions(e.element, e.message))
							.tooltip("show"), s.settings.highlight
							&& s.settings.highlight.call(s, e.element,
									s.settings.errorClass,
									s.settings.validClass)
				}), t.each(s.validElements(), function(i, e) {
					t(e).removeClass(s.settings.errorClass).addClass(
							s.settings.validClass).tooltip("destroy"),
							s.settings.unhighlight
									&& s.settings.unhighlight.call(s, e,
											s.settings.errorClass,
											s.settings.validClass)
				})
			},
			resetForm: function() {
				this.submitted = {};
				this.lastElement = null;
				this.prepareForm();
				this.hideErrors();
				this.elements()
						.removeClass( this.settings.errorClass )
						.removeData( "previousValue" )
						.removeAttr( "aria-invalid" )
						.tooltip("destroy");
			},
			applyTooltipOptions : function(s, i) {
				var e = {
					animation : t(s).data("animation") || !0,
					html : t(s).data("html") || !1,
					placement : t(s).data("placement") || "top",
					selector : t(s).data("animation") || !1,
					title : t(s).attr("title") || i,
					trigger : t.trim("manual " + (t(s).data("trigger") || "")),
					delay : t(s).data("delay") || 0,
					container : t(s).data("container") || !1
				};
				return this.settings.tooltip_options
						&& this.settings.tooltip_options[s.name]
						&& t.extend(e, this.settings.tooltip_options[s.name]),
						this.settings.tooltip_options
								&& this.settings.tooltip_options._all_
								&& t.extend(e,
										this.settings.tooltip_options._all_), e
			}
		}
	})
}(jQuery);
