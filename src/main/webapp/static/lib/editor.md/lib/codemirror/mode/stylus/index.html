<!doctype html>

<title>CodeMirror: Stylus mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">
<link rel="stylesheet" href="../../lib/codemirror.css">
<link rel="stylesheet" href="../../addon/hint/show-hint.css">
<script src="../../lib/codemirror.js"></script>
<script src="stylus.js"></script>
<script src="../../addon/hint/show-hint.js"></script>
<script src="../../addon/hint/css-hint.js"></script>
<style>.CodeMirror {background: #f8f8f8;} form{margin-bottom: .7em;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Stylus</a>
  </ul>
</div>

<article>
<h2>Stylus mode</h2>
<form><textarea id="code" name="code">
/* Stylus mode */
#id
.class
article
  font-family Arial, sans-serif

#id,
.class,
article {
  font-family: Arial, sans-serif;
}

// Variables
font-size-base = 16px
line-height-base = 1.5
font-family-base = "Helvetica Neue", Helvetica, Arial, sans-serif
text-color = lighten(#000, 20%)

body
  font font-size-base/line-height-base font-family-base
  color text-color

body {
  font: 400 16px/1.5 "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
}

// Variables
link-color = darken(#428bca, 6.5%)
link-hover-color = darken(link-color, 15%)
link-decoration = none
link-hover-decoration = false

// Mixin
tab-focus()
  outline thin dotted
  outline 5px auto -webkit-focus-ring-color
  outline-offset -2px

a
  color link-color
  if link-decoration
    text-decoration link-decoration
  &:hover
  &:focus
    color link-hover-color
    if link-hover-decoration
      text-decoration link-hover-decoration
  &:focus
    tab-focus()

a {
  color: #3782c4;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #2f6ea7;
}
a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
</textarea>
</form>
<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    extraKeys: {"Ctrl-Space": "autocomplete"},
  });
</script>

<p><strong>MIME types defined:</strong> <code>text/x-styl</code>.</p>

</article>
