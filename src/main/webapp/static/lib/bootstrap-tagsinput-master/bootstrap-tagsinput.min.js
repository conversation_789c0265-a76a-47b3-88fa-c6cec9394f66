(function(c){var b={tagClass:function(j){return"label"},focusClass:"focus",itemValue:function(j){return j?j.toString():j},itemText:function(j){return this.itemValue(j)},itemTitle:function(j){return null},freeInput:true,addOnBlur:true,maxTags:undefined,maxChars:undefined,confirmKeys:[13,44],delimiter:",",delimiterRegex:null,cancelConfirmKeysOnEmpty:false,onTagExists:function(k,j){j.hide().fadeIn()},trimValue:false,allowDuplicates:false,triggerChange:true};function g(l,j){this.isInit=true;this.itemsArray=[];this.$element=c(l);this.$element.hide();this.isSelect=(l.tagName==="SELECT");this.multiple=(this.isSelect&&l.hasAttribute("multiple"));this.objectItems=j&&j.itemValue;this.placeholderText=l.hasAttribute("placeholder")?this.$element.attr("placeholder"):"";var k=Math.max(20,this.placeholderText.length);if(k>70){k=70}this.inputSize=k;this.$container=c('<div class="bootstrap-tagsinput"></div>');this.$input=c('<input type="text" placeholder="'+this.placeholderText+'"/>').appendTo(this.$container);this.$element.before(this.$container);this.build(j);this.isInit=false}g.prototype={constructor:g,add:function(z,C,k){var s=this;if(s.options.maxTags&&s.itemsArray.length>=s.options.maxTags){return}if(z!==false&&!z){return}if(typeof z==="string"&&s.options.trimValue){z=c.trim(z)}if(typeof z==="object"&&!s.objectItems){throw ("Can't add objects when itemValue option is not set")}if(z.toString().match(/^\s*$/)){return}if(s.isSelect&&!s.multiple&&s.itemsArray.length>0){s.remove(s.itemsArray[0])}if(typeof z==="string"&&this.$element[0].tagName==="INPUT"){var B=(s.options.delimiterRegex)?s.options.delimiterRegex:s.options.delimiter;var t=z.split(B);if(t.length>1){for(var w=0;w<t.length;w++){this.add(t[w],true)}if(!C){s.pushVal(s.options.triggerChange)}return}}var q=s.options.itemValue(z).indexOf("[id=");var A="";if(q!=-1){var o=z.substring(q);A=z.substring(z.indexOf("=")+1,z.indexOf("]"))}else{if(getValue(c("#hiddenPersonId").val())!=""){A=c("#hiddenPersonId").val();c("#hiddenPersonId").val("");z=z+"[id="+A+"]"}}var l=s.options.itemValue(z),m=s.options.itemText(z),j=s.options.tagClass(z),r=s.options.itemTitle(z);var u=c.grep(s.itemsArray,function(D){return s.options.itemValue(D)===l})[0];if(u&&!s.options.allowDuplicates){if(s.options.onTagExists){var x=c(".tag",s.$container).filter(function(){return c(this).data("item")===u});s.options.onTagExists(z,x)}return}if(s.items().toString().length+z.length+1>s.options.maxInputLength){return}var y=c.Event("beforeItemAdd",{item:z,cancel:false,options:k});s.$element.trigger(y);if(y.cancel){return}s.itemsArray.push(z);var n=c('<span class="tag '+f(j)+(r!==null?('" title="'+r):"")+'">'+f(m)+'<span data-role="remove"></span></span>');n.data("item",z);s.findInputWrapper().before(n);n.after(" ");var p=(c('option[value="'+encodeURIComponent(l)+'"]',s.$element).length||c('option[value="'+f(l)+'"]',s.$element).length);if(s.isSelect&&!p){var v=c("<option selected>"+f(m)+"</option>");v.data("item",z);v.attr("value",l);s.$element.append(v)}if(!C){s.pushVal(s.options.triggerChange)}if(s.options.maxTags===s.itemsArray.length||s.items().toString().length===s.options.maxInputLength){s.$container.addClass("bootstrap-tagsinput-max")}if(c(".typeahead, .twitter-typeahead",s.$container).length){s.$input.typeahead("val","")}if(this.isInit){s.$element.trigger(c.Event("itemAddedOnInit",{item:z,options:k}))}else{s.$element.trigger(c.Event("itemAdded",{item:z,personId:A,options:k}))}},remove:function(l,m,k){var j=this;if(j.objectItems){if(typeof l==="object"){l=c.grep(j.itemsArray,function(o){return j.options.itemValue(o)==j.options.itemValue(l)})}else{l=c.grep(j.itemsArray,function(o){return j.options.itemValue(o)==l})}l=l[l.length-1]}if(l){var n=c.Event("beforeItemRemove",{item:l,cancel:false,options:k});j.$element.trigger(n);if(n.cancel){return}c(".tag",j.$container).filter(function(){return c(this).data("item")===l}).remove();c("option",j.$element).filter(function(){return c(this).data("item")===l}).remove();if(c.inArray(l,j.itemsArray)!==-1){j.itemsArray.splice(c.inArray(l,j.itemsArray),1)}}if(!m){j.pushVal(j.options.triggerChange)}if(j.options.maxTags>j.itemsArray.length){j.$container.removeClass("bootstrap-tagsinput-max")}j.$element.trigger(c.Event("itemRemoved",{item:l,options:k}))},removeAll:function(){var j=this;c(".tag",j.$container).remove();c("option",j.$element).remove();while(j.itemsArray.length>0){j.itemsArray.pop()}j.pushVal(j.options.triggerChange)},refresh:function(){var j=this;c(".tag",j.$container).each(function(){var l=c(this),n=l.data("item"),o=j.options.itemValue(n),k=j.options.itemText(n),p=j.options.tagClass(n);l.attr("class",null);l.addClass("tag "+f(p));l.contents().filter(function(){return this.nodeType==3})[0].nodeValue=f(k);if(j.isSelect){var m=c("option",j.$element).filter(function(){return c(this).data("item")===n});m.attr("value",o)}})},items:function(){return this.itemsArray},pushVal:function(){var j=this,k=c.map(j.items(),function(l){return j.options.itemValue(l).toString()});j.$element.val(k,true);if(j.options.triggerChange){j.$element.trigger("change")}},build:function(k){var j=this;j.options=c.extend({},b,k);if(j.objectItems){j.options.freeInput=false}a(j.options,"itemValue");a(j.options,"itemText");h(j.options,"tagClass");if(j.options.typeahead){var n=j.options.typeahead||{};h(n,"source");j.$input.typeahead(c.extend({},n,{source:function(r,t){function p(u){var w=[];for(var v=0;v<u.length;v++){var x=j.options.itemText(u[v]);s[x]=u[v];w.push(x)}t(w)}this.map={};var s=this.map,q=n.source(r);if(c.isFunction(q.success)){q.success(p)}else{if(c.isFunction(q.then)){q.then(p)}else{c.when(q).then(p)}}},updater:function(p){j.add(this.map[p]);return this.map[p]},matcher:function(p){return(p.toLowerCase().indexOf(this.query.trim().toLowerCase())!==-1)},sorter:function(p){return p.sort()},highlighter:function(q){var p=new RegExp("("+this.query+")","gi");return q.replace(p,"<strong>$1</strong>")}}))}if(j.options.typeaheadjs){var m=null;var o={};var l=j.options.typeaheadjs;if(c.isArray(l)){m=l[0];o=l[1]}else{o=l}j.$input.typeahead(m,o).on("typeahead:selected",c.proxy(function(q,p){if(o.valueKey){j.add(p[o.valueKey])}else{j.add(p)}j.$input.typeahead("val","")},j))}j.$container.on("click",c.proxy(function(p){if(!j.$element.attr("disabled")){j.$input.removeAttr("disabled")}j.$input.focus()},j));if(j.options.addOnBlur&&j.options.freeInput){j.$input.on("focusout",c.proxy(function(p){if(c(".typeahead, .twitter-typeahead",j.$container).length===0){j.add(j.$input.val());if(!j.$input.valid()){j.$input.focus()}else{j.$input.val("")}}},j))}j.$container.on({focusin:function(){j.$container.addClass(j.options.focusClass)},focusout:function(){j.$container.removeClass(j.options.focusClass)},});j.$container.on("keydown","input",c.proxy(function(p){var t=c(p.target),u=j.findInputWrapper();if(j.$element.attr("disabled")){j.$input.attr("disabled","disabled");return}switch(p.which){case 8:if(e(t[0])===0){var q=u.prev();if(q.length){j.remove(q.data("item"))}}break;case 46:if(e(t[0])===0){var r=u.next();if(r.length){j.remove(r.data("item"))}}break;case 37:var v=u.prev();if(t.val().length===0&&v[0]){v.before(u);t.focus()}break;case 39:var s=u.next();if(t.val().length===0&&s[0]){s.after(u);t.focus()}break;default:}var y=t.val().length,w=Math.ceil(y/5),x=y+w+1;var x=Math.max(this.inputSize,t.val().length);if(x>70){x=70}t.attr("size",x)},j));j.$container.on("keypress","input",c.proxy(function(t){var v=c(t.target);if(j.$element.attr("disabled")){j.$input.attr("disabled","disabled");return}var u=v.val(),s=j.options.maxChars&&u.length>=j.options.maxChars;if(j.options.freeInput&&(d(t,j.options.confirmKeys)||s)){if(u.length!==0){j.add(s?u.substr(0,j.options.maxChars):u)}if(j.options.cancelConfirmKeysOnEmpty===false){t.preventDefault()}}var r=v.val().length,q=Math.ceil(r/5),p=r+q+1;var p=Math.max(this.inputSize,v.val().length);if(p>70){p=70}v.attr("size",p)},j));j.$container.on("click","[data-role=remove]",c.proxy(function(p){if(j.$element.attr("disabled")){return}j.remove(c(p.target).closest(".tag").data("item"))},j));if(j.options.itemValue===b.itemValue){if(j.$element[0].tagName==="INPUT"){j.add(j.$element.val())}else{c("option",j.$element).each(function(){j.add(c(this).attr("value"),true)})}}},destroy:function(){var j=this;j.$container.off("keypress","input");j.$container.off("click","[role=remove]");j.$container.remove();j.$element.removeData("tagsinput");j.$element.show()},focus:function(){this.$input.focus()},input:function(){return this.$input},findInputWrapper:function(){var k=this.$input[0],j=this.$container[0];while(k&&k.parentNode!==j){k=k.parentNode}return c(k)}};c.fn.tagsinput=function(l,k,j){var m=[];this.each(function(){var o=c(this).data("tagsinput");if(!o){o=new g(this,l);c(this).data("tagsinput",o);m.push(o);if(this.tagName==="SELECT"){c("option",c(this)).attr("selected","selected")}c(this).val(c(this).val())}else{if(!l&&!k){m.push(o)}else{if(o[l]!==undefined){if(o[l].length===3&&j!==undefined){var n=o[l](k,null,j)}else{var n=o[l](k)}if(n!==undefined){m.push(n)}}}}});if(typeof l=="string"){return m.length>1?m:m[0]}else{return m}};c.fn.tagsinput.Constructor=g;function a(k,l){if(typeof k[l]!=="function"){var j=k[l];k[l]=function(m){return m[j]}}}function h(j,k){if(typeof j[k]!=="function"){var l=j[k];j[k]=function(){return l}}}var i=c("<div />");function f(j){if(j){return i.text(j).html()}else{return""}}function e(k){var l=0;if(document.selection){k.focus();var j=document.selection.createRange();j.moveStart("character",-k.value.length);l=j.text.length}else{if(k.selectionStart||k.selectionStart=="0"){l=k.selectionStart}}return(l)}function d(j,l){var k=false;c.each(l,function(o,n){if(typeof(n)==="number"&&j.which===n){k=true;return false}if(j.which===n.which){var q=!n.hasOwnProperty("altKey")||j.altKey===n.altKey,m=!n.hasOwnProperty("shiftKey")||j.shiftKey===n.shiftKey,p=!n.hasOwnProperty("ctrlKey")||j.ctrlKey===n.ctrlKey;if(q&&m&&p){k=true;return false}}});return k}c(function(){c("input[data-role=tagsinput], select[multiple][data-role=tagsinput]").tagsinput()})})(window.jQuery);