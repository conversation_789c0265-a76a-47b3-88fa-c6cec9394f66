(function(){function f(o,i,u){function s(r,t){if(!i[r]){if(!o[r]){var e="function"==typeof require&&require;if(!t&&e)return e(r,!0);if(l)return l(r,!0);var n=new Error("Cannot find module '"+r+"'");throw n.code="MODULE_NOT_FOUND",n}var a=i[r]={exports:{}};o[r][0].call(a.exports,function(t){var e=o[r][1][t];return s(e||t)},a,a.exports,f,o,i,u)}return i[r].exports}for(var l="function"==typeof require&&require,t=0;t<u.length;t++)s(u[t]);return s}return f})()({1:[function(t,e,r){"use strict";function n(){this.obsClient=null}var o={bucketName:{required:true,location:"param"},uploadPath:{required:true,location:"param"},videoFile:{required:true,location:"param"}};var a="/v1.0/";var i="vod.cn-north-1.myhuaweicloud.com";n.prototype.sRequest=function(n){return new Promise(function(a,o){var t={type:n.type,timeout:n.timeout||18e4,headers:{},url:n.url,data:typeof n.params==="string"?n.params:JSON.stringify(n.params||{}),beforeSend:function t(e,r){n.beforeSend&&n.beforeSend(e,r)},success:function t(e,r,n){a({res:e,status:r,xhr:n})},error:function t(e,r,n){o(e)}};if(n.contentType){t.contentType=n.contentType}if(n.dataType){t.dataType=n.dataType}$.ajax(t)})};n.prototype.sendGetRequest=function(n){return new Promise(function(a,o){var t={type:n.type,timeout:n.timeout||18e4,headers:{},url:n.url,data:n.params||{},beforeSend:function t(e,r){n.beforeSend&&n.beforeSend(e,r)},success:function t(e,r,n){a({res:e,status:r,xhr:n})},error:function t(e,r,n){o(e)}};if(n.contentType){t.contentType=n.contentType}if(n.dataType){t.dataType=n.dataType}$.ajax(t)})};n.prototype.initFactory=function(t){this.obsClient=new ObsClient({access_key_id:t.access_key_id,secret_access_key:t.secret_access_key,security_token:t.security_token,is_secure:true,server:"obs.myhwclouds.com",port:"443",signature:"v4",timeout:3600})};n.prototype.checkParam=function(t,e){t=t||{};var r={};for(var n in o){var a=o[n];if("required"in a&&!(n in t)){r["err"]=n+" is a required element!";return r}if(n in t&&!t[n]){r["err"]=n+" is Not allowed to be empty"}}if(!e){r["err"]="callback is Not allowed to be empty"}return r};n.prototype.getUUID=function(){var t=[];var e="0123456789abcdef";for(var r=0;r<36;r++){t[r]=e.substr(Math.floor(Math.random()*16),1)}t[14]="4";t[19]=e.substr(t[19]&3|8,1);t[8]=t[13]=t[18]=t[23]="";var n=t.join("");return n};e.exports=n},{}],2:[function(u,t,e){"use strict";var a={access_key_id:{required:true,location:"vodClient"},secret_access_key:{required:true,location:"vodClient"},security_token:{required:true,location:"vodClient"}};var r=u("./Utils");function n(t){this.uploadFilePublicParam={globalContext:{maxTaskNum:5,currentTaskNum:0},uploadedCount:0};this.upFlag=false;this.completedFlag=false;this._callbak=null;this.utils=new r;this.uploadedIndexes=[];this.uploadPartParameters=null;this.param=null;this.partSize=1024*1024*5;this.factory(t)}n.prototype.factory=function(t){t=t||{};var e={};for(var r in a){var n=a[r];if("required"in n&&!(r in t)){e["err"]=r+" is a required element!"}if(r in t&&!t[r]){e["err"]=r+" is Not allowed to be empty"}}if("err"in e){throw TypeError("vodClient err："+e["err"])}this.utils.initFactory(t)};n.prototype.uploadVideoFile=function(t,e){var r=this;var n=r.partSize;var a=t.videoFile;r.param=t;r._callbak=e;r.partCount=a.size%n===0?a.size/n:Math.ceil(a.size/n);r.startUploadAsset()};n.prototype.startUploadAsset=function(){var t=this;if(!t.upFlag){t.upFlag=true;var e=t.param;t.uploadFilePublicParam.uploadedCount=0;var r=t.utils.checkParam(e,t._callbak);if("err"in r){t._callbak(r["err"],null);return}t.upFlag&&t.uploadToObs(e)}};n.prototype.uploadToObs=function(n){var o=this;o.utils.obsClient.initiateMultipartUpload({Bucket:n.bucketName,Key:n.uploadPath},function(t,e){if(!t&&e.CommonMsg.Status<300){var r=a(e,n.videoFile);o.uploadPartParameters=r;o.upFlag&&o.mulitUploadPart(r)}else{o._callbak(t,null);o.upFlag=false}});function a(t,e){var r=o.partSize;var n=o.partCount;var a={bucketName:t.InterfaceResult.Bucket,objectKey:t.InterfaceResult.Key,uploadId:t.InterfaceResult.UploadId,file:e,fileSize:e.size,partSize:r,partCount:n,currentPartIndex:0,parts:[]};return a}};n.prototype.mulitUploadPart=function(o){var i=this;while(o.currentPartIndex<o.partCount&&i.uploadFilePublicParam.globalContext.currentTaskNum<i.uploadFilePublicParam.globalContext.maxTaskNum){(function(){var t=o.currentPartIndex*o.partSize;var e=o.currentPartIndex+1===o.partCount?o.fileSize:t+o.partSize;var r=o.currentPartIndex+1;o.currentPartIndex++;i.uploadFilePublicParam.globalContext.currentTaskNum++;var n=File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice;var a=n.call(o.file,t,e);i.upFlag&&i.douploadPart(r,o,a)})()}};n.prototype.douploadPart=function(o,i,u){var s=this;s.utils.obsClient.uploadPart({Bucket:i.bucketName,Key:i.objectKey,PartNumber:o,UploadId:i.uploadId,SourceFile:u},function(t,e){if(!t&&e.CommonMsg.Status<300){s.uploadFilePublicParam.globalContext.currentTaskNum--;if(s.uploadedIndexes[o-1]!==true){s.uploadFilePublicParam.uploadedCount++;i.parts.push({PartNumber:o,ETag:e.InterfaceResult.ETag})}s.uploadedIndexes[o-1]=true;var r=Math.floor(100*s.uploadFilePublicParam.uploadedCount/i.partCount);r>0?r=r-1:"0";s.upFlag&&s._callbak(null,r);u=null;if(i.parts.length===i.partCount){var n=i.parts.sort(function(t,e){if(t.PartNumber>=e.PartNumber){return 1}return-1});s.upFlag&&s.isComplete(i,n)}else{s.upFlag&&s.mulitUploadPart(i)}}else{var a=t||e.CommonMsg.Code;s._callbak(a,null);s.upFlag=false}})};n.prototype.isComplete=function(t,e){var r=this;r.utils.obsClient.completeMultipartUpload({Bucket:t.bucketName,Key:t.objectKey,UploadId:t.uploadId,Parts:e},function(t,e){if(!t&&e.CommonMsg.Status==200){r._callbak(null,100);r.upFlag=false;r.completedFlag=true}else{r._callbak(t,null);r.upFlag=false}})};n.prototype.cancelUpload=function(t){var e=this;var r;if(e.upFlag){r="success: Succeeded in canceling the file upload.";e.upFlag=false}else{r="warn: The file has been canceled."}t&&t(r)};n.prototype.restartUpload=function(t){var e=this;var r=e.uploadedIndexes;var n=e.uploadPartParameters;var a;var o=e.param;var i=o.videoFile;var u=e.partCount;if(e.upFlag){a="warn: The file is being uploaded."}else{a="success: Start to restore the upload.";for(var s=0;s<u;s++){if(r[s]!==true){if(s===0){e.uploadVideoFile(e.param,e._callbak)}else{var l=s*n.partSize;var f=s+1===u?n.fileSize:l+e.partSize;var c=File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice;var p=c.call(i,l,f);e.upFlag=true;e.douploadPart(s+1,n,p)}break}if(s===u-1){if(!e.completedFlag){var d=n.parts.sort(function(t,e){if(t.PartNumber>=e.PartNumber){return 1}return-1});e.isComplete(n,d)}else{a="warn: The file has been uploaded."}}}}t&&t(a)};n.prototype.str2Bytes=function(t){var e=0;var r=t.length;if(r%2!=0){return null}r/=2;var n=new Array;for(var a=0;a<r;a++){var o=t.substr(e,2);var i=parseInt(o,16);n.push(i);e+=2}return n};n.prototype.testMd5=function(t){var e=u("md5");var r=u("base64-js");var n=e(t);var a=this.str2Bytes(n);var o=r.fromByteArray(a);var i=10;console.log(""+t+"的md5----\x3e",n);console.log("Base64Js.fromByteArray----\x3e",o)};n.prototype.testFileMd5=function(t,r){var n=this;var e=u("browser-md5-file");var a=u("base64-js");if(!t){console.error("file dose not exist! or the file format error");return}e(t,function(t,e){r(t,a.fromByteArray(n.str2Bytes(e)))})};n.prototype.createAsset=function(u,s){var l=this;l.utils.obsClient.initiateMultipartUpload({Bucket:u.bucketName,Key:u.uploadPath},function(t,e){if(t){console.error("初始化失败",t);s(t,null);return}if(!t&&e.CommonMsg.Status<300){var r=e.InterfaceResult.UploadId;var n=5*1024*1024;var a=u.video_file;var o=a.size%n===0?Math.floor(a.size/n):Math.floor(a.size/n)+1;var i={bucketName:bucketName,objectKey:uploadPath,uploadId:r,file:a,fileSize:a.size,partSize:n,partCount:o,currentPartIndex:0,parts:[]};doUploadPart(i,u,asset_id,fileNameMsg,index,urls)}});var t=l.utils.sRequest(sendAssetParam).then(function(t){console.log("创建媒资成功",t.res);l.asset_Id=t.res.asset_id;var a={bucket:t.res.target.bucket,object_key:t.res.target.object,videoFile:u.video_file};var e=l.utils.makeParam("getInitializationUploadAuthorizationParam",t.res.target);var r=l.utils.getV4Auth(e);var n=l.utils.makeSendRequeat("getSendRequestParam",r);l.utils.sendGetRequest(n).then(function(t){if(t){console.log("获得初始化上传段任务授权成功",t.res);var e={sign_str:t.res.sign_str,object_key:a.object_key,bucket:a.bucket};l.InitializationUploadPart(e,function(t,e){if(t){console.log("初始化上传段任务失败",t)}if(e){console.log("初始化上传段任务成功",e);var r=o(e,a.videoFile);var n={globalContext:{maxTaskNum:5,currentTaskNum:0},uploadedCount:0,ETagData:[],xmlDoc:null};l.mulitUploadPart(r,n)}})}},function(t){console.log("获得初始化上传段任务授权失败",t);return s(t,null)})},function(t){console.log("创建媒资失败",t);return s(t,null)});function e(t){var e=t.video_file;var r=e.name.replace(/\s/g,"");var n=r.split(".");var a=n[n.length-1];var o=0;var i="";if(e.size>1024*1024){o=(Math.round(e.size*100/(1024*1024))/100).toString()+"MB"}else{o=(Math.round(e.size*100/1024)/100).toString()+"KB"}l.testFileMd5(e,function(t,e){i=e});var u={title:r,video_name:r,video_type:a.toUpperCase(),video_size:"1",video_md5:i};return u}function o(t,e){var r=t.getElementsByTagName("InitiateMultipartUploadResult");var n=r[0].getElementsByTagName("Bucket")[0].firstChild.nodeValue;var a=r[0].getElementsByTagName("Key")[0].firstChild.nodeValue;var o=r[0].getElementsByTagName("UploadId")[0].firstChild.nodeValue;var i=5*1024*1024;var u=e.size%i===0?e.size/i:Math.ceil(e.size/i);var s={bucketName:n,objectKey:a,uploadId:o,file:e,fileSize:e.size,partSize:i,partCount:u,currentPartIndex:0,parts:[]};return s}};n.prototype.checkUploadSucc=function(){var t=this;var e={asset_id:t.asset_Id,status:"CREATED"};var r=t.utils.makeParam("getCheckUploadSucc",e);var n=t.utils.getV4Auth(r);var a=t.utils.makeSendRequeat("getCheckUploadSucc",n);t.utils.sRequest(a).then(function(t,e){if(e){console.log("确认媒资上传失败")}if(t){console.log("确认媒资上传成功")}})};n.prototype.getUploadPercentage=function(t){var e=this;if(!up){return t("请在文件初始化上传后查询！",null)}else{return t(null,up)}};t.exports=n},{"./Utils":1,"base64-js":4,"browser-md5-file":5,md5:10}],3:[function(n,t,e){"use strict";(function(t,e){"use strict";var r=n("./VodClient");t.VodClient=r})(window,jQuery)},{"./VodClient":2}],4:[function(t,e,r){"use strict";r.byteLength=i;r.toByteArray=u;r.fromByteArray=v;var s=[];var l=[];var f=typeof Uint8Array!=="undefined"?Uint8Array:Array;var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var a=0,o=n.length;a<o;++a){s[a]=n[a];l[n.charCodeAt(a)]=a}l["-".charCodeAt(0)]=62;l["_".charCodeAt(0)]=63;function c(t){var e=t.length;if(e%4>0){throw new Error("Invalid string. Length must be a multiple of 4")}var r=t.indexOf("=");if(r===-1)r=e;var n=r===e?0:4-r%4;return[r,n]}function i(t){var e=c(t);var r=e[0];var n=e[1];return(r+n)*3/4-n}function p(t,e,r){return(e+r)*3/4-r}function u(t){var e;var r=c(t);var n=r[0];var a=r[1];var o=new f(p(t,n,a));var i=0;var u=a>0?n-4:n;for(var s=0;s<u;s+=4){e=l[t.charCodeAt(s)]<<18|l[t.charCodeAt(s+1)]<<12|l[t.charCodeAt(s+2)]<<6|l[t.charCodeAt(s+3)];o[i++]=e>>16&255;o[i++]=e>>8&255;o[i++]=e&255}if(a===2){e=l[t.charCodeAt(s)]<<2|l[t.charCodeAt(s+1)]>>4;o[i++]=e&255}if(a===1){e=l[t.charCodeAt(s)]<<10|l[t.charCodeAt(s+1)]<<4|l[t.charCodeAt(s+2)]>>2;o[i++]=e>>8&255;o[i++]=e&255}return o}function d(t){return s[t>>18&63]+s[t>>12&63]+s[t>>6&63]+s[t&63]}function h(t,e,r){var n;var a=[];for(var o=e;o<r;o+=3){n=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(t[o+2]&255);a.push(d(n))}return a.join("")}function v(t){var e;var r=t.length;var n=r%3;var a=[];var o=16383;for(var i=0,u=r-n;i<u;i+=o){a.push(h(t,i,i+o>u?u:i+o))}if(n===1){e=t[r-1];a.push(s[e>>2]+s[e<<4&63]+"==")}else if(n===2){e=(t[r-2]<<8)+t[r-1];a.push(s[e>>10]+s[e>>4&63]+s[e<<2&63]+"=")}return a.join("")}},{}],5:[function(t,e,r){e.exports=t("./src/browser-md5-file")},{"./src/browser-md5-file":6}],6:[function(t,e,r){"use strict";var f=t("spark-md5");e.exports=function(r,e){var n=File.prototype.slice||File.prototype.mozSlice||File.prototype.webkitSlice;var a=2097152;var o=Math.ceil(r.size/a);var i=0;var u=new f.ArrayBuffer;var s=new FileReader;l();s.onloadend=function(t){u.append(t.target.result);i++;if(i<o){l()}else{e(null,u.end())}};s.onerror=function(){e("oops, something went wrong.")};function l(){var t=i*a;var e=t+a>=r.size?r.size:t+a;s.readAsArrayBuffer(n.call(r,t,e))}}},{"spark-md5":11}],7:[function(t,e,r){var n={utf8:{stringToBytes:function(t){return n.bin.stringToBytes(unescape(encodeURIComponent(t)))},bytesToString:function(t){return decodeURIComponent(escape(n.bin.bytesToString(t)))}},bin:{stringToBytes:function(t){for(var e=[],r=0;r<t.length;r++)e.push(t.charCodeAt(r)&255);return e},bytesToString:function(t){for(var e=[],r=0;r<t.length;r++)e.push(String.fromCharCode(t[r]));return e.join("")}}};e.exports=n},{}],8:[function(t,e,r){(function(){var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r={rotl:function(t,e){return t<<e|t>>>32-e},rotr:function(t,e){return t<<32-e|t>>>e},endian:function(t){if(t.constructor==Number){return r.rotl(t,8)&16711935|r.rotl(t,24)&4278255360}for(var e=0;e<t.length;e++)t[e]=r.endian(t[e]);return t},randomBytes:function(t){for(var e=[];t>0;t--)e.push(Math.floor(Math.random()*256));return e},bytesToWords:function(t){for(var e=[],r=0,n=0;r<t.length;r++,n+=8)e[n>>>5]|=t[r]<<24-n%32;return e},wordsToBytes:function(t){for(var e=[],r=0;r<t.length*32;r+=8)e.push(t[r>>>5]>>>24-r%32&255);return e},bytesToHex:function(t){for(var e=[],r=0;r<t.length;r++){e.push((t[r]>>>4).toString(16));e.push((t[r]&15).toString(16))}return e.join("")},hexToBytes:function(t){for(var e=[],r=0;r<t.length;r+=2)e.push(parseInt(t.substr(r,2),16));return e},bytesToBase64:function(t){for(var e=[],r=0;r<t.length;r+=3){var n=t[r]<<16|t[r+1]<<8|t[r+2];for(var a=0;a<4;a++)if(r*8+a*6<=t.length*8)e.push(o.charAt(n>>>6*(3-a)&63));else e.push("=")}return e.join("")},base64ToBytes:function(t){t=t.replace(/[^A-Z0-9+\/]/gi,"");for(var e=[],r=0,n=0;r<t.length;n=++r%4){if(n==0)continue;e.push((o.indexOf(t.charAt(r-1))&Math.pow(2,-2*n+8)-1)<<n*2|o.indexOf(t.charAt(r))>>>6-n*2)}return e}};e.exports=r})()},{}],9:[function(t,e,r){e.exports=function(t){return t!=null&&(n(t)||a(t)||!!t._isBuffer)};function n(t){return!!t.constructor&&typeof t.constructor.isBuffer==="function"&&t.constructor.isBuffer(t)}function a(t){return typeof t.readFloatLE==="function"&&typeof t.slice==="function"&&n(t.slice(0,0))}},{}],10:[function(t,e,r){(function(){var g=t("crypt"),b=t("charenc").utf8,m=t("is-buffer"),_=t("charenc").bin,C=function(t,e){if(t.constructor==String)if(e&&e.encoding==="binary")t=_.stringToBytes(t);else t=b.stringToBytes(t);else if(m(t))t=Array.prototype.slice.call(t,0);else if(!Array.isArray(t))t=t.toString();var r=g.bytesToWords(t),n=t.length*8,a=1732584193,o=-271733879,i=-1732584194,u=271733878;for(var s=0;s<r.length;s++){r[s]=(r[s]<<8|r[s]>>>24)&16711935|(r[s]<<24|r[s]>>>8)&4278255360}r[n>>>5]|=128<<n%32;r[(n+64>>>9<<4)+14]=n;var l=C._ff,f=C._gg,c=C._hh,p=C._ii;for(var s=0;s<r.length;s+=16){var d=a,h=o,v=i,y=u;a=l(a,o,i,u,r[s+0],7,-680876936);u=l(u,a,o,i,r[s+1],12,-389564586);i=l(i,u,a,o,r[s+2],17,606105819);o=l(o,i,u,a,r[s+3],22,-1044525330);a=l(a,o,i,u,r[s+4],7,-176418897);u=l(u,a,o,i,r[s+5],12,1200080426);i=l(i,u,a,o,r[s+6],17,-1473231341);o=l(o,i,u,a,r[s+7],22,-45705983);a=l(a,o,i,u,r[s+8],7,1770035416);u=l(u,a,o,i,r[s+9],12,-1958414417);i=l(i,u,a,o,r[s+10],17,-42063);o=l(o,i,u,a,r[s+11],22,-1990404162);a=l(a,o,i,u,r[s+12],7,1804603682);u=l(u,a,o,i,r[s+13],12,-40341101);i=l(i,u,a,o,r[s+14],17,-1502002290);o=l(o,i,u,a,r[s+15],22,1236535329);a=f(a,o,i,u,r[s+1],5,-165796510);u=f(u,a,o,i,r[s+6],9,-1069501632);i=f(i,u,a,o,r[s+11],14,643717713);o=f(o,i,u,a,r[s+0],20,-373897302);a=f(a,o,i,u,r[s+5],5,-701558691);u=f(u,a,o,i,r[s+10],9,38016083);i=f(i,u,a,o,r[s+15],14,-660478335);o=f(o,i,u,a,r[s+4],20,-405537848);a=f(a,o,i,u,r[s+9],5,568446438);u=f(u,a,o,i,r[s+14],9,-1019803690);i=f(i,u,a,o,r[s+3],14,-187363961);o=f(o,i,u,a,r[s+8],20,1163531501);a=f(a,o,i,u,r[s+13],5,-1444681467);u=f(u,a,o,i,r[s+2],9,-51403784);i=f(i,u,a,o,r[s+7],14,1735328473);o=f(o,i,u,a,r[s+12],20,-1926607734);a=c(a,o,i,u,r[s+5],4,-378558);u=c(u,a,o,i,r[s+8],11,-2022574463);i=c(i,u,a,o,r[s+11],16,1839030562);o=c(o,i,u,a,r[s+14],23,-35309556);a=c(a,o,i,u,r[s+1],4,-1530992060);u=c(u,a,o,i,r[s+4],11,1272893353);i=c(i,u,a,o,r[s+7],16,-155497632);o=c(o,i,u,a,r[s+10],23,-1094730640);a=c(a,o,i,u,r[s+13],4,681279174);u=c(u,a,o,i,r[s+0],11,-358537222);i=c(i,u,a,o,r[s+3],16,-722521979);o=c(o,i,u,a,r[s+6],23,76029189);a=c(a,o,i,u,r[s+9],4,-640364487);u=c(u,a,o,i,r[s+12],11,-421815835);i=c(i,u,a,o,r[s+15],16,530742520);o=c(o,i,u,a,r[s+2],23,-995338651);a=p(a,o,i,u,r[s+0],6,-198630844);u=p(u,a,o,i,r[s+7],10,1126891415);i=p(i,u,a,o,r[s+14],15,-1416354905);o=p(o,i,u,a,r[s+5],21,-57434055);a=p(a,o,i,u,r[s+12],6,1700485571);u=p(u,a,o,i,r[s+3],10,-1894986606);i=p(i,u,a,o,r[s+10],15,-1051523);o=p(o,i,u,a,r[s+1],21,-2054922799);a=p(a,o,i,u,r[s+8],6,1873313359);u=p(u,a,o,i,r[s+15],10,-30611744);i=p(i,u,a,o,r[s+6],15,-1560198380);o=p(o,i,u,a,r[s+13],21,1309151649);a=p(a,o,i,u,r[s+4],6,-145523070);u=p(u,a,o,i,r[s+11],10,-1120210379);i=p(i,u,a,o,r[s+2],15,718787259);o=p(o,i,u,a,r[s+9],21,-343485551);a=a+d>>>0;o=o+h>>>0;i=i+v>>>0;u=u+y>>>0}return g.endian([a,o,i,u])};C._ff=function(t,e,r,n,a,o,i){var u=t+(e&r|~e&n)+(a>>>0)+i;return(u<<o|u>>>32-o)+e};C._gg=function(t,e,r,n,a,o,i){var u=t+(e&n|r&~n)+(a>>>0)+i;return(u<<o|u>>>32-o)+e};C._hh=function(t,e,r,n,a,o,i){var u=t+(e^r^n)+(a>>>0)+i;return(u<<o|u>>>32-o)+e};C._ii=function(t,e,r,n,a,o,i){var u=t+(r^(e|~n))+(a>>>0)+i;return(u<<o|u>>>32-o)+e};C._blocksize=16;C._digestsize=16;e.exports=function(t,e){if(t===undefined||t===null)throw new Error("Illegal argument "+t);var r=g.wordsToBytes(C(t,e));return e&&e.asBytes?r:e&&e.asString?_.bytesToString(r):g.bytesToHex(r)}})()},{charenc:7,crypt:8,"is-buffer":9}],11:[function(t,r,n){(function(t){if(typeof n==="object"){r.exports=t()}else if(typeof define==="function"&&define.amd){define(t)}else{var e;try{e=window}catch(t){e=self}e.SparkMD5=t()}})(function(f){"use strict";var i=function(t,e){return t+e&4294967295},n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function u(t,e,r,n,a,o){e=i(i(e,t),i(n,o));return i(e<<a|e>>>32-a,r)}function s(t,e,r,n,a,o,i){return u(e&r|~e&n,t,e,a,o,i)}function l(t,e,r,n,a,o,i){return u(e&n|r&~n,t,e,a,o,i)}function c(t,e,r,n,a,o,i){return u(e^r^n,t,e,a,o,i)}function p(t,e,r,n,a,o,i){return u(r^(e|~n),t,e,a,o,i)}function d(t,e){var r=t[0],n=t[1],a=t[2],o=t[3];r=s(r,n,a,o,e[0],7,-680876936);o=s(o,r,n,a,e[1],12,-389564586);a=s(a,o,r,n,e[2],17,606105819);n=s(n,a,o,r,e[3],22,-1044525330);r=s(r,n,a,o,e[4],7,-176418897);o=s(o,r,n,a,e[5],12,1200080426);a=s(a,o,r,n,e[6],17,-1473231341);n=s(n,a,o,r,e[7],22,-45705983);r=s(r,n,a,o,e[8],7,1770035416);o=s(o,r,n,a,e[9],12,-1958414417);a=s(a,o,r,n,e[10],17,-42063);n=s(n,a,o,r,e[11],22,-1990404162);r=s(r,n,a,o,e[12],7,1804603682);o=s(o,r,n,a,e[13],12,-40341101);a=s(a,o,r,n,e[14],17,-1502002290);n=s(n,a,o,r,e[15],22,1236535329);r=l(r,n,a,o,e[1],5,-165796510);o=l(o,r,n,a,e[6],9,-1069501632);a=l(a,o,r,n,e[11],14,643717713);n=l(n,a,o,r,e[0],20,-373897302);r=l(r,n,a,o,e[5],5,-701558691);o=l(o,r,n,a,e[10],9,38016083);a=l(a,o,r,n,e[15],14,-660478335);n=l(n,a,o,r,e[4],20,-405537848);r=l(r,n,a,o,e[9],5,568446438);o=l(o,r,n,a,e[14],9,-1019803690);a=l(a,o,r,n,e[3],14,-187363961);n=l(n,a,o,r,e[8],20,1163531501);r=l(r,n,a,o,e[13],5,-1444681467);o=l(o,r,n,a,e[2],9,-51403784);a=l(a,o,r,n,e[7],14,1735328473);n=l(n,a,o,r,e[12],20,-1926607734);r=c(r,n,a,o,e[5],4,-378558);o=c(o,r,n,a,e[8],11,-2022574463);a=c(a,o,r,n,e[11],16,1839030562);n=c(n,a,o,r,e[14],23,-35309556);r=c(r,n,a,o,e[1],4,-1530992060);o=c(o,r,n,a,e[4],11,1272893353);a=c(a,o,r,n,e[7],16,-155497632);n=c(n,a,o,r,e[10],23,-1094730640);r=c(r,n,a,o,e[13],4,681279174);o=c(o,r,n,a,e[0],11,-358537222);a=c(a,o,r,n,e[3],16,-722521979);n=c(n,a,o,r,e[6],23,76029189);r=c(r,n,a,o,e[9],4,-640364487);o=c(o,r,n,a,e[12],11,-421815835);a=c(a,o,r,n,e[15],16,530742520);n=c(n,a,o,r,e[2],23,-995338651);r=p(r,n,a,o,e[0],6,-198630844);o=p(o,r,n,a,e[7],10,1126891415);a=p(a,o,r,n,e[14],15,-1416354905);n=p(n,a,o,r,e[5],21,-57434055);r=p(r,n,a,o,e[12],6,1700485571);o=p(o,r,n,a,e[3],10,-1894986606);a=p(a,o,r,n,e[10],15,-1051523);n=p(n,a,o,r,e[1],21,-2054922799);r=p(r,n,a,o,e[8],6,1873313359);o=p(o,r,n,a,e[15],10,-30611744);a=p(a,o,r,n,e[6],15,-1560198380);n=p(n,a,o,r,e[13],21,1309151649);r=p(r,n,a,o,e[4],6,-145523070);o=p(o,r,n,a,e[11],10,-1120210379);a=p(a,o,r,n,e[2],15,718787259);n=p(n,a,o,r,e[9],21,-343485551);t[0]=i(r,t[0]);t[1]=i(n,t[1]);t[2]=i(a,t[2]);t[3]=i(o,t[3])}function h(t){var e=[],r;for(r=0;r<64;r+=4){e[r>>2]=t.charCodeAt(r)+(t.charCodeAt(r+1)<<8)+(t.charCodeAt(r+2)<<16)+(t.charCodeAt(r+3)<<24)}return e}function v(t){var e=[],r;for(r=0;r<64;r+=4){e[r>>2]=t[r]+(t[r+1]<<8)+(t[r+2]<<16)+(t[r+3]<<24)}return e}function a(t){var e=t.length,r=[1732584193,-271733879,-1732584194,271733878],n,a,o,i,u,s;for(n=64;n<=e;n+=64){d(r,h(t.substring(n-64,n)))}t=t.substring(n-64);a=t.length;o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(n=0;n<a;n+=1){o[n>>2]|=t.charCodeAt(n)<<(n%4<<3)}o[n>>2]|=128<<(n%4<<3);if(n>55){d(r,o);for(n=0;n<16;n+=1){o[n]=0}}i=e*8;i=i.toString(16).match(/(.*?)(.{0,8})$/);u=parseInt(i[2],16);s=parseInt(i[1],16)||0;o[14]=u;o[15]=s;d(r,o);return r}function o(t){var e=t.length,r=[1732584193,-271733879,-1732584194,271733878],n,a,o,i,u,s;for(n=64;n<=e;n+=64){d(r,v(t.subarray(n-64,n)))}t=n-64<e?t.subarray(n-64):new Uint8Array(0);a=t.length;o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(n=0;n<a;n+=1){o[n>>2]|=t[n]<<(n%4<<3)}o[n>>2]|=128<<(n%4<<3);if(n>55){d(r,o);for(n=0;n<16;n+=1){o[n]=0}}i=e*8;i=i.toString(16).match(/(.*?)(.{0,8})$/);u=parseInt(i[2],16);s=parseInt(i[1],16)||0;o[14]=u;o[15]=s;d(r,o);return r}function r(t){var e="",r;for(r=0;r<4;r+=1){e+=n[t>>r*8+4&15]+n[t>>r*8&15]}return e}function y(t){var e;for(e=0;e<t.length;e+=1){t[e]=r(t[e])}return t.join("")}if(y(a("hello"))!=="5d41402abc4b2a76b9719d911017c592"){i=function(t,e){var r=(t&65535)+(e&65535),n=(t>>16)+(e>>16)+(r>>16);return n<<16|r&65535}}if(typeof ArrayBuffer!=="undefined"&&!ArrayBuffer.prototype.slice){(function(){function l(t,e){t=t|0||0;if(t<0){return Math.max(t+e,0)}return Math.min(t,e)}ArrayBuffer.prototype.slice=function(t,e){var r=this.byteLength,n=l(t,r),a=r,o,i,u,s;if(e!==f){a=l(e,r)}if(n>a){return new ArrayBuffer(0)}o=a-n;i=new ArrayBuffer(o);u=new Uint8Array(i);s=new Uint8Array(this,n,o);u.set(s);return i}})()}function g(t){if(/[\u0080-\uFFFF]/.test(t)){t=unescape(encodeURIComponent(t))}return t}function e(t,e){var r=t.length,n=new ArrayBuffer(r),a=new Uint8Array(n),o;for(o=0;o<r;o+=1){a[o]=t.charCodeAt(o)}return e?a:n}function b(t){return String.fromCharCode.apply(null,new Uint8Array(t))}function m(t,e,r){var n=new Uint8Array(t.byteLength+e.byteLength);n.set(new Uint8Array(t));n.set(new Uint8Array(e),t.byteLength);return r?n:n.buffer}function _(t){var e=[],r=t.length,n;for(n=0;n<r-1;n+=2){e.push(parseInt(t.substr(n,2),16))}return String.fromCharCode.apply(String,e)}function C(){this.reset()}C.prototype.append=function(t){this.appendBinary(g(t));return this};C.prototype.appendBinary=function(t){this._buff+=t;this._length+=t.length;var e=this._buff.length,r;for(r=64;r<=e;r+=64){d(this._hash,h(this._buff.substring(r-64,r)))}this._buff=this._buff.substring(r-64);return this};C.prototype.end=function(t){var e=this._buff,r=e.length,n,a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o;for(n=0;n<r;n+=1){a[n>>2]|=e.charCodeAt(n)<<(n%4<<3)}this._finish(a,r);o=y(this._hash);if(t){o=_(o)}this.reset();return o};C.prototype.reset=function(){this._buff="";this._length=0;this._hash=[1732584193,-271733879,-1732584194,271733878];return this};C.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash}};C.prototype.setState=function(t){this._buff=t.buff;this._length=t.length;this._hash=t.hash;return this};C.prototype.destroy=function(){delete this._hash;delete this._buff;delete this._length};C.prototype._finish=function(t,e){var r=e,n,a,o;t[r>>2]|=128<<(r%4<<3);if(r>55){d(this._hash,t);for(r=0;r<16;r+=1){t[r]=0}}n=this._length*8;n=n.toString(16).match(/(.*?)(.{0,8})$/);a=parseInt(n[2],16);o=parseInt(n[1],16)||0;t[14]=a;t[15]=o;d(this._hash,t)};C.hash=function(t,e){return C.hashBinary(g(t),e)};C.hashBinary=function(t,e){var r=a(t),n=y(r);return e?_(n):n};C.ArrayBuffer=function(){this.reset()};C.ArrayBuffer.prototype.append=function(t){var e=m(this._buff.buffer,t,true),r=e.length,n;this._length+=t.byteLength;for(n=64;n<=r;n+=64){d(this._hash,v(e.subarray(n-64,n)))}this._buff=n-64<r?new Uint8Array(e.buffer.slice(n-64)):new Uint8Array(0);return this};C.ArrayBuffer.prototype.end=function(t){var e=this._buff,r=e.length,n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a,o;for(a=0;a<r;a+=1){n[a>>2]|=e[a]<<(a%4<<3)}this._finish(n,r);o=y(this._hash);if(t){o=_(o)}this.reset();return o};C.ArrayBuffer.prototype.reset=function(){this._buff=new Uint8Array(0);this._length=0;this._hash=[1732584193,-271733879,-1732584194,271733878];return this};C.ArrayBuffer.prototype.getState=function(){var t=C.prototype.getState.call(this);t.buff=b(t.buff);return t};C.ArrayBuffer.prototype.setState=function(t){t.buff=e(t.buff,true);return C.prototype.setState.call(this,t)};C.ArrayBuffer.prototype.destroy=C.prototype.destroy;C.ArrayBuffer.prototype._finish=C.prototype._finish;C.ArrayBuffer.hash=function(t,e){var r=o(new Uint8Array(t)),n=y(r);return e?_(n):n};return C})},{}]},{},[3]);