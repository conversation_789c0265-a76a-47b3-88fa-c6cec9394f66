<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd" version="3.1">
    <display-name>capital-lcab-manage</display-name>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            classpath*:/spring/applicationContext-root.xml,
            classpath*:/spring/applicationContext-persistence.xml,
            classpath*:/spring/applicationContext-jedis.xml,
            classpath*:/spring/applicationContext-security.xml
        </param-value>
    </context-param>
    <context-param>
        <param-name>webAppRootKey</param-name>
        <param-value>capital-lcab-manage.root</param-value>
    </context-param>
    <context-param>
        <param-name>spring.profiles.active</param-name>
        <param-value>dev</param-value>
    </context-param>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.web.util.WebAppRootListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
    </listener>

    <!--健康检查过滤器 -->
    <filter>
        <filter-name>healthCheckFilter</filter-name>
        <filter-class>com.stock.core.security.HealthCheckFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>healthCheckFilter</filter-name>
        <url-pattern>/healthCheck</url-pattern>
    </filter-mapping>
    <!--动态数据库过滤器 -->
    <filter>
        <filter-name>dynamicDataSourceFilter</filter-name>
        <filter-class>com.stock.core.security.DynamicDataSourceFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>dynamicDataSourceFilter</filter-name>
        <servlet-name>capital-lcab-manage</servlet-name>
    </filter-mapping>
    <!--请求跟踪过滤器 -->
    <filter>
        <filter-name>traceRequestFilter</filter-name>
        <filter-class>com.stock.core.web.TraceRequestFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>traceRequestFilter</filter-name>
        <servlet-name>capital-lcab-manage</servlet-name>
    </filter-mapping>

    <!--编码过滤器 -->
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <!--强制转换编码(request和response均适用) -->
        <init-param>
            <param-name>ForceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <servlet-name>capital-lcab-manage</servlet-name>
    </filter-mapping>

    <!-- 会话过滤器 -->
    <filter>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <servlet-name>capital-lcab-manage</servlet-name>
    </filter-mapping>

    <!-- 安全过滤器 -->
    <filter>
        <filter-name>springSecurityFilterChain</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>springSecurityFilterChain</filter-name>
        <servlet-name>capital-lcab-manage</servlet-name>
    </filter-mapping>

    <!--前端控制器 -->
    <servlet>
        <servlet-name>capital-lcab-manage</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>throwExceptionIfNoHandlerFound</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath*:/spring/applicationContext-mvc.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>capital-lcab-manage</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>
    
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/views/error/404.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>

    <session-config>
        <session-timeout>360</session-timeout>
    </session-config>

</web-app>