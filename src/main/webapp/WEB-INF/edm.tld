<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2.0"
        xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd">
    <description>edm taglib</description>
    <display-name>edm Tags</display-name>
    <tlib-version>2.3</tlib-version>
    <short-name>e</short-name>
    <uri>/edm-tags</uri>
    <tag>
        <description>base html tag</description>
        <name>base</name>
        <tag-class>com.stock.core.tag.BaseTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>css modules, default all</description>
            <name>cssModules</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>javascript modules, default all</description>
            <name>jsModules</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <dynamic-attributes>false</dynamic-attributes>
    </tag>
    <tag>
        <description>import business js tag</description>
        <name>js</name>
        <tag-class>com.stock.core.tag.JsTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>add md5 parameter</description>
            <name>debug</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <dynamic-attributes>false</dynamic-attributes>
    </tag>
    <tag>
        <description>grid</description>
        <name>grid</name>
        <tag-class>com.stock.core.tag.GridTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>id</description>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>query action url</description>
            <name>action</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>data source</description>
            <name>dataSource</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>support ajax load data</description>
            <name>ajax</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>css style</description>
            <name>cssStyle</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>css class</description>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>state restore</description>
            <name>restore</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>paging</description>
            <name>paging</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>ordering</description>
            <name>ordering</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>length menu</description>
            <name>lengthMenu</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>default display length</description>
            <name>defaultPageSize</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <dynamic-attributes>false</dynamic-attributes>
    </tag>
    <tag>
        <description>grid column</description>
        <name>gridColumn</name>
        <tag-class>com.stock.core.tag.GridColumnTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>label</description>
            <name>label</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>display column</description>
            <name>displayColumn</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>order column</description>
            <name>orderColumn</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>render column</description>
            <name>renderColumn</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>orderable</description>
            <name>orderable</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>css style</description>
            <name>cssStyle</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>css class</description>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <dynamic-attributes>false</dynamic-attributes>
    </tag>
    <tag>
        <description>datatable</description>
        <name>table</name>
        <tag-class>com.stock.core.tag.TableTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>query action url</description>
            <name>action</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>column num</description>
            <name>col</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>id</description>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>css style</description>
            <name>cssStyle</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>css class</description>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <dynamic-attributes>false</dynamic-attributes>
    </tag>
    <tag>
        <description>orderColumnTag</description>
        <name>orderColumn</name>
        <tag-class>com.stock.core.tag.OrderColumnTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>label</description>
            <name>label</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>column</description>
            <name>column</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>orderClass</description>
            <name>orderClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>orderActiveClass</description>
            <name>orderActiveClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>iconAscClass</description>
            <name>iconAscClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <description>iconDescClass</description>
            <name>iconDescClass</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>pager tag</description>
        <name>pager</name>
        <tag-class>com.stock.core.tag.PagerTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>action</description>
            <name>action</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>querySession</description>
            <name>querySession</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Renders an HTML 'select' element. Supports databinding to the selected option.</description>
        <name>codelist</name>
        <tag-class>com.stock.core.tag.CodeListTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <description>Path to property for data binding</description>
            <name>path</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Enable/disable HTML escaping of rendered values.</description>
            <name>htmlEscape</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Equivalent to "class" - HTML Optional Attribute</description>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Equivalent to "class" - HTML Optional Attribute. Used when the bound field has errors.</description>
            <name>cssErrorClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Equivalent to "style" - HTML Optional Attribute</description>
            <name>cssStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Optional Attribute. Setting the value of this attribute to 'true' (without the quotes) will disable the HTML element.</description>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>code table no</description>
            <name>codeno</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>add empty option</description>
            <name>emptyoption</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>modify empty option label</description>
            <name>emptyLabel</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <dynamic-attributes>true</dynamic-attributes>
    </tag>

    <tag>
        <description>date tag.</description>
        <name>date</name>
        <tag-class>com.stock.core.tag.DateTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Path to property for data binding</description>
            <name>path</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Enable/disable HTML escaping of rendered values.</description>
            <name>htmlEscape</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Equivalent to "class" - HTML Optional Attribute</description>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Equivalent to "class" - HTML Optional Attribute. Used when the bound field has errors.</description>
            <name>cssErrorClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Equivalent to "style" - HTML Optional Attribute</description>
            <name>cssStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Optional Attribute. Setting the value of this attribute to 'true' (without the quotes) will disable the HTML element.</description>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Standard Attribute</description>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Optional Attribute</description>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Optional Attribute</description>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Optional Attribute</description>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Event Attribute</description>
            <name>onselect</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>HTML Optional Attribute. Setting the value of this attribute to 'true' (without the quotes) will make the HTML element readonly.</description>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Common Optional Attribute</description>
            <name>autocomplete</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>string format type</description>
            <name>format</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>start display view</description>
            <name>startView</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>min display view</description>
            <name>minView</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>max display view</description>
            <name>maxView</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>minute step</description>
            <name>minuteStep</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>show today</description>
            <name>showToday</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>placeholder</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <dynamic-attributes>true</dynamic-attributes>
    </tag>
</taglib>