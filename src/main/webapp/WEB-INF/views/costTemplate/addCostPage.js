var historyPriceList = []

$(document).ready(function () {

    $('#saveBtn').bind('click', function () {

        popConfirm("确认保存?", function () {
            saveCostInfo();
        });

    });

    $('#closeBtn').bind('click', function () {
        popConfirm('确定关闭？', function () {
            closeWin();
        });
    });
})

//勾选用户类型/机构名称
function personTypeChange(ele, index) {
    var checkFlag = $('input[name="' + ele.name + '"]').is(':checked')
    if (checkFlag) {
        //开放是否收费输入框
        var html1 =
            '<input type="radio" name="ifCharge_' + index + '" id="ifNotCharge_' + index + '" value="0" checked="checked" onchange="notChargeChange(this,' + index + ')" /> <label for="ifNotCharge_' + index + '"> 免费 </label>' +
            '<input type="radio" name="ifCharge_' + index + '" id="ifCharge_' + index + '" value="1" style="margin-left: 15px" onchange="chargeChange(this,' + index + ')" /> <label for="ifcharge_' + index + '"> 收费 </label>'
        $('input[name="' + ele.name + '"]').parent().parent().find('.ifChargeClass').html(html1)
        var html2 = '<input style="width: 60%" type="text" name="limitNumber_' + index + '" id="limitNumber_' + index + '" onkeyup="value=value.replace(/[^\\-?\\d.]/g,\'\')" value="" class="isNumber"/><label>人</label>'
        $('input[name="' + ele.name + '"]').parent().parent().find('.limitNumberClass').html(html2)
        //查询历史收费记录
        var personType = $('input[name="' + ele.name + '"]').val()
        recoveryPrice(personType)
    } else {
        $('input[name="' + ele.name + '"]').parent().parent().find('.ifChargeClass').html("--")
        $('input[name="' + ele.name + '"]').parent().parent().find('.ifDiscountClass').html("--")
        $('input[name="' + ele.name + '"]').parent().parent().find('.discountClass').html("--")
        $('input[name="' + ele.name + '"]').parent().parent().find('.priceClass').html("--")
        $('input[name="' + ele.name + '"]').parent().parent().find('.limitNumberClass').html("--")
    }
}

//是否收费
function notChargeChange(ele, index) {
    $('input[name="' + ele.name + '"]').parent().parent().find('.ifDiscountClass').html("--")
    $('input[name="' + ele.name + '"]').parent().parent().find('.discountClass').html("--")
    $('input[name="' + ele.name + '"]').parent().parent().find('.priceClass').html('--')
}

function chargeChange(ele, index) {
    //开放是否打折选择框
    var html1 =
        '<input type="radio" name="ifDiscount_' + index + '" id="ifDiscount_' + index + '" value="0" checked="checked" onchange="notDiscountChange(this,' + index + ')" /> <label for="ifDiscount_' + index + '"> 不打折 </label>' +
        '<input type="radio" name="ifDiscount_' + index + '" id="ifNotDiscount_' + index + '" value="1" style="margin-left: 15px" onchange="discountChange(this,' + index + ')" /> <label for="ifNotDiscount_' + index + '"> 打折 </label>'
    $('input[name="' + ele.name + '"]').parent().parent().find('.ifDiscountClass').html(html1)
    //开放实际价格输入框
    let originalPrice = getValue($("#originalPrice").val())
    var html2 = '<input type="text" style="width: 60%" id="price_' + index + '" name="price_' + index + '" value="' + originalPrice + '" onkeyup="value=value.replace(/[^\\-?\\d.]/g,\'\')" value="" class="isNumber" onblur="priceCal(this,' + index + ')"/><label>元</label>'
    $('input[name="' + ele.name + '"]').parent().parent().find('.priceClass').html(html2)

    //查询历史收费记录
    var personType = $(ele).parent().parent().find('#codeValue_'+index).val()
    recoveryPrice(personType)
}

//是否打折
function notDiscountChange(ele, index) {
    let originalPrice = ''
    if (getValue($("#originalPrice").val()) != undefined) {
        //不打折时 实际价格 = 原价
        originalPrice = getValue($("#originalPrice").val());
        $("#price_" + index).val(originalPrice)
    }
    $('input[name="' + ele.name + '"]').parent().parent().find('.discountClass').html("--")
}

function discountChange(ele, index) {
    var html = '<input type="text" style="width: 60%" id="discount_' + index + '" name="discount_' + index + '" onkeyup="value=value.replace(/[^\\-?\\d.]/g,\'\')" value="" class="isNumber" onblur="discountCal(this,' + index + ')"/><label>折</label>'
    $('input[name="' + ele.name + '"]').parent().parent().find('.discountClass').html(html)

    //查询历史收费记录
    var personType = $(ele).parent().parent().find('#codeValue_'+index).val()
    recoveryPrice(personType)
}

//根据折扣计算价格
function discountCal(obj, index) {
    if (getValue($("#originalPrice").val()) == "") {
        popMsg("请填写原价");
        return;
    }
    var originalPrice = getValue($("#originalPrice").val())
    var discount = $(obj).parent().parent().find("input[name^='discount']").val()
    var price = Number(originalPrice) * Number(discount) / 10
    $(obj).parent().parent().find("input[name^='price']").val(price.toFixed(2))
}

//根据价格计算折扣
function priceCal(obj, index) {
    if (getValue($("#originalPrice").val()) == "") {
        popMsg("请填写原价");
        return;
    }
    var originalPrice = getValue($("#originalPrice").val())
    var price = $(obj).parent().parent().find("input[name^='price']").val()
    var discount = Number(price) / Number(originalPrice) * 10
    $(obj).parent().parent().find("input[name^='discount']").val(discount.toFixed(2))
}

//保存cost参数
function saveCostInfo() {
    if (getValue($("#originalPrice").val()) == "") {
        popMsg("请填写原价");
        return;
    }
    if (getValue($("#templateName").val()) == "") {
        popMsg("请填写模板名称");
        return;
    }
    let personTypes = []
    $("#discountTableId").find("tr").each(function (n, obj) {
        var checkFlag, personType, ifCharge, ifDiscount, originalPrice, discount, price, limitNumber = ''
        $(obj).find('td').each(function (index, tdObj) {
            var nodes = tdObj.children
            for (let i = 0; i < nodes.length; i++) {
                let id = getValue(nodes[i].id)
                if (id.indexOf("codeValue") != -1) {
                    //人员类型列,没被选中，跳过
                    if (!nodes[i].checked) {
                        checkFlag = false
                        continue;
                    }
                    //选中
                    checkFlag = true
                    personType = nodes[i].value
                }
                if (id.indexOf("ifNotCharge") != -1) {
                    //收费类型-免费,选中
                    if (nodes[i].checked) {
                        ifCharge = nodes[i].value
                        discount = '0'
                        price = '0'
                        ifDiscount = '0'
                    }
                } else if (id.indexOf("ifCharge") != -1) {
                    //收费类型-收费,选中
                    if (nodes[i].checked) {
                        ifCharge = nodes[i].value
                    }
                }

                if (id.indexOf("ifNotDiscount") != -1) {
                    //打折类型-不打折,选中
                    if (nodes[i].checked) {
                        ifDiscount = nodes[i].value
                    } else {
                        discount = '10'//不打折默认10折
                    }
                } else if (id.indexOf("ifDiscount") != -1) {
                    //打折类型-打折,选中
                    if (nodes[i].checked) {
                        ifDiscount = nodes[i].value
                    }
                }

                if (id.indexOf("discount") != -1) {
                    //折数
                    if (nodes[i].value != '') {
                        discount = nodes[i].value
                    }
                }

                if (id.indexOf("price") != -1) {
                    //实际价格
                    if (nodes[i].value != '') {
                        price = nodes[i].value
                    } else {
                        //不打折默认原价
                        price = $("#originalPrice").val()
                    }
                }

                if (id.indexOf("limitNumber") != -1) {
                    //限制人数
                    if (nodes[i].value != '') {
                        limitNumber = nodes[i].value
                    }
                }
            }
        })
        var originalPrice = getValue($("#originalPrice").val())
        if (checkFlag) {
            if (originalPrice !== undefined && originalPrice !== '') {
                let name = $(obj).find('td').eq(0).find('label').text()
                if (price == '') {
                    popMsg(name + "请填写实际价格")
                    return;
                }
            }
            var personTypeDto = {
                relationId: $("#relationId").val(),
                templateName: $("#templateName").val(),
                personType: personType,
                ifFree: ifCharge,
                originalPrice: originalPrice,
                ifDiscount: ifDiscount,
                discount: discount,
                price: price,
                limitNumber: limitNumber,
                type: 0
            }
            personTypes.push(personTypeDto)
        }
    })
    //调用父级方法赋值
    var param = {
        relationId: $("#relationId").val(),
        personTypeList: personTypes
    }
    ajaxData("/costTemplate/saveTemplateCost", JSON.stringify(param), function (data) {
        popMsg("保存成功");
        closeWinCallBack();
    }, "application/json;charset=UTF-8");
}