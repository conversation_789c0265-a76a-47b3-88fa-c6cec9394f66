$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });

    $("#btnClear").bind("click", function() {
        document.getElementById("capcoCostDto").reset();
        search();
    });

    $("#addCost").bind("click", function() {
        addCost();
    });

})
function search(){
    ajaxTableQuery("tableAll", "/costTemplate/queryCostTemplate", $("#capcoCostDto").formSerialize());
}


// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function buyOpenTime(data, type, row, meta) {
    if(data.openStartTime === "" || data.openStartTime === null){
        return "- -";
    }else{
        return data.openStartTime + " 至 " + data.openEndTime;
    }
}

function releaseFlag(data, type, row, meta) {
    if(data.releaseFlag === '1'){
        return "是";
    }else{
        return "否";
    }
}

function columnOperation(data, type, row, meta) {
    let str = '';
    let deleteCostAuth = '';
    if (document.getElementById("deleteCostAuth")) {
        deleteCostAuth = true
    }
    str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="addCost(\'' + data.relationId + '\')">编辑</span>';
    if (deleteCostAuth) {
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="deleteCost(\'' + data.relationId + '\')">删除</span>';
    }
    if(!str) {
        str = '--';
    }
    return str;
}

function addCost(relationId){
    let param;
    let title;
    if (relationId) {
        title = "编辑收费模板";
        param = {
            relationId: relationId
        }
    } else {
        title = "新增收费模板";
    }
    popWin(title, '/costTemplate/addCostPageInit', param, '80%', '75%', callBackAddExam, '', callBackAddExam);
}

function deleteCost(relationId){
    popConfirm("当前操作不可恢复，确认删除?", function () {
        let param = {
            relationId:relationId
        }
        ajaxData("/costTemplate/deleteTemplateCost",param, function (res) {
            if(res){
                popMsg("删除成功");
            }else{
                popMsg("删除失败");
            }
            callBackAddExam();
        })
    });
}

function updateCard(id,releaseFlag){
    popConfirm("当前操作不可恢复，确认操作?", function () {
        let param = {
            id:id,
            releaseFlag:releaseFlag
        }
        ajaxData("/membershipCardConfig/updateMembershipCardStatus",param, function (res) {
            if(res){
                popMsg("已保存");
            }else{
                popMsg("修改失败");
            }
            callBackAddExam();
        })
    });
}



function callBackAddExam(){
    ajaxTableReload("tableAll", false);
}
