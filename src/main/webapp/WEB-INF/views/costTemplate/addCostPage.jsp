<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>北京上市公司协会培训管理平台</title>
  <e:base/>
  <e:js/>
</head>
<body>
<div class="panel">
  <div class="panel-body">
    <div id="chargeId" class="col-md-12" style="margin: 10px 0">
      <div>
        <form:form modelAttribute="capcoCostDto" id="queryForm" autocomplete="off">
          <form:hidden path="relationId" value="${capcoCostDto.relationId}"/>
          <div class="row">
            <label><span style="color: #FF0000;font-weight: bold;">*</span>模板名称：</label>
            <div style="width: 120px;display: inline-block">
              <form:input path="templateName" value="${templateName}" cssClass="form-control"/>
            </div>
            <label><span style="color: #FF0000;font-weight: bold;">*</span>原价：</label>
            <div style="width: 120px;display: inline-block">
              <form:input path="originalPrice" value="${originalPrice}" onkeyup="value=value.replace(/[^0-9.]/g,'')" cssClass="form-control"  onblur="originalPriceCal()"/>
            </div>
          </div>
          <c:if test="${personTypeList != null}">
            <table class="table table-bordered no-margin" style="text-align: center;float:left;" id="configCourseType">
              <thead>
              <tr>
                <td width="25%" class="sch-td">机构名称</td>
                <td width="15%" class="sch-td">收费类型</td>
                <td width="15%" class="sch-td">打折类型</td>
                <td width="10%" class="sch-td">折数</td>
                <td width="10%" class="sch-td">实际价格</td>
                <td width="10%" class="sch-td">限制人数</td>
              </tr>
              </thead>
              <tbody id="discountTableId">
              <c:forEach items="${personTypeList}" var="item" varStatus="status">
                <%--机构名称--%>
                <td style="text-align: left;padding-left: 40px">
                  <input type="checkbox" name="interest_${status.index}"
                         id="codeValue_${status.index}" value="${item.codeValue}"
                         onchange="personTypeChange(this,${status.index})"
                          <c:if test="${item.relationId !='' && item.relationId != null}">
                            checked="checked"
                          </c:if>
                  />
                  <label>${item.codeName}</label>
                </td>
                <%--收费类型--%>
                <td class="ifChargeClass">
                  <c:choose>
                    <c:when test="${item.relationId !='' && item.relationId != null}">
                      <input type="radio" name="ifCharge_${status.index}"
                             id="ifNotCharge_${status.index}"
                             value="0" onchange="notChargeChange(this,${status.index})"
                              <c:if test="${item.ifFree == '0'}">
                                checked="checked"
                              </c:if>
                      />
                      <label>免费</label>
                      <input type="radio" name="ifCharge_${status.index}"
                             id="ifCharge_${status.index}"
                             value="1" style="margin-left: 15px"
                             onchange="chargeChange(this,${status.index})"
                              <c:if test="${item.ifFree == '1'}">
                                checked="checked"
                              </c:if>
                      />
                      <label for="ifCharge_${status.index}">收费</label>
                    </c:when>
                    <c:otherwise>
                      --
                    </c:otherwise>
                  </c:choose>
                </td>
                <%--打折类型--%>
                <td class="ifDiscountClass">
                  <c:choose>
                    <c:when test="${item.relationId !='' && item.relationId != null && item.ifFree != '0'}">
                      <input type="radio" name="ifDiscount_${status.index}"
                             id="ifDiscount_${status.index}" value="0"
                             onchange="notDiscountChange(this,${status.index})"
                              <c:if test="${item.ifDiscount == '0'}">
                                checked="checked"
                              </c:if>
                      />
                      <label for="ifDiscount_${status.index}">不打折</label>
                      <input type="radio" name="ifDiscount_${status.index}"
                             id="ifNotDiscount_${status.index}" value="1"
                             style="margin-left: 15px"
                             onchange="discountChange(this,${status.index})"
                              <c:if test="${item.ifDiscount == '1'}">
                                checked="checked"
                              </c:if>
                      />
                      <label for="ifNotDiscount_${status.index}">打折</label>
                    </c:when>
                    <c:otherwise>
                      --
                    </c:otherwise>
                  </c:choose>
                </td>
                <%--折数--%>
                <td class="discountClass">
                <c:choose>
                  <c:when test="${item.relationId !='' && item.relationId != null && item.ifFree != '0' && item.ifDiscount != '0'}">
                    <input type="text" name="discount_${status.index}" style="width: 60%"
                           id="discount_${status.index}"
                           onkeyup="value=value.replace(/[^\-?\d.]/g,'')"
                           value="${item.discount}"
                           class="isNumber" onblur="discountCal(this,${status.index})"/>
                    <label>折</label>
                    </td>
                  </c:when>
                  <c:otherwise>
                    --
                  </c:otherwise>
                </c:choose>
                <%--实际价格--%>
                <td class="priceClass">
                  <c:choose>
                    <c:when test="${item.relationId !='' && item.relationId != null && item.ifFree != '0'}">
                      <input type="text" name="price_${status.index}" style="width: 60%"
                             id="price_${status.index}"
                             onkeyup="value=value.replace(/[^\-?\d.]/g,'')"
                             value="${item.price}"
                             class="isNumber" onblur="priceCal(this)"/>
                      <label>元</label>
                    </c:when>
                    <c:otherwise>
                      --
                    </c:otherwise>
                  </c:choose>
                </td>
                <%--限制人数--%>
                <td class="limitNumberClass">
                  <c:choose>
                    <c:when test="${item.relationId !='' && item.relationId != null}">
                      <input type="text" name="limitNumber_${status.index}" style="width: 60%"
                             id="limitNumber_${status.index}"
                             onkeyup="value=value.replace(/[^\-?\d.]/g,'')"
                             value="${item.limitNumber}"
                             class="isNumber"/>
                      <label>人</label>
                    </c:when>
                    <c:otherwise>
                      --
                    </c:otherwise>
                  </c:choose>
                </td>
                </tr>
              </c:forEach>
              </tbody>
            </table>
          </c:if>
        </form:form>
      </div>
    </div>
    <div class="col-md-12" style="text-align: center;padding-top: 30px">
      <input type="button" id="closeBtn" class="btn btn-default" value="关闭">
      <input type="button" id="saveBtn" class="btn btn-primary" value="保存">
    </div>
  </div>
</div>
</body>
</html>
