<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <script type="text/javascript">
        document.getElementById("capcoCostDto").addEventListener("submit", function(event) {
            event.preventDefault(); // 阻止表单提交
        });
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="capcoCostDto" id="capcoCostDto" >
            <div class="row" style="margin-top: 10px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="width: 100px">模板名称：</label>
                    <div class="col-md-3">
                        <form:input path="templateName" placeholder="请输入模板名称" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="col-md-12 text-right" >
                        <span id="btnClear" class="btn btn-default">清空</span>
                        <sec:authorize access="hasAuthority('RES_QUERY_COST_TEMPLATE_LIST_AUTHORITY_3')" >
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_ADD_COST_PAGE_INIT_AUTHORITY_3')" >
                            <span id="addCost" class="btn btn-primary">新建</span>
                        </sec:authorize>
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_DELETE_COST_TEMPLATE_AUTHORITY_3')">
            <%--调价模板删除权限--%>
            <input type="hidden" id="deleteCostAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/costTemplate/queryCostTemplate" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="模板名称" displayColumn="templateName" orderable="false"
                              cssClass="text-center" cssStyle="width:14%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:14%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
