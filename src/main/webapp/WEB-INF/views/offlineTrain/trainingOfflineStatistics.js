$(document).ready(function() {
    queryRegistrationByIdList()//统计页数值
    //查询
    $("#btnQuery").bind("click",function() {
        var param = {
            trainId : $("#trainId").val(),
            nickName : $("#nickName").val(),
            phone :  $("#phone").val(),
            status : $("#status").val(),
            paymentStatus : $("#paymentStatus").val(),
        }

        ajaxTableQuery("table1", "/trainingOffline/trainingOfflineSignUpList",param);
        queryRegistrationByIdList();
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("superviseInfoForm").reset();
        var param = {
            trainId : $("#trainId").val(),
        }
        ajaxTableQuery("table1", "/trainingOffline/trainingOfflineSignUpList",param);
    });
    //导出
    $("#exportTable").bind("click",function () {
        window.open(contextPath + "/trainingOffline/exportDetailTable?" + $("#superviseInfoForm").serialize());
    });
    //添加
    $("#addUser").bind("click", function () {
        let trainId = $("#trainId").val();
        let param = {
            trainId: trainId
        }
        parent.popWin('添加人员',"/trainingOffline/trainingOfflineAddUserInit", param, "80%", "100%", callBackAddUser);
    });
});

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

// 操作
function renderColumnOperation(data, type, row, meta){
    var str1 = '<a href="#" onClick="toExamine(\''+data.id+'\',\''+data.status+'\',\''+data.trainId+'\')" style="margin-right:4px;">审核</a>';
    if(data.paymentStatus == "未交费"){
        var str3 =	'<a href="#" onClick="releaseInfo(\''+data.id+'\',\''+data.paymentStatus+'\',\''+data.trainId+'\')" style="margin-right:4px;">缴费</a>';
    }else{
        var str3 =	'<a href="#" onClick="releaseInfo(\''+data.id+'\',\''+data.paymentStatus+'\',\''+data.trainId+'\')" style="margin-right:4px;">取消缴费</a>';
    }
    return str1 + "| " + str3;
}

//缴费
function releaseInfo(id,paymentStatus,trainId){
    if(paymentStatus == '已缴费'){
        popConfirm("确认取消缴费吗？",function(){
            var param = {
                id:id,
                paymentStatus:0,
                trainId:trainId
            };
            ajaxData("/trainingOffline/saveTrainingOfflineSignUpMoney",param,function(data) {
                if(data.length >= 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1",false);
                    queryRegistrationByIdList()
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }else{
        popConfirm("确认缴费吗？",function(){
            var param = {
                id:id,
                paymentStatus:1,
                trainId:trainId
            };
            ajaxData("/trainingOffline/saveTrainingOfflineSignUpMoney",param,function(data) {
                if(data.length > 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1",false);
                    queryRegistrationByIdList()
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}
function toExamine(id,status,trainId){
    var param ={
        id:id,
        status:'0'
    };
    parent.popWin('审核', '/trainingOffline/trainingOfflineToExamine', param, '40%', '50%', callBackAddUser,'',callBackAddUser);
    // popWin("审核", "/trainingOffline/trainingOfflineToExamine", param, "350px", "300px", "", "");
}
function callBackAddUser() {
    queryRegistrationByIdList()
    ajaxTableReload("table1",false);
}

function queryRegistrationByIdList() {
    var param = {
        trainId : $("#trainId").val(),
        nickName : $("#nickName").val(),
        phone :  $("#phone").val(),
        status : $("#status").val(),
        paymentStatus : $("#paymentStatus").val(),
    };
    ajaxData("/trainingOffline/trainingOfflineRegistrationByIdList",param,function(data) {
        if(data.singUpNum !=null && data.singUpNum !='' ){
            document.getElementById("trainNum").innerHTML =
                "本活动报名人数"+data.singUpNum+"人,审核通过"+data.toExamineNum+"人，"+data.waitExamineNum+"人待审核，"+data.notExamineNum+"人未通过。"+
                data.toMoneyNum+"人已缴费，共收取"+data.moneyAllNum+"元";
        }else{
            document.getElementById("trainNum").innerHTML = '无报名人员数据'
        }

    });
}



