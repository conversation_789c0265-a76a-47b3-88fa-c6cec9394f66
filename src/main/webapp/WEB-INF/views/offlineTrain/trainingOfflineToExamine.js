$(document).ready(function () {
    $('#btnSave').click(function () {
        var param = {
            id : $("#id").val(),
            status :  $("#status").val(),
            trainId :  $("#trainId").val(),
        }
        ajaxSubmitForm("/trainingOffline/saveTrainingOfflineSignUpMoney", "",function(data){
            closeWinCallBack();
            // queryRegistrationByIdList()
            popMsg("审核成功");
        })
    });

    $('#btnClose').click(function () {
        closeWinCallBack();
    });

    tSelectInit();
})

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'labelValue',
        name: 'labelName',
        value: 'labelValue',
        grade: 1,
        resultType: 'children',
        inputType: 'radio', // 单选
        useFooter: false,// 不显示清空、确定
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#status').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
