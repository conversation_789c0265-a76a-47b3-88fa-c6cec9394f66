<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />

    <style>
        .user_type_row {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="trainingOfflineAddForm" modelAttribute="trainingOfflineDto" cssClass="form-horizontal">
            <form:hidden path="trainId" value="${trainingOfflineDto.trainId}"/>
            <form:hidden path="publishType" value="${trainingOfflineDto.publishType}"/>
            <form:hidden path="timeRangeMapList" value="${trainingOfflineDto.timeRangeMapList}"/>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>活动类型:</label>
                <div class="col-md-10" style="width: 370px;margin-left: -10px;">
                    <form:select path="trainType" cssClass="form-control" onchange="rem()">
                        <form:option value="">请选择</form:option>
                        <c:forEach items="${trainType}" var="item" varStatus="status">
                            <form:option value="${item.codeValue}">${item.codeName}</form:option>
                        </c:forEach>
                    </form:select>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>活动标题:</label>
                <div class="col-md-10 no-padding">
                    <form:input path="trainTitle" cssClass="form-control"  placeholder="请输入标题" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>封面图:</label>
                <div class="col-md-10" style="display: inline-block;position: relative; ">
                        <span class="btn btn-sm btn-success fileinput-button">
                            <span>上传</span>
                            <input id="up" type="file" name="files">
                        </span>
                    <span onclick="clearImg()" class="btn btn-sm" style="margin-left: 20px;">删除</span>
                    <div style="color: red">(注：请上传5：3的图片)</div>
                </div>
                <form:hidden path="trainImage"/>
            </div>
            <div class="col-md-12"  style="display: flex;margin-left: 100px;margin-bottom:5px ">
                <div id="imgId" class="col-md-5" >
                        <img   id="ImgPr" src="${trainingOfflineDto.trainImage}" style="width: 200px;height: 130px;"/>
                        <input type="hidden" name="fileId" id="fileId" value="${trainingOfflineDto.trainImage}"/>
                        <input type="hidden" name="delFileId" id="delFileId" value="${trainingOfflineDto.trainImage}"/>
                        <form:hidden path="trainImage" />
                </div>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 91px;"><font color="#FF0000">*</font>报名时间:</label>
                <div class="col-md-10 daterange" style="width: 370px">
                    <form:input path="signUpTimeStr" placeholder="请输报名时间" cssClass="form-control"
                                autocomplete="on"/>
                </div>
            </div>
            <form:hidden path="activeTimeStr"/>
            <div class="col-md-12" id="activeTimeRange" style="display: flex;align-items: center">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 91px;">活动场次:</label>
                <div class="col-md-10 daterange" style="width: 370px">
                    <input id="activeTimeStr9" name="activeTimeStr9" class="form-control valid" placeholder="请输入活动时间" type="text" value="" readonly="" data-original-title="" title="" aria-invalid="false">
                </div>
                <div class="no-padding control-label" style="margin-top: 5px;width: 310px;">
                    <span>报名人数</span>
                    <input id="signLimit" style="width: 240px;display: inline;margin-left: 10px" class="form-control" placeholder="请输入该场次最大报名人数" value="0" type="number" autocomplete="off">
                </div>
                <input id="timeRangeId" type="hidden" value="">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 391px;display: flex">
                    <i class="fa fa-plus-square-o btn-row-add" style="font-size: 20px;margin-left: 10px" title="新增时间段" onclick="addTimeRange()"></i>
                </label>
            </div>
            <div class="col-md-12" id="issueTimeId" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 91px;">证书下发时间</label>
                <div class="col-md-10 no-padding daterange" style="width: 350px;margin-left: 10px;">
                    <form:input placeholder="请选择证书下发时间点" autocomplete="off" path="issueTime" cssClass="jeinput form-control " readonly="true" />
                    <i class="glyphicon glyphicon-calendar fa fa-calendar daterangetag" style="bottom: 16px;right: 8px;"></i>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>活动地点:</label>
                <div class="col-md-10 no-padding" style="display: flex;">
                    <form:input path="address" cssClass="form-control"  placeholder="请输入活动地点" maxlength="45"
                                autocomplete="off"/>
                    <%--<span style="color:red;font-size: 10px;width: 200px">（注：需要精确到市）</span>--%>
                </div>
            </div>
            <%--<div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>具体地点:</label>
                <div class="col-md-10 no-padding" style="">
                    <form:input path="specificAddress" cssClass="form-control"  placeholder="请输入具体地点" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>--%>
            <div class="col-md-12"  style="display: flex" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>是否需要人脸验证:</label>
                <div class="col-md-10" style="width: 370px;margin-left: -10px;">
                    <form:select path="ifCertified" cssClass="form-control"  >
                        <form:option value="">请选择</form:option>
                        <form:option value="0">不需要</form:option>
                        <form:option value="1">需要</form:option>
                    </form:select>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>是否允许缴费:</label>
                <div class="col-md-10" style="width: 370px;margin-left: -10px;">
                    <form:select path="ifPayShow" cssClass="form-control"  >
                        <form:option value="1">允许</form:option>
                        <form:option value="0">不允许</form:option>
                    </form:select>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>是否支持开票:</label>
                <div class="col-md-10" style="width: 370px;margin-left: -10px;">
                    <form:select path="ifInvoiceShow" cssClass="form-control"  >
                        <form:option value="1">支持</form:option>
                        <form:option value="0">不支持</form:option>
                    </form:select>
                </div>
            </div>

<%--            <div id="astrict">--%>
<%--                <div class="col-md-12"  style="display: flex" >--%>
<%--                    <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>是否限制人数:</label>--%>
<%--                    <div class="col-md-10" id="block" style="width: 370px;margin-left: -10px;">--%>
<%--                        <form:select path="peopleLimit" cssClass="form-control"  >--%>
<%--                            <form:option value="">请选择</form:option>--%>
<%--                            <form:option value="0">否</form:option>--%>
<%--                            <form:option value="1">是</form:option>--%>
<%--                        </form:select>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div class="col-md-12"  style="display: flex;" >--%>
<%--                        <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000"></font>限制数量:</label>--%>
<%--                        <div class="col-md-10 no-padding" style="width: 183px">--%>
<%--                            <form:input path="signUpNumber" id="signUpNumber" cssClass="form-control"  placeholder="限制人数" onkeyup="value=value.replace(/[^\-?\d.]/g,'')" maxlength="45"--%>
<%--                                        autocomplete="off"/>--%>
<%--                        </div>--%>
<%--                </div>--%>
<%--            </div>--%>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>活动形式:</label>
                <div class="col-md-10" style="width: 370px;margin-left: -10px;">
                    <form:select path="learningForm" cssClass="form-control">
                        <form:option value="">请选择</form:option>
                        <form:option value="0">线上</form:option>
                        <form:option value="1">线下</form:option>
                    </form:select>
                </div>
            </div>
            <div>
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;margin-left: 10px;"><font color="#FF0000">*</font>适用用户类型:</label>
                <div class="col-md-10" style="display: inline-block;position: relative;padding-left: 0 ">
                    <input id="btnSelectCost" type="button" style="margin-right: 15px;" class="btn" value="选择用户类型"/>
                    <form:hidden path="personTypeList" value="${trainingOfflineDto.personTypeList}"/>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex;padding-top: 5px;">
                <label class="col-md-2 no-padding control-label" style="top: 5px;width: 100px;">讲师:</label>
                    <div class="col-md-10 no-padding" style="width: 350px">
                        <input id="trainTeacher" type="text" class="t-select" json-data='${teacherList}' selected-ids="${trainingOfflineDto.trainTeacher}"/>
                        <input name="trainTeacher" type="hidden" placeholder="请选择讲师" value='${trainingOfflineDto.trainTeacher}'/>
                        <input name="trainTeacherStr" type="hidden" value='${trainingOfflineDto.trainTeacherStr}'/>
                    </div>
            </div>
            <div class="col-md-12"  style="display: flex;padding-top: 5px;">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">职务:</label>
                <div class="col-md-10 no-padding" style="width: 350px">
                    <input id="post" type="text" class="t-select" json-data='${postList}' selected-ids="${trainingOfflineDto.post}"/>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex;">
                <label class="col-md-2 no-padding control-label" style="top: 5px;width: 100px;">辖区:</label>
                <div class="col-md-10 no-padding" style="width: 350px">
                    <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' selected-ids="${trainingOfflineDto.belongCommission}"/>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex;padding-top: 5px;">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">课件:</label>
                <div class="col-md-10" style="display: inline-block;position: relative;">
                    <span class="btn btn-sm btn-success fileinput-button">
                        <span>上传</span>
                        <input id="fileupload" type="file" name="files" multiple>
                    </span>
                </div>
            </div>
            <div class="form-group" style="margin-left:91px; margin-top: 2px" id="fileDiv">
                <c:forEach items="${trainingOfflineDto.courseList}" var="item" varStatus="status">
                    <div class="col-md-12">
                        <span >${status.index + 1}、</span>
                        <span onclick="downloadFile('${item.id}')" style="cursor: pointer;" class="file-span"  data-file-type="1" data-atta-id="${item.id}" data-file-id="${item.attUrl}">${item.attName}</span>
                        <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>
                    </div>
                </c:forEach>
            </div>

            <div class="col-md-12" style="display: flex;margin-bottom: 10px;">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 91px;margin-left: 12px;">正文:</label>
                <div class="col-md-10 no-padding" style="width: 100%">
                    <script id="content" type="text/plain"  style="width: 100% !important;">${trainingOfflineDto.trainContent}</script>
                </div>
            </div>
<%--            <div class="col-md-12" style="display: flex;margin-bottom: 10px;">--%>
<%--                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 91px;margin-left: 12px;">正文:</label>--%>
<%--                <div class="col-md-10 no-padding" style="width: 100%;">--%>
<%--                    <script id="content" type="text/plain">${trainingOfflineDto.trainContent}</script>--%>
<%--                </div>--%>
<%--            </div>--%>
<%--            <div class="col-md-12" style="display: flex">--%>
<%--                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">web正文</label>--%>
<%--                <div class="col-md-10 no-padding" >--%>
<%--                    <script id="content" type="text/plain">${trainingDynamicsDto.trainContent}</script>--%>
<%--                </div>--%>
<%--            </div>--%>
            <div class="row" style="margin-top: 20px;text-align: center;">
                <div id="not_save">
                    <input type="button"  style="margin-right: 15px;" class="btn btn-primary" value="保存" onclick="saveTrain()"/>
                    <input type="button"  style="margin-right: 15px;" class="btn btn-primary" id="releaseClick" value="取消发布" />
                    <input type="button" id="btnCancelSave" class="btn btn-default" value="取消"/>
                </div>
                <div id="save">
                    <input type="button"  style="margin-right: 15px;" class="btn btn-primary" value="保存" onclick="saveTrain(0)"/>
                    <input type="button" onclick="saveTrain(1)" style="margin-right: 15px;" class="btn btn-primary" value="保存并发布" />
                    <input type="button" id="btnCancel" class="btn btn-default" value="取消"/>
                </div>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
