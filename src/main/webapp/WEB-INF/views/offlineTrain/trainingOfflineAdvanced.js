var selIds = [];
var selPerson = [];
var indexNum = 0;
$(document).ready(function() {

    // 下拉初始化
    tSelectInit();

    // 日期初始化
    dateInit();

    //查询
    $("#btnQuery").bind("click",function() {
        search()
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("superviseInfoForm").reset();
        $('input[name="personType"]').val("");
        $('input[name="status"]').val("");
        $('input[name="belongCommission"]').val("");

        search()
    });
    //导出
    $("#exportTable").bind("click",function () {
        window.open(contextPath + "/trainingOffline/exportDetailTable?" + $("#superviseInfoForm").serialize());
    });
    // excel上传
    modelUpdate('btnUploadFile');
    $("#makeCertificate").bind("click",function () {
        makeCertificate();
    });
    $("#downCertificate").bind("click",function () {
        downCertificate();
    });
    //添加人员 - 从系统审核判定不通过的人员里选择
    $("#addUser").bind("click", function () {
        let trainId = $("#trainId").val();
        let param = {
            trainId: trainId
        }
        parent.popWin('添加人员',"/trainingOffline/trainingOfflineAddUserInit", param, "100%", "100%", callBackAddUser);
    });
    //添加人员 - 从所有用户里选择
    $("#addNewUser").bind("click", function () {
        let trainId = $("#trainId").val();
        let param = {
            trainId: trainId
        }
        parent.popWin('添加未报名人员',"/trainingOffline/trainingOfflineAddNewUserInit", param, "100%", "100%", callBackAddUser);
    })
    //发动短信通知
    $("#sendSMSNotification").bind("click",function() {
        sendSMSNotification()
    });
    //批量审核
    $("#batchExamine").bind("click",function() {
        batchExamine(selIds)
    });
    //顺延
    $("#putOff").bind("click", function () {
        if (selIds == "" || selIds == null || selIds == undefined) {
            popMsg("请选择人员")
            return;
        }
        let trainId = $("#trainId").val();
        // 过滤已审核通过的人员
        for (let i = 0;i < selPerson.length; i++){
            if (selPerson[i].content == '通过') {
                popMsg("不允许顺延审核已通过的人员")
                return;
            }
        }
        let param = {
            trainId: trainId,
            ids: selIds.join()
        }
        parent.popWin('人员顺延',"/trainingOffline/offPutInit", param, "70%", "70%", function (res){
            param.trainId = res
            ajaxData("/trainingOffline/offPutTrain",param, function (resp) {
                selIds = []
                selPerson = []
                popMsg("顺延成功")
                callBackAddUser()
            })
        });
    });

    $('#allCheck').click(function () {
        let nodes = document.getElementsByClassName("selDeclare")
        let flag = false
        if(this.checked){
            flag = true
        }
        for (let i = 0; i < nodes.length; i++) {
            var id = nodes[i].attributes.getNamedItem("d-id").nodeValue;//通过选择的input按钮获取ID
            let tds = nodes[i].parentNode.parentNode.parentNode.childNodes
            var content = tds[11].innerText
            if (flag) {
                nodes[i].checked = true
            } else {
                nodes[i].checked = false
            }
            if (nodes[i].checked) {
                if (selIds.indexOf(id) === -1) {
                    selIds.push(id)
                    selPerson.push({id: id, content: content})
                }
            } else {
                selIds.some((item,i)=>{
                    if(item === id){
                        selIds.splice(i,1)
                        selPerson.splice(i,1)
                    }
                })
            }
        }
    })

});

function batchExamine(ids) {
    if(ids == "" || ids == null || ids == undefined || ids.length == 0){
        popMsg("请选择数据")
        return
    }
    var content = '<div style="width: 100%;padding-top: 10px;">' +
        '<div class="col-md-6" style="margin-left: 25%;margin-top: 50px;">' +
        '<select id="checkStatus" class="form-control">' +
        '<option value="1" selected>通过</option>' +
        '<option value="2">不通过</option>' +
        '</select>' +
        '</div>' +
        '</div>';
    var layerIndex = layer.open({
        type:'1',
        title: '报名审核',
        content:content,
        btn: ['确认', '取消'],
        area: ['400px', '270px'],
        yes: function(index, layero){
            var param = {
                trainId : $("#trainId").val(),
                ids : ids instanceof Array ? ids.join() : ids,
                status : $("#checkStatus").val()
            }
            ajaxData("/trainingOffline/batchExamine",param,function(data) {
                selIds = []
                selPerson = []
                $("#allCheck").prop("checked",false)
                popMsg("审核成功")
                callBackAddUser()
            });
            layer.close(layerIndex);
        }
    });
}

//日期初始化
function dateInit() {
    dataRangePickerInit($('#createTime'), null, null, function () {
    }, function () {
    });
}

function search(){
    ajaxTableQuery("table1", "/trainingOffline/trainingOfflineSignUpSysList",$("#superviseInfoForm").formSerialize());
    ajaxTableQuery("table2", "/trainingOffline/trainingOfflineSignUpUserList",$("#superviseInfoForm").formSerialize());
}

function sendSMSNotification(){
    popConfirm("确定一键审核并发送短信通知？",function(){
        var param = {
            trainId : $("#trainId").val()
        }
        ajaxData("/trainingOffline/sendSMSNotification",param,function(data) {
            popMsg("审核并发送短信成功")
            callBackAddUser()
        });
    })
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#status').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

function nickName(data, type, row, meta){
    let content = '<a href="javascript:void(0)" onclick="personDetail(\''+ data.userId +'\')">' + data.nickName + '</a>';
    return content;
}

function personDetail(id) {
    var param ={
        id:id,
    };
    parent.popWin('查看用户信息', '/trainUserManage/addUserInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);
}


function selectInit() {
    var str='<select id="timeRangeId" name class="form-control">'
    str+='<option value="">请选择</option>'
    var list=JSON.parse(timeRangeList)
    for (var i=0;i<list.length;i++){
        var codeValue= list[i].id
        var codeName= list[i].timeRange
        str+='<option value="'+codeValue+'">'+codeName+'</option>'
    }
    str+='</select>'
    $('#timeRangeSel').append(str)
}

// 操作
function renderColumnOperation(data, type, row, meta){
    if (data.totalAmount == null){
        var str4 = '<a href="#" onClick="batchExamine(\''+ data.id +'\')" style="margin-right:4px;">审核</a>';
        var str1 = '<a href="#" onClick="toExamine(\''+data.id+'\',\''+data.status+'\',\''+data.trainId+'\')" style="margin-right:4px;">移除</a>';
        var str2 = '<a href="#" onClick="openCertificate(\''+data.signUser+'\')" style="margin-right:4px;">下发证书</a>';
        if(data.paymentStatus == "0"){
            var str3 =	'<a href="#" onClick="releaseInfo(\''+data.id+'\',\''+data.paymentStatus+'\',\''+data.trainId+'\')" style="margin-right:4px;">缴费</a>';
        }else{
            var str3 =	'<a href="#" onClick="releaseInfo(\''+data.id+'\',\''+data.paymentStatus+'\',\''+data.trainId+'\')" style="margin-right:4px;">取消缴费</a>';
        }
        var str5 = '<a href="#" onClick="updateTimeRange(\''+ data.id +'\')" style="margin-right:4px;">修改场次时间</a>';
        return str4 + "| " + str1 + "| " + str3 + "| " + str2 + "| " + str5;
    }else {
        var str2 = '<a href="#" onClick="openCertificate(\''+data.signUser+'\')" style="margin-right:4px;">下发证书</a>';
        return str2;
    }
}

function updateTimeRange(id){
    // let param1 = {
    //     trainId: $("#trainId").val(),
    // }
    // ajaxData("/trainingOffline/getTimeRangeList", param1, function (res) {
    //     
    //     console.log(res)
    //     if (res) {
    //         timeRangeListStr = JSON.stringify(res);
    //     }
    // })
    var content = '<div style="width: 100%;padding-top: 10px;">' +
        '<div style="margin-left: 10%;margin-top: 20px;">' +
        '<select style="width: 90%" id="timeRangeId" class="form-control">' +
        '<option value="">请选择</option>'
    var list=JSON.parse(timeRangeListStr)
    for (var i=0;i<list.length;i++){
        var codeValue= list[i].id
        var codeName= list[i].timeRange
        content+='<option value="'+codeValue+'">'+codeName+'</option>'
    }
    content += '</select>' +
        '</div>' +
        '</div>';
    var layerIndex = layer.open({
        type:'1',
        title: '修改场次时间',
        content:content,
        btn: ['确认', '取消'],
        area: ['500px', '270px'],
        yes: function(index, layero){
            var param = {
                id:id,
                timeRangeId:$("#timeRangeId").val()
            }
            ajaxData("/trainingOffline/updateTimeRange",param,function(data) {
                popMsg("修改成功")
                callBackAddUser()
            });
            layer.close(layerIndex);
        }
    });
}

function paymentStatus(data, type, row, meta){
    if(data.paymentStatus == '0'){
        return '未缴费'
    }else {
        return '已缴费'
    }
}

//缴费
function releaseInfo(id,paymentStatus,trainId){
    if(paymentStatus == '1'){
        popConfirm("确认取消缴费吗？",function(){
            var param = {
                id:id,
                paymentStatus:0
            };
            ajaxData("/trainingOffline/saveTrainingOfflineSignUpMoney",param,function(data) {
                popMsg("操作成功");
                callBackAddUser()
            });
        })
    }else{
        popConfirm("确认缴费吗？",function(){
            var param = {
                id:id,
                paymentStatus:1
            };
            ajaxData("/trainingOffline/saveTrainingOfflineSignUpMoney",param,function(data) {
                popMsg("操作成功");
                callBackAddUser()
            });
        })
    }
}
function openCertificate(signUser) {
    popConfirm("确认下发证书吗？下发后不能撤回！",function(){
        var param = {
            userId:signUser,
            trainType:$("#trainType").val(),
            trainId:$("#trainId").val()
        };
        ajaxData("/examMonitoring/openCertificate",param,function(data) {
            if(data) {
                popMsg("下发成功成功");
            /*    ajaxTableReload("table1",false);
                queryRegistrationByIdList()*/
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}
function toExamine(id,status,trainId){
    var param ={
        id:id,
        status:'2',
        trainId : trainId,
        ifSystem : '0'
    };
    popConfirm("确定移除该报名?", function () {
        ajaxData("/trainingOffline/saveTrainingOfflineSignUpMoney",param,function (data){
            popMsg("移除成功");
            callBackAddUser()
        })
    })
    // parent.popWin('审核', '/trainingOffline/trainingOfflineToExamine', param, '40%', '50%', callBackAddUser,'',callBackAddUser);
}
function callBackAddUser() {
    ajaxTableReload("table1",false);
    ajaxTableReload("table2",false);
}

function makeCertificate(recordId,examRank) {
    popConfirm("确定制作证书?这会花费一定的时间，建议最少一个小时后下载证书！", function () {
        var param = {
            businessId:recordId,
            examRank:examRank,
            certificateName:examRank,
            trainId: $("#trainId").val(),
            trainType: $("#trainType").val(),
        }
        ajaxData("/examMonitoring/makeCertificate", param, function (res) {
            if (res) {
                popMsg("成功");
            } else {
                popMsg("失败");
            }
            closeWin();
        })
    })
}
function downCertificate() {
    window.open(contextPath+"/examMonitoring/downCertificate?trainId="+$("#trainId").val())
}


function rcIndex(data, type, row, meta) {
    var flag = false;

    selIds.forEach((item)=>{
        if(item == data.id){
            flag = true
        }
    })

    var str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;" onclick="selCompany(this)">';
    if(flag){
        str += '<input  type="checkbox" checked name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }else {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="id" value="' + data.id + '">'
    str += '</div>';
    return str;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var tds = item.parentNode.parentNode.childNodes
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    var content = tds[11].innerText
    if (chk.prop('checked')) {
        if (selIds.indexOf(id) === -1) {
            selIds.push(id)
            selPerson.push({id: id, content: content})
        }
    } else {
        selIds.some((item,i)=>{
            if(item === id){
                selIds.splice(i,1)
                selPerson.splice(i,1)
            }
        })
    }
}

function modelUpdate(inputId) {
    $("#" + inputId).fileupload({
        url : contextPath + "/trainingOffline/importSuccessPerson",
        dataType : "json",
        autoUpload : true,
        formData:{trainId:$("#trainId").val()},
        add : function(e,data) {
            if(data.files[0].name.indexOf("xlsx") == -1 && data.files[0].name.indexOf("xls") == -1) {
                popMsg("只支持excel格式文件上传");
                return;
            }
            data.submit();
        },
        submit : function(e, data) {
            index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
        },
        done : function(e, data) {
            
            layer.close(index);
            var rtnRlt = data.result.result;
            if(rtnRlt != null){
                callBackAddUser()
                popAlert("导入成功");
            }else {
                popAlert("导入失败,请检查导入数据");
            }
        },
        fail : function(e, data) {
            layer.close(index);
            popAlert("导入失败,请检查导入数据");
        }
    });
}




