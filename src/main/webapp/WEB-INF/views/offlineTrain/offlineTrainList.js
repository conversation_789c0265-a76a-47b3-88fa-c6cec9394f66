$(document).ready(function() {
    //查询
    $("#btnQuery").bind("click",function() {
        var param = {
            trainTitle : $("#trainTitle").val(),
            codeName :  $("#codeName").val(),
            publish : $("#publish").val(),
        }

        ajaxTableQuery("table1", "/trainingOffline/trainingOfflineInitList",param);
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("superviseInfoForm").reset();
        var param = {
            noticeType : "0",
        }
        ajaxTableQuery("table1", "/trainingOffline/trainingOfflineInitList",param);
    });
    //新增
    $("#btnAdd").bind("click", function() {
        var param = {
            pageFlg:'0',
            codeType:"1"
        }
        popWin("培训新增", "/trainingOffline/trainingOfflineInitAdd", param, "98%", "98%", winCallback, "",winCallback);
    });
});

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

// 操作
function renderColumnOperation(data, type, row, meta){
    var str1 = '';
    var str2 = '';
    var str3 = '';
    var str4 = '';
    if (document.getElementById("editAuth")) {
        str1 = '<a href="#" onClick="infoEdit(\'' + data.trainId + '\')" style="margin-right:4px;">编辑</a>';
    }
    if (document.getElementById("releaseAuth")) {
        if (data.publishType === "0") {
            str3 = '<a href="#" onClick="releaseInfo(\'' + data.trainId + '\',\'' + data.publishType + '\')" style="margin-right:4px;">发布</a>';
        } else {
            str3 = '<a href="#" onClick="releaseInfo(\'' + data.trainId + '\',\'' + data.publishType + '\')" style="margin-right:4px;">取消发布</a>';
        }
        str2 =	'<a href="#" onClick="infoDel(\''+data.trainId+'\')" style="margin-right:4px;">删除</a>';
    }
    if (document.getElementById("singnUpAuth")) {
        str4 = '<a href="#" onClick="singnUp(\'' + data.trainId + '\',\'' + data.trainType + '\')" style="margin-right:4px;">报名统计</a>';
    }
    if (str1 === '' && str2 === '' && str3 === '' && str4 === '') {
        return '-';
    }
    return str1 + "| " + str3 + "| " + str4 + "|" + str2;
}
// 报名时间
function signUpTime(data, type, row, meta){
    var str1 = data.signUpStartTime;
    var str2 =	data.signUpEndTime;
    return str1 + "至 " + str2;
}
// 培训时间
function activityTime(data, type, row, meta){
    var str1 = data.activityStartTime;
    var str2 =	data.activityEndTime;
    return str1 + "至 " + str2;
}

function infoEdit(id){
    var param = {
        trainId:id,
    }
    popWin("编辑信息", "/trainingOffline/trainingOfflineInitAdd", param, "98%", "98%", winCallback, "",winCallback);
}

//删除
function infoDel(id){
    popConfirm("确认删除该线下培训动态吗？",function(){
        var param = {
            trainId:id,
            releaseDelType:'99',//99的代表删除操作
            status:'0'
        };
        ajaxData("/trainingOffline/saveTrainingOfflineInfo",param,function(data) {
            if(data.length > 0) {
                popMsg("删除成功");
                ajaxTableReload("table1",false);
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}
//报名统计
function singnUp(id, trainType){
    var param = {
        trainId:id,
        trainType:trainType
    }
    if (trainType === '0') {
        // popWin("报名统计", "/trainingOffline/trainingOfflineRegistration", param, "100%", "100%", winCallback, "");
        popWin("报名统计", "/trainingOffline/trainingOfflineRegistrationAdvanced", param, "100%", "100%", winCallback, "");
    } else {
        popWin("报名统计", "/trainingOffline/trainingOfflineRegistrationAdvanced", param, "100%", "100%", winCallback, "");
    }
}
//发布、取消发布
function releaseInfo(id,publishType){
    if(publishType == '1'){
        popConfirm("确认取消发布吗？",function(){
            var param = {
                trainId:id,
                publish:0,
                releaseDelType:'0'//0的代表发布相关操作
            };
            ajaxData("/trainingOffline/saveTrainingOfflineInfo",param,function(data) {
                if(data.length >= 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1",false);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }else{
        popConfirm("确认发布吗？",function(){
            var param = {
                trainId:id,
                publish:1,
                releaseDelType:'0'//0的代表发布相关操作
            };
            ajaxData("/trainingOffline/saveTrainingOfflineInfo",param,function(data) {
                if(data.length > 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1",false);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}


function winCallback(paraWin, paraCallBack) {
    ajaxTableReload("table1", false);
}

function orgCallback(){
    ajaxData("/trainingDynamics/saveClassificationList?codeType="+'1',"",function(data) {
        $('#subType').empty()
        var option='<option value="">全部</option>'
        for (var i=0;i<data.length;i++){
            option+='<option value="'+data[i].codeValue+'">'+data[i].codeName+'</option>'
        }
        $('#subType').append(option)
    });
}
