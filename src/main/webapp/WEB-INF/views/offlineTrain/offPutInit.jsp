<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />

    <style>
        .user_type_row {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <div class="panel">
        <div class="panel-body">
            <div class="row" style="padding-left: 10px;padding-right: 10px">
                <e:grid id="table1" action="/trainingOffline/getTrainingOfflineList?trainType=1&trainId=${offlineTrainDto.trainId}"
                        cssClass="table table-striped table-hover">
                    <e:gridColumn label="<input type='radio'  id='allCheck' disabled>"
                                  renderColumn="rcIndex" orderable="false" cssClass="text-center"
                                  cssStyle="width:5%"/>
                    <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                                  cssStyle="width:2%"/>
                    <e:gridColumn label="培训名称" displayColumn="trainTitle" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%"/>
                    <e:gridColumn label="报名时间" renderColumn="signUpTime"  orderable="false"
                                  cssClass="text-center" cssStyle="width:20%;"/>
                    <e:gridColumn label="培训时间" renderColumn="activityTime"  orderable="false"
                                  cssClass="text-center" cssStyle="width:20%;"/>
                    <e:gridColumn label="培训地点" displayColumn="address" orderable="false"
                                  cssClass="text-center" cssStyle="width:15%;"/>
                    <e:gridColumn label="是否发布" displayColumn="publish" orderable="false"
                                  cssClass="text-center" cssStyle="width:5%;"/>
                </e:grid>
            </div>
            <div class="row">
                <div class="col-xs-12 text-center" >
                    <sapn id="btnSubmit" class=" btn btn-primary">选择</sapn>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
