//@ sourceURL=trainingOfflineAddUser.js
var selIds = [];
var selPerson = [];
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();

    //selectInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
        $('input[name="personType"]').val("");
        $('input[name="checkStatus"]').val("");
        $('input[name="belongCommission"]').val("");
        $('input[name="personLabel"]').val("");
        $('#personType').val("")
        $('#belongCommission').val("")
        search();
    });

    $("#saveUserInfo").click(function () {
        saveUserInfo();
    });

    $('#allCheck').click(function () {
        let nodes = document.getElementsByClassName("selDeclare")
        let flag = false
        if(this.checked){
            flag = true
        }
        for (let i = 0; i < nodes.length; i++) {
            var id = nodes[i].attributes.getNamedItem("d-id").nodeValue;//通过选择的input按钮获取ID
            let tds = nodes[i].parentNode.parentNode.parentNode.childNodes
            var content = tds[8].innerText
            if (flag) {
                nodes[i].checked = true
            } else {
                nodes[i].checked = false
            }
            if (nodes[i].checked) {
                if (selIds.indexOf(id) === -1) {
                    selIds.push(id)
                    selPerson.push({id: id, content: content})
                }
            } else {
                selIds.some((item,i)=>{
                    if(item === id){
                        selIds.splice(i,1)
                        selPerson.splice(i,1)
                    }
                })
            }
        }
    });

    //顺延
    $("#putOff").bind("click", function () {
        if (selIds == "" || selIds == null || selIds == undefined) {
            popMsg("请选择人员")
            return;
        }
        let trainId = $("#trainId").val();
        // 过滤已审核通过的人员
        for (let i = 0;i < selPerson.length; i++){
            if (selPerson[i].content == '通过') {
                popMsg("不允许顺延审核已通过的人员")
                return;
            }
        }
        let param = {
            trainId: trainId,
            ids: selIds.join()
        }
        parent.popWin('人员顺延',"/trainingOffline/offPutInit", param, "70%", "70%", function (res){
            param.trainId = res
            ajaxData("/trainingOffline/offPutTrain",param, function (resp) {
                selIds = []
                selPerson = []
                popMsg("顺延成功")
                callBackAddUser()
            })
        });
    });
});

function selectInit() {
    var str='<select id="timeRangeId" name class="form-control">'
    str+='<option value="">请选择</option>'
    var list=JSON.parse(timeRangeList)
    for (var i=0;i<list.length;i++){
        var codeValue= list[i].id
        var codeName= list[i].timeRange
        str+='<option value="'+codeValue+'">'+codeName+'</option>'
    }
    str+='</select>'
    $('#selectCheck').append(str)
}

function search() {
    let trainId = $("#trainId").val();
    let param = $("#queryForm").formSerialize()+"&id="+trainId;
    ajaxTableQuery("tableAll", "/trainingOffline/getOfflineUserInfoList",
        param);
}

function callBackAddUser() {
    search()
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#checkStatus').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#personLabel').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return data.accId;
}

function realName(data, type, row, meta){
    let content = '<a href="javascript:void(0)" onclick="personDetail(\''+ data.userId +'\')">' + data.realName + '</a>';
    return content;
}

function personDetail(id) {
    var param ={
        id:id,
    };
    parent.popWin('查看用户信息', '/trainUserManage/addUserInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);
}


function saveUserInfo() {
    if (selIds == "" || selIds == null || selIds == undefined) {
        popMsg("请选择人员")
        return;
    }
    let trainId = $("#trainId").val();
    let param = {
        trainId: trainId,
        signUpIds: selIds.join(','),
        timeRangeId: $("#timeRangeId").val()
    }
    ajaxData("/trainingOffline/saveUserInfo", param, function () {
        closeWinCallBack();
    })
}

function renderColumnOperation(data, type, row, meta){
    var str1 = '<a href="#" onClick="toExamine(\''+data.id+'\',\''+data.status+'\',\''+data.trainId+'\')" style="margin-right:4px;">恢复</a>';
    return str1;
}

function toExamine(id,status,trainId){
    var param ={
        id:id,
        status:'0',
        ifSystem : '0'
    };
    popConfirm("确定移除?", function () {
        ajaxData("/trainingOffline/saveTrainingOfflineSignUpMoney",param,function (data){
            popMsg("移除成功");
            callBackAddUser()
        })
    })
}

function rcIndex(data, type, row, meta) {
    var flag = false;

    selIds.forEach((item)=>{
        if(item == data.id){
            flag = true
        }
    })

    var str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;" onclick="selCompany(this)">';
    if(flag){
        str += '<input  type="checkbox" checked name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }else {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="id" value="' + data.id + '">'
    str += '</div>';
    return str;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var tds = item.parentNode.parentNode.childNodes
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    var content = tds[8].innerText
    if (chk.prop('checked')) {
        if (selIds.indexOf(id) === -1) {
            selIds.push(id)
            selPerson.push({id: id, content: content})
        }
    } else {
        selIds.some((item, i) => {
            if (item === id) {
                selIds.splice(i, 1)
                selPerson.splice(i, 1)
            }
        })
    }
}


