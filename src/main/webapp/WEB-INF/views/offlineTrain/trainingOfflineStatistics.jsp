<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            margin-top: 6px;
        }

        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="OfflineTrainSignUpDto" id="superviseInfoForm">
        <input type="hidden" id="trainId"  name="trainId" value="${OfflineTrainSignUpDto.trainId}"/>
        <div class="row">
            <label class=" col-md-1 control-label">姓名</label>
            <div class="col-md-3">
                <form:input path="nickName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"
                            onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
            </div>
            <label class=" col-md-1 control-label">手机号</label>
            <div class="col-md-3">
                <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"
                            onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
            </div>
            <label class=" col-md-1 control-label">审核状态</label>
            <div class="col-xs-3">
                <form:select path="status" cssClass="form-control">
                    <form:option value="">请选择</form:option>
                    <form:option value="0">待审核</form:option>
                    <form:option value="1">通过</form:option>
                    <form:option value="2">未通过</form:option>
                </form:select>
            </div>
            <label class=" col-md-1 control-label">是否缴费</label>
            <div class="col-xs-3">
                <form:select path="paymentStatus" cssClass="form-control">
                    <form:option value="">请选择</form:option>
                    <form:option value="0">否</form:option>
                    <form:option value="1">是</form:option>
                </form:select>
            </div>
        </div>
    </div>
    <div class="row" style="margin-bottom: 8px;margin-right: 8px;float: right;">
        <span id="btnClear" class="btn btn-default">清空条件</span>
        <span id="btnQuery" class="btn btn-primary">查询</span>
        <span id="exportTable" class="btn btn-primary">导出</span>
    </div>
    </form:form>
    <div  id="trainNum" style="width: 900px;top: 138px;position: absolute;"></div>
    <div class="row" style="padding-left: 10px;padding-right: 10px">
        <e:grid id="table1" action="/trainingOffline/trainingOfflineSignUpList?trainId=${OfflineTrainSignUpDto.trainId}"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:3%"/>
            <e:gridColumn label="公司" displayColumn="companyName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="姓名" displayColumn="nickName" orderable="false"
                          cssClass="text-center" cssStyle="width:8%"/>
            <e:gridColumn label="用户性质" displayColumn="codeName" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="报名时间" displayColumn="createTime" orderable="false"
                          cssClass="text-center" cssStyle="width:10%;"/>
            <e:gridColumn label="审核状态" displayColumn="status" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="应缴费" displayColumn="money" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="缴费状态" displayColumn="paymentStatus" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                          cssClass="text-center" cssStyle="width:12%;"/>
        </e:grid>
    </div>
</div>
</div>
</body>
</html>

