//@ sourceURL=insertTeacherManagementInit.js
let _fileUploadUrl = contextPath + '/filetempupload';
$(document).ready(function() {
    if($("#publishType").val() == '1'){
        $("#not_save").show();
        $("#save").hide();
    }
    if($("#publishType").val() == '0'){
        $("#not_save").hide();
        $("#save").show();
    }
    if($("#trainId").val() == ''|| $("#trainId").val() == null){//新增
        $("#not_save").hide();
        $("#save").show();
    }

    if($("#fileId").val() == ""  ){
        $("#imgId").css("display", "none");
    }else{
        $("#imgId").css("display", "black");
    }
    $("#btnSelectCost").bind("click", function() {
        var relationId = $("#trainId").val()
        if(getValue(relationId) == ""){
            popMsg("请先保存数据")
            return ;
        }
        let personTypeList = $("#personTypeList").val()
        var param = {
            existPersonType:true,
            existIfFree:true,
            existIfDiscount:true,
            existDiscount:true,
            existPrice:true,
            existLimitNumber:true,
            relationId:$("#trainId").val()
        }
        popWin("选择用户类型", "/selectTrainTypeCost", param, "80%", "80%");
    });
    dateInit();
    tSelectInit();
    // dateRangeDataInit();
    //选择讲师
    $("#choiceTeacher").bind("click", function() {
        parent.popWin("讲师列表", "/trainingOffline/trainingOfflineTeacherAdd", "", "90%", "90%", "", "");
    });
    //附件上传
    modelUpdate("fileupload");
    /**
     * 课件上传
     */
    function modelUpdate(fileUploadId) {
        $("#" + fileUploadId).fileupload({
            url: _fileUploadUrl,
            dataType: 'text',
            autoUpload: true,
            add: function(e, data) {
                // 上传文件时校验文件格式
                data.submit();
            },
            submit: function(e, data) {
                index = layer.load(1, {
                    shade: [0.1, '#fff'] //0.1透明度的白色背景
                });
            },
            done: function(e, data) {
                layer.close(index);
                $.each($.parseJSON(data.result), function(index, file) {
                    let divStr =
                        ' <div class="col-xs-12 file-content">' +
                        ' <span onclick="downFile()" class="file-span" title="下载" data-file-type="0" data-atta-id="" data-file-id="' + file.fileRelaId + '">' + file.fileName + '</span>' +
                        ' <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>' +
                        ' </div>';
                    $("#fileDiv").append(divStr);
                })
            },
            fail: function(e, data) {
                popMsg("上传附件失败！");
            }
        });
    }

    //上传图片
    var url = contextPath + '/filetempupload';
    $('#up').fileupload({
        url : url,
        dataType : 'json',
        autoUpload : true,
        submit : function(e, data) {
            index = layer.load(1, {
                shade : [ 0.1, '#fff' ]
                // 0.1透明度的白色背景
            });
        },
        done : function(e, data) {
            $.each(data.result, function(index, file) {
                $("#fileId").val(file.fileRelaId);
                $("#teachPic").val(file.fileRelaId);
            });
            layer.close(index);
            $("#imgId").css("display", "block");
        }
    });

    $("#up").uploadPreview({
        Img : "ImgPr",
        Width : 50,
        Height : 50
    });

    // 富文本初始化
    ueEditorInit();
    $("#trainingOfflineAddForm").validate({
        rules: {
            "trainTitle": {
                required : true,
            },
            "trainImage": {
                required : true,
            },
            "signUpTimeStr": {
                required : true,
            },
            "activeTimeStr": {
                required : true,
            },
            "address": {
                required : true,
            },
            "specificAddress": {
                required : true,
            },
            "peopleLimit": {
                required : true,
            },
            "learningForm": {
                required : true,
            },

        },
    });

    // 取消
    $('#btnCancel').bind('click', function() {
        closeWinCallBack();
    });
    $('#btnCancelSave').bind('click', function() {
        closeWinCallBack();
    });
    // 取消发布
    $('#releaseClick').bind('click', function () {
        releaseClick();
    });

    rem();
    //保存完的活动不允许更改类型，改完类型之后发证书那步会有问题！！！！
    if ($("#trainId").val() != null && $("#trainId").val() != ''){
        $("#trainType").attr("disabled", true);
    }
})

function rem() {
    // if(getValue($("#trainType").val()) == '1'){
    //   $("#astrict").attr({"style":"display:none"})
    // }else {
    //     $("#astrict").attr({"style":"display:black"})
    // }
}

function winCallback(paraWin, paraCallBack) {
    ajaxTableReload("table1", false);
}


var timeNum = 1
var nodes = ["activeTimeStr0","activeTimeStr1","activeTimeStr2","activeTimeStr3","activeTimeStr4","activeTimeStr5","activeTimeStr6","activeTimeStr7","activeTimeStr8"]
var backNodes = ["activeTimeStr9"]
function deleteNode(node) {
    // 获取父节点
    var parentDiv = node.parentNode.parentNode;
    var inputElement = parentDiv.getElementsByClassName('form-control')[0];
    var inputId = inputElement.id;
    var id = inputId
    nodes.push(id)
    //从backNodes中取出指定的一个对象
    let index = backNodes.indexOf(id)
    if (index !== -1) {
        backNodes.splice(index,1)
        node.parentNode.parentNode.remove()
        timeNum -= 1
    }
}

function addTimeRange(str,num,id) {
    timeNum += 1
    if (timeNum > 10) {
        timeNum -= 1
        popMsg("最大只能有10个场次")
        return;
    }
    if(!str) {
        str = nodes.pop()
    }
    if(!num){
        num = 0
    }
    backNodes.push(str)
    var html = "<div class=\"col-md-10 daterange\" style=\"width:370px;margin-left: 90px\">" +
        "<input id= " + str + " name= " + str + " class=\"form-control\" placeholder=\"请输入活动时间\" type=\"text\" value='' readonly=\"\">" +
        "</div>" +
        "<div class=\"col-md-8 no-padding control-label\" style=\"margin-top: 5px;width: 350px;\">" +
        "<span>报名人数</span>" + "<input style='width: 240px;display: inline;margin-left: 10px' class=\"form-control\" value=" + num + " placeholder=\"请输入该场次最大报名人数\" type=\"number\" autocomplete=\"off\">" +
        "<span style=\"color: #145ccd;cursor:pointer;margin-left: 5px\" onclick='deleteNode(this)'>删除</span>" +
        "</div>"
    if(id){
        // html添加一个input隐藏域，存放id
        html += "<input id='" + id + "' type=\"hidden\" readonly=\"\" value='" + id + "'>"
    }
    var newTimeRange = document.createElement('div');
    newTimeRange.className = 'col-md-12';
    newTimeRange.style.display = 'flex';
    newTimeRange.style.alignItems = 'center';
    newTimeRange.innerHTML = html
    var dom = document.getElementById("trainingOfflineAddForm");
    dom.insertBefore(newTimeRange, document.getElementById("issueTimeId"));
    var activeTimeStr = $('#' + str).val();
    dataRangePickerInit($('#' + str), null, null, function () {}, function () {
    },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1}
    );
}


function downFile() {
    popMsg("请先保存新上传的附件，再进行下载");
    return;
}
//删除课件
function removeRow(obj){
    var fileId = $(obj).prev().attr("value");
    if(fileId.indexOf('-') != -1){
        $(obj).parent().remove();
    }else{
        popConfirm('删除后无法恢复，确认删除吗？',function(){
            var parma = {attachmentId:fileId};
            ajaxData('/signManage/deleteAttachment',parma,function(){
                $(obj).parent().remove();
            });
        });
    }
}
/**
 * 删除图片
 * @param obi
 * @param fieldId
 */
function clearImg(obi, fieldId) {
    popConfirm("确认删除封面图片", function() {
        $("#fileId").val("");
        $("#imgId").css("display", "none");
        $("#up").val("");
        if ($("#teachPic").val() != "") {
            $("delFileId").val($("#teachPic").val());
            $("#teachPic").val("");
        }
    });
}
$.fn
    .extend({
        uploadPreview : function(opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img : "ImgPr",
                Width : 100,
                Height : 100,
                ImgType : [ "gif", "jpeg", "jpg", "bmp", "png" ],
                Callback : function() {
                }
            }, opts || {});
            _self.getObjectURL = function(file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }
                if ($("#teachPic").val() != "") {
                    $(
                        '<input type="hidden" name="delFileId" value="'
                        + $("#teachPic").val() + '"/>').appendTo(
                        '#delFileIdList');
                    $("#teachPic").val("");
                }

                $("#imgId").css("display", "block");
                return url
            };

            _this.change(function() {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter' : 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width' : opts.Width
                                            + 'px',
                                        'height' : opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })

/**
 * 富文本初始化
 */
function ueEditorInit() {
    
    UE.delEditor('content');
    um = UE.getEditor('content', {
        initialFrameHeight: 600,
        textarea: "content",
        elementPathEnabled: false,
        autoHeightEnabled: true
    });
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack,
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        style: {},
    };
    $('#trainTeacher').tselectInit(null, teaSelectOptions);
    let belongCommissionSelect= {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        allCheck:true,
        customCallBack: tSelectCustomCallBack,
        submitCallBack: belongCommissionSelectSubmitCallBack
    };
    $('#post').tselectInit(null, belongCommissionSelect);
    $('#belongCommission').tselectInit(null, belongCommissionSelect);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
function belongCommissionSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('trainTeacher') + '"]').val(d.value);
    if (t.context.id == 'trainTeacher'){
        $('input[name="trainTeacherStr"]').val(d.name)
    }
}
function dateInit() {
    //報名時間
    var signUpTimeStr = $('#signUpTimeStr').val();
    if (signUpTimeStr.trim() != '') {
        var signUpTimeStr = signUpTimeStr.split(' 至 ');
        dataRangePickerInit($('#signUpTimeStr'), signUpTimeStr[0], signUpTimeStr[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    } else {
        dataRangePickerInit($('#signUpTimeStr'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    }
    //活動時間
    var activeTimeStr = $('#activeTimeStr9').val();
    if (activeTimeStr.trim() != '') {
        var activeTimeStr = activeTimeStr.split(' 至 ');
        dataRangePickerInit($('#activeTimeStr9'), activeTimeStr[0], activeTimeStr[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    } else {
        dataRangePickerInit($('#activeTimeStr9'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    }
    //额外的活动时间
    var timeRangeMapList = $('#timeRangeMapList').val();
    if(timeRangeMapList) {
        console.log(timeRangeMapList)
        timeRangeMapList = timeRangeMapList.replace(/=/g, '": "').replace(/{/g, '{"').replace(/}/g, '"}').replace(/, /g, '", "').replace(/}", "{/g,'}, {');
        timeRangeMapList = JSON.parse(timeRangeMapList);
        var firstTime = timeRangeMapList.shift()
        // 填充第一个时间选择器activeTimeStr9和人数选择器signLimit
        $('#activeTimeStr9').val(firstTime.timeRange)
        $('#signLimit').val(firstTime.signLimit)
        $('#timeRangeId').val(firstTime.id)
        timeRangeMapList.forEach(item => {
            // 从节点列表nodes里取出一个id，增加一个时间区间选择节点，节点id为str
            var str = nodes.pop()
            addTimeRange(str,item.signLimit,item.id)
            // 新增节点之后，填充值
            timeRange = item.timeRange
            $('#'+str).val(timeRange)
            timeRange = timeRange.split(' 至 ');
            dataRangePickerInit($('#'+str), timeRange[0], timeRange[1], function () {
            }, function () {
            },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
        })
    }

    jeDate("#issueTime", {
        festival: true,
        minDate: "1900-01-01", //最小日期
        maxDate: "2099-12-31", //最大日期
        method: {
            choose: function(params) {
            }
        },
        format: "YYYY-MM-DD hh:mm"
    });
}
/**
 * 删除课件
 * @param item
 */
function removeFile(item) {
    // 移除页面元素
    $(item).parent().remove();
}

function saveTrain(type) {
    // 用户类型
    let files = [];
    let courseList = [];
    $.each($('.file-span'), function(index, fileItem) {
        let fileType = $(fileItem).data('file-type');
        let attId = $(fileItem).data('atta-id');
        let tempId = $(fileItem).data('file-id');
        if (getValue(attId) != '') {
            courseList.push({
                id: attId
            });
        }else {
            files.push({
                dataType: fileType,
                attachmentId: attId,
                tempId: tempId
            })
        }
    });
    if( getValue($('#trainTitle').val())  == ""  ){
        popMsg("请输入活动标题");
        return;
    }
    if( getValue($("#fileId").val())  == ""  ){
        popMsg("请上传封面图");
        return;
    }

    if( getValue($('#signUpTimeStr').val())  == ""  ){
        popMsg("请选择报名时间");
        return;
    }

    // if(getValue($("#activeTimeStr").val()) == "") {
    //     popMsg("请选择活动时间");
    //     return;
    // }
    // if ($('#signUpTimeStr').val().split(' 至 ')[1] > $("#activeTimeStr").val().split(' 至 ')[0]) {
    //     popMsg("报名时间请小于活动时间");
    //     return;
    // }

    if(getValue($("#address").val()) == "") {
        popMsg("请输入活动地点");
        return;
    }

    if(getValue($("#ifCertified").val()) == "" && getValue($("#ifCertified").val()) != '1') {
        popMsg("请选择是否需要人脸验证");
        return;
    }
    if ($('#peopleLimit').val()=='1' && getValue($("#trainType").val()) != '1' && ($("#signUpNumber").val() == null ||  $("#signUpNumber").val() == "" || $("#signUpNumber").val() == 0)) {
        popMsg("请输入限制数量");
        return;
    }
    if(getValue($("#trainType").val()) == "") {
        popMsg("请选择活动类型");
        return;
    }
    if(getValue($("#learningForm").val()) == "") {
        popMsg("请选择活动形式");
        return;
    }
    if ($("#trainingOfflineAddForm").valid()) {
        var errMsg = ''
        var timeRangeMapList = [];
        var timeRangeList = []
        backNodes.forEach(item => {
            // 获取活动时间节点
            var activeTimeNode = document.getElementById(item);

            var signLimitNode = activeTimeNode.parentNode.nextElementSibling.querySelector('input');
            var idNode = activeTimeNode.parentNode.nextElementSibling.nextElementSibling;

            var signLimit = signLimitNode.value;
            let range = $("#" + item).val();
            //获取父节点的下一个节点的子节点的input输入框的值
            if(timeRangeMapList.find(e => range == e.timeRange)) {
                errMsg = "活动时间不能重复"
                return;
            }
            if(range) {
                timeRangeMapList.push({
                    timeRange: range,
                    signLimit: signLimit,
                    id: idNode ? idNode.value : ''
                })
                timeRangeList.push(range)
            } else {
                errMsg = "有尚未选择的活动时间"
                return;
            }
        })
        if (errMsg) {
            popMsg(errMsg)
            return;
        }
        console.log(timeRangeMapList)
        var param = {
            trainId:$("#trainId").val(),//Id
            trainTitle:$("#trainTitle").val(),//标题
            trainImage :$("#fileId").val(),//封面图
            address :$("#address").val(),//地址
            specificAddress :$("#specificAddress").val(),//地址
            trainContent :$('textarea[name="content"]').val(),//正文
            trainTeacher:$('input[name="trainTeacher"]').val(),
            trainTeacherStr:$('input[name="trainTeacherStr"]').val(),
            publish:type,//是否发布
            signUpTimeStr :$("#signUpTimeStr").val(),
            activeTimeStr :$("#activeTimeStr").val(),
            timeRangeMapList: timeRangeMapList,
            timeRangeList: timeRangeList,
            teacherList: $('input[name="trainTeacher"]').val(),
            attList: files,//课件
            courseList: courseList,
            trainType:$("#trainType").val(),
            learningForm:$("#learningForm").val(),
            peopleLimit:$("#peopleLimit").val(),
            signUpNumber:$("#signUpNumber").val(),
            issueTime:$("#issueTime").val(),
            post:$('input[name="post"]').val(),//职务
            belongCommission:$('input[name="belongCommission"]').val(),
            ifCertified :$("#ifCertified").val(),
            ifPayShow :$("#ifPayShow").val(),
            ifInvoiceShow :$("#ifInvoiceShow").val(),
        };
        if (type == '1'){
            popConfirm("确认保存并发布？",function(){
                ajaxData("/trainingOffline/saveTrainingAdd",JSON.stringify(param),function(res) {
                    $("#trainId").val(res)
                    $("#publishType").val('1');
                    $("#not_save").show();
                    $("#save").hide();
                    popMsg("保存发布成功");
                }, "application/json;charset=UTF-8");
            })
        }else {
            popConfirm("确认保存？",function(){
                ajaxData("/trainingOffline/saveTrainingAdd",JSON.stringify(param),function(res) {
                    $("#trainId").val(res)
                    popMsg("保存成功");
                }, "application/json;charset=UTF-8");
            })
        }
    }
}


//发布、取消发布
function releaseClick(){
    var publishType = $("#publishType").val();
    if(publishType == '1'){
        popConfirm("确认取消发布吗？",function(){
            var param = {
                trainId:$("#trainId").val(),
                publish:0,
                releaseDelType:'0'//0的代表发布相关操作
            };
            ajaxData("/trainingOffline/saveTrainingOfflineInfo",param,function(data) {
                if(data.length >= 0) {
                    popMsg("取消发布成功");
                    $("#publishType").val('0');
                    $("#not_save").hide();
                    $("#save").show();
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}
// //日期控件初始化
// function dateRangeDataInit() {
//         dataRangePickerInit($('#contDate'), $("#signUpStartTime").val(), $("#signUpEndTime").val(), function() {
//         }, function() {
//         },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 60});
//
//         dataRangePickerInit($('#contDate1'), $("#activityStartTime").val(), $("#activitySendTime").val(), function() {
//         }, function() {
//         },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 60});
// }

