<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
    <script type="text/javascript">
    </script>
    <style>
    </style>
</head>
<body style="background-color: #fff">
<div class="panel-body">
    <form:form id="queryForm" modelAttribute="OfflineTrainSignUpDto">
        <div class="col-md-12">
            <label class="col-md-4 control-label" style="text-align: center">审核结果:</label>
            <div class="col-md-8">
                <input id="status" type="text" class="t-select" json-data='${checkStatusList}' selected-ids="${OfflineTrainSignUpDto.status}"/>
                <input name="status" type="hidden" placeholder="审核状态" />
            </div>
        </div>
        <input type="hidden" id="id" name="id" value="${OfflineTrainSignUpDto.id}"/>
        <input type="hidden" id="trainId" name="trainId" value="${OfflineTrainSignUpDto.trainId}"/>
        <div class="row" style="text-align: center;margin-top: 60px">
            <button type="button" class="btn btn-primary" style="margin-top:20px" id="btnSave">确定</button>
            <button type="button" class="btn btn-default" style="margin-top:20px" id="btnClose">取消</button>
        </div>
    </form:form>
</div>
</body>
</html>