var selTrain = '';
$(document).ready(function() {
    $('#btnSubmit').click(function () {
        submitOn();
    });
});

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

// 报名时间
function signUpTime(data, type, row, meta){
    var str1 = data.signUpStartTime;
    var str2 =	data.signUpEndTime;
    return str1 + "至 " + str2;
}

// 培训时间
function activityTime(data, type, row, meta){
    var str1 = data.activityStartTime;
    var str2 =	data.activityEndTime;
    return str1 + "至 " + str2;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        selTrain = id
    }
}
function submitOn() {
    if (selTrain !== null && selTrain !== ''){
        closeWinCallBack(selTrain)
    }else {
        popMsg("请选择活动")
    }
}

function rcIndex(data, type, row, meta) {
    var str = '';
    str += '<div class="classCheckBox case-opt"  onclick="selCompany(this)">';
    if (selTrain == '' || selTrain != data.trainId) {
        str +=
            '<input type="radio" name="checkRow" class="selDeclare"  d-id="' + data.trainId + '"  value="' + data.trainId + '" class="hidden">';
    } else {
        str +=
            '<input checked="true" type="radio" name="checkRow" class="selDeclare"  d-id="' + data.trainId + '"  value="' + data.trainId + '" \ >';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="trainId" value="' + data.trainId + '">'
    str += '</div>';
    return str;
}