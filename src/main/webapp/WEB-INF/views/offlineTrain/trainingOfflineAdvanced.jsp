<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            margin-top: 6px;
        }
        input {
            background-color: #fff !important;
        }
    </style>
    <script>
        var timeRangeListStr ='${timeRangeListStr}'
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="OfflineTrainSignUpDto" id="superviseInfoForm">
        <input type="hidden" id="trainId"  name="trainId" value="${OfflineTrainSignUpDto.trainId}"/>
        <input type="hidden" id="trainType"  name="trainType" value="${OfflineTrainSignUpDto.trainType}"/>
        <div class="row">
            <label class=" col-md-1 control-label">姓名</label>
            <div class="col-md-3">
                <form:input path="nickName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off" onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
            </div>
            <label class="col-md-1 control-label">用户类型</label>
            <div class="col-md-3">
                <input placeholder="请选择用户类型" id="personType" type="text" class="t-select" json-data='${personTypeList}' />
                <input name="personType" type="hidden"  />
            </div>
            <label class=" col-md-1 control-label">手机号</label>
            <div class="col-md-3">
                <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off" onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
            </div>
            <label class="col-md-1 control-label">审核状态</label>
            <div class="col-md-3">
                <input placeholder="请选择审核状态" id="status" type="text" class="t-select" json-data='${checkStatusList}'/>
                <input name="status" type="hidden" />
            </div>
            <label class=" col-md-1 control-label">是否缴费</label>
            <div class="col-xs-3">
                <form:select path="paymentStatus" cssClass="form-control">
                    <form:option value="">请选择</form:option>
                    <form:option value="0">否</form:option>
                    <form:option value="1">是</form:option>
                </form:select>
            </div>
            <label class=" col-md-1 control-label">公司代码</label>
            <div class="col-md-3">
                <form:input path="companyCode" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off" onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
            </div>
            <label class=" col-md-1 control-label">公司名称</label>
            <div class="col-md-3">
                <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
            </div>
            <label class="col-md-1 control-label">报名时间</label>
            <div class="col-md-3 daterange">
                <form:input path="createTime" placeholder="报名时间范围" cssClass="form-control" />
            </div>
            <label class="col-md-1 control-label">用户活动时间</label>
            <div class="col-md-3">
                <form:select path="timeRange" cssClass="form-control">
                    <form:option value="">全部</form:option>
                    <form:options items="${timeRangeList}" itemLabel="timeRange" itemValue="id" />
                </form:select>
            </div>
            <label class="col-md-1 control-label">所在辖区</label>
            <div class="col-md-3">
                <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
                <input name="belongCommission" type="hidden" />
            </div>
            <label class=" col-md-1 control-label">岗位</label>
            <div class="col-md-3">
                <form:input path="jobName" placeholder="请输入岗位" cssClass="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="row">
            <div class="col-md-24">
                <div style="margin-bottom: 8px;margin-right: 8px;float: right;">
                    <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="btnQuery" class="btn btn-primary">查询</span>
                    <span id="exportTable" class="btn btn-primary">导出</span>
                    <span id="sendSMSNotification" style="display: none" class="btn btn-primary">发送短信通知</span>
                    <span id="batchExamine" class="btn btn-primary">批量审核</span>
                    <span id="putOff" class="btn btn-primary">顺延</span>
                    <a href="javascript:void(0);" class="file btn btn-warning btn-facebook btn-outline"  style='float: right;width: 115px;'>
                        <i class="fa fa-upload"> </i> 导入审核结果
                        <input  id="btnUploadFile" type="file" name="files" style="width: 68px;" multiple />
                    </a>
                    <span id="makeCertificate" class="btn btn-primary">一键制作证书</span>
                    <span id="downCertificate" style="display: none" class="btn btn-primary">一键下载证书</span>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6" style="padding-top: 18px;">
                <span style="font-size: 14px;font-weight: bold">系统判定人员详情:</span>
            </div>
            <div style="margin-bottom: 8px;margin-right: 8px;float: right;">
                注：导入文件应为导出文件删除审核未通过人员，并且清空空白行，暂不支持新增人员
            </div>
        </div>
    </div>
    </form:form>
    <div  id="trainNum" style="width: 900px;top: 138px;position: absolute;"></div>
    <div class="row" style="padding-left: 10px;padding-right: 10px">
        <e:grid id="table1" action="/trainingOffline/trainingOfflineSignUpSysList?trainId=${OfflineTrainSignUpDto.trainId}"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="<input type='checkbox'  id='allCheck'>"
                          renderColumn="rcIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:5%" />
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:3%"/>
            <e:gridColumn label="姓名" renderColumn="nickName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="公司代码" displayColumn="companyCode" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="用户性质" displayColumn="personTypeStr" orderable="false"
                          cssClass="text-center" cssStyle="width:12%;"/>
            <e:gridColumn label="岗位" displayColumn="jobName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="辖区" displayColumn="belongCommissionStr" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="报名时间" displayColumn="createTime" orderColumn="createTime"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="活动时间" displayColumn="timeRange" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="中级下发证书时间" displayColumn="examineTime" orderColumn="examineTime"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="审核状态" displayColumn="statusStr" orderable="false"
                          cssClass="text-center" cssStyle="width:4%;"/>
            <e:gridColumn label="应缴费" displayColumn="money" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="缴费状态" renderColumn="paymentStatus" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                          cssClass="text-center" cssStyle="width:10%;"/>
        </e:grid>
    </div>
    <div class="row" style="margin-bottom: 8px;margin-right: 8px; margin-top: 10px;">
        <div class="col-md-6" style="padding-top: 18px;">
            <span style="font-size: 14px;font-weight: bold;padding-left: 15px;">人工判定人员详情:</span>
        </div>
        <div class="col-md-6">
            <div style="margin-bottom: 8px;margin-right: 8px;float: right;">
                <span id="addNewUser" class="btn btn-primary">添加人员</span>
                <span id="addUser" class="btn btn-primary">未通过人员</span>
            </div>
        </div>
    </div>
    <div class="row" style="padding-left: 10px;padding-right: 10px">
        <e:grid id="table2" action="/trainingOffline/trainingOfflineSignUpUserList?trainId=${OfflineTrainSignUpDto.trainId}"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:3%"/>
            <e:gridColumn label="姓名" renderColumn="nickName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="公司代码" displayColumn="companyCode" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="用户性质" displayColumn="personTypeStr" orderable="false"
                          cssClass="text-center" cssStyle="width:12%;"/>
            <e:gridColumn label="岗位" displayColumn="jobName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="辖区" displayColumn="belongCommissionStr" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="报名时间" displayColumn="createTime" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="活动时间" displayColumn="timeRange" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="中级下发证书时间" displayColumn="examineTime" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="审核状态" displayColumn="statusStr" orderable="false"
                          cssClass="text-center" cssStyle="width:4%;"/>
            <e:gridColumn label="应缴费" displayColumn="money" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="缴费状态" renderColumn="paymentStatus" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                          cssClass="text-center" cssStyle="width:12%;"/>
        </e:grid>
    </div>
</div>
</div>
</body>
</html>

