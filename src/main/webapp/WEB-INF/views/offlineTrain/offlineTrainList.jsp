<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            text-align: center;
            margin-top: 6px;
        }

        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="OfflineTrainDto" id="superviseInfoForm">
        <div class="row">
            <label class=" col-md-1 control-label">培训名称</label>
            <div class="col-md-3">
                <form:input path="trainTitle" placeholder="请输入培训名称" cssClass="form-control" autocomplete="off"/>
            </div>
                <%--<label class=" col-md-1 control-label">开放人群</label>
                <div class="col-md-3">
                    <form:select path="codeName" id="codeName" name="codeName" class="form-control">
                        <form:option value="">全部</form:option>
                        <c:forEach var="property" items="${TypeList}" varStatus="index">
                                <form:option value="${property.code_value}">${property.code_name}</form:option>
                        </c:forEach>
                    </form:select>
                </div>--%>
            <label class=" col-md-1 control-label">是否发布</label>
            <div class="col-xs-3">
                <form:select path="publish" cssClass="form-control">
                    <form:option value="">全部</form:option>
                    <form:option value="0">否</form:option>
                    <form:option value="1">是</form:option>
                </form:select>
            </div>
        </div>
    </div>
    <div class="row" style="margin-bottom: 8px;margin-right: 8px;float: right;">
        <sec:authorize access="hasAuthority('RES_TRAINING_OFFLINE_TRAINING_OFFLINE_INIT_ADD_AUTHORITY_3')" >
            <span id="btnAdd" class="btn btn-primary">新建现场活动</span>
        </sec:authorize>
        <span id="btnClear" class="btn btn-default">清空</span>
        <sec:authorize access="hasAuthority('RES_TRAINING_OFFLINE_TRAINING_OFFLINE_INIT_LIST_AUTHORITY_3')" >
            <span id="btnQuery" class="btn btn-primary">查询</span>
        </sec:authorize>
    </div>
    </form:form>
    <sec:authorize access="hasAuthority('RES_TRAINING_OFFLINE_TRAINING_OFFLINE_INIT_ADD_AUTHORITY_3')" >
        <%--编辑权限--%>
        <input type="hidden" id="editAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_TRAINING_OFFLINE_SAVE_TRAINING_OFFLINE_INFO_AUTHORITY_3')" >
        <%--发布/删除权限--%>
        <input type="hidden" id="releaseAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_TRAINING_OFFLINE_ADVANCED_AUTHORITY_3')" >
        <%--报名统计权限--%>
        <input type="hidden" id="singnUpAuth" value="true">
    </sec:authorize>
    <div class="row" style="padding-left: 10px;padding-right: 10px">
        <e:grid id="table1" action="/trainingOffline/trainingOfflineInitList"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:2%"/>
            <e:gridColumn label="培训名称" displayColumn="trainTitle" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <%--<e:gridColumn label="适用用户类型" displayColumn="codeName" orderable="false"
                          cssClass="text-center" cssStyle="width:8%"/>--%>
            <e:gridColumn label="报名时间" renderColumn="signUpTime" orderColumn="signUpStartTime"
                          cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
            <e:gridColumn label="培训时间" renderColumn="activityTime" orderColumn="activityStartTime"
                          cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
            <e:gridColumn label="培训地点" displayColumn="address" orderable="false"
                          cssClass="text-center" cssStyle="width:8%;"/>
            <e:gridColumn label="是否发布" displayColumn="publish" orderable="false"
                          cssClass="text-center" cssStyle="width:2%;"/>
            <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                          cssClass="text-center" cssStyle="width:14%;"/>
        </e:grid>
    </div>
</div>
</div>
</body>
</html>

