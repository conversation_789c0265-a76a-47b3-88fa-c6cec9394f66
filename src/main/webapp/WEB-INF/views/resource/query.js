//@ sourceURL=query.js
$(document).ready(
		function() {
			// 查询
			$("#btnQuery").bind("click", function() {
				// 直接提交
				submitForm("/resource/query");
			});

			if(document.getElementById("btnAdd")){
				$("#btnAdd").bind(
						"click",
						function() {

							parent.popWin("新增资源", "/resource/addpop", "", "500px",
									"450px", myWinCallback, callBack);
						});
			}
			
		});

function editPop(url, data) {
	parent.popWin("编辑资源", url, data, "500px", "450px", myWinCallback, callBack);
}
function itemPop(url, data) {
	parent.popWin("子资源信息", url, data, "90%", "80%", myWinCallback, callBack);
}
function myWinCallback(paraWin, paraCallBack) {
	//2016/08/29 bug2015 baiyang start
	submitForm("/resource/query?toPageState=back");
	//2016/08/29 bug2015 baiyang end
}
function callBack(data) {

	var jsonObj = {
		result : [ {} ]
	};

	closeWinCallBack(jsonObj);

}