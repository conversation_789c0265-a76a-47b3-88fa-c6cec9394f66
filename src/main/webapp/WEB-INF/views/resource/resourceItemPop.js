//@ sourceURL=resourceItemPop.jsp

// 表单校验规则
var validator;
// 当前选择节点
var selectNode;
var param
var parentid
$(document).ready(
		function() {
			parentid = $("#pId").val();
			$("#btnAdd").bind(
					"click",
					function() {
						//2017.10.16 by zhanghaoyu start
//						param = "id=" + $("#pId").val();
						var params = {
								id : $("#pId").val()
						}
						//2017.10.16 by zhanghaoyu end
						parent.popWin("新增子资源", "/resource/additempop", params, "500px",
								"350px", myWinCallback, callBack);
					});

		});

function edititemPop(url, data) {
	parent.popWin("编辑子资源", url, data, "500px", "450px", myWinCallback, callBack);
}

function myWinCallback(paraWin, paraCallBack) {

	movePage('/resource/itempop?data=' + parentid);

}
function callBack(data) {
}
