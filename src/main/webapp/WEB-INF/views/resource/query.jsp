<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js />
</head>
<body>
    <div class="panel" id="myPanel">
        <div class="panel-heading">
            <i class="icon icon-search"></i>查询
        </div>
        <div class="panel-body">
            <form:form action="/resource/query" modelAttribute="resourceDto">
                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10 text-right">
                        <input type="button" id="btnQuery" class="btn btn-primary" value="查询" /> 
                        <sec:authorize access="hasAuthority('RES_RESOURCE')" >
                        <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3')" >
                        <input type="button"
                            id="btnAdd" class="btn btn-default" value="新增" />
                            </sec:authorize>
                            </sec:authorize>
                             <label class="col-md-1 control-label">资源名称</label>
                        <div class="col-md-3">
                            <input type="text" style="display: none;" />
                            <form:input cssClass="form-control" path="resourceName" />
                        </div>
                    </div>
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table action="/resource/query" col="5" cssClass="table table-hover table-striped">
                <tr>
                    <th width="5%" align="center">序号</th>
                    <th width="20%" align="center">资源名称</th>
                    <th width="20%" align="center">资源描述</th>
                    <th width="20%" align="center">资源类型</th>
                    <th width="20%" align="center">URL</th>
                     <sec:authorize access="hasAuthority('RES_RESOURCE')" >
                        <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3') OR hasAuthority('RES_RESOURCE_AUTHORITY_2')" >
                    <th width="5%" align="center">操作</th>
                    </sec:authorize>
                    </sec:authorize>
                </tr>
                <c:forEach var="item" items="${queryList}" varStatus="status">
                    <tr>
                        <td align="center"><c:out value="${status.count}" /></td>
                        <td align="left"><c:out value="${item.resourceName}" /></td>
                        <td align="left"><c:out value="${item.resourceDes}" /></td>
                        <c:if test="${item.resourceType == 0}">
                            <td align="center"><c:out value="菜单资源" /></td>
                        </c:if>
                        <c:if test="${item.resourceType != 0}">
                            <td align="center"><c:out value="数据资源" /></td>
                        </c:if>
                        <td align="left"><c:out value="${item.resourceUrl}" /></td>
 <sec:authorize access="hasAuthority('RES_RESOURCE')" >
                        
                        <td align="center"><span class="widget-icon"><sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3') OR hasAuthority('RES_RESOURCE_AUTHORITY_2')" >
                         <a
                                href="javascript:editPop('/resource/editpop','data=<c:out value="${item.id}" />')"><i
                                    class="icon icon-edit"></i></a> <a
                                href="javascript:itemPop('/resource/itempop','data=<c:out value="${item.id}" />')"><i
                                    class="icon icon-th-large"></i></a>
                                    </sec:authorize>
                                    <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3')" >
                                     <a
                                href="javascript:movePage('/resource/delect?id=<c:out value="${item.id}" />')"><i
                                    class="icon icon-trash"></i></a>
                                    </sec:authorize> 
                        </span></td>
                        
                        </sec:authorize> 
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>
</body>
</html>
