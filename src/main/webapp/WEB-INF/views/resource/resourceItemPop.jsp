<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body>

    <div class="panel" id="myPanel">

        <div class="panel-body">
            <div class="form-group">
                <div class="col-md-offset-2 col-md-10 text-right">
                <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3')" >
                    <input type="button" id="btnAdd" class="btn btn-primary" value="新增" />
                    </sec:authorize>
                </div>
            </div>
        </div>
    </div>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table action="/resource/query" col="5" cssClass="table table-hover table-striped">
                <tr>
                    <th width="5%" align="center">序号</th>
                    <th width="20%" align="left">资源名称</th>
                    <th width="20%" align="left">资源描述</th>
                    <th width="20%" align="left">URL</th>
                    <th width="15%" align="center">操作</th>
                </tr>
                <c:forEach var="item" items="${resourceListDto.resourceDtoList}" varStatus="status">
                    <tr>
                        <td align="center"><c:out value="${status.count}" /></td>
                        <td align="left"><c:out value="${item.resourceName}" /></td>
                        <td align="left"><c:out value="${item.resourceDes}" /></td>
                        <td align="left"><c:out value="${item.resourceUrl}" /></td>
                        <td align="center"><span class="widget-icon">
                        <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3') OR hasAuthority('RES_RESOURCE_AUTHORITY_2')" >
                         <a
                                href="javascript:edititemPop('/resource/edititempop','data=<c:out value="${item.id}" />')"><i
                                    class="icon icon-edit"></i></a>
                                    </sec:authorize>
                                    <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3')" >
                                    <a
                                href="javascript:movePage('/resource/delectofpop?id=<c:out value="${item.id}" />&pId=<c:out value="${item.pId}" />')"><i
                                    class="icon icon-trash"></i></a>
                                    </sec:authorize>
                        </span></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>
    <form:form action="" modelAttribute="resourceDto" id="resourceForm">
        <form:hidden path="pId" id="pId" />
    </form:form>
</body>
</html>