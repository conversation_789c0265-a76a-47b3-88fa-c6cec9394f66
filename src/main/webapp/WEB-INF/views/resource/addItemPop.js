//@ sourceURL=addItemPop.js

// 表单校验规则
var validator;
// 当前选择节点
var selectNode;
$(document).ready(
		function() {

			// 设定表单校验规则
			$("#resourceForm").validate({
				rules : {

					"resourceName" : {
						// 必须
						required : true,
						// 后台校验：唯一性校验
						remote : {
							url : contextPath + "/resource/checkName",
							type : "post",
							async : false,
							data : {
								name : function() {
									return $("#resourceName").val();
								}
							}
						},
						// 长度
						maxlength : 32
					},
					"resourceDes" : {
						required : true,
					},
					"resourceUrl" : {
						required : true,
						// 后台校验：唯一性校验
						remote : {
							url : contextPath + "/resource/checkUrl",
							type : "post",
							async : false,
							data : {
								url : function() {
									return $("#resourceUrl").val();
								}
							}
						},
						// 长度
						maxlength : 256
					}
				},
				messages : {

					"resourceName" : {
						remote : "名称重复！"
					},
					"resourceUrl" : {
						remote : "名称重复！"
					},
				}
			});

			// 保存
			$("#btnSave")
					.bind(
							"click",
							function() {

								if ($("#id").val() == '') {
									if ($("#resourceForm").valid()) {
										//2017.10.9 by zhanghaoyu start
//										var param = "id=" + $("#id").val()
//										+ "&pId=" + $("#pId").val()
//										+ "&resourceName="
//										+ $("#resourceName").val()
//										+ "&resourceDes="
//										+ $("#resourceDes").val()
//										+ "&resourceUrl="
//										+ $("#resourceUrl").val();
										var param = {
												id : $("#id").val() ,
												pId : $("#pId").val() ,
												resourceName : $("#resourceName").val() ,
												resourceDes : $("#resourceDes").val() ,
												resourceUrl :$("#resourceUrl").val()
										}
										//2017.10.9 by zhanghaoyu end
										if ($("#id").val() == "") {
											ajaxData("/resource/addSave",
													param, function(data) {
														callback(data)
													});

										} else {
											ajaxData("/resource/editSave",
													param, function(data) {
														callback(data)
													});
										}
									}
								} else {
									//2017.10.9 by zhanghaoyu start
//									var param = "id=" + $("#id").val()
//									+ "&pId=" + $("#pId").val()
//									+ "&resourceName="
//									+ $("#resourceName").val()
//									+ "&resourceDes="
//									+ $("#resourceDes").val()
//									+ "&resourceUrl="
//									+ $("#resourceUrl").val();
									var param = {
											id : ("#id").val() ,
											pId : $("#pId").val() ,
											resourceName : $("#resourceName").val() ,
											resourceDes : $("#resourceDes").val() ,
											resourceUrl : $("#resourceUrl").val()
									}
									//2017.10.9 by zhanghaoyu end
									if ($("#id").val() == "") {
										ajaxData("/resource/addSave", param,
												function(data) {
													callback(data)
												});

									} else {
										ajaxData("/resource/editSave", param,
												function(data) {
													callback(data)
												});
									}

								}

							});

			// 关闭
			$("#btnClose").bind("click", function() {
				closeWin();
			});

		});

function myWinCallback(paraWin, paraCallBack) {
}

function callback(data) {

	var jsonObj = {
		result : [ {} ]
	};

	closeWinCallBack(jsonObj);

}