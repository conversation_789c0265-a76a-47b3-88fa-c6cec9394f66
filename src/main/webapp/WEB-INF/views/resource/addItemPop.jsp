<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-alt"> 资讯管理</i>
        </div>
        <div class="panel-body">
            <form:form action="" modelAttribute="resourceDto" id="resourceForm">

                <table>
                    <tr height="50px">
                        <td width="50%" align="center"><b>资源名称</b></td>
                        <td width="50%"><form:input path="resourceName" id="resourceName" cssClass="form-control" /></td>
                    </tr>
                    <tr height="50px">
                        <td width="50%" align="center"><b>资源描述</b></td>
                        <td width="50%"><form:input path="resourceDes" id="resourceDes" cssClass="form-control" /></td>
                    </tr>
                    <tr height="50px">
                        <td width="50%" align="center"><b>URL</b></td>
                        <td width="50%"><form:input path="resourceUrl" id="resourceUrl" cssClass="form-control" /></td>
                    </tr>
                </table>
                <div class="form-group">
                    <div class="col-md-offset-10 col-md-2" align="right">
                    <sec:authorize access="hasAuthority('RES_RESOURCE_AUTHORITY_3')" >
                        <input type="button" id="btnSave" class="btn btn-primary" value="保存" /> 
                        </sec:authorize>
                        <input type="button"
                            id="btnClose" class="btn btn-default" value="关闭" />
                    </div>
                </div>
                <form:hidden path="id" id="id" />
                <form:hidden path="pId" id="pId" />
            </form:form>
        </div>
    </div>
</body>
</html>