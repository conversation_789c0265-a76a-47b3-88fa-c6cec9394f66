//@ sourceURL=resourcePop.js

//var setting = {
//	view : {
//		showIcon : false,
//		dblClickExpand : false
//	},
//	callback : {
//		onClick : onSelectClick,
//
//	},
//	data : {
//		simpleData : {
//			enable : true,
//			idKey : "id",
//			pIdKey : "pMenuId",
//			rootPId : 0
//		},
//		key : {
//			name : "menuName"
//		}
//	},
//};
// 表单校验规则
var validator;
// 当前选择节点
var selectNode;
$(document).ready(
		function() {

			// 设定表单校验规则
			$("#resourceForm").validate({
				rules : {

					"resourceName" : {
						// 必须
						required : true,
						// 后台校验：唯一性校验
						remote : {
							url : contextPath + "/resource/checkName",
							type : "post",
							async : false,
							data : {
								name : function() {
									return $("#resourceName").val();
								}
							}
						},
						// 长度
						maxlength : 32
					},
					"resourceDes" : {
						required : true,
					},
					"resourceType" : {
						required : true,
					},
					"resourceUrl" : {
						required : true,
						// 后台校验：唯一性校验
						remote : {
							url : contextPath + "/resource/checkUrl",
							type : "post",
							async : false,
							data : {
								url : function() {
									return $("#resourceUrl").val();
								}
							}
						},
						// 长度
						maxlength : 256
					}
//					"typeSel" : {
//						required : true,
//					},
				},
				messages : {

					"resourceName" : {
						remote : "名称重复！"
					},
					"resourceUrl" : {
						remote : "名称重复！"
					},
				}
			});

//			var zNodes = $.parseJSON($("#resourceListId").text());
//			$.fn.zTree.init($("#informationType"), setting, zNodes);

			// 保存
			$("#btnSave").bind(
					"click",
					function() {
						
//						if($("#typeSel").val() == ''){
//							
//							popAlert("选择菜单内容不可为空！");
//						}
						
						if($("#id").val() == ""){
							
							if ($("#resourceForm").valid()) {
								//2017.10.9 by zhanghaoyu start
//								var param = "id=" + $("#id").val()
//								+ "&resourceName="
//								+ $("#resourceName").val()
//								+ "&resourceDes=" + $("#resourceDes").val()
//								+ "&resourceType="
//								+ $("#resourceType").val()
//								+ "&resourceUrl=" + $("#resourceUrl").val();
////								+ "&relation=" + selectNode.id;
								var param = {
										id : $("#id").val() ,
										resourceName : $("#resourceName").val() ,
										resourceDes : $("#resourceDes").val() ,
										resourceType : $("#resourceType").val() ,
										resourceUrl : $("#resourceUrl").val()
								}
								//2017.10.9 by zhanghaoyu end
								if ($("#id").val() == "") {
									ajaxData("/resource/addSave", param, function(
											data) {
										callback(data)
									});

								} else {
									ajaxData("/resource/editSave", param, function(
											data) {
										callback(data)
									});
								}
							}
						}else {
							//2017.10.9 by zhanghaoyu start
//							var param = "id=" + $("#id").val()
//							+ "&resourceName="
//							+ $("#resourceName").val()
//							+ "&resourceDes=" + $("#resourceDes").val()
//							+ "&resourceType="
//							+ $("#resourceType").val()
//							+ "&resourceUrl=" + $("#resourceUrl").val();
////							+ "&relation=" + selectNode.id;
							var param = {
									id : $("#id").val() ,
									resourceName : $("#resourceName").val() ,
									resourceDes : $("#resourceDes").val() ,
									resourceType : $("#resourceType").val() ,
									resourceUrl : $("#resourceUrl").val()
							}
							//2017.10.9 by zhanghaoyu end
							if ($("#id").val() == "") {
								ajaxData("/resource/addSave", param, function(
										data) {
									callback(data)
								});

							} else {
								ajaxData("/resource/editSave", param, function(
										data) {
									callback(data)
								});
							}
						}
						
					});

			// 关闭
			$("#btnClose").bind("click", function() {
				closeWin();
			});

		});

function callBack(data) {
	$("#Id").val("");
}
//function onSelectClick(e, treeId, treeNode) {
//	var treeObj = $.fn.zTree.getZTreeObj("informationType");
//	var nodes = treeObj.getSelectedNodes();
//	if (nodes.length > 0) {
//		selectNode = nodes[0];
//	}
//	if (selectNode.menuName == "云端管理平台") {
//		$("#typeSel").val("");
//	} else {
//		$("#typeSel").val(selectNode.menuName);
//	}
//	hideMenu()
//}
//
//function showMenu() {
//
//	var obj = $("#typeSel");
//	var left = $("#typeSel").offset().left;
//	var top = $("#typeSel").offset().top;
//	$("#menuContent").css({
//		left : left + "px",
//		top : top + obj.outerHeight() + "px",
//		width : obj.outerWidth() + "px",
//		height : "100px"
//	}).slideDown("fast");
//	$(document).bind("mousedown", onBodyDown);
//}
//
//function hideMenu() {
//	$("#menuContent").fadeOut("fast");
//	$(document).unbind("mousedown", onBodyDown);
//}
//
//function onBodyDown(event) {
//	if (!(event.target.id == "menuBtn" || event.target.id == "typeSel"
//			|| event.target.id == "menuContent" || $(event.target).parents(
//			"#menuContent").length > 0)) {
//		hideMenu();
//	}
//}

function myWinCallback(paraWin, paraCallBack) {
}

function callback(data) {


	var jsonObj = {
		result : [ {} ]
	};

	closeWinCallBack(jsonObj);
}