<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body>
<div class="panel">
        <div class="panel-body">
            <form:form id="newsManageEditDtoForm" modelAttribute="newsManageEditDto" cssClass="form-horizontal">
                <div class="col-xs-12">
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;text-align: center;"><font color="#FF0000">*</font>新闻标题</label>
                    <div class="col-xs-10 no-padding">
                        <form:input path="newsTitle" cssClass="form-control" />
                    </div>
                </div>
                <div class="col-xs-12">
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;text-align: center;"><font color="#FF0000">*</font>新闻分类</label>
                    <div class="col-xs-4 no-padding">
                        <input id="newsType" json-data='${newsTypeList}' type="text" class="t-select pullDownList" placeholder="新闻分类" />
                        <form:hidden path="newsType" id="newsType" />
                    </div>
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;text-align: center;"><font color="#FF0000">*</font>来源</label>
                    <div class="col-xs-4 no-padding">
                        <form:input path="source" cssClass="form-control" />
                    </div>
                </div>
                <div class="col-xs-12">
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;text-align: center;"><font color="#FF0000"></font>原文链接</label>
                    <div class="col-xs-10 no-padding">
                        <form:input path="sourceUrl" cssClass="form-control" />
                    </div>
                </div>
                
                <div class="col-xs-12">
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;text-align: center;"><font color="#FF0000">*</font>发布日期</label>
                    <div class="col-xs-4 no-padding">
                        <input id="published" name="published" class="form-control"  type="text" value="${newsManageEditDto.pushTime}"
                        input-type="datetimepicker" data-date-autoclose="true" data-date-today-highlight="true" 
                        data-date-today-btn="true" data-font-awesome="true" data-date-language="zh-CN" 
                        data-date-format="yyyy-mm-dd" data-start-view="month" data-min-view="month" 
                        data-max-view="decade" data-minute-step="5">
                    </div>
                </div>

                <div class="col-xs-12">
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;text-align: center;"><font color="#FF0000"></font>新闻图片</label>
                    <div class="col-xs-5" style="padding-right: 0px !important;">
                        <span class="btn btn-sm btn-success fileinput-button"><span>上传</span><input id="up" type="file" name="files"></span>
                        <span onclick="clearImg()" class="btn btn-sm" style="margin-left: 20px;">删除</span>
                        <span onclick="previewImg()" class="btn btn-sm previewClass" style="margin-left: 20px;display: none;">预览</span>
                    </div>
                    
                    <label class="col-xs-5 control-label no-padding" style="text-align: center;color: red;">大图尺寸不小于:683*385,小图尺寸不小于:220*146</label>
                </div>
                <div class="col-xs-12" style="max-height: 90px;">
                    <label class="col-xs-2 no-padding control-label" style="margin-top: 5px;"></label>
                    <div id="imgId" class="col-xs-4" style="display: none">
                        <img id="ImgPr" src="${newsManageEditDto.picUrl}" style="width: 50px;height: 50px;"/>
                        <input type="hidden" name="fileId" id="imgField" />
                        <input type="hidden" id="delFileId" name="delFileId" />
                        <form:hidden path="picId" />
                    </div>
                     <label class="col-xs-2 no-padding control-label imgTypeRadio" style="margin-top: 5px;text-align: center;display: none;"><font color="#FF0000">*</font>新闻图片类型</label>
                    <div class="col-xs-4 no-padding imgTypeRadio" style="display: none;">
                        <input type="radio" name="autoCrawlPub" value="1" style="margin-right: 5px;">小图
                        <input type="radio" name="autoCrawlPub" value="2" style="margin-left: 20px;margin-right: 5px;">大图
                    </div>
                </div>
                <div class="row" style="margin-top: 20px;text-align: center;">
                    <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存" />
                    <input type="button" id="btnCancel" class="btn btn-default" value="取消" />
                </div>
                <form:hidden path="id" id="id" />
                <form:hidden path="imgType" />
            </form:form>
        </div>
    </div>     
</body>
</html>