//@ sourceURL=editNews.js
$(document).ready(function() {
	 tSelectDataInit();
	$("#newsManageEditDtoForm").validate({
		rules : {
			"newsTitle" : {
				required : true,
				maxlength : 500
			},
//			"sourceUrl" : {
//				required : true,
//				maxlength : 2000
//			},
			"source":{
				required : true,
				maxlength : 500
			},
			"published" : {
				required : true
			},
			"newsType" : {
				required : true
			},
		}
	});
	// 保存
	$("#btnSave").bind("click", function() {
		if( !(getValue($("#imgField").val()) == "" && getValue($("#picId").val()) == "") ) {
			if(getValue($('input:radio[name="autoCrawlPub"]:checked').val()) == ""){
				popMsg("请选择新闻图片类型");
				return;
			}
		}

		if(getValue($('input[name="newsType"]').val()) == ""){
			popMsg("请选择新闻分类");
			return;
		}
//		if(getValue($('input[name="sourceUrl"]').val()) != ""){
//			var flag = /^((https|http|ftp|rtsp|mms){0,1}(:\/\/){0,1})www\.(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/i.test($('input[name="sourceUrl"]').val());
//			if(!flag){
//				popMsg("请检查原文链接是否正确");
//			}
//		}
		if ($("#newsManageEditDtoForm").valid()) {
			var param = {
					"id":$("#id").val(),
					"newsTitle":$('input[name="newsTitle"]').val(),
					"newsType":$('input[name="newsType"]').val(),
					"source":$('input[name="source"]').val(),
					"sourceUrl":$('input[name="sourceUrl"]').val(),
					"pushTime":$('input[name="published"]').val(),
					"imgField" :$("#imgField").val(),
					"picId":$("#picId").val(),
					"delFileId":$('input[name="delFileId"]').val(),
					"imgType":$('input:radio[name="autoCrawlPub"]:checked').val()
			};
			ajaxData("/newsManagement/saveNews",param,function(data) {
				popMsg("保存成功");
				closeWinCallBack();
			});
		};
	});
	// 取消
	$('#btnCancel').bind('click', function() {
		closeWin();
	});
	
	if ($("#ImgPr").attr("src") != "") {
		$("#imgId").css("display", "block");
		$(".imgTypeRadio").show();
		$(".previewClass").show();
		var autoCrawlPub = $("#imgType").val() || 1;
		$("input[type='radio'][name='autoCrawlPub'][value="+autoCrawlPub+"]")[0].checked = true;
	}
	
	var url = contextPath + '/filetempupload';
	$('#up').fileupload({
		url : url,
		dataType : 'json',
		autoUpload : true,
		submit : function(e, data) {
			index = layer.load(1, {
				shade : [ 0.1, '#fff' ]
			// 0.1透明度的白色背景
			});
		},
		done : function(e, data) {
			$.each(data.result, function(index, file) {
				$("#imgField").val(file.fileId);
			});
			layer.close(index);
		}
	});
	$("#up").uploadPreview({
		Img : "ImgPr",
		Width : 50,
		Height : 50
	});

});

//预览
function previewImg(){
	var flag =$('input:radio[name="autoCrawlPub"]:checked').val();
	var url = $("#ImgPr").attr("src");
	if(url != null && url != ""){
		if(url.indexOf("?") != -1){
			var urlArr = url.split("?");
			if(flag == "1"){
				window.open(urlArr[0]+"!c_220x146"+"?"+urlArr[1]);
			}else{
				window.open(urlArr[0]+"!c_683x385"+"?"+urlArr[1]);
			}
		}else{
			if(flag == "1"){
				window.open(url+"!c_220x146");
			}else{
				window.open(url+"!c_683x385");
			}
		}

	}

}


function clearImg(obi, fieldId) {
	popConfirm("确认删除", function() {
		$("#imgField").val("");
		$("#imgId").css("display", "none");
		$(".imgTypeRadio").hide();
		$(".previewClass").hide();
		$("#up").val("");
		if ($("#picId").val() != "") {
			$('input[name="delFileId"]').val($("#picId").val());
			$("#picId").val("");
		}
	});
}
$.fn
		.extend({
			uploadPreview : function(opts) {
				var _self = this, _this = $(this);
				opts = jQuery.extend({
					Img : "ImgPr",
					Width : 100,
					Height : 100,
					ImgType : [ "gif", "jpeg", "jpg", "bmp", "png" ],
					Callback : function() {
					}
				}, opts || {});
				_self.getObjectURL = function(file) {
					var url = null;
					if (window.createObjectURL != undefined) {
						url = window.createObjectURL(file)
					} else if (window.URL != undefined) {
						url = window.URL.createObjectURL(file)
					} else if (window.webkitURL != undefined) {
						url = window.webkitURL.createObjectURL(file)
					}
					if ($("#picId").val() != "") {
						$('input[name="delFileId"]').val($("#picId").val());
						$("#picId").val("");
					}

					$("#imgId").css("display", "block");
					$(".imgTypeRadio").show();
					$(".previewClass").show();
					return url
				};

				_this
						.change(function() {
							if (this.value) {
								if (!RegExp(
										"\.(" + opts.ImgType.join("|") + ")$",
										"i").test(this.value.toLowerCase())) {
									alert("选择文件错误,图片类型必须是"
											+ opts.ImgType.join("，") + "中的一种");
									this.value = "";
									return false
								}
								$.browser = new Object();
								$.browser.msie = /msie/
										.test(navigator.userAgent.toLowerCase());
								if ($.browser.msie) {
									try {
										$("#" + opts.Img)
												.attr(
														'src',
														_self
																.getObjectURL(this.files[0]))
									} catch (e) {
										var src = "";
										var obj = $("#" + opts.Img);
										var div = obj.parent("div")[0];
										_self.select();
										if (top != self) {
											window.parent.document.body.focus()
										} else {
											_self.blur()
										}
										src = document.selection.createRange().text;
										document.selection.empty();
										obj.hide();
										obj
												.parent("div")
												.css(
														{
															'filter' : 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
															'width' : opts.Width
																	+ 'px',
															'height' : opts.Height
																	+ 'px'
														});
										div.filters
												.item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
									}
								} else {
									$("#" + opts.Img).attr('src',
											_self.getObjectURL(this.files[0]))
								}
								opts.Callback()
							}
						})
			}
		})

		
function tSelectDataInit() {
	 var selPlateIds = $('input[name="newsType"]').val();
	    var tSelectOptions2 = {
	        customCallBack: tSelectCustomCallBack,
	        submitCallBack: tSelectSubmitCallBack,
	        id: 'id',
	        pid: 'parentId',
	        name: 'codeName',
	        value: 'codeValue',
	        grade: 1,
	        openDown: false,
	        resultType: 'children',
	        style: {},
	        selectedIds: selPlateIds
	    };
	    $("#newsType").tselectInit(null, tSelectOptions2);
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
	// 提交检索
	$('input[name="' + t.attr('id') + '"]').val(d.value);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
	//
}

