//@ sourceURL=newsMarkdown.js
var picNo = $("#picNo").val();
$(document).ready(function() {
var temp = $(document).scrollTop();
	$(window).scroll(function() {
		var offsetTop =   document.documentElement.clientHeight + getScrollTop()-75;
		var offsetTopStr = offsetTop + "px";
		$("#btn_Cancle_div").animate({
			top : offsetTopStr
		}, {
			duration : 500,
			queue : false
		});
	});
	$(window).scroll();
	

  
	$("#lawTreeView").bind("click", function() {
		//2017.10.16 by zhanghaoyu start
//		var param = "id=" + $("#id").val();
		var param = {
				id : $("#id").val()
		}
		//2017.10.16 by zhanghaoyu end
		popWin($("#lawsName").val(), "/lawsManage/lawTextInit", param, "100%", "100%");
	});

	function getScrollTop() {
		var scrollPos;
		if (window.pageYOffset) {
			scrollPos = window.pageYOffset;
		} else if (document.compatMode && document.compatMode != 'BackCompat') {
			scrollPos = document.documentElement.scrollTop;
		} else if (document.body) {
			scrollPos = document.body.scrollTop;
		}
		return scrollPos;
	}
	
	//需求4543 2018/6/22 by liuh Start
	//上传图片
	$("#picUpload").bind("click",function() {
		picUpload();
	});

	//繁体转简体 xuehui
	$("#convert2Simple").bind("click",function () {
        $("#lawSrcUrl").val(markEditor.getMarkdown());
        submitForm("/lawsManage/convert2Simple");
    })
    // demand 6344 start
	if ($("#modifyType").val() == "3") {
		$(".panel-body input").attr("readOnly", "true");
		$(".panel-body input").attr("disAbled", "true");
		$(".panel-body select").attr("disabled","true");
		$(".panel-body button").attr("disabled","true");
		$(".panel-body i").attr("onclick", "");
		$(".panel-body .text-danger").attr("onclick", "");
		$(".panel-body #lawAllView").removeAttr("disabled");
		$(".panel-body #lawTreeView").removeAttr("disabled");
		$(".panel-body #btnCancel").removeAttr("disabled");
	}
	// demand 6344 end
	// demand 7907 start
	document.addEventListener('paste', function (event) {
		pastePicture(event);
	});
	// demand 7907 end
});

function picUpload(){
	var businessId = $("#id").val();
	//图片的序号
	if(picNo == undefined || picNo == "") {
		picNo = "1";
	}
	var url = contextPath+'/lawsManage/saveTempLawsPic?'+'&businessId='+businessId+'&picNo='+picNo;
	$('#picUpload').fileupload({
		url : url,
		dataType : 'json',
		autoUpload: true,
		add:function(e,data){
			var fileName = data.files[0].name.toLowerCase();
			var fileStuff = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length);
			if( fileStuff != 'bmp' && fileStuff != 'png' && fileStuff != 'jpeg' && fileStuff  != 'jpg'){
				popMsg('请上传正确格式图片');
				return;
			}
			data.submit();
		},
		done : function(e, data) {
			//将图片添加到页面显示
			addLinkPic(data.result);
		},
		progressall : function(e, data) {
			
		}
	});
}

function addLinkPic(result){
	var picPathArray = result.result;
	if(picPathArray){
		for(var index in picPathArray){
			var selection = markEditor.getSelection();
			markEditor.replaceSelection('![]('+picPathArray[index]+')');
		}
	}
	var cursor = markEditor.getCursor();
	markEditor.setCursor({
		line : cursor.line + 1,
		ch : 0
	});
	markEditor.focus();
	//添加后更新图片的序号
	picNo = parseInt(picNo)+1;
//	$("#picNo").val(picNo);
}
//需求4543 2018/6/22 by liuh end
