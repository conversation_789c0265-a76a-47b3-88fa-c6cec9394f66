<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:js />
<body>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-alt"><label>&nbsp;专题新闻数据</label></i>
        </div>
        <div class="panel-body">
            <form:form id="newsManageDtoForm" modelAttribute="newsManageDto">
                <div class="row">
                    <div class="col-xs-6">
                        <form:input path="keyWord" cssClass="form-control" placeholder="新闻标题，内容关键字"/>
                    </div>
                    <div class="col-xs-3 daterange" style="padding-right: 0px;">
                         <input type="text" id="pushTime" name="pushTime" class="t-select form-control radius" placeholder="请选择提问日期"/>
                    </div>
                    <div class="col-xs-3">
                        <input id="newsType" json-data='${newsTypeList}' type="text" class="t-select pullDownList" placeholder="新闻分类" />
                        <form:hidden path="newsType" id="newsType" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-3">
                        <form:input path="source" cssClass="form-control" placeholder="来源"/>
                    </div>
                    <div class="col-xs-3">
                        <form:select path="status" cssClass="form-control">
                            <form:option value="">发布状态</form:option>
                            <form:option value="0">未发布</form:option>
                            <form:option value="1">已发布</form:option>
                        </form:select>
                    </div>
                    <div class="col-xs-6">
                        <div class="col-xs-12" id="numBtn" align="right">
                            <input type="button" id="clearAllOptions" class="btn btn-4 btn-bny" value="清空条件">&nbsp;&nbsp;
                            <input type="button" id="btnQuery" class="btn btn-primary"  value="查询">&nbsp;&nbsp;
                             <input type="button" id="btnAdd" class="btn text-center btn-primary" value="新增" >
                        </div> 
                    </div>
                </div>
            </form:form>
            <div class="row;margin-left:auto;margin-right:auto;" style="margin-top:10px;">
                <e:grid id="table_id" action="/newsManagement/listSearch" cssClass="table table-striped table-hover">
                    <e:gridColumn label="新闻标题" displayColumn="newsTitle" orderable="false" cssClass="text-center" cssStyle="width:15%"/>
                    <e:gridColumn label="新闻分类" displayColumn="sourceName" orderable="false" cssClass="text-center" cssStyle="width:8%"/>
                    <e:gridColumn label="发布时间" displayColumn="pushTime" orderColumn="pushTime" cssClass="text-center"  cssStyle="width:8%"/>
                    <e:gridColumn label="来源" displayColumn="source" orderable="false" cssClass="text-center" cssStyle="width:5%"/>
                    <e:gridColumn label="发布状态" renderColumn="renderColumnStatus"  orderable="false" cssClass="text-center" cssStyle="width:5%;"/>
                    <e:gridColumn label="最后更新时间" displayColumn="updateTimeStr" orderColumn="updateTime" cssClass="text-center" cssStyle="width:5%;"/>
                    <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false" cssClass="text-center" cssStyle="width:10%"/>
                </e:grid>
            </div>
        </div>
    </div>    
</body>
</html>