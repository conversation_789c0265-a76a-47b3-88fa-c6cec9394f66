<%--
/***************************************************************
* 程序名 : lawmarkdown.jsp
* 日期  :  2015-7-8
* 作者  :  zhujg
* 模块  :  法律法规XML结构库作成
* 描述  :  法律法规XML结构库作成
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
<style type="text/css">
.relation_name{
    text-overflow:ellipsis;
    overflow:hidden;
    white-space:nowrap;
    width:350px;
}
.float-span{
    float:left;
}
</style>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/lib/editor.md/css/style.css" />
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/lib/editor.md/css/editormd.css" />
<link rel="shortcut icon" href="https://pandao.github.io/editor.md/favicon.ico" type="image/x-icon" />
<script src="${pageContext.request.contextPath}/static/lib/editor.md/js/editormd.js"></script>

</head>
<body>
    <div class="panel" style="height: 25px;">
        <div class="panel-heading" style="height: 45px; padding-bottom: 5px;">
            <i class="icon icon-list" style="float: left">专题新闻数据【${newsManageEditDto.newsTitle}】</i>
            <span
                style="float: right; font-size: 10px; line-height: 0px; text-align: right;">
                <b>更新者</b>：${newsManageEditDto.updateUser}&nbsp;&nbsp;<b>最终更新时间：</b> <fmt:formatDate
                    value="${newsManageEditDto.updateTime}" type="both"
                    pattern="yyyy.MM.dd HH:mm" />
            </span>
        </div>

        <div class="panel-body" style="height: 40px;">
            <form:form id="newsManageEditDtoForm" action=""
                modelAttribute="newsManageEditDto">
                <form:hidden path="id" id="id" value="${newsManageEditDto.id}" />
                <form:hidden path="newsText" id="newsText"
                    value="${newsManageEditDto.newsText}" />
                <form:hidden path="newsMarkdownText" id="newsMarkdownText"
                    value="${newsManageEditDto.newsMarkdownText}" />
                <form:hidden path="status" id="status"
                    value="${newsManageEditDto.status}" />
                <!-- 是否发布 -->   
                <input id="isPublish" name="isPublish" type="hidden">   
                <!-- end -->      
                <div class="col-xs-14" style="position: absolute; right: 35px;">
                    标注状态：&nbsp;
                    <c:choose>
                        <c:when test="${newsManageEditDto.status == '0'}">
                            <label class='radio-inline'> <input type="radio" id="status_1" name="optionsRadios" value="0" checked> 未标注 </label>
                            <label class='radio-inline'> <input type="radio" id="status_2" name="optionsRadios" value="1"> 标注中</label>
                            <label class='radio-inline'> <input type="radio" id="status_3" name="optionsRadios" value="2"> 完成</label>
                        </c:when>
                        <c:when test="${newsManageEditDto.status == '1'}">
                            <label class='radio-inline'> <input type="radio" id="status_1" name="optionsRadios" value="0" > 未标注 </label>
                            <label class='radio-inline'> <input type="radio" id="status_2" name="optionsRadios" value="1" checked> 标注中</label>
                            <label class='radio-inline'> <input type="radio" id="status_3" name="optionsRadios" value="2"> 完成</label>
                        </c:when>
                        <c:otherwise>
                            <label class='radio-inline'> <input type="radio" id="status_1" name="optionsRadios" value="0"> 未标注 </label>
                            <label class='radio-inline'> <input type="radio" id="status_2" name="optionsRadios" value="1"> 标注中</label>
                            <label class='radio-inline'> <input type="radio" id="status_3" name="optionsRadios" value="2" checked> 完成</label>
                        </c:otherwise>
                    </c:choose>
                    &nbsp;
                    <input type="button" id="btnSave" name = "test02" class="btn btn-sm btn-primary"
                        value="保存" />
                    <input type="button" id="btnSaveAndPublish" class="btn btn-sm btn-primary"
                        value="保存并发布" />
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel">
        <div class="panel-body"
            style="padding-top: 5px; padding-bottom: 5px; padding-left: 15px; text-align: left;">
            <div style="height: 40px ; width: 20px">
                
            </div>
        </div>
    </div>
    <div class="panel-body" style="padding-top: 5px; padding-bottom: 5px;">
        <div id="test-editormd">
            <textarea style="display: none;" id="markdownTextarea">${newsManageEditDto.newsText}</textarea>
        </div>
        <!-- <button class="btn btn-warning" id="markdownInit">初始化导入</button> -->

    <span style="font-size:10px; float:left">说明：1.使用快捷键进行编辑时，自动追加对应的标签的同时会自动追加空行，会在保存时自动去除。</span><br>
    <span style="font-size:10px; float:left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    2.指引开始和结尾各有且只有一组【@@@】符号，若以外的场合会造成标注结果不正确。</span>
    </div>
    
    <div class="panel">
        <div id="btn_Cancle_div" class="col-md-14"
            style="position: absolute; right: 10px; z-index: 9999; float: left;">
            <!--需求4543 2018/6/22 by liuh Start-->
            <span class="btn btn-sm btn-primary fileinput-button">添加图片<input type="file" id="picUpload" name="files"/></span>
            <!--需求4543 2018/6/22 by liuh end-->
            <button class="btn btn-sm btn-primary" id="h1">一级标题-F6</button>
            <button class="btn btn-sm btn-primary" id="h2">二级标题-F7</button>
            <button class="btn btn-sm btn-primary" id="blockquote">正文-F9</button>
        </div>
    </div>
    <script type="text/javascript">
        if ($("#lawMarkdownText").text != null) {
            $("#SavedDataPreview").attr('disabled', false);
            $("#SavedDataPreview").attr("class", "btn btn-sm btn-primary");
            $("#lawAllView").attr('disabled', false);
            $("#lawAllView").attr("class", "btn btn-sm btn-primary");
        };
        var markEditor;
        var status = $("#status").val();
        $(function() {
            markEditor = editormd("test-editormd", {
                width : "100%",
                height : 640,
                syncScrolling : "true",
                path : contextPath + "/static/lib/editor.md/lib/",
                disabledKeyMaps : [ "F6", "F7", "F8", "F9", "F10", "F11","Ctrl-1","Ctrl-2" // disable some default keyboard shortcuts handle
                ],
                toolbar : false,
                onload : function() {
                    /** f6:\n# f7:\## f8:\###  f9:> f10:>>
                    h1:章（一级标题）-F6
                    h2节（二级标题）-F7
                    h3 条（三级标题）-F8
                    blockquote 正文（缩进）-F9
                    blockquote2 正文（2倍缩进）-F10*/
                    var keyMap = {
                        "F6" : function(cm) {
                            addMark(markEditor, "H1");
                        },
                        "F7" : function(cm) {
                            addMark(markEditor, "H2");
                        },
                        "F8" : function(cm) {
                            addMark(markEditor, "H3");
                        },
                        "F9" : function(cm) {
                            addMark(markEditor, "blockquote");
                        },
                        "F10" : function(cm) {
                            addMark(markEditor, "blockquote2");
                        },
                        "F11" : function(cm) {
                            addMark(markEditor, "guide");
                        },
                        "Ctrl-1" : function(cm) {
                            addMark(markEditor, "right");
                        },
                        "Ctrl-2" : function(cm) {
                            addMark(markEditor, "center");
                        },
                    };
                    this.addKeyMap(keyMap);
                }
            });
            //只保存标注原文
            $("#btnSave").bind("click", function() {

            	$("#newsText").val(markEditor.getMarkdown());
                $("#newsMarkdownText").val(markEditor.getPreviewedHTML());
            	$("#status").val($("input[name='optionsRadios']:checked").val());
            	$("#isPublish").val("0");
            	if ($("#newsManageEditDtoForm").valid()) {
                    ajaxSubmitForm("/newsManagement/updateNewsContent", "",callBack, 0);
            	}
           });
            //保存并发布
            $("#btnSaveAndPublish").bind("click",function() {

                $("#newsText").val(markEditor.getMarkdown());
                $("#newsMarkdownText").val(markEditor.getPreviewedHTML());
                $("#status").val("2");
                $("#isPublish").val("1");
                if ($("#newsManageEditDtoForm").valid()) {
                    ajaxSubmitForm("/newsManagement/updateNewsContent", "",callBack, 0);
                }
            });

            //保存的HTML文本内容确认（待发布结果）
            $("#SavedDataPreview").bind(
                    "click",
                    function() {
                        if ($("#SavedDataPreview").valid()) {
                            var param = "id=" + $("#id").val();
                            window.open(contextPath + 
                                    "/lawsManage/lawmarkdownResultView?"+param);
                        }
                    });
            //保存的HTML全文内容预览（其他功能用）
            $("#lawAllView").bind(
                    "click",
                    function() {
                        if ($("#lawAllView").valid()) {
                            var param = "id=" + $("#id").val();
                            window.open(contextPath + "/lawsManage/lawAllView?"
                                    + param);
                        }
                    });

            /*      //初始化文本文件
                    $("#markdownInit").bind("click", function() {
                        submitForm("/lawsManage/lawmarkdownAgainInit");
                    }); */
            // 取消按钮提交
            $("#btnCancel").bind("click", function() {
                submitForm("/lawsManage/lawsManageInit?toPageState=back");
            });

            // 自动标注来函
            $('#autoLabel').click(function() {
                ;
                $("#lawSrcUrl").val(markEditor.getMarkdown());
                $("#lawMarkdownText").val(markEditor.getPreviewedHTML());
                submitForm("/lawsManage/autoLabelLaw");
            });
            
            // 导入原文
            $('#importText').click(function() {
                ;
                var param = {
                    id : $("#id").val()
                }
                ajaxData("/lawsManage/getPkulawText", param, function(data) {
                    if (getValue(data.result) == '1') {
                        markEditor.setMarkdown(data.pkulawText);
                    } else {
                        popMsg("无可导入的原文")
                    }
                });
                
            });
            
            $("#previewing").click(function() {
                markEditor.previewing();
            });
            $("#previewed").click(function() {
                markEditor.previewed();
            });
            $("#blockquote").click(function() {
                addMark(markEditor, "blockquote");
            });
            $("#blockquote2").click(function() {
                addMark(markEditor, "blockquote2");
            });
            $("#h1").click(function() {
                addMark(markEditor, "H1");
            });
            $("#h2").click(function() {
                addMark(markEditor, "H2");
            });
            $("#h3").click(function() {
                addMark(markEditor, "H3");
            });
            $("#guide").click(function() {
                addMark(markEditor, "guide");
            });
        });
        function addMark(markEditor, key) {
            var mark = "";
            /** f8:\n# f9:\## f10:\###  f11:> f12:>>
             h1:章（一级标题）-F8
             h2节（二级标题）-F9
             h3 条（三级标题）-F10
             blockquote 正文（缩进）-F11
             blockquote2 正文（2倍缩进）-F12*/
            switch (key) {
            case "H1":
                mark = "\r\#";
                break;
            case "H2":
                mark = "\r\##";
                break;
            case "H3":
                mark = "\r\###";
                break;
            case "blockquote":
                mark = "\r\>";
                break;
            case "blockquote2":
                mark = "\r\>>";
                break;
            case "center":
                mark="<p align=center>";
                break;
            case "right":
                mark="<p align=right>"
                break;
            default:
                mark = "@@@";
            }
            var cursor = markEditor.getCursor();
            var selection = markEditor.getSelection();
            
            if (key == "guide") {
                if (cursor.ch !== 0) {
                    markEditor.setCursor(cursor.line, 0);
                    markEditor.replaceSelection(mark +"\n"+ selection+"\n"+mark);
                    markEditor.setCursor(cursor.line + 2, cursor.ch + 2);
                } else {
                    markEditor.replaceSelection(mark+"\n" + selection+"\n"+mark);
                }
            } else if(key=="right"){
                if (cursor.ch !== 0) {
                    //markEditor.setCursor(cursor.line, 0);
                    //alert(key)
                    markEditor.replaceSelection(mark + selection+"</p>");
                    markEditor.setCursor(cursor.line + 2, cursor.ch + 2);
                } else {
                    markEditor.replaceSelection(mark+ selection+"</p>");
                }
            }else if(key=="center"){
                
                if (cursor.ch !== 0) {
                    //markEditor.setCursor(cursor.line, 0);
                    markEditor.replaceSelection(mark + selection+"</p>");
                    markEditor.setCursor(cursor.line + 2, cursor.ch + 2);
                } else {
                    markEditor.replaceSelection(mark + selection+"</p>");
                }
            }else{              
                if (cursor.ch !== 0) {
                    //markEditor.setCursor(cursor.line, 0);
                    markEditor.replaceSelection(mark + selection);
                    markEditor.setCursor(cursor.line + 2, cursor.ch + 2);
                } else {
                    markEditor.replaceSelection(mark + selection);
                }
                markEditor.setCursor({
                    line : cursor.line + 2,
                    ch : 0
                });
                markEditor.focus();
            }
        }
        //保存回调函数
        function callBack() {
            //需求4654 2018/7/3 by liuh Start
            //保存后设置status的值
            if($("input[name='optionsRadios']:checked").val() == '2') {
                status = '3';
            //bug12736 2018/7/5 by liuh Start 保存后,根据标注状态设置status的值
            } else {
                status = '';
            }
            //bug12736 2018/7/5 by liuh end
            //需求4654 2018/7/3 by liuh end
            $("#SavedDataPreview").attr('disabled', false);
            $("#SavedDataPreview").attr("class", "btn btn-sm btn-primary");
            $("#lawAllView").attr('disabled', false);
            $("#lawAllView").attr("class", "btn btn-sm btn-primary");
            popMsg("保存成功");
        }
        //从附件中提取原文文本
        function importOrgText(id, lawsId, attName) {
            var param = id + "SPLIT" + lawsId
            if (attName != '' && attName !='null') {
                param = param + "SPLIT" + attName.substring(attName.lastIndexOf(".") + 1, attName.length);
            } else {
                param = param + "SPLIT ";
            }
            $("#id").val(param);
            submitForm("/lawsManage/importOrgText");
        }
        //显示附件列表
        function fileListToggle() {
            $("div[name='attachmentList_div']").toggle(500);
        }
    </script>
</html>
