$(document).ready(function(){
	//日期控件初始化
	dateRangeDataInit();
	
	tSelectDataInit();
	
	// 清除条件
	$('#clearAllOptions').click(function() {
		popConfirm('您确认要清空吗？', clearAllOptions, $(this));
	});
	
	  //查询
    $("#btnQuery").bind("click",function(){
    	ajaxTableQuery("table_id","/newsManagement/listSearch", $("#newsManageDtoForm").serialize());
    });
	
	// 新增  width: 600px;  height: 500px;
	$("#btnAdd").bind("click", function() {
		var para = "id=" + "";
		popWin("专题新闻数据-新增", "/newsManagement/editNews", para, "600px", "500px", myWinCallback, "");
	});
	
})
// 回刷列表页
function myWinCallback(paraWin, paraCallBack) {
	ajaxTableQuery("table_id", "/newsManagement/listSearch", $("#newsManageDtoForm").formSerialize());
}

//清空所有条件
function clearAllOptions() {
	$("input[name='keyWord']").val('');
	$("input[name='source']").val('');
	$("#status").val('');
	$("input[name='newsType']").val('');
	tSelectDataInit();
	var dateRange = $('.daterange input');
	dateRangeDataInit(dateRange, null, null, function() {
		treeClickTag = 'submit';
		search(true);
	}, function() {
		treeClickTag = 'submit';
		search(true);
	});
	search();
}

//日期控件初始化
function dateRangeDataInit() {
	var options = {
		opens : 'left'
	}
	dataRangePickerInit($('#pushTime'), null, null, function() {
		treeClickTag = "submit";
		search(true);
	}, function() {
		treeClickTag = "submit";
		search(true);
	}, options);
}
//业务类型和板块初始化
function tSelectDataInit() {
	var tSelectOptions2 = {
			customCallBack : tSelectCustomCallBack,
			submitCallBack : tSelectSubmitCallBack,
			id : 'id',
			pid : 'parentId',
			name : 'codeName',
			value : 'codeValue',
			grade : 1,
			resultType : 'children',
			style : {}
		};
	$("#newsType").tselectInit(null, tSelectOptions2);
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
	// 提交检索
	$('input[name="' + t.attr('id') + '"]').val(d.value);
	treeClickTag = 'submit';
	search(true);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
	//
}

//时间控件点击查询
function search(){
	 ajaxTableQuery("table_id","/newsManagement/listSearch", $("#newsManageDtoForm").serialize());
}

function renderColumnStatus(data, type, row, meta){
	if(data.status == "3" || data.status == "2"){
		return "已发布";
	}else{
		return "未发布";
	} 	
	 	
}

//操作列
function renderColumnOperation(data, type, row, meta){  
	 var str1 = '<a href="#" onClick="importLaws(\''+data.id+'\')" style="margin-right:4px;" title="编辑"><i class="icon icon-edit"></i></a>&nbsp;';
	 var str3 = '<a href="#" onclick="CreateLawXML(\''+data.id+'\')" style="cursor:pointer"><i class="icon icon-list-ol" title="标注"></i></a>&nbsp;'
	 //var str2 =	'<sec:authorize access="hasAuthority(\'RES_NEW_MANAGEMENT_INIT_AUTHORITY_3\')" ><a href="#" onClick="delCase(\''+data.id+'\')" style="margin-right:4px;" title="删除"><i class="icon icon-trash"></i></a><c:authorize>';
	 var str2 = '<a href="#" onClick="delCase(\''+data.id+'\')" style="margin-right:4px;" title="删除"><i class="icon icon-trash"></i></a>&nbsp;';
	 return str1+str3+str2 ;
}

//删除
function delCase(id){
    popConfirm("确认删除",function(){
        var param = {id:id};
        ajaxData("/newsManagement/deleteNews",param,function(data) {
            ajaxTableReload("table_id",false);
        });
    })
}

//编辑
function importLaws(id){
	var para = "id=" + id;
	popWin("专题新闻数据-编辑", "/newsManagement/editNews", para, "600px", "500px", myWinCallback, "");
}

//标注
function CreateLawXML(id){
	var para = "id=" + id;
	popWin("专题新闻数据-标注", "/newsManagement/newsMarkdown", para, "100%", "100%", myWinCallback, "");
}

