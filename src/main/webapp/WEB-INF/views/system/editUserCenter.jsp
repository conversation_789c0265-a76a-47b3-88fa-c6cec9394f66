<%--
/***************************************************************
* 程序名 : addUser.jsp
* 日期  :  2015-7-17
* 作者  :  zouxl
* 模块  :  用户管理新增页面
* 描述  :  用户管理新增页面
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js/>
</head>
<body>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 个人中心
        </div>
        <div class="panel-body">
            <form:form class="form-horizontal" action="/userManager/editUserCenter" modelAttribute="editUserDto" id="userForm">
            <form:hidden path="id" />
                <div class="form-group">
                    <label class="col-md-1 control-label">用户姓名</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="realName" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">登陆账号</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="userName" disabled="true"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">移动电话</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="telephone" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">固定电话</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="phone" />
                    </div>
                </div> 
                <div class="form-group">
                    <label class="col-md-1 control-label">电子邮箱</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="mail" />
                    </div>
                </div> 
                <div class="form-group">
                    <label class="col-md-1 control-label">传真</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="fax" />
                    </div>
                </div> 
                <div class="form-group">
                    <label class="col-md-1 control-label">年龄</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="age" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">性别</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="sex" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">岗位</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="jobs" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">QQ</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="qq" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">微信</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="wechat" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">所属公司</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="companyCode" />
                    </div>
                </div>                                                                                                  
                <div class="form-group">
                    <div class="col-md-offset-1 col-md-2" align="center">
                    <input type="button" id="updatePassword" class="btn btn-default"  onclick="updatePersonPassword()" value="修改密码" />
                    &nbsp;&nbsp;
                    <sec:authorize access="hasAuthority('RES_TRADERULES_INIT')" >
                           <sec:authorize access="hasAuthority('RES_TRADERULES_INIT_AUTHORITY_3')" >
                                    <input type="button" id="btnConfirm" class="btn btn-primary" value="保存" />
                        </sec:authorize>
                    </sec:authorize>
                    </div>
                </div>
            </form:form>
        </div>
    </div>
  
</body>
</html>
