<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
    </style>
    <script>
        var personTypeList = '${personTypeList}';
        var personPostList = '${personPostList}';
    </script>
</head>
<body>
<div class="panel"  style="padding: 15px 15px 40px">
    <form:form modelAttribute="smsDto" id="sendSmsForm">
        <div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">手机号用，隔开：</label>
                    <div class="col-md-3">
                        <form:textarea path="phones" placeholder="请输入手机号用，隔开" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div  class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">申请原因：</label>
                    <div class="col-md-11">
                        <form:textarea path="content" placeholder="请输入短信内容" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div  class="row">
                <div class="col-md-12">
                    <sec:authorize access="hasAuthority('RES_SEND_SMS_AUTHORITY_3')" >
                        <%--发送短信权限--%>
                        <span id="btnSend" class="btn btn-primary">发送短信</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_SEND_SMS_BY_USER_AUTHORITY_3')" >
                        <%--发送短信权限  --%>
                        <span id="btnSendByUser" class="btn btn-primary">选择用户发送短信</span>
                    </sec:authorize>
                </div>
            </div>
        </div>
    </form:form>

</div>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="smsListVO" id="queryForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="realName" placeholder="请输入姓名" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">手机号</label>
                    <div class="col-md-3">
                        <form:input path="tel" placeholder="请输入手机号" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">发送时间段</label>
                    <div class="col-md-3 daterange">
                        <form:input path="sendTime" placeholder="请选择发送时间段" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">短信内容</label>
                    <div class="col-md-3">
                        <form:input path="message" placeholder="请输入短信内容" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-12 text-right"  style="padding-top: 5px">
                        <sec:authorize access="hasAuthority('RES_SEND_SMS_LIST_AUTHORITY_3')" >
                            <%--查询发送短信列表权限--%>
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空条件</span>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/sendSms/querySmsList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="姓名" renderColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="手机号" renderColumn="tel" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="短信内容" renderColumn="message" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="发送时间" renderColumn="sendTime" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
