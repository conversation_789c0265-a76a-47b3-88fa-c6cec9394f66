//@ sourceURL=eidtPermissionResource.js
// 初始化
var plateSelectIds = new Array();
var testSelectIds = new Array();
var friendSelectIds = new Array();
var readySelectIds = new Array();
$(document).ready(
	function() {
		;
		// 关闭
		$("#btnClose").bind("click", function() {
			closeWin();
		});
		//保存
		$("#btnConfirm").bind("click",function(){
			saveInfo();
		});

		$("#plateCheck").bind("click", function(){
            checkBoxChange($("#plateCheck"),"plate");
        });

        $("#testCheck").bind("click", function(){
            checkBoxChange($("#testCheck"),"test");
        });

        $("#friendLinkCheck").bind("click", function(){
            checkBoxChange($("#friendLinkCheck"),"friend");
        });

        $("#readyCheck").bind("click", function(){
            checkBoxChange($("#readyCheck"),"ready");
        });



        var plateObj = new Array();
        plateObj = document.getElementsByClassName("plate_environment");
        var plateCheckedCount = 0;
        for(var i = 0;i<plateObj.length;i++){
            if(plateObj[i].checked){
                plateSelectIds.push(plateObj[i].value);
                plateCheckedCount = plateCheckedCount + 1;
            }
        }
        if(plateObj.length == plateCheckedCount){
            $('#plateCheck').prop( "checked", true );
        }
        var testObj = new Array();
        testObj = document.getElementsByClassName("test_environment");
        var testCheckedCount = 0;
        for(var i = 0;i<testObj.length;i++){
            if(testObj[i].checked){
                testSelectIds.push(testObj[i].value);
                testCheckedCount = testCheckedCount + 1;
            }
        }
        if(testObj.length == testCheckedCount){
            $('#testCheck').prop( "checked", true );
        }
        var friendObj = new Array();
        friendObj = document.getElementsByClassName("friend_environment");
        var friendCheckedCount = 0;
        for(var i = 0;i<friendObj.length;i++){
            if(friendObj[i].checked){
                friendSelectIds.push(friendObj[i].value);
                friendCheckedCount = friendCheckedCount + 1;
            }
        }
        if(friendObj.length == friendCheckedCount){
            $('#friendLinkCheck').prop( "checked", true );
        }
        var readyObj = new Array();
        readyObj = document.getElementsByClassName("ready_environment");
        var readyCheckedCount = 0;
        for(var i = 0;i<readyObj.length;i++){
            if(readyObj[i].checked){
                readySelectIds.push(readyObj[i].value);
                readyCheckedCount = readyCheckedCount + 1;
            }
        }
        if(readyObj.length == readyCheckedCount){
            $('#readyCheck').prop( "checked", true );
        }
        //全选监听事件
        //预发布
        $(".ready_environment").change(function(){
            var readyObj = document.getElementsByClassName("ready_environment");
            var readyCheckedCount = 0;
            for(var i = 0;i<readyObj.length;i++){
                if(readyObj[i].checked){
                    readyCheckedCount = readyCheckedCount + 1;
                }
            }
            var count = $(".ready_environment").length;
            if(readyCheckedCount == count){
                $('#readyCheck').prop( "checked", true );
            }else{
                $('#readyCheck').prop( "checked", false );
            }
        });

        //生产
        $(".plate_environment").change(function(){
            var plateObj = document.getElementsByClassName("plate_environment");
            var plateCheckedCount = 0;
            for(var i = 0;i<plateObj.length;i++){
                if(plateObj[i].checked){
                    plateCheckedCount = plateCheckedCount + 1;
                }
            }
            var count = $(".plate_environment").length;
            if(plateCheckedCount == count){
                $('#plateCheck').prop( "checked", true );
            }else{
                $('#plateCheck').prop( "checked", false );
            }
        });

        $(".test_environment").change(function(){
            var testObj = document.getElementsByClassName("test_environment");
            var testCheckedCount = 0;
            for(var i = 0;i<testObj.length;i++){
                if(testObj[i].checked){
                    testCheckedCount = testCheckedCount + 1;
                }
            }
            var count = $(".test_environment").length;
            if(testCheckedCount == count){
                $('#testCheck').prop( "checked", true );
            }else{
                $('#testCheck').prop( "checked", false );
            }
        });

        $(".friend_environment").change(function(){
            var friendObj = document.getElementsByClassName("friend_environment");
            var friendCheckedCount = 0;
            for(var i = 0;i<friendObj.length;i++){
                if(friendObj[i].checked){
                    friendCheckedCount = friendCheckedCount + 1;
                }
            }
            var count = $(".friend_environment").length;
            if(friendCheckedCount == count){
                $('#friendLinkCheck').prop( "checked", true );
            }else{
                $('#friendLinkCheck').prop( "checked", false );
            }
        });
});

function saveInfo(){
    plateSelectIds = new Array();
    testSelectIds = new Array();
    friendSelectIds = new Array();
    readySelectIds = new Array();

    var plateObj = new Array();
    plateObj = document.getElementsByClassName("plate_environment");
    for(var i = 0;i<plateObj.length;i++){
        if(plateObj[i].checked){
            plateSelectIds.push(plateObj[i].value);
        }
    }
    var testObj = new Array();
    testObj = document.getElementsByClassName("test_environment");
    for(var i = 0;i<testObj.length;i++){
        if(testObj[i].checked){
            testSelectIds.push(testObj[i].value);
        }
    }
    var friendObj = new Array();
    friendObj = document.getElementsByClassName("friend_environment");
    for(var i = 0;i<friendObj.length;i++){
        if(friendObj[i].checked){
            friendSelectIds.push(friendObj[i].value);
        }
    }
    var readyObj = new Array();
    readyObj = document.getElementsByClassName("ready_environment");
    for(var i = 0;i<readyObj.length;i++){
        if(readyObj[i].checked){
            readySelectIds.push(readyObj[i].value);
        }
    }
    var param = {
        permissionRoleId : $("#permissionRoleId").val(),
        plateSelectIdsStr : JSON.stringify(plateSelectIds),
        testSelectIdsStr : JSON.stringify(testSelectIds),
        friendSelectIdsStr : JSON.stringify(friendSelectIds),
        readySelectIdsStr : JSON.stringify(readySelectIds)
    }

    ajaxData("/userManager/savePermissionResource", param, function() {
        closeWinCallBack();
    });
}
function callBack(){
	closeWinCallBack();
}

//全选方法
function checkBoxChange(checkbox,type) {
    ;
    var obj = new Array();
    if("plate" == type){
        obj = document.getElementsByClassName("plate_environment");
    }else if("test" == type){
        obj = document.getElementsByClassName("test_environment");
    }else if("friend" == type){
        obj = document.getElementsByClassName("friend_environment");
    }else if("ready" == type){
        obj = document.getElementsByClassName("ready_environment");
    }
    if (checkbox[0].checked == true) {
        //全选
        for(var i = 0;i<obj.length;i++){
            obj[i].checked = true;
        }
    } else {
        //全不选
        for(var i = 0;i<obj.length;i++){
            obj[i].checked = false;
        }
    }
}
