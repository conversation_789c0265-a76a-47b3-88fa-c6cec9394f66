<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<style>
    .no-padding-hr {
        padding-left: 0!important;
        padding-right: 0!important;
    }
    .form-horizontal .control-label {
        padding-top: 7px;
        margin-bottom: 5px;
        font-size: 14px;
        color: #1c2534;
        text-align: right;
        font-family: SimSun;
    }
    .red_star {
        color: red;
        font-size: 9px;
    }
.layer-ext-moon .layui-layer-setwin .layui-layer-close2 {
    background: url(default.png) 0 0;
    display: inline-block;
    width: 22px;
    height: 22px;
    background-image: url("../../../../images/home.png");
    background-position: -162px -64px;
    }
body .layer-ext-moon .layui-layer-title {
    background: #a1aab5;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    height: 46px;
    line-height: 46px;
    font-family: 'SimSun';
    }
</style>
<e:base />
<e:js />
</head>
<body>
    <div class="panel-body" style='background:#fff;'>
        <form:form action="" modelAttribute="userManagerDto" id="userForm" class="form-horizontal">
            <div class="form-group" style="margin-top: 15px;">
                <label class="col-xs-2 control-label no-padding-hr"><span class="red_star">*</span>旧密码</label>
                <div class="col-xs-10">
                    <form:input type="password" cssClass="form-control required" id="oldPassword" path="oldPassword" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-xs-2 control-label no-padding-hr"><span class="red_star">*</span>新密码</label>
                <div class="col-xs-10">
                    <form:input type="password" cssClass="form-control required" id="newPassword" path="newPassword" />
                </div>
                <div style="display: none">
                    <form:input cssClass="form-control" id="username" path="userName" />
                    <form:input cssClass="form-control" id="id" path="id" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-xs-2 control-label no-padding-hr"><span class="red_star">*</span>确认密码</label>
                <div class="col-xs-10">
                    <form:input type="password" cssClass="form-control required" id="affirm" path="affirm" />
                </div>
            </div>
           <%-- <input type="hidden" value="${complicatedPasswordClass}" id="complicatedPasswordClass">
            <input type="hidden" value="${complicatedPasswordFlag}" id="complicatedPasswordFlag">  --%>
        </form:form>
    </div>
    <div align="center" style="padding-right: 10px;">
        <input type="button" id="userSave" class="btn btn-primary" value="保存" />&nbsp;&nbsp;&nbsp;&nbsp;<input type="button"
            id="btnClose" class="btn btn-default" value="关闭" />
    </div>
</body>
</html>