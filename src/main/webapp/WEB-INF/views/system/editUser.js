//@ sourceURL=editUser.js
$(document).ready(function() {
	// 设定表单校验规则
	$("#userForm").validate({
		rules : {
			"realName" : {
				required : true,
				maxlength : 64
			},
			// "telephone" : {
			// 	required : true,
			// 	digits : true,
			// 	// 后台校验
			// 	remote : {
			// 		url : contextPath + "/userManager/remoteValidateTelephone",
			// 		type : "post",
			// 		data : {
			// 			userId : function() {
			// 				return $("#id").val();
			// 			}
			// 		}
			// 	},
			// 	maxlength : 30
			// },
			"phone" : {
				maxlength : 64
			},
			// "mail" : {
			// 	required : true,
			// 	email : true,
			// 	maxlength : 128
			// },
		},
		messages : {
			// "telephone" : {
			// 	remote : "系统中已存在此移动电话！"
			// }
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	// 确定
	$("#btnConfirm").bind("click", function() {
		if ($("#userForm").valid()) {
			ajaxSubmitForm("/userManager/editSave", null, callBack, 0);
		}
	});
	// 关闭
	$("#btnClose").bind("click", function() {
		closeWin();
	});
	// 初始化部门下拉框
	tSelectDataInit();
    var reserveDepPermission = $("#reserveDepPermission").val();
    if("1" == reserveDepPermission){
        $("#permissionCheck").attr("checked",true);
	}
    $("#dingFlag"+ $('#dingFlag').val()).prop('checked','true');
    
    //上传图片 & 是否显示在首页
	if ($("#ImgPr").attr("src") != "") {
		$("#imgId").css("display", "block");
		$("#indexhomeShowFlag").css("display", "block");
	}
	var isIndexhomeShow = $("#isIndexhomeShow").val();
    if("1" == isIndexhomeShow){
        $("#indexhomeShowCheck").attr("checked",true);
	}
	
	var url = contextPath + '/filetempupload';
	$('#up').fileupload({
		url : url,
		dataType : 'json',
		autoUpload : true,
		submit : function(e, data) {
			index = layer.load(1, {
				shade : [ 0.1, '#fff' ]
			// 0.1透明度的白色背景
			});
		},
		done : function(e, data) {
			$.each(data.result, function(index, file) {
				$("#imgField").val(file.fileId);
			});
			layer.close(index);
		}
	});
	
	// $("#up").uploadPreview({
	// 	Img : "ImgPr",
	// 	Width : 120,
	// 	Height : 120
	// });
});
//保存成功
function callBack(data) {
	var jsonObj = {
			result : [ {
				name : "",
				testDate : ""
			}]
		};
	//closeWin("保存成功");
	closeWinCallBack(jsonObj);
	//popMsg("保存成功");
}

//下拉框初始化获取数据
function tSelectDataInit() {
	var departmentSelect = $('input[name="orgId"]').val();
	var tSelectOptions = {
			customCallBack : tSelectCustomCallBack1,
			submitCallBack : tSelectSubmitCallBack1,
			cancelCallBack : tSelectClearCallBack1,
			id : 'orgNo',
			pid : 'pOrgNo',
			name : 'orgName',
			value : 'id',
			grade : 3,
			resultType : 'children',
			inputType: 'radio', // 单选
			selectedIds : departmentSelect,
			//调整单级树下拉框高度(400->280)，参考数据源得出
			style : {tMaxHeight : 280}
		};
	
	$('#departmentSelectShow').tselectInit(null, tSelectOptions);

}

// 下拉框点击确定回调方法
function tSelectSubmitCallBack1(t, d) {
	$('input[name="orgId"]').val(d.value);
}

// 下拉框点击清空回调方法
function tSelectClearCallBack1(t) {
	$('input[name="orgId"]').val('');
}
// 下拉框自定义方法
function tSelectCustomCallBack1(t) {
	$('.t-select-table').slideUp('fast');
}

// 下拉框点击确定回调方法
function departmentSelectSubmitCallBack(t, d) {
    $('input[name="loginAuthority"]').val(d.value);
}

// 下拉框点击清空回调方法
function departmentSelectClearCallBack(t) {
    $('input[name="loginAuthority"]').val('');
}
// 下拉框自定义方法
function departmentSelectCustomCallBack(t) {
    $('.t-select-table').slideUp('fast');
}