<%--
/***************************************************************
* 程序名 : parameterEdit.jsp
* 日期  :  2015-8-19
* 作者  :  wangyuan
* 模块  :  系统管理
* 描述  :  系统参数设置(编辑)
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body style="padding-top: 0px;">
	<div class="panel">
		<div class="panel-body">
			<form:form action="/parameter/parameterEditSave"
				modelAttribute="parameterDto" id="parameterForm">
				<div class="form-group">
					<label class="col-md-1 control-label">参数标识</label>
					<div class="col-md-2">
						<form:input cssClass="form-control" path="paramCode"
							disabled="true" />
						<form:hidden path="paramCode" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-md-1 control-label">参数名称</label>
					<div class="col-md-2">
						<form:input cssClass="form-control" path="paramDesc" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-md-offset-11 text-right">
                    <sec:authorize access="hasAuthority('RES_PARAMETER_INIT_AUTHORITY_3')" > 
						<button type="button" class="btn btn-primary" id="btnAdd">新增</button>
                        </sec:authorize>
					</div>
				</div>
				<table id="items" class="table table-hover table-striped">
					<tr>
						<th width="10%" align="center">子参数标识</th>
						<th width="10%" align="center">子参数名称</th>
						<th width="10%" align="center">子参数值</th>
						<th width="5%" align="center">操作</th>
					</tr>
					<c:forEach var="item" items="${parameterDto.items}"
						varStatus="status">
						<tr>
							<td align="center"><form:hidden
									path="items[${status.index}].id" /> <form:hidden
									path="items[${status.index}].paramValue" /> <form:input
									path="items[${status.index}].paramValue"
									cssClass="form-control" disabled="true" /></td>
							<td align="center"><form:input
									path="items[${status.index}].paramName" cssClass="form-control" /></td>
							<td align="center"><form:input
									path="items[${status.index}].paramContent"
									cssClass="form-control" /></td>
							<td align="center"></td>
						</tr>
					</c:forEach>
				</table>
			</form:form>
			<div class="form-group">
				<div class="col-md-offset-10 text-right">
                <sec:authorize access="hasAuthority('RES_PARAMETER_INIT_AUTHORITY_3')" > 
					<button type="button" class="btn btn-primary" id="btnSave">保存</button>
                    </sec:authorize>
					<button type="button" class="btn btn-default" id="btnBack">返回</button>
				</div>
			</div>
		</div>
	</div>

	<form:form action="" modelAttribute="emptyParameterDto">
		<table style="display: none;" id="template">
			<tr>
				<td align="center"><form:hidden path="items[0].id" /> <form:input
						path="items[0].paramValue" cssClass="form-control" /></td>
				<td align="center"><form:input path="items[0].paramName"
						cssClass="form-control" /></td>
				<td align="center"><form:input path="items[0].paramContent"
						cssClass="form-control" /></td>
				<td align="center"><i class="icon icon-trash"
					onclick="removeRow(this)"></i></td>
			</tr>
		</table>
	</form:form>
</body>
</html>