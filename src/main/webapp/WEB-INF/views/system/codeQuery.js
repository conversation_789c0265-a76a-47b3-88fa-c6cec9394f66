//@ sourceURL=codeQuery.js
$(document).ready(function() {
	// 查询
	$("#btnQuery").bind("click",function() {
		ajaxTableQuery("table_id", "/code/ajaxQuery", $("#codeForm").formSerialize());
	});
	
	// 新增系统参数
	if(document.getElementById("btnAdd")){
		$("#btnAdd").bind("click",function() {
			parent.popWin("新增业务代码", "/code/codeAddInit", "", "1000px", "500px", myWinCallback, "");
		});
	}
	
});

// 渲染操作列 data:后台传过来的整行数据
// 编辑按钮操作
function renderColumnOperation(data, type, row, meta) {
	return '<a onclick="codeEdit(\''+ data.codeNo + '\',\'' + data.codeDesc +  '\')"><i class="icon icon-edit"></i></a>';
}

// 编辑
function codeEdit(codeNo, codeDesc) {
	//2017.10.16 by zhanghaoyu start
//	var param = "codeNo=" + codeNo + "&codeDesc=" + codeDesc;
	var param = {
			codeNo : codeNo ,
			codeDesc : codeDesc
	}
	//2017.10.16 by zhanghaoyu end
	parent.popWin("编辑业务代码", "/code/codeEditInit", param, "1000px", "500px", myWinCallback, "");
}

function myWinCallback() {
	popMsg("保存成功");
	//2016/08/29 bug2015 baiyang start
	ajaxTableReload("table_id", false);
//	ajaxTableQuery("table_id", "/code/ajaxQuery", $("#codeForm").formSerialize());
	//2016/08/29 bug2015 baiyang end
}