<%--
/***************************************************************
* 程序名 : userManagerQuery.jsp
* 日期  :  2015-7-11
* 作者  :  zouxl
* 模块  :  用户管理
* 描述  :  分页查询页面
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
<e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-list-ul"></i> 未分配角色列表
    </div>
    <div class="panel-body">
        <e:table action="/userManager/roleChoose" col="9" cssClass="table table-hover table-striped">
            <tr>
                <th width="10%" align="center">序号</th>
                <th width="20%" align="center">角色名称</th>
                <th width="60%" align="center">角色描述</th>              
                <th width="10%" align="center">操作</th>
            </tr>
            <c:forEach var="item" items="${queryRoleList}" varStatus="status">
            <tr>
                <td align="center"><c:out value="${status.count}" /></td>
                <td align="left"><c:out value="${item.roleName}" /></td>
                <td align="left"><c:out value="${item.roleDes}" /></td>
                <td align="center">
                <span class="widget-icon">
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3')" >
                <a href="javascript:addRole('/userManager/addRole?id=<c:out value="${item.id}" />')"><i class="icon icon-plus"></i></a>
                </sec:authorize>
                </span>
                </td>
            </tr>
            </c:forEach>
        </e:table>
    </div>
    
    <div class="panel-heading">
        <i class="icon icon-list-ul"></i> 已分配角色列表
    </div>
    <div class="panel-body">
        <e:table action="/userManager/roleChoose" col="9" cssClass="table table-hover table-striped">
            <tr>
                <th width="10%" align="center">序号</th>
                <th width="20%" align="center">角色名称</th>
                <th width="60%" align="center">角色描述</th>              
                <th width="10%" align="center">操作</th>
            </tr>
            <c:forEach var="item" items="${queryUserRoleList}" varStatus="status">
            <tr>
                <td align="center"><c:out value="${status.count}" /></td>
                <td align="left"><c:out value="${item.roleName}" /></td>
                <td align="left"><c:out value="${item.roleDes}" /></td>
                <td align="center">
                <span class="widget-icon">
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3')" >
                <a href="javascript:delRole('/userManager/delRole?id=<c:out value="${item.id}" />')"><i class="icon icon-minus"></i></a>
                </sec:authorize>
                </span>
                </td>
            </tr>
            </c:forEach>
        </e:table>
    </div>
    <div id="userId" style="display: none;">${userId}</div>
</div>  
    
</body>
</html>
