<%--
/***************************************************************
* 程序名 : orgslist.jsp
* 日期  :  2015-7-7
* 作者  :  朱继刚
* 模块  :  系统管理
* 描述  :  组织机构
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>易董 云端管理平台</title>
	<e:js/>
</head>
<body>
<div style="width: 100%;display: inline-block;padding: 20px">
		<div style="float: left;width:260px;BORDER-RIGHT: #999999 1px dashed">
			<ul id="tree" class="ztree" style="width: 260px; overflow: auto;"></ul>
		</div>
		<div style="width: 770px;float: left;padding-left: 20px">
			<div>
				<label class="label">当前选中的组织：</label><span id="hitLabel" style="margin-left: 5px"></span>
			</div>
			<div style="margin-top: 5px">
				<label class="label">组织代码: </label><span id="orgNoLabel" style="margin-left: 5px"></span>
			</div>
			<form id="orgsForm" method="POST" class="smart-form" style="margin-top: 5px" onsubmit="return false;">
				<input type="hidden" id="pOrgNo" name="pOrgNo" />
				<input type="hidden" id="" name="" />
				<input type="hidden" id="lId" name="id" />
				<input type="hidden" id="orgNo"  name="orgNo" />
				<label class="label">组织名称</label>
				<label class="input">
					<input type="text" id="orgName" size="30" maxlength="30" name="orgName" onchange="checkOrgName" />
				</label>
				<br/>
			</form>
			<button class="btn btn-primary " onclick="update()">更新</button>
			<button class="btn btn-primary " onclick="del()">删除</button>
			<button class="btn btn-primary " onclick="addchild()">建立下级组织</button>
			<button class="btn btn-primary " onclick="addbrother()" >建立同级组织</button>
		</div>
</div>


<div class="modal fade" id="myModal" tabindex="-1" role="dialog"
	 aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
				<h4 class="modal-title" id="myModalLabel">添加新组织</h4>
			</div>
			<div class="modal-body">
				<form class="smart-form" id="smartform" method="post"
					  onsubmit="return false;">
					<input type="hidden" id="pOrgId" name="pOrgNo" />
					<input type="hidden" id="orderId" name="orderId" />
					<label class="label">组织代码</label>
					<input type="text" id="newOrgNo" size="30" name="orgNo" onchange="checkOrgId(this)"/>
					<br/>
					<label class="label"> 组织名称 </label>
					<input type="text" id="orgNewName" size="30" name="orgName" onchange="checkOrgName(this)"/>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				<button type="button" class="btn btn-primary" onclick="save()">保存</button>
			</div>
		</div>
	</div>
</div>
</body>
</html>