//@ sourceURL=loginAuthority.js
$(document).ready(function() {
			// 查询
			$("#btnQuery").bind("click", function() {
				submitForm("/userManager/roleQuery");
			});
			// 根据URL弹出窗口
			if(document.getElementById("btnPopWin")){
				$("#btnPopWin").bind(
						"click",
						function() {
							parent.popWin("新增门户登录角色", "/userManager/addRolepop", "", "500px",
									"400px", myWinCallback, "保存成功");
						});
			}
			

		});
// 弹出窗口的回调函数 paraWin:弹出窗口传来的值 paraCallBack:调用窗口时自己传的回调参数值
function myWinCallback(paraWin, paraCallBack) {
	//alert(1);
	//alert(paraWin.result[0].name);
	//alert(paraCallBack);
	popMsg(paraCallBack);
	//2016/08/29 bug2015 baiyang start
	submitForm("/userManager/roleQuery?toPageState=back");
	//2016/08/29 bug2015 baiyang end
}
function delConfirm(url) {
	popConfirm("请确认是否删除此角色？", myConfirmCallBack, url);
}

function myConfirmCallBack(url) {
	//2016/08/29 bug2015 baiyang start
	movePage(url);
	//2016/08/29 bug2015 baiyang end
}
function editPop(url, data) {
	parent.popWin("编辑角色", url, data, "500px", "430px", editPopCallBack, null);
}
function editPopCallBack(){
	popMsg("保存成功");
}
function roleChoose(url, data) {
	parent.popWin("权限配置", url, data, "800px", "90%", roleChooseCallback,null);
}
function roleChooseCallback(){
	popMsg("保存成功");
}
function roleView(url, data) {
	parent.popWin("<span style='font-weight:100;'>角色查看</span>", url, data, "90%", "90%", roleViewCallback,null);
}
function roleViewCallback(){
		popMsg("保存成功");
}