<%--
/***************************************************************
* 程序名 : codeAdd.jsp
* 日期  :  2015-8-22
* 作者  :  wangyuan
* 模块  :  系统管理
* 描述  :  业务代码设置(编辑)
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body style="padding-top: 0px;">
	<div class="panel">
		<div class="panel-body">
			<form:form action="/code/codeEditSave" modelAttribute="codeDto"
				id="codeForm">
				<div class="form-group">
					<label class="col-md-1 control-label">代码标识</label>
					<div class="col-md-2">
						<form:input cssClass="form-control" path="codeNo" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-md-1 control-label">代码名称</label>
					<div class="col-md-2">
						<form:input cssClass="form-control" path="codeDesc" />
					</div>
				</div>
				<div class="form-group">
					<div class="col-md-offset-11 text-right">
						<button type="button" class="btn btn-primary" id="btnAdd">新增</button>
					</div>
				</div>
				<table id="items" class="table table-hover table-striped">
					<tr>
						<th width="10%" align="center">子代码值</th>
						<th width="10%" align="center">子代码名称</th>
						<th width="10%" align="center">显示序号</th>
						<th width="10%" align="center">有效</th>
						<th width="5%" align="center">操作</th>
					</tr>
					<c:forEach var="item" items="${codeDto.items}" varStatus="status">
						<tr>
							<td align="center"><form:input
									path="items[${status.index}].codeValue" cssClass="form-control" /></td>
							<td align="center"><form:input
									path="items[${status.index}].codeName" cssClass="form-control" /></td>
							<td align="center"><form:input
									path="items[${status.index}].sortNo" cssClass="form-control" /></td>
							<td align="center"><form:checkbox
									path="items[${status.index}].validFlag" value="1" /></td>
							<td align="center"></td>
						</tr>
					</c:forEach>
				</table>
			</form:form>
			<div class="form-group">
				<div class="col-md-offset-10 text-right">
					<button type="button" class="btn btn-primary" id="btnSave">保存</button>
					<button type="button" class="btn btn-default" id="btnBack">返回</button>
				</div>
			</div>
		</div>
	</div>

	<form:form action="" modelAttribute="emptyCodeDto">
		<table style="display: none;" id="template">
			<tr>
				<td align="center"><form:input path="items[0].codeValue"
						cssClass="form-control" /></td>
				<td align="center"><form:input path="items[0].codeName"
						cssClass="form-control" /></td>
				<td align="center"><form:input path="items[0].sortNo"
						cssClass="form-control" /></td>
				<td align="center"><form:checkbox path="items[0].validFlag"
						value="1" /></td>
				<td align="center"><i class="icon icon-trash"
					onclick="removeRow(this)"></i></td>
			</tr>
		</table>
	</form:form>
</body>
</html>
