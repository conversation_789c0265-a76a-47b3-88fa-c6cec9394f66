
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
<%--    <e:js/>--%>
    <script>
        function save() {
            ajaxSubmitForm("/bwgManage/saveChange","",function (data) {
                if(data == '0'){
                    popMsg("修改成功");
                    setTimeout(function (){closeWin();},1000);
                }else{
                    popMsg(data);
                }
            },0);
        }
    </script>
</head>
<body style="padding-top:0px;">
    <form:form action="/bwgManage/saveChange" modelAttribute="dto" id="form">
            <div class="row" style="width: 300px">
                <div class="col-xs-12" style="margin-top: 8px" align="left">请输入修改密码：
                    <form:hidden path="activations" />
                    <form:hidden path="effectiveTimeStart" />
                    <form:hidden path="effectiveTimeEnd" />
                    <form:hidden path="effectiveScope" />
                    <form:hidden path="id" />
                </div>
            </div>
        <br>
            <div class="row" style="width: 300px">
                <div class="col-xs-12" align="center">
                    <form:password path="password" />
                </div>
            </div>
        <br>
            <div class="row" style="width: 300px">
                <div class="col-xs-12" align="center">
                    <input type="button" id="btnConfirm" class="btn btn-primary" onclick="save()" value="保存" />
                </div>
            </div>
    </form:form>

</body>
</html>
