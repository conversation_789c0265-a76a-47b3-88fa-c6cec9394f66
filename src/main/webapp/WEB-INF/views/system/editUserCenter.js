//@ sourceURL=editUserCenter.js
$(document).ready(function() {
	// 设定表单校验规则
	$("#userForm").validate({
		rules : {
			"realName" : {
				required : true,
				maxlength : 64
			},
			"telephone" : {
				required : true,
				digits : true,
				maxlength : 30
			},
			"phone" : {
				required : true,
				digits : true,
				maxlength : 64
			},
			"mail" : {
				required : true,
				email : true,
				maxlength : 128
			},
			"fax" : {
				required : true,
				digits : true,
				maxlength : 30
			},
			"age" : {
				digits : true,
				maxlength : 3
			},
			"jobs" : {
				maxlength : 30
			},
			"qq" : {
				digits : true,
				maxlength : 20
			},
			"wechat" : {
				maxlength : 30
			},
			"companyCode" : {
				maxlength : 30
			}
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	if ($("#btnConfirm").val() != undefined) {
	// 确定
	$("#btnConfirm").bind("click", function() {
		// 每一种直接给父页面值
		// $("#name", parent.document).val("弹出窗口设置的值");
		// 第二种通过父页面的回调函数传弹出窗口的值
		// 拼弹出窗口的值
		if ($("#userForm").valid()) {
			ajaxSubmitForm("/userManager/editSave", null, callBack, 0);
		}
	});
	}
});
//保存成功
function callBack(data) {
	popMsg("保存成功");
}
function updatePersonPassword() {
    parent.popWin("设置账号密码", "/userManager/updatePersonPassword", "",
            "400px", "300px", passWordWinCallback, null);
}
function passWordWinCallback(paraWin, paraCallBack) {
}