//@ sourceURL=orgslist.js
var zTree;
var zNodes;
var setting = {
	async : {
		enable : true,
		url : contextPath + "/orgmgr/getAllOrg",
		autoParam : [ "id" ]
	},
	view : {
		dblClickExpand : true,
		showLine : true,
		selectedMulti : false
	},
	data : {
		simpleData : {
			enable : true,
			idKey : "id",
			pIdKey : "pid",
			rootPId : ""
		}
	},
	callback : {
		onAsyncSuccess : zTreeOnAsyncSuccess,
		onClick : zTreeOnClick,
		beforeClick : beforeClick
	}
};
function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
	zNodes = treeNode;
	if (zTree == null) {
		zTree = $.fn.zTree.getZTreeObj("tree");
	}
	zTree.expandNode(zTree.getNodeByParam("id", $("#orgNo").val(), null), true,
		false, false);
	zTree.selectNode(zTree.getNodes[0], null);
	var node;
	if ($("#newOrgNo").val() != "") {
		node = zTree.getNodeByParam("id", $("#newOrgNo").val(), null);
		zTree.expandNode(node, true, false, false);

		zTree.selectNode(node);

		$("#hitLabel").html(node.name);
		$("#pOrgNo").val(node.pid);
		$("#orgNoLabel").html(node.id);
		$("#orgNo").val(node.id);
		$("#orgName").val(node.name);
		$("#lId").val(node.lId);
		if (node.children != null) {
			$("#orderId").val(node.children.length + 1);
		} else {
			$("#orderId").val("1");
		}
		$("#newOrgNo").val("");
		$("#orgNewName").val("");

	} else if ($("#orgNo").val() != "") {

		if (flag == 2) {
			// 删除，重新加载
			node = zTree.getNodeByParam("id", $("#pOrgNo").val(), null);
			zTree.expandNode(node, true, false, false);
			zTree.selectNode(node, null);

			$("#hitLabel").html(node.name);

			$("#orgNo").val(node.id);
			$("#pOrgNo").val(node.pid);
			$("#lId").val(node.lId);
			$("#orgNoLabel").val(node.id);
			if (node.orgType == 0) {
				$("#orgType").prop("checked", true);
			} else {
				$("#orgType").prop("checked", false);
			}

			if (node.status == 0) {
				$("#status").prop("checked", true);
			} else {
				$("#status").prop("checked", false);
			}

		} else {
			// 修改，重新加载
			if (flag == 3) {
				node = zTree.getNodeByParam("id", $("#orgNo").val(), null);

				zTree.expandNode(node, true, false, false);
				zTree.selectNode(node);

				zTree.expandNode(node, true, false, false);
				zTree.selectNode(node, null);
				$("#hitLabel").html(node.name);
				$("#orgNo").val(node.id);
				$("#pOrgNo").val(node.pid);
				$("#lId").val(node.lId);
				$("#orgNoLabel").val(node.id);
				if (node.orgType == 0) {
					$("#orgType").prop("checked", true);
				} else {
					$("#orgType").prop("checked", false);
				}
				if (node.status == 0) {
					$("#status").prop("checked", true);
				} else {
					$("#status").prop("checked", false);
				}
			}
		}
	}

};
function beforeClick(treeId, treeNode) {

}
function zTreeOnClick(event, treeId, treeNode) {

	$("#hitLabel").html(treeNode.name);
	$("#pOrgNo").val(treeNode.pid);
	$("#orgNoLabel").html(treeNode.id);
	$("#orgNo").val(treeNode.id);
	$("#orgName").val(treeNode.name);
	if (treeNode.orgType == "0") {
		$("#orgType").prop("checked", true);
	} else {
		$("#orgType").prop("checked", false);
	}
	if (treeNode.status == "0") {
		$("#status").prop("checked", true);
	} else {
		$("#status").prop("checked", false);
	}
	$("#lId").val(treeNode.lId);
	if (treeNode.children != null) {
		$("#orderId").val(treeNode.children.length + 1);
	} else {
		$("#orderId").val("1");
	}
};

var zTree;
var t;
var err;
var AjaxURL;
$(document).ready(function() {
	$('#orgsForm').submit(function() {
			$.ajax({
					type : "POST",
					url : AjaxURL,
					dataType : "json",
					async : true,
					data : $('#orgsForm').serialize(),
					success : function(data) {
						if (zTree != null)
							zTree.destroy();
						zTree = $.fn.zTree.getZTreeObj("tree");
						t = $("#tree");
						t = $.fn.zTree.init(t, setting, zNodes);
					},
					error : function(data) {

					}
				}
			);
		}
	);
	$('#smartform').submit(function() {
			$.ajax({
					type : "POST",
					url : AjaxURL,
					dataType : "json",
					async : true,
					data : $('#smartform').serialize(),
					success : function(data) {
						zTree.reAsyncChildNodes(null, "refresh");
						$("#myModal").modal('hide');
					},
					error : function(data) {

					}
				}
			);
		}
	);
	t = $("#tree");
	t = $.fn.zTree.init(t, setting, zNodes);
	zTree = $.fn.zTree.getZTreeObj("tree");

});

var flag = 0;
function addchild() {
	if ($("#pOrgNo").val() == undefined) {
		alert("请选择一个组织机构，然后再添加下级机构。");
		return false;
	}
	flag = 0;
	$("#myModal").modal('show')
	$("#pOrgId").val($("#orgNo").val());
}
// 建立同级组织
function addbrother() {
	flag = 1;
	$("#myModal").modal('show')
	$("#pOrgId").val($("#pOrgNo").val());
}
function save() {

	if (flag == 0) {
		AjaxURL = contextPath + "/orgmgr/add";
	} else {
		AjaxURL = contextPath + "/orgmgr/addb";
	}
	$("#smartform").submit();
}

function update() {
	flag = 3;
	AjaxURL = contextPath + "/orgmgr/modify";
	$('#orgsForm').submit();
}

function del() {
	if (confirm("确认删除该机构吗？")) {
		flag = 2;// 删除
		AjaxURL = contextPath + "/orgmgr/del";
		$('#orgsForm').submit();
	}
}
function checkOrgId() {

	ajaxData("/orgmgr/checkOrgId", $("#smartform").formSerialize(),function(data){
		if(!data){
			popMsg("该代码已经存在");
			$("#newOrgNo").val("");
		}
	});
}
function checkOrgName(obj) {
		AjaxURL = contextPath + "/orgmgr/checkOrgName";
	ajaxData("/orgmgr/checkOrgName", $("#smartform").formSerialize(), function(data){
		if(!data){
			popMsg("该名称已经存在");
			$("#orgNewName").val("");
			$("#orgName").val("");
		}
	});
}