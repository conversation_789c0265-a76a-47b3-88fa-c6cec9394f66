//@ sourceURL=roleSetPop.js
$(document).ready(function() {
	//未分配全选设置
	$("#btnSelectAll").bind("click",function() {
		selectAll("#btnSelectAll",'#allRole');
	});
	//已分配全选设置
	$("#btnSelectHasAll").bind("click",function() {
		selectAll("#btnSelectHasAll",'#hasedRole');
	});
	//上移
	$("#btnUp").bind("click",function() {
		moveUp();
	});
	//下移
	$("#btnDown").bind("click",function() {
		moveDown();
	});
	//保存
	$("#btnSave").bind("click",function() {
		save();
	});
	//取消
	$("#btnCancel").bind("click",function() {
		closeWin();
	});
});
function save(){
	var ids = getTableData();
	//2017.10.10 by zhanghaoyu start
	var url ="/roleManager/addRole";
	var param = {
			id : ids ,
			roleId : $("#userId").html()
	}
	//2017.10.10 by zhanghaoyu end
	ajaxData(url,param,saveCallback);
}
function saveCallback(data){
	closeWinCallBack();
}
function getTableData() {
	var ids = '';
	$('#hasedRole').find("input[type='checkbox']").each(function(index,element) {
		ids+=this.id+",";
	});
	return ids;
}
function moveUp(){
	var checkNum = 0;
	var selectAllValue = $('#btnSelectHasAll').val();
	$('#hasedRole').find("input[name='roleCheck']:checked").each(function(index,element) {
		checkNum++;
		var row = '<tr>'+$(element).parent().parent().html()+'</tr>';
		$(row).appendTo($('#allRole'));
		$(element).parent().parent().remove();
		refreshIndex('#hasedRole');
		refreshIndex('#allRole');
		if(selectAllValue ="1"){
			$('#btnSelectHasAll').val("");
			$('#btnSelectHasAll').text("全选");
		}
	});
	if(checkNum ==0){
		popMsg("请选择要移除的资源");
		return;
	}
}
function moveDown(){
	var checkNum = 0;
	var selectAllValue = $('#btnSelectAll').val();
	$('#allRole').find("input[name='roleCheck']:checked").each(function(index,element) {
		checkNum++;
		var row ='<tr>'+ $(element).parent().parent().html()+'</tr>';
		$(row).appendTo($('#hasedRole'));
		$(element).parent().parent().remove();
		refreshIndex('#hasedRole');
		refreshIndex('#allRole');
	});
	if(checkNum ==0){
		popMsg("请选择要增加的资源");
		return;
	}else{
		if(selectAllValue =="1"){
			$('#btnSelectAll').val("");
			$('#btnSelectAll').text("全选");
		}
	}
}
function addRole(url) {
	var userId = $("#userId").html()
	url = url+"&userId="+userId;
	popConfirm("确定为用户分配此角色？", addConfirmCallBack, url);
}
function selectAll(btnId,chooseId){
	var selectAllValue = $(btnId).val();
	$(chooseId).find("input[type='checkbox']").each(function() {
		if(selectAllValue == ""){
			if (!$(this).is(":checked")){
				$(this).click();
			}
		}else{
			if ($(this).is(":checked")){
				$(this).click();
			}
		}
	});
	if(selectAllValue == ""){
		$(btnId).val("1");
		$(btnId).text("取消");
	}else{
		$(btnId).val("");
		$(btnId).text("全选");
	}
}
function addRoleCallBack(url) {
	movePage(url);
}
function delRole(url) {
	var userId = $("#userId").html()
	url = url+"&userId="+userId;
	popConfirm("确定删除此角色？", delConfirmCallBack, url);
}
function delConfirmCallBack(url) {
	movePage(url);
}
function refreshIndex(tableId){
	$(tableId).find("input[type='checkbox']").each(function(index,element) {
		var row =$(element).parent().parent();
		row.find("td:eq(1)").text(index+1);
	});
}