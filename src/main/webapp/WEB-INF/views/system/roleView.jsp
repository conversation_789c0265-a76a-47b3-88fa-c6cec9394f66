<%--
/***************************************************************
* 日期  :  2018-6-29
* 作者  :  WH
* 模块  :  角色管理
* 描述  :  角色弹窗查询页面
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
<e:js/>
<script type="text/javascript">
var roleId = '${roleId}';
</script>
<style>
.panel-body {
	margin:15px;
    padding-top: 0px;
}
</style>
</head>
<body >
<div class="panel-body">
	<div class="example"> 
		<div class="row">
			<form:form modelAttribute="roleUserSearchDto" id="searchForm">
				<div class="col-md-2">
					<form:input path="searchRealName" cssClass="form-control" placeholder="姓名" onkeydown='if(event.keyCode==13) search();'/>
				</div>
				<div class="col-md-2">
					<form:input path="searchUserName" cssClass="form-control" placeholder="账号" onkeydown='if(event.keyCode==13) search();'/>
				</div>
				<div class="col-md-2">
					<form:input path="searchTelephone" cssClass="form-control" placeholder="手机号" onkeydown='if(event.keyCode==13) search();'/>
				</div>
				<div class="col-md-6 text-right">
					<input type="button" id="btnQuery" class="btn btn-primary" value="查询">
				</div>
			</form:form>
		</div>
	
		<div class="row"style= "overflow:auto";>
		    <e:grid id="table_id" action="/roleManager/roleViewQuery?roleId=${roleId }" cssClass="table table-striped table-hover" >
		       <e:gridColumn label="序号" renderColumn="sort" orderable="false" cssClass="text-center" cssStyle="width:5%"/>
		       <e:gridColumn label="名称 "  displayColumn="realName" orderable="false" cssClass="text-center"/>
		       <e:gridColumn label="账号"  displayColumn="userName" orderable="false" orderColumn="dry_good_type" cssClass="text-center"/>
		       <e:gridColumn label="手机"  displayColumn="telephone" orderable="false" orderColumn="dry_good_type" cssClass="text-center"/>
		       <e:gridColumn label="座机"  displayColumn="phone" orderable="false" orderColumn="dry_good_type" cssClass="text-center"/>
		       <e:gridColumn label="email" displayColumn="mail" orderable="false" cssClass="text-center"/>
		   </e:grid>
		</div>
	</div> 
</div>
</body>
</html>