<%--
/***************************************************************
* 程序名 : parameterQuery.jsp
* 日期  :  2015-8-19
* 作者  :  wangyuan
* 模块  :  系统管理
* 描述  :  系统参数设置(查询)
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:js />
</head>
<body>
	<div class="panel">
		<div class="panel-heading">
			<i class="icon icon-search"></i>系统参数设置
		</div>
		<div class="panel-body">
			<form:form action="" modelAttribute="parameterDto" id="parameterForm">
				<div class="form-group">
					<label class="col-md-1 control-label">参数标识</label>
					<div class="col-md-2">
						<form:input cssClass="form-control" path="paramCode" />
						<input style="display: none">
					</div>
				</div>
				<div class="form-group">
					<label class="col-md-1 control-label">参数名称</label>
					<div class="col-md-2">
						<form:input cssClass="form-control" path="paramDesc" />
						<input style="display: none">
					</div>
				</div>
				<div class="form-group">
					<div class="col-md-offset-6 col-md-6" align="right">
						<input type="button" id="btnQuery" class="btn btn-primary"
							value="查询" />
                            <sec:authorize access="hasAuthority('RES_PARAMETER_INIT')" >
                            <sec:authorize access="hasAuthority('RES_PARAMETER_INIT_AUTHORITY_3')" >
                             <input type="button" id="btnAdd"
							class="btn btn-primary" value="新增" />
                           </sec:authorize> 
                           </sec:authorize>
					</div>
				</div>
			</form:form>
		</div>
	</div>
	<div class="panel">
		<div class="panel-heading">
			<i class="icon icon-list-ul"></i> 查询结果
		</div>
		<div class="panel-body">
			<e:grid id="table_id" action="/parameter/ajaxQuery"
				cssClass="table table-striped table-hover">
				<e:gridColumn label="参数标识" displayColumn="paramCode"
					orderable="false" cssClass="text-left" />
				<e:gridColumn label="参数名称" displayColumn="paramDesc"
					orderable="false" cssClass="text-left" />
				<e:gridColumn label="更新者" displayColumn="updateUser"
					orderable="false" cssClass="text-left" />
				<e:gridColumn label="更新时间" displayColumn="updateTime"
					orderable="false" cssClass="text-center" />
                   <sec:authorize access="hasAuthority('RES_PARAMETER_INIT')" >
                            <sec:authorize access="hasAuthority('RES_PARAMETER_INIT_AUTHORITY_3')" > 
				<e:gridColumn label="操作" renderColumn="renderColumnOperation"
					orderable="false" cssClass="text-center" />
                   </sec:authorize> 
                   </sec:authorize>
			</e:grid>
		</div>
	</div>
</body>
</html>