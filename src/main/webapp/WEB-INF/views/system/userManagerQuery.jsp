<%--
/***************************************************************
* 程序名 : userManagerQuery.jsp
* 日期  :  2015-7-11
* 作者  :  zouxl
* 模块  :  用户管理
* 描述  :  分页查询页面
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-search"></i>查询
    </div>
    <div class="panel-body">
        <form:form action="/userManager/query" modelAttribute="userManagerDto" >
        <div class="row">
            <div>
              <label class="col-md-1 control-label">用户姓名</label>
              <div class="col-md-3">
                 <form:input cssClass="form-control" path="realName" />
              </div>
            </div>
            <div>
              <label class="col-md-1 control-label">登陆账号</label>
              <div class="col-md-3">
                 <form:input cssClass="form-control" path="userName" />
              </div>
            </div>
            <div>
                <label class="col-md-1 control-label">所属机构</label>
                <div class="col-md-3">
                    <input id="departmentSelectShow" json-data='${orgList}' type="text" class="t-select pullDownList" placeholder="请选择部门" />
                    <form:hidden path="department" />
                </div>
            </div>
        </div>
        <div class="row">
            <div>
                <label class="col-md-1 control-label">角色</label>
                <div class="col-md-3">
                    <input id="roleSelectShow" json-data='${roleList}' type="text" class="t-select pullDownList" placeholder="请选择角色" />
                    <form:hidden path="role" />
                </div>
            </div>
            <div class="row">
                <div class="" align="right" style="padding-right: 20px;">
                    <input type="button" id="btnQuery"  class="btn btn-primary" value="查询" />
                    <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3')" >
                            <input type="button" id="btnPopWin" class="btn btn-default" value="新增用户" />
                        </sec:authorize>
                    </sec:authorize>
                </div>
            </div>
        </div>
        </form:form>
    </div>
</div>


<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-list-ul"></i> 查询结果
    </div>
    <div class="panel-body" style="overFlow-x: scroll">
        <e:table action="/userManager/query" col="9" cssClass="table table-hover table-striped">
            <tr>
                <th width="7%" style="text-align: center;">序号</th>
                <th width="7%" style="text-align: center;">用户姓名</th>
                <th width="7%" style="text-align: center;">登录账号</th>
                <th width="7%" style="text-align: center;">手机号码</th>
                <th width="7%" style="text-align: center;">所属机构</th>
                <th width="14%" style="text-align: center;">角色</th>
                <th width="14%" style="text-align: center;">锁定状态</th>
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT')" >
                    <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3') OR hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_2')" >
                <th width="10%" style="text-align: center;">操作</th>
                </sec:authorize>
                </sec:authorize>
                
            </tr>
            <c:forEach var="item" items="${queryList}" varStatus="status">
            <tr>
                <td align="center"><c:out value="${status.count}" /></td>
                <td align="center"><c:out value="${item.realName}" /></td>
                <td align="center"><c:out value="${item.userName}" /></td>
                <td align="center"><c:out value="${item.telephone}" /></td>
                <td align="center"><c:out value="${item.orgId}" /></td>
                <td align="center"><c:out value="${item.role}" /></td>
                <td align="center" name="lockState">
                <c:choose>
       				<c:when test="${item.lockType == 1 || item.lockType == 2}">已锁定</c:when>
       				<c:otherwise>可用</c:otherwise>
                </c:choose>
                </td>
                 <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT')" >
                    
                <td align="center">
                <span class="widget-icon">
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3') OR hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_2')" >
                <a href="javascript:editPop('/userManager/editUserpop','data=<c:out value="${item.id}" />')"><i class="icon icon-edit" title="编辑"></i></a>
                </sec:authorize>
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3')" >
                <a href="javascript:changePasswordPop('/userManager/changePasswordpop','data=<c:out value="${item.id}" />')"><i class="icon icon-building" title="修改密码"></i></a>
                </sec:authorize>
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3') OR hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_2')" >
                <c:if test="${item.isAdmin != 1}">
                	<a href="javascript:roleChoose('/userManager/roleChoose','data=<c:out value="${item.id}" />')"><i class="icon icon-group" title="分配角色"></i></a>
                </c:if>
                </sec:authorize>
                <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3')" >
                <a href="javascript:delConfirm('/userManager/del?id=<c:out value="${item.id}" />')"><i class="icon icon-trash" title="删除"></i></a>
                
                <c:choose>
                	<c:when test="${item.lockType == 1 || item.lockType == 2}">
                		<a href="javascript:releaseLock('/userManager/releaseLock?id=<c:out value="${item.id}" />',this)"><i class="fa fa-lock" title="解除锁定"></i></a>
                	</c:when>
                	<c:otherwise>
                		<a href="javascript:lockUser('/userManager/lockUser?id=<c:out value="${item.id}" />',this)"><i class="fa fa-unlock-alt" title="锁定用户"></i></a>
                	</c:otherwise>
                </c:choose>
                </sec:authorize>
                </span>
                </td>
                </sec:authorize>
            </tr>
            </c:forEach>
        </e:table>
    </div>
</div>  
    
</body>
</html>
