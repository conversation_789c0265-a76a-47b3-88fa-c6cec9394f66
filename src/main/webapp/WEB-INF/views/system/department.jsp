<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-search"></i>查询
    </div>
    <div class="panel-body">
        <form:form action="/department/query" modelAttribute="dingTalkDepartments" >
            <div class="form-group">
              <label class="col-md-1 control-label">部门名称</label>
              <div class="col-md-3">
                 <form:input cssClass="form-control" path="name" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-1 control-label">部门等级</label>
              <div class="col-md-3">
                  <input id="levelList" json-data='${levelList}' type="text" class="t-select pullDownList" placeholder="请选择等级" />
                  <!-- <input id="department" type="hidden" path="department" /> -->
                  <%--<form:hidden path="level"/>--%>
                  <form:hidden path="levelList"/>
                 <%--<form:input cssClass="form-control" path="level" />--%>
              </div>
            </div>
            <div class="form-group">
                <div class="col-md-3" align="right">
                    <input type="button" id="btnQuery"  class="btn btn-primary" value="&nbsp&nbsp&nbsp查&nbsp&nbsp询&nbsp&nbsp&nbsp" />
                </div>
            </div>
        </form:form>
    </div>
</div>


<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-list-ul"></i> 查询结果
    </div>
    <div class="panel-body">
        <e:table action="/department/query" col="9" cssClass="table table-hover table-striped">
            <tr>
                <th width="10%" align="center">序号</th>
                <th width="20%" align="center">ID</th>
                <th width="20%" align="center">一级部门名称</th>
                <th width="20%" align="center">二级部门名称</th>
                <th width="20%" align="center">三级部门名称</th>
               <sec:authorize access="hasAuthority('RES_DEPARTMENT_INIT')" >
                    <sec:authorize access="hasAuthority('RES_DEPARTMENT_INIT_AUTHORITY_3') OR hasAuthority('RES_DEPARTMENT_INIT_AUTHORITY_2')" >
                <th width="10%" align="center">操作</th>
                </sec:authorize>
                </sec:authorize>
            </tr>
            <c:forEach var="item" items="${queryList}" varStatus="status">
            <tr>
                <td align=""><c:out value="${status.count}" /></td>
                <td align=""><c:out value="${item.id}" /></td>
                <td align=""><c:out value="${item.firstLevel}" /></td>
                <td align=""><c:out value="${item.secondLevel}" /></td>
                <td align=""><c:out value="${item.thirdLevel}" /></td>
                <sec:authorize access="hasAuthority('RES_DEPARTMENT_INIT')" >
                <td align="">
                    <span class="widget-icon">
                        <sec:authorize access="hasAuthority('RES_DEPARTMENT_INIT_AUTHORITY_3') OR hasAuthority('RES_DEPARTMENT_INIT_AUTHORITY_2')" >
                            <a href="javascript:roleChoose('/department/popRole','data=<c:out value="${item.id}" />')"><i class="icon icon-group"></i></a>
                        </sec:authorize>
                    </span>
                </td>
                </sec:authorize>
            </tr>
            </c:forEach>
        </e:table>
    </div>
</div>  
</body>
</html>
