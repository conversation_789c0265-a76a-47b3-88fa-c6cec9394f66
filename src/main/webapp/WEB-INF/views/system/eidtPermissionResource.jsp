<%--
/***************************************************************
* 程序名 : eidtPermissionResource.jsp
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:base/>
<e:js />
<style>
    .environmental_check{
        width: 45%;
        float: left;
        margin: 20px 0 20px 8px;
    }
    .check_all{
        text-align: -webkit-right;
        width: 50%;
        float: left;
        margin: 20px 8px 20px 0;
    }
    .sheet{
        background-color: #f7f7f7;
    }
    .text_margin_left{
        margin-left:5px;
    }
    .text_margin_right{
        margin-left:5px;
    }
</style>
<script>


</script>
</head>
<body>
    <div class="panel">
        <div class="panel-body">
            <p style="margin:15px 0px 15px 30px;">门户登录角色名称：${roleDesc}</p>
            <input type="hidden" id="permissionRoleId" value="${permissionRoleId}">
            <input type="hidden" id="friendLinkCheckLst" value="${friendLinkCheckLst}">
            <input type="hidden" id="plateCheckLst" value="${plateCheckLst}">
            <input type="hidden" id="testCheckLst" value="${testCheckLst}">
            <input type="hidden" id="readyCheckLst" value="${readyCheckLst}">
            <input type="hidden" id="friendLinkLst" value="${friendLink}">
            <input type="hidden" id="plateLst" value="${plate}">
            <input type="hidden" id="testLst" value="${test}">
            <input type="hidden" id="readyLst" value="${ready}">
            <div class="row row-class">
                <span class="environmental_check sheet">
                    <span class="text_margin_left">生产环境</span>
                </span>
                <span class="check_all sheet">
                    <input type="checkbox" id="plateCheck"><span class="text_margin_right">全选</span>
                </span>
            </div>
            <div class="row plate">
                <c:forEach items="${plate}" var="item" varStatus="status">
                    <div style="width:220px;float:left;padding-left: 40px;">
                        <input type="checkbox" class="plate_environment" ${item.checkedStatus} value="${item.codeValue}" id="${item.codeValue}">${item.codeName}(${item.codeValue})
                    </div>
                </c:forEach>
            </div>
            <div class="row row-class">
                <span class="environmental_check sheet">
                    <span class="text_margin_left">预发布环境</span>
                </span>
                <span class="check_all sheet">
                    <input type="checkbox" id="readyCheck"><span class="text_margin_right">全选</span>
                </span>
            </div>
            <div class="row ready">
                <c:forEach items="${ready}" var="item" varStatus="status">
                    <div style="width:220px;float:left;padding-left: 40px;">
                        <input  type="checkbox" class="ready_environment" ${item.checkedStatus} value="${item.codeValue}" id="${item.codeValue}">${item.codeName}(${item.codeValue})
                    </div>
                </c:forEach>
            </div>
            <div class="row row-class">
                <span class="environmental_check sheet">
                    <span class="text_margin_left">测试环境</span>
                </span>
                <span class="check_all sheet">
                    <input type="checkbox" id="testCheck"><span class="text_margin_right">全选</span>
                </span>
            </div>
            <div class="row test">
                <c:forEach items="${test}" var="item" varStatus="status">
                    <div style="width:220px;float:left;padding-left: 40px;">
                        <input  type="checkbox"  class="test_environment"  ${item.checkedStatus} value="${item.codeValue}" id="${item.codeValue}">${item.codeName}(${item.codeValue})
                    </div>
                </c:forEach>
            </div>
            <div class="row row-class">
                <span class="environmental_check sheet">
                    <span class="text_margin_left">友情链接</span>
                </span>
                <span class="check_all sheet">
                    <input type="checkbox" id="friendLinkCheck"><span class="text_margin_right">全选</span>
                </span>
            </div>
            <div class="row friendLink">
                <c:forEach items="${friendLink}" var="item" varStatus="status">
                    <div style="width:220px;float:left;padding-left: 40px;">
                        <input  type="checkbox"  class="friend_environment" ${item.checkedStatus} value="${item.codeValue}" id="${item.codeValue}">${item.codeName}
                    </div>
                </c:forEach>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12" align="center">
                <input type="button" id="btnClose" class="btn btn-default" value="取消" />
                <input type="button" id="btnConfirm" class="btn btn-primary" value="确定" />
            </div>
        </div>
    </div>
</body>
</html>
