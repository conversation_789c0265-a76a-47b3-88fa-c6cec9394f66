//@ sourceURL=filedownload.js

$(document).ready(
		function() {

			// 保存
			if(document.getElementById("btnAdd")){
				$("#btnAdd").bind(
						"click",
						function() {
							//2017.10.10 by zhanghaoyu start
//							var param = "id=" + $("#id").val() + "&url="
//									+ $("#url").val() + "&starttime="
//									+ $("#starttime").val() + "&endtime="
//									+ $("#endtime").val() + "&pagecount="
//									+ $("#pagecount").val();
							var param = {
									id : $("#id").val() ,
									url : $("#url").val() ,
									starttime : $("#starttime").val() ,
									endtime : $("#endtime").val() ,
									pagecount : $("#pagecount").val()
							}
							//2017.10.10 by zhanghaoyu end
							ajaxData("/download/add", param, callback);

						});
			}
			
		});

function callback(data) {
	//2016/08/29 bug2015 baiyang start
	ajaxTableReload("table_id", false);
//	ajaxTableQuery("table_id", "/download/ajaxQuery", $("#taskDownload")
//			.formSerialize());
	//2016/08/29 bug2015 baiyang end
}

// 资讯列表索引列
function renderColumnIndex(data, type, row, meta) {
	return meta.row + 1;
}