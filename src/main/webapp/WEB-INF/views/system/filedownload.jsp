<%--
/***************************************************************
* 程序名 : orgslist.jsp
* 日期  :  2015-7-7
* 作者  :  朱继刚
* 模块  :  系统管理
* 描述  :  组织机构
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js />
</head>
<body>

    <div class="panel-body">
        <form:form action="/download/Init" modelAttribute="taskDownload" id='taskDownload'>
            <div class="form-group">
                <label class="col-md-1 control-label">抓取公告 解析URL</label>
                <div class="col-md-3">
                    <form:input cssClass="form-control" path="url" id="url" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-1 control-label">开始日期</label>
                <div class="col-md-3">
                    <e:date cssClass="form-control" path="starttime" id="starttime" />
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-1 control-label">结束日期</label>
                <div class="col-md-3">
                    <e:date cssClass="form-control" path="endtime" id="endtime" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-1 control-label">总页数</label>
                <div class="col-md-3">
                    <form:input cssClass="form-control" path="pagecount" id="pagecount" />
                </div>
            </div>
            <div class="col-md-offset-2 col-md-10 text-right">
            <sec:authorize access="hasAuthority('RES_DOWNLOAD_DECLARE')" >
            <sec:authorize access="hasAuthority('RES_DOWNLOAD_DECLARE_AUTHORITY_3')" >
                <input type="button" id="btnAdd" class="btn btn-primary" value="保存" />
                </sec:authorize>
                </sec:authorize>
            </div>
        </form:form>
    </div>

    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:grid id="table_id" action="/download/ajaxQuery" cssClass="table table-striped table-hover">

                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                    cssStyle="width:4%" />

                <e:gridColumn label="URL" displayColumn="url" orderable="false" cssClass="text-left"
                    cssStyle="width:10%" />

                <e:gridColumn label="起始日期" displayColumn="starttime" cssClass="text-left" cssStyle="width:8%" />

                <e:gridColumn label="结束日期" displayColumn="endtime" cssClass="text-left" cssStyle="width:8%" />

                <e:gridColumn label="总页数" displayColumn="pagecount" orderable="false" cssClass="text-left"
                    cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</body>
</html>