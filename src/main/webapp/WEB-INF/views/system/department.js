//@ sourceURL=department.js
$(document).ready(function() {
			// 查询
			$("#btnQuery").bind("click", function() {
				submitForm("/department/query");
			});

			// 初始化下拉框
			tSelectDataInit();
		});

//下拉框初始化获取数据
function tSelectDataInit() {
    var departmentSelect = $('input[name="levelList"]').val();
    var tSelectOptions = {
        customCallBack : tSelectCustomCallBack1,
        submitCallBack : tSelectSubmitCallBack1,
        cancelCallBack : tSelectClearCallBack1,
        id : 'codeValue',
        pid : '',
        name : 'codeName',
        value : 'codeValue',
        grade : 1,
        resultType : 'children',
        selectedIds : departmentSelect,
        //调整单级树下拉框高度(400->280)，参考数据源得出
        style : {tMaxHeight : 280}
    };

    $('#levelList').tselectInit(null, tSelectOptions);
}
// 下拉框点击确定回调方法
function tSelectSubmitCallBack1(t, d) {
    $('input[name="level"]').val(d.value);
}

// 下拉框点击清空回调方法
function tSelectClearCallBack1(t) {
    $('input[name="level"]').val('');
}
// 下拉框自定义方法
function tSelectCustomCallBack1(t) {
    $('.t-select-table').slideUp('fast');
}
function delConfirm(url) {
	popConfirm("请确认是否删除此角色？", myConfirmCallBack, url);
}

function myConfirmCallBack(url) {
	//2016/08/29 bug2015 baiyang start
	movePage(url);
	//2016/08/29 bug2015 baiyang end
}
function editPop(url, data) {
	parent.popWin("编辑角色", url, data, "500px", "400px", editPopCallBack, null);
}
function editPopCallBack(){
	popMsg("保存成功");
}
function roleChoose(url, data) {
	parent.popWin("选择资源", url, data, "90%", "90%", roleChooseCallback,null);
}
function roleChooseCallback(){
	popMsg("保存成功");
}
function roleView(url, data) {
	parent.popWin("<span style='font-weight:100;'>角色查看</span>", url, data, "90%", "90%", roleViewCallback,null);
}
function roleViewCallback(){
		popMsg("保存成功");
}