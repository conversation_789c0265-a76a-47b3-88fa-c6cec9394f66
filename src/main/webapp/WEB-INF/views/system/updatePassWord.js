//@ sourceURL=updatePassWord.js
// 表单校验规则
var validator;
// 判断是新增、修改
var key = "isNew";
$(document).ready(
		function() {

			$.validator.addMethod("checkPasswordDAS", function(value, element) {
				var isValid=true;
				var rule=/(?=.*((?=\D)[^A-Za-z]))(?=.*\d)+(?=.*[A-Za-z]).*/;
				if (!rule.test(value)) {
					isValid = false;
				}
				return isValid;
			}, "当前密码不符合规范，密码必须为:数字+字母+符号");
			$.validator.addMethod("checkPasswordDA", function(value, element) {
				var isValid=true;
				var rule=/^(?!\d+$)+(?![a-zA-Z]+$)+(?=[0-9a-zA-Z]+$)/;
					if (!rule.test(value)) {
						isValid = false;
					}
				return isValid;
			}, "当前密码不符合规范，密码必须为:数字+字母");
			// 设定表单校验规则
			validator = $("#userForm").validate({
				rules : {
					"newPassword" : {
						maxlength : 18,
						minlength : 6
					},
					"oldPassword" : {
						maxlength : 18,
						minlength : 6
					}
				},
			});
//			var flag=$("#complicatedPasswordFlag").val();
//			var type=$("#complicatedPasswordClass").val();
//			var obj=$("#newPassword");
//			if(flag==true||flag=="true"){
//				if(getValue(type)=='0'){
//					obj.rules("add", {
//						checkPasswordDA : true,
//					});
//				}else if(getValue(type)=='1'){
//					obj.rules("add", {
//						checkPasswordDAS : true,
//					});
//				}
//			}

			// 保存
			$("#userSave").bind(
					"click",
					function() {
						if($("#userForm").valid()){
							if ($("#newPassword").val() != $("#affirm").val()) {
								popMsg("密码不一致！");
								return;
							}
							if ($("#newPassword").val().indexOf(" ") != -1 || $("#username").val().indexOf(" ") != -1) {
								popMsg("不可含有空格！");
								return;
							}
							//2017.10.10 by zhanghaoyu start
//							var param = "userName=" + $("#username").val() + "&"
//							+ "oldPassword="
//							+ $.md5($("#oldPassword").val()) + "&"
//							+ "newPassword="
//							+ $.md5($("#newPassword").val());
							var param = {
									userName : $("#username").val() ,
									oldPassword : $.md5($("#oldPassword").val()) ,
									newPassword : $.md5($("#newPassword").val())
							}
							//2017.10.10 by zhanghaoyu end
							ajaxData("/userManager/passwordUpdate", param,
									usernameCallback);
						}
					});

			// 关闭
			$("#btnClose").bind("click", function() {
				closeWin();
			});
		});

function usernameCallback(data) {

	if (!data) {
		popMsg("旧密码输入错误！");
		return;
	}

	var jsonObj = {
		result : [ {} ]
	};

	closeWinCallBack(jsonObj);
	popMsg("修改成功！");
}