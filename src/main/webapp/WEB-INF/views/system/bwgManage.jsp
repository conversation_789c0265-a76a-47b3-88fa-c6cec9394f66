
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:js/>
</head>
<body style="padding-top:0px;">
<div class="panel">
    <div class="panel-heading">
        特殊时期画面黑白灰显示
    </div>
    <div class="panel-body">
        <form:form modelAttribute="dto" id="form">
            <div class="row">
                <div class="col-md-1" style="margin-top: 8px" align="center">是否打开</div>
                <div class="col-md-5">
                    <form:checkbox path="activations" id="activation" onclick="changeValue()" />
                </div>
                <div class="col-md-6"></div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-1" style="margin-top: 8px" align="center">生效时间</div>
                <div class="col-md-5">
                    <div class="input-group">
                        <e:date cssClass="form-control" path="effectiveTimeStart" format="yyyy-mm-dd hh:ii" />
                        <span class="input-group-addon fix-border">至</span>
                        <e:date cssClass="form-control" path="effectiveTimeEnd" format="yyyy-mm-dd hh:ii" />
                    </div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-1" style="margin-top: 8px" align="center">生效范围</div>
                <div class="col-md-5">
                    <input id="effectiveScopeSelect" json-data='${effectiveScopeLabel}' type="text" class="t-select" placeholder="请选择生效范围" />
                    <form:hidden path="effectiveScope" />
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-offset-2 col-md-2" align="center">
                    <input type="button" id="btnConfirm" class="btn btn-primary" onclick="save()" value="保存" />
                </div>
            </div>
            <form:hidden path="id" />
        </form:form>
    </div>
</div>

</body>
</html>
