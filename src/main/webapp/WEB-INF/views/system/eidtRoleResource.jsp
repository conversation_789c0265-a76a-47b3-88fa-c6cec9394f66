<%--
/***************************************************************
* 程序名 : selectCloudPalte.jsp
* 日期  :  2015-8-13
* 作者  :  fanguangyuan
* 模块  :  会议分类
* 描述  :  获取云端模板
* 备注  :
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:base/>
<e:js />
<style>
.widget {
    height: 30px;
    position: relative;
    background-color: #a1aab5;
    border: 1px solid #aaa;
    z-index: 1000;
    float: left;
    display: inline;
    margin: 3px;
    text-align: center;
    border-color: #fff;
    color: #fff;
}

.widgetClose {
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 999;
    display: none;
}
</style>
<script>
</script>
</head>
<body>
    <div class="panel">
        <div class="panel-body">
            <form:form id="modelsForm" modelAttribute="menuRoleDto" cssClass="form-horizontal">
                <form:hidden name="roleId" id="roleId" path="roleId"/>
                <table id="items" class="table table-hover table-striped">
                   <tr>
                       <th class="col-md-2" style="text-align:center">资源名称</th>
                       <th class="col-md-3" style="text-align:center">资源描述</th>
                       <th class="col-md-4" style="text-align:center">操作权限</th>
                    </tr>
                    <c:forEach var="item" items="${menuRoleDto.meunuResourceList}" varStatus="status">
                        <tr>
                            <td align="center">
                                <form:hidden path="meunuResourceList[${status.index}].resourceId"/>
                                <form:hidden path="meunuResourceList[${status.index}].id"/>
                                <c:out value="${item.plateName}"/>
                            </td>
                            <td align="center"><c:out value="${item.menuName}"/></td>
                            <td align="center">
                                 <form:radiobuttons path="meunuResourceList[${status.index}].authorityFlag" name="${item.id}"
                                                    items="${authorityFlagMap}" delimiter="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"/>
                            </td>
                        </tr>
                    </c:forEach>
                </table>
                <div class="row" style="padding-top: 10px;">
                    <div class="col-md-12" align = "center">
                    <sec:authorize access="hasAuthority('RES_ROLEMANAGER_INIT_AUTHORITY_3')" >
                        <button type="button" class="btn btn-primary" id="btnSave">保存</button>
                        </sec:authorize>
                        <button type="button" class="btn btn-default" id="btnClose">关闭</button>
                    </div>
                </div>
                <input type="hidden" name="menuResource" id="menuResource" value = "${menuRoleDto.meunuResourceList}">
                </form:form>
            </div>
    </div>
</body>
</html>
