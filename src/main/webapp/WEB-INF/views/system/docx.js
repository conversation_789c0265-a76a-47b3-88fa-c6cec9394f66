//@docx.js
$(document).ready(function() {
	init();
});
var TANGER_OCX_OBJ;
var TANGER_OCX_bDocOpen = !1;
function init()
{
	TANGER_OCX_OBJ = document.getElementById("TANGER_OCX");
	//ObjectDisplay(!0);
    TANGER_OCX_OBJ.AddDomainToTrustSite(document.domain),

    //TANGER_OCX_OBJ.AddDocTypePlugin(".pdf","PDF.NtkoDocument","*******","ntkooledocall.cab",51,true);	
	//TANGER_OCX_OBJ.CreateNew("Word.Document");
    TANGER_OCX_OBJ.BeginOpenFromURL ("http://localhost:8080/edm/office/model.docx");
   //TANGER_OCX_OBJ.BeginOpenFromURL("http://localhost:8080/edm/office/model.docx");
    TANGER_OCX_bDocOpen = !0;
   		
}
function protectrevision() {
	
    TANGER_OCX_OBJ.ActiveDocument.TrackRevisions = !TANGER_OCX_OBJ.ActiveDocument.TrackRevisions;
}
function officeToolBar() {
    TANGER_OCX_OBJ.toolbars = !TANGER_OCX_OBJ.toolbars
}
function objSaveAsPDFFile(a) {
	TANGER_OCX_OBJ.ShowDialog(3);
}
function setvisibleinfo(a) {
	  document.getElementById("infovalue").innerHTML = a
	}



