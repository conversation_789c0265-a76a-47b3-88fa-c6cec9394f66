//@ sourceURL=bwgManage.js
$(document).ready(function() {
	$("#form").validate({
		rules : {
			"activation" : {
				required : true
			},
			"effectiveTimeStart" : {
				required : true,
				date : true
			},
			"effectiveTimeEnd" : {
				required : true,
				date : true
			},
			"effectiveScope" : {
				required : true
			}
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	$("#activation").switcher();
	effectiveScopeSelectDataInit();
});
//保存成功
function callBack(data) {
	var jsonObj = {
			result : [ {
				name : "",
				testDate : ""
			}]
		};
	//closeWin("保存成功");
	closeWinCallBack(jsonObj);
	//popMsg("保存成功");
}

function effectiveScopeSelectDataInit() {
	var targetSelect = $("#effectiveScope").val();
	var tSelectOptions2 = {
		customCallBack : tSelectCustomCallBack,
		submitCallBack : tSelectSubmitCallBack,
		id: 'value',
		name: 'label',
		value: 'value',
		resultType: 'all',
		grade : 1,
		style : {},
		selectedIds : targetSelect
	};
	$('#effectiveScopeSelect').tselectInit(null, $.extend({}, tSelectOptions2));
}
//下拉框点击清空回调方法
function tSelectClearCallBack(t) {
	$('input[name=effectiveScope]').val('');
}
// 下拉框自定义方法
function tSelectCustomCallBack(t) {
	//
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
	// 提交检索
	$('input[name=effectiveScope]').val(d.value);
	$("#effectiveScopeSelect").val(d.name);
}

function save(){
	if($("#form").valid()){
		var param = {
			'id':$("#id").val(),
			'activations':$("#activation").val(),
			'effectiveTimeStart':$("#effectiveTimeStart").val(),
			'effectiveTimeEnd':$("#effectiveTimeEnd").val(),
			'effectiveScope':$('input[name=effectiveScope]').val(),
		};
		parent.popWin("确定修改密码","/bwgManage/passwordCheckInit",param,"400px","200px");
	}

}

function changeValue() {
	var value = true;
	if(!$("#activation")[0].checked) {
		value = false;
	}
	$('#activation').val(value);
}