//@ sourceURL=parameterEdit.js
var rowSize;
$(document).ready(function() {
	$.validator.addMethod("paramValueRepeat", function(value, element) {
		var isValid = true;
		$("#items tr").each(function() {
			var $input = $("input[name*='paramValue']", $(this));
			if ($(element).prop("name") == $input.prop("name")) {
				return true;
			}
			if ($input.val() == value) {
				isValid = false;
				return false;
			}
		});
		return isValid;
	}, "子参数标识不能重复");

	$.validator.addMethod("paramNameRepeat", function(value, element) {
		var isValid = true;
		$("#items tr").each(function() {
			var $input = $("input[name*='paramName']", $(this));
			if ($(element).prop("name") == $input.prop("name")) {
				return true;
			}
			if ($input.val() == value) {
				isValid = false;
				return false;
			}
		});
		return isValid;
	}, "子参数名称不能重复");

	// 设定表单校验规则
	var validator = $("#parameterForm").validate({
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});

	rowSize = $("#items tr").length - 1;
	
	// 为初始化的列表里的行动态添加校验规则
	$("#items tr").each(function(index) {
		if (index == 0) {
			return true;
		}
		addRowValidateRules($(this), index);
	})

	// 增加一行，并为新增的行动态添加校验规则
	$("#btnAdd").click(function() {
		// 往table增加一行
		addRow();
	});
	
	// 返回
	$("#btnBack").bind("click", function() {
		closeWin();
	});
	
	// 保存
	$("#btnSave").bind("click", function() {
		if ($("#parameterForm").valid()) {
        	ajaxSubmitForm("/parameter/parameterEditSave", null, function(data){
        		closeWinCallBack();
        	});
        }
	});
});

//往table增加一行
function addRow() {
	// 取得行的模板
	var template = $.trim($("#template").html());
	var $newRow = $(template.replace(new RegExp("\\[0\\]", "g"),
			"[" + rowSize + "]").replace(new RegExp("item0", "g"),
			"item" + rowSize));
	$newRow.data("index", rowSize);
	// 往table里动态添加一行
	$newRow.appendTo("#items");
	// 动态为新增的行添加校验规则
	addRowValidateRules($newRow, rowSize);
	rowSize = refreshTableIndex("items");
}

// 删除table的一行
function removeRow(obj) {
	$(obj).parent().parent().remove();
	rowSize = refreshTableIndex("items");
}

// 为每行添加校验规则
function addRowValidateRules($trObj, index) {
	$("input[name*='paramValue']", $trObj).rules("add", {
		required : true,
		maxlength : 100,
		paramValueRepeat : true
	});
	$("input[name*='paramName']", $trObj).rules("add", {
		required : true,
		maxlength : 100,
		paramNameRepeat : true
	});
	$("input[name*='paramContent']", $trObj).rules("add", {
		maxlength : 500
	})
}