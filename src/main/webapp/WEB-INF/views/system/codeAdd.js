//@ sourceURL=codeAdd.js
var rowSize;
$(document).ready(function() {
	$.validator.addMethod("codeValueRepeat", function(value, element) {
		var isValid = true;
		$("#items tr").each(function() {
			var $input = $("input[name*='codeValue']", $(this));
			if ($(element).prop("name") == $input.prop("name")) {
				return true;
			}
			if ($input.val() == value) {
				isValid = false;
				return false;
			}
		});
		return isValid;
	}, "子代码值不能重复");

	$.validator.addMethod("codeNameRepeat", function(value, element) {
		var isValid = true;
		$("#items tr").each(function() {
			var $input = $("input[name*='codeName']", $(this));
			if ($(element).prop("name") == $input.prop("name")) {
				return true;
			}
			if ($input.val() == value) {
				isValid = false;
				return false;
			}
		});
		return isValid;
	}, "子代码名称不能重复");

	$.validator.addMethod("sortNoRepeat", function(value, element) {
		var isValid = true;
		$("#items tr").each(function() {
			var $input = $("input[name*='sortNo']", $(this));
			if ($(element).prop("name") == $input.prop("name")) {
				return true;
			}
			if ($input.val() == value) {
				isValid = false;
				return false;
			}
		});
		return isValid;
	}, "显示序号不能重复");

	// 设定表单校验规则
	var validator = $("#codeForm").validate({
		rules : {
			"codeNo" : {
				required : true,
				// 后台校验
				remote : {
					url : contextPath + "/code/remoteValidate",
					type : "post",
					data : {
						codeNo : function() {
							return $("#codeNo").val();
						}
					}
				},
				maxlength : 50
			},
			"codeDesc" : {
				required : true,
				maxlength : 500
			},
		},
		messages : {
			"codeNo" : {
				remote : "代码标识已存在"
			}
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});

	rowSize = $("#items tr").length - 1;

	// 为初始化的列表里的行动态添加校验规则
	$("#items tr").each(function(index) {
		if (index == 0) {
			return true;
		}
		addRowValidateRules($(this), index);
	})

	// 增加一行，并为新增的行动态添加校验规则
	$("#btnAdd").click(function() {
		// 往table增加一行
		addRow();
	});

	// 返回
	$("#btnBack").bind("click", function() {
		closeWin();
	});

	// 保存
	$("#btnSave").bind("click", function() {
		if ($("#codeForm").valid()) {
			if ($("#items tr").length == 1) {
				popAlert("至少输入一条子代码数据");
			} else {
				ajaxSubmitForm("/code/codeAddSave", null, function(data) {
					closeWinCallBack();
				});
			}
		}
	});
});

// 往table增加一行
function addRow() {
	// 取得行的模板
	var template = $.trim($("#template").html());
	var $newRow = $(template.replace(new RegExp("\\[0\\]", "g"),
			"[" + rowSize + "]").replace(new RegExp("item0", "g"),
			"item" + rowSize));
	$newRow.data("index", rowSize);
	// 往table里动态添加一行
	$newRow.appendTo("#items");
	// 动态为新增的行添加校验规则
	addRowValidateRules($newRow, rowSize);
	rowSize = refreshTableIndex("items");
}

// 删除table的一行
function removeRow(obj) {
	$(obj).parent().parent().remove();
	rowSize = refreshTableIndex("items");
}

// 为每行添加校验规则
function addRowValidateRules($trObj, index) {
	$("input[name*='codeValue']", $trObj).rules("add", {
		required : true,
		maxlength : 50,
		codeValueRepeat : true
	});
	$("input[name*='codeName']", $trObj).rules("add", {
		required : true,
		maxlength : 128,
		codeNameRepeat : true
	});
	$("input[name*='sortNo']", $trObj).rules("add", {
		required : true,
		number : true,
		sortNoRepeat : true
	});
}