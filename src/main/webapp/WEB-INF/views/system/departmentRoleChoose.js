//@ sourceURL=departmentRoleChoose.js
$(document).ready(function() {
			;
		});

function addRole(url) {
	;
	var departmentId = $("#departmentId").html()
	url = url+"&departmentId="+departmentId;
	popConfirm("确定为部门分配此角色？", addConfirmCallBack, url);
}
function addConfirmCallBack(url) {
	movePage(url);
}
function delRole(url) {
	var departmentId = $("#departmentId").html()
	url = url+"&departmentId="+departmentId;
	popConfirm("确定删除此角色？", delConfirmCallBack, url);
}
function delConfirmCallBack(url) {
	movePage(url);
}