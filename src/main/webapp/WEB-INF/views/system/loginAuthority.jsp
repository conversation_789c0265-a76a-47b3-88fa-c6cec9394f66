<%--
/***************************************************************
* 程序名 : loginAuthority.jsp
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-search"></i>查询
    </div>
    <div class="panel-body">
        <form:form action="/userManager/roleQuery" modelAttribute="permissionRoleDto" >
            <div class="form-group">
              <label class="col-md-1 control-label">门户登录角色名称</label>
              <div class="col-md-3">
                 <form:input cssClass="form-control" path="roleName" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-1 control-label">门户登录角色描述</label>
              <div class="col-md-3">
                 <form:input cssClass="form-control" path="roleDes" />
              </div>
            </div>
            <div class="form-group">
                <div class="col-md-3" align="right">
                    <input type="button" id="btnQuery"  class="btn btn-primary" value="&nbsp&nbsp&nbsp查&nbsp&nbsp询&nbsp&nbsp&nbsp" />
                    <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT')" >
                    <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_3')" >
                    <input type="button" id="btnPopWin" class="btn btn-default" value="新增角色" />
                    </sec:authorize>
                    </sec:authorize>
                </div>
            </div>
        </form:form>
    </div>
</div>


<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-list-ul"></i> 查询结果
    </div>
    <div class="panel-body">
        <e:table action="/userManager/roleQuery" col="9" cssClass="table table-hover table-striped">
            <tr>
                <th width="10%" align="center">序号</th>
                <th width="10%" align="center">ID</th>
                <th width="20%" align="center">门户登录角色名称</th>
                <th width="50%" align="center">门户登录角色描述</th>
               <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT')" >
                    <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_3') OR hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_2')" >
                <th width="10%" align="center">操作</th>
                </sec:authorize>
                </sec:authorize>
            </tr>
            <c:forEach var="item" items="${queryList}" varStatus="status">
            <tr>
                <td align="center"><c:out value="${status.count}" /></td>
                <td align="left"><c:out value="${item.id}" /></td>
                <td align="left"><c:out value="${item.roleName}" /></td>
                <td align="left"><c:out value="${item.roleDes}" /></td>
                <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT')" >
                    
                <td align="center">
                <span class="widget-icon">
                <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_3') OR hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_2')" >
                <a href="javascript:editPop('/userManager/editRolepop','data=<c:out value="${item.id}" />')"><i class="icon icon-edit"></i></a>
                <a href="javascript:roleView('/userManager/roleViewpop','data=<c:out value="${item.id}" />')"><i class="icon icon-user"></i></a>
                <a href="javascript:roleChoose('/userManager/queryPermission','data=<c:out value="${item.id}" />')"><i class="icon icon-group"></i></a>
                </sec:authorize>
                <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_3')" >
                <a href="javascript:delConfirm('/userManager/delPermission?id=<c:out value="${item.id}" />')"><i class="icon icon-trash"></i></a>
                </sec:authorize>
                </span>
                </td>
                
                 </sec:authorize>
            </tr>
            </c:forEach>
        </e:table>
    </div>
</div>  
</body>
</html>
