//@ sourceURL=parameterQuery.js
$(document).ready(function() {
	// 查询
	$("#btnQuery").bind("click",function() {
		ajaxTableQuery("table_id", "/parameter/ajaxQuery", $("#parameterForm").formSerialize());
	});
	
	// 新增系统参数
	if(document.getElementById("btnAdd")){
		$("#btnAdd").bind("click",function() {
			parent.popWin("新增系统参数", "/parameter/parameterAddInit", "", "1000px", "500px", myWinCallback, "");
		});
	}
	
});

// 渲染操作列 data:后台传过来的整行数据
// 编辑按钮操作
function renderColumnOperation(data, type, row, meta) {
	return '<a onclick="parameterEdit(\''+ data.paramCode + '\',\'' + data.paramDesc + '\')"><i class="icon icon-edit"></i></a>';
}

// 编辑
function parameterEdit(paramCode, paramDesc) {
	//2017.10.16 by zhanghaoyu start
//	var param = "paramCode=" + paramCode + "&paramDesc=" + paramDesc;
	var param = {
			paramCode : paramCode ,
			paramDesc : paramDesc
	}
	//2017.10.16 by zhanghaoyu end
	parent.popWin("编辑系统参数", "/parameter/parameterEditInit", param, "1000px", "500px", myWinCallback, "");
}

function myWinCallback() {
	popMsg("保存成功");
	//2016/08/29 bug2015 baiyang start
//	ajaxTableQuery("table_id", "/parameter/ajaxQuery", $("#parameterForm").formSerialize());
	ajaxTableReload("table_id", false);
	//2016/08/29 bug2015 baiyang end
}