<%--
/***************************************************************
* 程序名 : addUser.jsp
* 日期  :  2015-7-17
* 作者  :  zouxl
* 模块  :  用户管理新增页面
* 描述  :  用户管理新增页面
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
<e:js/>
</head>
<body style="padding-top:0px;">
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 用户信息
        </div>
        <div class="panel-body">
            <form:form action="/userManager/editUser" modelAttribute="editUserDto" id="userForm">
            <form:hidden path="id" />
                <div class="form-group">
                    <label class="col-md-1 control-label">用户姓名</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="realName"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">移动电话</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="telephone"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">固定电话</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="phone" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">电子邮箱</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="mail"/>
                    </div>
                </div> 
                <div class="form-group">
                    <label class="col-md-1 control-label">传真</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="fax" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">机构</label>
                    <div class="col-md-3">
                        <input id="departmentSelectShow" json-data='${orgList}' type="text" class="t-select pullDownList" placeholder="请选择机构" />
                        <form:hidden path="orgId" />
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-offset-10 col-md-2" align="right">
                    <sec:authorize access="hasAuthority('RES_USERMANAGER_INIT_AUTHORITY_3')" >
                        <input type="button" id="btnConfirm" class="btn btn-primary" value="保存" />
                        </sec:authorize>
                        <input type="button" id="btnClose" class="btn btn-default" value="关闭" />
                    </div>
                </div>
            </form:form>
        </div>
    </div>
  
</body>
</html>
