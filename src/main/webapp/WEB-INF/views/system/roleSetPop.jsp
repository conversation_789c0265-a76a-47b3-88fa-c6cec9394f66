<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 未分配资源列表
        </div>
        <div class="panel-body">
            <e:table action="/roleManager/queryRole" col="9" cssClass="table table-hover table-striped" id ="allRole">
                <tr>
                    <th width="10%" align="center" ><a id="btnSelectAll" style="color:black">全选</a></th>
                    <th width="10%" align="center">序号</th>
                    <th width="20%" align="center">资源名称</th>
                    <th width="20%" align="center">资源描述</th>
                    <th width="30%" align="center">资源URL</th>
                </tr>
                <c:forEach var="item" items="${noResource}" varStatus="status">
                    <tr>
                        <td align="center"><input type="checkbox" name="roleCheck" id ="${item.id}"/></td>
                        <td align="center"><c:out value="${status.count}" /></td>
                        <td align="left"><c:out value="${item.resourceName}" /></td>
                        <td align="left"><c:out value="${item.resourceDes}" /></td>
                        <td align="left"><c:out value="${item.resourceUrl}" /></td>
<!--                         <td align="center"><span class="widget-icon"> <a -->
<%--                                 href="javascript:addRole('/roleManager/addRole?id=<c:out value="${item.id}" />')"><i --%>
<!--                                     class="icon icon-plus"></i></a> -->
<!--                         </span></td> -->
                        
                    </tr>
                </c:forEach>
            </e:table>
        </div>
       
        <div class="panel-heading">
             <div class="row">
                <div class="col-md-1" align="center" style="font-size: 15px" style="margin-left:25px">
                    <input type="button" id="btnUp" value="上移"
                        class="btn btn-primary" />
                </div>
                <div class="col-md-1" align="center" style="font-size: 15px">
                     <input type="button" id="btnDown" value="下移"
                        class="btn btn-primary" />
                </div>
                <div class="col-md-4" align="left">
                        <input type="button" id="btnSave" value="保存"
                        class="btn btn-primary" />
                        <input type="button" id="btnCancel" class="btn btn-default" style="margin-left:25px"
                            value="取消" />
                </div>
        </div>
        </div>
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 已分配资源列表
        </div>
        <div class="panel-body">
            <e:table action="/roleManager/queryRole" col="9" cssClass="table table-hover table-striped" id ="hasedRole">
                <tr>
                    <th width="10%" align="center"><a id="btnSelectHasAll" style="color:black">全选</a></th>
                    <th width="10%" align="center">序号</th>
                    <th width="20%" align="center">资源名称</th>
                    <th width="20%" align="center">资源描述</th>
                    <th width="30%" align="center">资源URL</th>
                    
                </tr>
                <c:forEach var="item" items="${haveResource}" varStatus="status">
                    <tr>
                        <td align="center"><input type="checkbox" name="roleCheck" id ="${item.id}"/></td>
                        <td align="center"><c:out value="${status.count}" /></td>
                        <td align="left"><c:out value="${item.resourceName}" /></td>
                        <td align="left"><c:out value="${item.resourceDes}" /></td>
                        <td align="left"><c:out value="${item.resourceUrl}" /></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
        <div id="userId" style="display: none;">${userId}</div>
    </div>
</body>
</html>
