//@ sourceURL=editPermissionRole.js
$(document).ready(function() {
	// 设定表单校验规则
	$("#roleForm").validate({
		rules : {
			"roleName" : {
				required : true,
				maxlength : 50
			},
			"roleDes" : {
				required : true,
				maxlength : 256
			}
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	// 确定
	$("#btnConfirm").bind("click", function() {
		// 每一种直接给父页面值
		// $("#name", parent.document).val("弹出窗口设置的值");
		// 第二种通过父页面的回调函数传弹出窗口的值
		// 拼弹出窗口的值
		if ($("#roleForm").valid()) {
			ajaxSubmitForm("/userManager/editPermissionSave", null, callBack, 0);
		}
	});
	// 关闭
	$("#btnClose").bind("click", function() {
		closeWin();
	});
});
//保存成功
function callBack(data) {
	var jsonObj = {
			result : [ {
				name : "",
				testDate : ""
			}]
		};
	//closeWin("保存成功");
	closeWinCallBack(jsonObj);
	//popMsg("保存成功");
}