//@ sourceURL=userManagerQuery.js
$(document).ready(function() {
	// 查询
	$("#btnQuery").bind("click", function() {
		submitForm("/userManager/query");
		// 初始化部门下拉框
		tSelectDataInit();
		// 初始化角色下拉框
		tSelectDataInitRole();
	});
	// 根据URL弹出窗口
	if(document.getElementById("btnPopWin")){
		$("#btnPopWin").bind(
			"click",
			function() {
				parent.popWin("新增用户", "/userManager/addUserpop", "", "1000px",
					"500px", myWinCallback, "保存成功");
			});
	}
	// 初始化部门下拉框
	tSelectDataInit();
	// 初始化角色下拉框
	tSelectDataInitRole();
});

// 弹出窗口的回调函数 paraWin:弹出窗口传来的值 paraCallBack:调用窗口时自己传的回调参数值
function myWinCallback(paraWin, paraCallBack) {
	if(getValue(paraCallBack) != "") {
		popMsg(paraCallBack);
	}
	submitForm("/userManager/query?toPageState=back");
}
function delConfirm(url) {
	popConfirm("请确认是否删除此用户？", myConfirmCallBack, url);
}

function myConfirmCallBack(url) {
	ajaxData(url,null,function(){
		submitForm("/userManager/query?toPageState=back");
	})
}
function editPop(url, data) {
	parent.popWin("编辑用户", url, data, "1000px", "500px", myWinCallback, "保存成功");
}
function changePasswordPop(url, data) {
	parent.popWin("修改密码", url, data, "1000px", "200px", myWinCallback, "保存成功");
}
function roleChoose(url, data) {
	parent.popWin("选择角色", url, data, "1000px", "600px", myWinCallback, "保存成功", myWinCallback);
}
function releaseLock(url,obj){
	ajaxData(url,null,function(){
		popMsg("已解锁该用户");
		submitForm("/userManager/query?toPageState=back");
	})
}
function lockUser(url,obj){
	ajaxData(url,null,function(){
		popMsg("已锁定该用户");
		submitForm("/userManager/query?toPageState=back");
	});
}

//下拉框初始化获取数据
function tSelectDataInit() {
	var departmentSelect = $('input[name="department"]').val();
	var tSelectOptions = {
		customCallBack : tSelectCustomCallBack1,
		submitCallBack : tSelectSubmitCallBack1,
		cancelCallBack : tSelectClearCallBack1,
		id : 'orgNo',
		pid : 'pOrgNo',
		name : 'orgName',
		value : 'id',
		grade : 3,
		resultType : 'all',
		selectedIds : departmentSelect,
		style : {tMaxHeight : 280}
	};
	$('#departmentSelectShow').tselectInit(null, tSelectOptions);
}

// 下拉框点击确定回调方法
function tSelectSubmitCallBack1(t, d) {
	$('input[name="department"]').val(d.allValue);
}

// 下拉框点击清空回调方法
function tSelectClearCallBack1(t) {
	$('input[name="department"]').val('');
}
// 下拉框自定义方法
function tSelectCustomCallBack1(t) {
	$('.t-select-table').slideUp('fast');
}

//下拉框初始化获取数据
function tSelectDataInitRole() {
	var roleSelect = $('input[name="role"]').val();
	var tSelectOptions = {
		customCallBack : tSelectCustomCallBack,
		submitCallBack : tSelectSubmitCallBack,
		cancelCallBack : tSelectClearCallBack,
		id : 'id',
		pid : '',
		name : 'roleName',
		value : 'id',
		grade : 1,
		resultType : 'children',
		selectedIds : roleSelect,
		//调整单级树下拉框高度(400->280)，参考数据源得出
		style : {tMaxHeight : 280}
	};

	$('#roleSelectShow').tselectInit(null, tSelectOptions);
}

// 下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
	;
	$('input[name="role"]').val(d.value);
}

// 下拉框点击清空回调方法
function tSelectClearCallBack(t) {
	$('input[name="role"]').val('');
}
// 下拉框自定义方法
function tSelectCustomCallBack(t) {
	$('.t-select-table').slideUp('fast');
}
