//@ sourceURL=addPermissionRole.js
$(document).ready(function() {
	// 设定表单校验规则
	$("#roleForm").validate({
		rules : {
			"roleName" : {
				required : true,
				// 后台校验
				remote : {
					url : contextPath + "/userManager/remotePermissionValidate",
					type : "post",
					data : {
						name : function() {
							return $("#name").val();
						}
					}
				},
				maxlength : 50
			},
			"roleDes" : {
				required : true,
				maxlength : 256
			}
		},
		messages : {
			"roleName" : {
				remote : "系统中已存在角色名称！"
			}
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	// 确定
	$("#btnConfirm").bind("click", function() {
		// 每一种直接给父页面值
		// $("#name", parent.document).val("弹出窗口设置的值");
		// 第二种通过父页面的回调函数传弹出窗口的值
		// 拼弹出窗口的值
		if ($("#roleForm").valid()) {
			ajaxSubmitForm("/userManager/addPermissionSave", null, callBack, 0);
		}

	});
	// 关闭
	$("#btnClose").bind("click", function() {
		closeWin();
	});
});
//保存成功
function callBack(data) {
	var jsonObj = {
			result : [ {
				name : "",
				testDate : ""
			}]
		};
	//closeWin("保存成功");
	closeWinCallBack(jsonObj);
	//popMsg("保存成功");
}