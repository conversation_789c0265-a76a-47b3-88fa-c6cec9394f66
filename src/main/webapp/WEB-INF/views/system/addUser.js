//@ sourceURL=addUser.js
$(document).ready(function() {
	// 设定表单校验规则
	$("#userForm").validate({
		rules : {
			"userName" : {
				required : true,
				// 后台校验
				remote : {
					url : contextPath + "/userManager/remoteValidate",
					type : "post",
					data : {
						name : function() {
							return $("#name").val();
						}
					}
				},
				maxlength : 64
			},
			"realName" : {
				required : true,
				maxlength : 64
			},
			"password" : {
				required : true,
				maxlength : 40
			},
			// "telephone" : {
			// 	required : true,
			// 	digits : true,
			// 	// 后台校验
			// 	remote : {
			// 		url : contextPath + "/userManager/remoteValidateTelephone",
			// 		type : "post",
			// 		data : {
			// 			telephone : function() {
			// 				return $("#telephone").val();
			// 			}
			// 		}
			// 	},
			// 	maxlength : 30
			// },
			"phone" : {
				maxlength : 64
			},
			// "mail" : {
			// 	required : true,
			// 	email : true,
			// 	maxlength : 128
			// },
			"fax" : {
				digits : true,
				maxlength : 30
			},
			"age" : {
				digits : true,
				maxlength : 3
			},
			"jobs" : {
				maxlength : 30
			},
			"qq" : {
				digits : true,
				maxlength : 20
			},
			"wechat" : {
				maxlength : 30
			},
			"companyCode" : {
				maxlength : 30
			}
		},
		messages : {
			"userName" : {
				remote : "系统中已存在此账号！"
			}
			// ,
			// "telephone" : {
			// 	remote : "系统中已存在此移动电话！"
			// }
		},
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	var url = contextPath + '/filetempupload';
	$('#up').fileupload({
		url : url,
		dataType : 'json',
		autoUpload : true,
		submit : function(e, data) {
			index = layer.load(1, {
				shade : [ 0.1, '#fff' ]
				// 0.1透明度的白色背景
			});
		},
		done : function(e, data) {
			$.each(data.result, function(index, file) {
				$("#imgField").val(file.fileId);
			});
			layer.close(index);
		}
	});

	$("#btnConfirm").bind("click", function() {
		if ($("#userForm").valid()) {
			$("#password").val($.md5($("#password").val()));
			ajaxSubmitForm("/userManager/addSave", null, callBack, 0);
		}

	});
	// 关闭
	$("#btnClose").bind("click", function() {
		closeWin();
	});
	// 初始化部门下拉框
	tSelectDataInit();
});
//保存成功
function callBack(data) {
	var jsonObj = {
		result : [ {
			name : "",
			testDate : ""
		}]
	};
	closeWinCallBack(jsonObj);
}


//下拉框初始化获取数据
function tSelectDataInit() {
	var departmentSelect = $('input[name="orgId"]').val();
	var tSelectOptions = {
		customCallBack : tSelectCustomCallBack1,
		submitCallBack : tSelectSubmitCallBack1,
		cancelCallBack : tSelectClearCallBack1,
		id : 'orgNo',
		pid : 'pOrgNo',
		name : 'orgName',
		value : 'id',
		grade : 3,
		resultType : 'children',
		inputType: 'radio', // 单选
		selectedIds : departmentSelect,
		style : {tMaxHeight : 280}
	};

	$('#departmentSelectShow').tselectInit(null, tSelectOptions);
}

// 下拉框点击确定回调方法
function tSelectSubmitCallBack1(t, d) {
	$('input[name="orgId"]').val(d.value);
}

// 下拉框点击清空回调方法
function tSelectClearCallBack1(t) {
	$('input[name="orgId"]').val('');
}
// 下拉框自定义方法
function tSelectCustomCallBack1(t) {
	$('.t-select-table').slideUp('fast');
}


// 下拉框点击确定回调方法
function authoritySelectSubmitCallBack(t, d) {
	$('input[name="loginAuthority"]').val(d.value);
}

// 下拉框点击清空回调方法
function authoritySelectClearCallBack(t) {
	$('input[name="loginAuthority"]').val('');
}
// 下拉框自定义方法
function authoritySelectCustomCallBack(t) {
	$('.t-select-table').slideUp('fast');
}