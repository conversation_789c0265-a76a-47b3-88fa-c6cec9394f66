<%--
/***************************************************************
* 程序名 : orgslist.jsp
* 日期  :  2015-7-7
* 作者  :  朱继刚
* 模块  :  系统管理
* 描述  :  组织机构
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
</head>
<body>

<script type="text/javascript">
$(document).ready(function() {
	init();
});
var TANGER_OCX_OBJ;
var TANGER_OCX_bDocOpen = !1;
function init()
{
	TANGER_OCX_OBJ = document.getElementById("TANGER_OCX");
	//ObjectDisplay(!0);
    //TANGER_OCX_OBJ.AddDomainToTrustSite(document.domain),

    TANGER_OCX_OBJ.AddDocTypePlugin(".pdf","PDF.NtkoDocument","*******","ntkooledocall.cab",51,true);	
	//TANGER_OCX_OBJ.CreateNew("Word.Document");
    TANGER_OCX_OBJ.BeginOpenFromURL ("http://localhost:8080/edm/office/law.pdf");
   //TANGER_OCX_OBJ.BeginOpenFromURL("http://localhost:8080/edm/office/model.docx");
    TANGER_OCX_bDocOpen = !0;
   		
}
function protectrevision() {
	
    TANGER_OCX_OBJ.ActiveDocument.TrackRevisions = !TANGER_OCX_OBJ.ActiveDocument.TrackRevisions;
}
function officeToolBar() {
    TANGER_OCX_OBJ.toolbars = !TANGER_OCX_OBJ.toolbars
}
function objSaveAsPDFFile(a) {
	TANGER_OCX_OBJ.ShowDialog(3);
}
function setvisibleinfo(a) {
	  document.getElementById("infovalue").innerHTML = a
	}
</script>



<button onclick="protectrevision();">强制留痕模式</button>
<button onclick="objSaveAsPDFFile('d:\\test.pdf');">导出PDF</button>
<button onclick="officeToolBar();">显示功能区</button>
<button onclick='TANGER_OCX_OBJ.SaveToURL("http://localhost:8080/edm/orgmgr/upload","name","","board.docx",0)'>保存</button>
<!-- 
<form id="myform" method="POST" enctype="multipart/form-data"
      action="/edm/orgmgr/upload">
    Ftype:<input name="type"  value="word"/>
workflow:<input name="workflow" value="node"/>
select file:<input type="file" name="name"/><br /> <br /> <input type="submit"
                                                     value="Upload"> Press here to upload the file!
</form> -->
<div class="alert alert-danger">
<span>信息:</span><span id="infovalue"></span>
</div>

<div id="ocxobject" STYLE="HEIGHT:800PX" >
	<script src="static/lib/office/officecontrol/ntkoofficecontrol.js"></script>
</div>


</body>
</html>