<%--
/***************************************************************
* 程序名 : addUser.jsp
* 日期  :  2015-7-17
* 作者  :  zouxl
* 模块  :  用户管理新增页面
* 描述  :  用户管理新增页面
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
<e:js/>
</head>
<body style="padding-top:0px;">
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 新增门户登录角色
        </div>
        <div class="panel-body">
            <form:form class="form-horizontal" action="/userManager/editRole" modelAttribute="editRoleDto" id="roleForm">
            <form:hidden path="id" />
                <div class="form-group">
                    <label class="col-md-1 control-label">权限名称</label>
                    <div class="col-md-1">
                        <form:input cssClass="form-control" path="roleName" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">权限描述</label>
                    <div class="col-md-1">
                        <form:textarea cssClass="form-control" path="roleDes" rows="5"/>
                    </div>
                </div>                                                                                                
                <div class="form-group">
                    <div class="col-md-offset-1 col-md-2" align="center">
                        <sec:authorize access="hasAuthority('RES_LOGIN_AUTHORITY_INIT_AUTHORITY_3')" >
                            <input type="button" id="btnConfirm" class="btn btn-primary" value="保存" />
                        </sec:authorize>
                        <input type="button" id="btnClose" class="btn btn-default" value="关闭" />
                    </div>
                </div>
            </form:form>
        </div>
    </div>
  
</body>
</html>
