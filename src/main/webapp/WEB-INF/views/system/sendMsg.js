var teaSelectOptions = {
    id: 'codeValue',
    name: 'codeName',
    value: 'codeValue',
    grade: 1,
    resultType: 'all',
    allCheck: true,//是否显示全选按钮
    style: {},
    customCallBack: tSelectCustomCallBack,
    submitCallBack: tSelectSubmitCallBack
};
$(document).ready(function () {
    $("#sendSmsForm").validate({
        rules: {
            "phones": {
                required: true,
                maxlength: 1000
            },
            "content": {
                required: true,
                maxlength: 512
            },
        },
        tooltip_options: {
            "_all_": {
                trigger: 'focus'
            }
        }
    });
    //导出
    $('#btnSend').click(function () {
        if ($("#sendSmsForm").valid()) {
            ajaxData("/sendSms/sendSms", $("#sendSmsForm").serialize(), function (res) {
                
                if (res) {
                    popMsg("发送成功！")
                } else {
                    popMsg("发送失败！")
                }
            })
        }
    });
    $('#btnSendByUser').click(function () {
        var content = "<div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>用户类型：</label>" +
            "                    <div class=\"col-sm-6\">" +
            "                        <input id=\"personType\" type=\"text\" class=\"t-select\" json-data='"+ personTypeList +"' placeholder=\"请选择用户类型\"/>" +
            "                        <input name=\"personType\" type=\"hidden\" />" +
            "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>部门(包含)：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                        <input id=\"sendMessageOrgName\" type=\"text\" style='width: 300px' placeholder=\"请填写部门，多个部门用英文逗号(,)分隔\"/>" +
            "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>部门(不包含：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                        <input id=\"sendMessageNotOrgName\" type=\"text\" style='width: 300px' placeholder=\"请填写部门，多个部门用英文逗号(,)分隔\"/>" +
            "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>职务(包含)：</label>" +
            "                    <div class=\"col-sm-6\">" +
            "                        <input id=\"personPost\" type=\"text\" class=\"t-select\" json-data='"+ personPostList +"' placeholder=\"请选择职务\"/>" +
            "                        <input name=\"personPost\" type=\"hidden\" />" +
            "                    </div></div>"+
            // " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            // "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>职务(不包含)：</label>" +
            // "                    <div class=\"col-sm-6\">" +
            // "                        <input id=\"personPostNotIncluded\" type=\"text\" class=\"t-select\" json-data='"+ personPostList +"' placeholder=\"请选择职务\"/>" +
            // "                        <input name=\"personPostNotIncluded\" type=\"hidden\" />" +
            // "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>岗位(包含)：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                        <input id=\"sendMessageJobName\" type=\"text\" style='width: 300px' placeholder=\"请填写岗位，多个岗位用英文逗号(,)分隔\"/>" +
            "                    </div></div>"+
            // " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            // "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>岗位(不包含)：</label>" +
            // "                    <div class=\"col-sm-8\">" +
            // "                        <input id=\"sendMessageNotJobName\" type=\"text\" style='width: 300px' placeholder=\"请填写岗位，多个岗位用英文逗号(,)分隔\"/>" +
            // "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>公司代码(包含)：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                        <input id=\"sendMessageComapnyCode\" type=\"text\" style='width: 300px' placeholder=\"请填写公司代码，多个公司代码用英文逗号(,)分隔\"/>" +
            "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>公司代码(不包含)：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                        <input id=\"sendMessageNotComapnyCode\" type=\"text\" style='width: 300px' placeholder=\"请填写公司代码，多个公司代码用英文逗号(,)分隔\"/>" +
            "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>注册时间：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                        <input id=\"createDateAfter\" type=\"text\" name=\"createDateAfter\" cssClass=\"jeinput form-control \" readonly=\"true\" autocomplete=\"off\" style='width: 300px' placeholder=\"请选择用户注册时间\"/>" +
            "                    </div></div>"+
            " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
            "                    <label class=\"col-sm-3 control-label\" style='text-align: right'>短信内容：</label>" +
            "                    <div class=\"col-sm-8\">" +
            "                       <textarea id=\"remindContent\" class=\"teach-intro-textarea\" style='width: 450px;height: 300px'></textarea>"+
            "                    </div></div>"+
            "</div>";
        var layerIndex = layer.open({
            title:'短信提醒',
            content: content,
            btn: ['确定', '关闭'],
            area:["660px","660px"],
            yes: function(index){
                if ($("#remindContent").val() != null && $("#remindContent").val() != ''){
                    var param = {
                        createDateAfter:$("#createDateAfter").val(),
                        remindContent: $("#remindContent").val(),
                        personType: $('input[name="personType"]').val(),
                        personPost: $('input[name="personPost"]').val(),
                        personPostNotIncluded: $('input[name="personPostNotIncluded"]').val(),
                        orgName: $('#sendMessageOrgName').val(),
                        notOrgName: $('#sendMessageNotOrgName').val(),
                        jobName: $('#sendMessageJobName').val(),
                        notJobName : $("#sendMessageNotJobName").val(),
                        companyCode: $("#sendMessageComapnyCode").val(),
                        notComapnyCode : $("#sendMessageNotComapnyCode").val(),
                    }
                    ajaxData("/sendSms/sendSmsByUser", param, function (res) {
                        addLoading();
                        var myVar = window.setInterval(function() {
                            popMsg("短信发送成功!")
                            clearInterval(myVar)
                        },1000);
                    })
                }else {
                    popMsg("发送失败，短信提醒内容为必填项！")
                }
            },
            no:function (index,) {
                layer.close(layerIndex);
            }
        })

        //$('#applyPerson').tselectInit(null, tSelectOptions);
        $('#personType').tselectInit(null, teaSelectOptions);
        $('#personPost').tselectInit(null, teaSelectOptions);
        $('#personPostNotIncluded').tselectInit(null, teaSelectOptions);
        jeDate("#createDateAfter", {
            festival: true,
            minDate: "1900-01-01", //最小日期
            maxDate: "2099-12-31", //最大日期
            method: {
                choose: function(params) {
                }
            },
            format: "YYYY-MM-DD hh:mm"
        });
    });
    dateInit()
    //查询
    $('#btnQuery').click(function () {
        search();
    });
    //清空条件
    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        dateInit();
        search();
    });
 })

function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

//姓名
function realName(data) {
    return data.realName ? data.realName : '--';
}

//手机号
function tel(data) {
    return data.tel;
}

//短信内容
function message(data) {
    return data.message;
}

//发送时间
function sendTime(data) {
    return data.sendTime;
}

function search() {
    ajaxTableQuery("tableAll", "/sendSms/querySmsList",
        $("#queryForm").formSerialize());
    isSearchFlag = true;
}

function dateInit() {
    dataRangePickerInit($('#sendTime'), null, null, function () {
    }, function () {
    });
}

function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}