//@ sourceURL=roleChoose.js
$(document).ready(function() {
			
		});

function addRole(url) {
	var userId = $("#userId").html()
	url = url+"&userId="+userId;
	popConfirm("确定为用户分配此角色？", addConfirmCallBack, url);
}
function addConfirmCallBack(url) {
	movePage(url);
}
function delRole(url) {
	var userId = $("#userId").html()
	url = url+"&userId="+userId;
	popConfirm("确定删除此角色？", delConfirmCallBack, url);
}
function delConfirmCallBack(url) {
	movePage(url);
}