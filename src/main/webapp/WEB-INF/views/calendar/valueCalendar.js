//@ sourceURL=valueCalendar.js
var pageNo = 1;
var total = 0;
var pageSize = 10;
var bol = true;
$(function(){
    var copyWriter = $("#copyWriterSearch", parent.document).val();
    var yearSelect = $("#yearSelect" , parent.document).val();
    var monthSelect = $("#monthSelect" , parent.document).val();
	var param = {
		"pageNo" : pageNo ,
		"pageSize" : pageSize
	}
	if(yearSelect!=undefined&&yearSelect!=null&&yearSelect!=""){
        param["year"] = new Date(yearSelect);
	}
	if(yearSelect!=undefined&&yearSelect!=null&&yearSelect!=""&&monthSelect!=undefined&&monthSelect!=null&&monthSelect!=""){
        param["month"] = new Date(yearSelect,monthSelect);
	}
    if(copyWriter!=undefined&&copyWriter!=null&&copyWriter!=""){
        param["copyWriter"] = copyWriter;
    }
	//加载图片数据
	ajaxData("/calendar/getValueCalendarList", param, imgInit);
	pageNo += 1;
	window.parent.parent.onscroll=function(){
		//获取滚动条距顶高度----↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
//		var v = $(window.parent.parent.document.documentElement).scrollTop() || $(window.parent.parent.document.body).scrollTop();
		//--------------------↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
		var viewH,contentH,scrollTop,scrollBottom=2;
		try{
			var viewH =$(window.parent.parent).height();//可见高度
	        var contentH =$(window.parent.parent.document.body).get(0).scrollHeight;//内容高度
	        var scrollTop =$(window.parent.parent.document).scrollTop();//滚动高度
			var scrollBottom = contentH - viewH - scrollTop;
		}catch(e){}
		if(scrollBottom < 1&& parent.reHeightFrame == '#theme'){
			if(bol){
				if((pageNo-1)*pageSize < total){
                    var param = {
                        "pageNo" : pageNo ,
                        "pageSize" : pageSize
                    }
                    var copyWriter = $("#copyWriterSearch", parent.document).val();
                    var yearSelect = $("#yearSelect" , parent.document).val();
                    var monthSelect = $("#monthSelect" , parent.document).val();
                    if(yearSelect!=undefined&&yearSelect!=null&&yearSelect!=""){
                        param["year"] = new Date(yearSelect);
                    }
                    if(yearSelect!=undefined&&yearSelect!=null&&yearSelect!=""&&monthSelect!=undefined&&monthSelect!=null&&monthSelect!=""&&monthSelect!="00"){
                        param["month"] = new Date(yearSelect,monthSelect);
                    }
                    if(copyWriter!=undefined&&copyWriter!=null&&copyWriter!=""){
                        param["copyWriter"] = copyWriter;
                    }
					//加载图片数据
					ajaxData("/calendar/getValueCalendarList", param, imgInit);
					pageNo += 1;
				}
			}
		}
	}
	
});
//将图片数据填入页面
function imgInit(data){
	bol = false;
	total = data['total'];
	$.each(data.data, function(index, obj){
		var copyWriter = obj['copyWriter'] ? obj['copyWriter'] : '';
		var quote = obj['quote'] ? obj['quote']: '';
		var calendarDate = obj['calendarDate'] ? obj['calendarDate'] : '';
		var detailParam = {};
        detailParam['data'] =obj;
        // var detailParam = obj;
        console.log(detailParam)
        var detailTpl = template('calendarDateDetailTemp', detailParam);
        $("#showPicture").append(detailTpl);
		// var str1 = 	'<div class="col-md-4 col top10px"><div class="myGutter">'
        //     + 	'	<div class="top5px" style="text-align: center;">'
        //     + 	'		<span class="labelSpan" title="日期">'+ '日期:' +'</span>'
        //     + 	'		<span title="'+ (obj['calendarDate'] ? obj['calendarDate'] : '') +'">'+ calendarDate +'</span>'
        //     + 	'	</div>'
		// 		 + 	'	<div class="black">'
		// 		 + 	'		<img alt="图片加载失败" src="'+ obj['webBackground'] +'" onclick="valueCalendarEdit(\''+obj['id']+'\')" class="valueCalendarEdit" width="100%" height="168">'
		// 		 + 	'	</div>'
		// 		 + 	'	<div class="top5px">'
        //     + 	'		<span class="labelSpan " title="法文">'+ '法文:' +'</span>'
		// 		 + 	'		<span class="abc" title="'+ (obj['copyWriter'] ? obj['copyWriter'] : '') +'">'+ copyWriter +'</span>'
		// 		 + 	'	</div>'
		// 		 + 	'	<div class="top5px">'
        //     + 	'		<span class="labelSpan" title="金句名言">'+ '金句名言：' +'</span>'
		// 		 + 	'		<span class="abc" title="'+ obj['quote'] +'">'+ quote +'</span>'
		// 		 + 	'	</div>'
		// 		 + 	'	<div class="top5px pLine row leftAndRight0px">'
        //     +  '		<sec:authorize access="hasAuthority(\'RES_APPLICATION_VALUE_SAVE\')" >'
        //     +	'			<div class="col-md-6" ><input style="float:right;margin-left:20px;" type="button" onclick="del.call(this,\''+obj['id']+'\')" value="删除" class="btn btn-default" /></div>'
        //     +  '		</sec:authorize>'
        //     +  '		<sec:authorize access="hasAuthority(\'RES_APPLICATION_VALUE_DELETE\')" >'
        //     +	'			<div class="col-md-6" ><input style="float:left;margin-right:20px;" type="button"  onclick="valueCalendarEdit(\''+obj['id']+'\')" value="编辑" class="btn btn-primary" /></div>'
        //     +  '		</sec:authorize>'
        //     + 	'	</div>'
		// 		 + 	'</div></div>';
			// $(str1).appendTo("#showPicture");
	})
	bol = true;
}

//删除
function del(id){
	// var div = $(this).parent().parent().parent().parent().parent();
	window.parent.popConfirm("确认删除?",function(){
		var param = {
				"id" : id
		}
		ajaxData("/calendar/valueCalendarDelete",param,function(){			// layer.msg('删除成功!');
            window.parent.layer.msg('删除成功(￣▽￣＃) = ﹏﹏');
            window.parent.themeRefresh();
			// div.remove();
		});
    })
}

function popPreview(flag,data) {
    console.log('执行')
	var jsonData = JSON.parse(data.replace(/\n/g,"\\n").replace(/\r/g,"\\r"))
    var div = window.parent.document.getElementById("previewDiv");
    console.log(flag,window.parent.document.getElementById("sidebar").clientHeight,window.parent.document.getElementById("sidebar") - 29 + 'px')
    window.parent.document.getElementById("previewDiv").style.height = window.parent.document.getElementById("sidebar").clientHeight - 29 + 'px'
    var fadeDiv = window.parent.document.getElementById("fade");
    var webImg1 =  window.parent.document.getElementById("webImgBg");
    var appImg =  window.parent.document.getElementById("appimg");
	webImg1.src = jsonData.webBackground
	appImg.src = jsonData.appBackground
    window.parent.document.getElementById("fawen1").innerHTML = jsonData.copyWriter
    window.parent.document.getElementById("fawen2").innerHTML = jsonData.copyWriter
    window.parent.document.getElementById("jinjv1").innerHTML = jsonData.quote
    window.parent.document.getElementById("jinjv2").innerHTML = jsonData.quote
    var year = jsonData.calendarDate.split("-");
	var mouth = convertToChinese(year[1]);
	var week = getWeekDate(jsonData.calendarDate)
	var yearList = jsonData.yinLiDate.split("月");
    window.parent.document.getElementById("nianyue1").innerHTML = mouth + '月 / ' + year[0]
    window.parent.document.getElementById("nianyue2").innerHTML = mouth + '月 / ' + year[0]
    window.parent.document.getElementById("riqi1").innerHTML = year[2]
    window.parent.document.getElementById("riqi2").innerHTML = year[2]
	if(jsonData.solarFestival == ''){
        window.parent.document.getElementById("week1").innerHTML = week + ' / ' + yearList[1] + ' ' + jsonData.solarTerm  + jsonData.lunarFestival
        window.parent.document.getElementById("week2").innerHTML = week + ' / ' + yearList[1] + ' ' + jsonData.solarTerm  + jsonData.lunarFestival
	}else if(jsonData.lunarFestival == ''){
        window.parent.document.getElementById("week1").innerHTML = week + ' / ' + yearList[1] + ' ' + jsonData.solarTerm + jsonData.solarFestival
        window.parent.document.getElementById("week2").innerHTML = week + ' / ' + yearList[1] + ' ' + jsonData.solarTerm + jsonData.solarFestival
	}else{
        window.parent.document.getElementById("week1").innerHTML = week + ' / ' + yearList[1] + ' ' + jsonData.solarTerm + jsonData.solarFestival + '·' + jsonData.lunarFestival
        window.parent.document.getElementById("week2").innerHTML = week + ' / ' + yearList[1] + ' ' + jsonData.solarTerm + jsonData.solarFestival + '·' + jsonData.lunarFestival
	}
	if(1==flag){
		div.style.display = "block";
        fadeDiv.style.display = "block";
	}else{
		div.style.display = "none";
        fadeDiv.style.display = "none"
	}
}
function getWeekDate(today) {
    var now = new Date(today);
    var day = now.getDay();
    var weeks = new Array(
        "周日",
        "周一",
        "周二",
        "周三",
        "周四",
        "周五",
        "周六"
    );
    var week = weeks[day];
    return week;
}
function convertToChinese(num) {
    var N = [
        "一",
        "二",
        "三",
        "四",
        "五",
        "六",
        "七",
        "八",
        "九",
        "十",
        "十一",
        "十二",
    ];
    var str = Number(num);
    var C_Num = N[str - 1];
    return C_Num;
}
//修改
function valueCalendarEdit(id){
	var param = {
			id : id
	}
	window.parent.popWin("价值历详情","/calendar/valueCalendarEditInit",param,"80%","80%",null);
}
