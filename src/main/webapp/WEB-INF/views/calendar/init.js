//@ sourceURL=init.js
var reHeightFrame;
var reHeightTag;
var reHeightTimer;
var reHeightLinkTag;
var loadFrameStatus = new Array();

var copyWriterSearch = $("#copyWriterSearch")
var yearSelect = $("#yearSelect")
var monthSelect = $("#monthSelect")

var theme = $("#theme");
var message = $("#message");
var advertisement = $("#advertisement");

var themeContent = (theme[0] || {}).contentWindow;

$(document).ready(function(){
	//显示第一个tab页
	$('ul.nav-tabs a:first').tab('show');
	// tab页初始化
	tabPageInit();
    $("#valueSearch").bind('click',valueSearch);
    $("#valueCalendarSave").bind('click',valueCalendarSave);
	$("#clearSearch").bind('click',clearSearch);
})

function clearSearch(){
    $("#copyWriterSearch").val("");
    $("#yearSelect").val(2021)
    $("#monthSelect").val("")
}

function valueSearch(){
    window.themeRefresh();
}

// tab页初始化
function tabPageInit(){
	reHeightLinkTag = $('a[href="javascript:movePage(\'/calendar/valueCalendarInit\')"]');
	reHeightFrame = '#theme';
	// 调整iframe高度定时器
	reHeightInteval();
	
	var tab1 = $('a[href="#tab1"]');
	if (tab1.parent().hasClass('active')) {
		theme.attr('src', contextPath + "/calendar/valueCalendarInit");
	}
	// tab页点击事件
	$('a[data-toggle="tab"]').on('click', function(e) {
		iframeObj = e.target;
		if (iframeObj.hash == "#tab1") {
			reHeightFrame = '#theme';
		}
	});
}

function themeRefresh(){
    console.log("themeRefresh")
	theme.attr('src', contextPath + "/calendar/valueCalendarInit");
}
//调节iframe高度
function adjust_R_e_g_u_l_atoryHeight(frameLabel) {
	if (Object.prototype.toString.call(frameLabel) === "[object String]") {
		frameLabel = '#' + frameLabel.replace(/\#/g, "");
		var frame = $(frameLabel);
		if (frame.length > 0) {
			if (frame[0].contentWindow.$) {
				var frameHeight = frame[0].contentWindow.$("body").height();
				if (frameHeight < 500) {
					frame.height(500);
				} else {
					frame.height(frameHeight + 50);
				}
			}
		}
	}
}
//调整iframe高度定时器
function reHeightInteval() {
	if (!reHeightTimer) {
		reHeightTag = true;
		reHeightTimer = setInterval(function() {
			if (reHeightTag) {
				adjust_R_e_g_u_l_atoryHeight(reHeightFrame);
			} else {
				closeReHeightInteval();
			}
		}, 500);
	}
}
//关闭调整iframe高度定时器
function closeReHeightInteval() {
	clearInterval(reHeightTimer);
	reHeightTimer = false;
}
// 去掉索引返回高亮标签
function delSolrHighLightStr(title) {
	if (title) {
		title = title.replaceAll('<font color=\"#FF0000\">', '');
		title = title.replaceAll('</font>', '');
	}
	return title || '';
}
// 把半角?改成全角？
function formatParams(str) {
	str = str.replace(/\?/g, "？");
	return str;
}
//增加价值历
function valueCalendarSave(){
	parent.popWin("价值历详情","/calendar/valueCalendarEditInit","","80%","80%",null);
}
function popPreview(flag) {

    var div = document.getElementById("previewDiv");
    var fadeDiv = document.getElementById("fade");
    if(1==flag){
        div.style.display = "block";
        fadeDiv.style.display = "block"
    }else{
        div.style.display = "none";
        fadeDiv.style.display = "none"
    }
}