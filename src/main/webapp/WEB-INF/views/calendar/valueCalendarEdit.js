//@ sourceURL=valueCalendarEdit.js
var ruleBaseUrl
$(function(){
    $("#valueCalendarSave").on("click",valueCalendarSave);
    $("#cancel").on("click",cancel);
	//校验条件
	$("#valueCalendarForm").validate({
		rules : {
            "calendarDate" : {
                required : true
            },
            "copyWriter" : {
                required : true,
                maxlength : 120
            },
            "quote" : {
                required : true,
                maxlength : 100
            }
		}
	});


	init();

});
//控件初始化
function init(){
    ruleBaseUrl = $("#ruleBaseUrl").val();
    // console.log(ruleBaseUrl)
    var  dateSelect= new Date($('#dateSelect').val()) ;
    var now = new Date()
    //今天以前的日期不可编辑日期
    if(dateSelect.getFullYear()<now.getFullYear()||(dateSelect.getFullYear()==now.getFullYear()&&dateSelect.getMonth()<now.getMonth())||(dateSelect.getFullYear()==now.getFullYear()&&dateSelect.getMonth()==now.getMonth()&&dateSelect.getDate()<=now.getDate())){
        $('#dateSelect').attr("disabled",true);
    }
	//上传图片
	$('.newUp').uploadPreview({});
    $('.newWebUp').uploadPreview2({});
	generateFileId();
    generateFileId2();
	//删除图片
	$("#delImg").on("click",clearImg);
    $("#delWebImg").on("click",clearWebImg);
	//如果是修改页面通过url读取图片
	if($("#appBackground").val()){
		$("#imgId").css("display","block");
        $("#addImg").css("display","none");
        $("#ImgPr").attr("src",$("#appBackground").val());
        // $("#oldUrl").val($("#appBackground").val());
	}
    if($("#webBackground").val()){
        $("#imgWebId").css("display","block");
        $("#addWebImg").css("display","none");
        $("#ImgWebPr").attr("src",$("#webBackground").val());
        // $("#oldUrl2").val($("#webBackground").val());
    }
    dateChange();
}
function isNotNull(obj){
    var flag = false;
    if(obj!=undefined&&obj!=null&&obj!=""){
        flag = true;
    }
    return flag;
}

function dateChange(e){
    // console.log("dataChange");
    this.generalRule(e);
    this.shjysRule(e);
    this.hzbRule(e);
    this.kcbRule(e);
    this.szjysRule(e);
    this.szbRule(e);
    this.zxbRule(e);
    this.cybRule(e);
}
function generalRule(e){
    var calendarDate = $("#dateSelect").val()
    var generalRuleId = $("#generalRuleId").val();
    var generalArticleId = $("#generalArticleId").val();
    if(isNotNull(generalRuleId)&&isNotNull(generalArticleId)&&isNotNull(calendarDate)){
        $("#generalRuleUrl").html(ruleBaseUrl+"/"+generalRuleId+"/"+generalArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#generalRuleUrl").html("");
    }
}
function shjysRule(e){
    var calendarDate = $("#dateSelect").val()
    var shjysRuleId = $("#shjysRuleId").val();
    var shjysArticleId = $("#shjysArticleId").val();
    if(isNotNull(shjysRuleId)&&isNotNull(shjysArticleId)&&isNotNull(calendarDate)){
        $("#shjysRuleUrl").html(ruleBaseUrl+"/"+shjysRuleId+"/"+shjysArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#shjysRuleUrl").html("");
    }
}

function hzbRule(e){
    var calendarDate = $("#dateSelect").val();
    var hzbRuleId = $("#hzbRuleId").val();
    var hzbArticleId = $("#hzbArticleId").val();
    if(isNotNull(hzbRuleId)&&isNotNull(hzbArticleId)&&isNotNull(calendarDate)){
        $("#hzbRuleUrl").html(ruleBaseUrl+"/"+hzbRuleId+"/"+hzbArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#hzbRuleUrl").html("");
    }
}

function kcbRule(e){
    var calendarDate =  $("#dateSelect").val();
    var kcbRuleId = $("#kcbRuleId").val();
    var kcbArticleId = $("#kcbArticleId").val();
    if(isNotNull(kcbRuleId)&&isNotNull(kcbArticleId)&&isNotNull(calendarDate)){
        $("#kcbRuleUrl").html(ruleBaseUrl+"/"+kcbRuleId+"/"+kcbArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#kcbRuleUrl").html("");
    }
}

function szjysRule(e){
    var calendarDate =  $("#dateSelect").val();
    var szjysRuleId = $("#szjysRuleId").val();
    var szjysArticleId = $("#szjysArticleId").val();
    if(isNotNull(szjysRuleId)&&isNotNull(szjysArticleId)&&isNotNull(calendarDate)){
        $("#szjysRuleUrl").html(ruleBaseUrl+"/"+szjysRuleId+"/"+szjysArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#szjysRuleUrl").html("");
    }
}

function szbRule(e){
    var calendarDate =  $("#dateSelect").val();
    var szbRuleId = $("#szbRuleId").val();
    var szbArticleId = $("#szbArticleId").val();
    if(isNotNull(szbRuleId)&&isNotNull(szbArticleId)&&isNotNull(calendarDate)){
        $("#szbRuleUrl").html(ruleBaseUrl+"/"+szbRuleId+"/"+szbArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#szbRuleUrl").html("");
    }
}

function zxbRule(e){
    var calendarDate =  $("#dateSelect").val();
    var zxbRuleId = $("#zxbRuleId").val();
    var zxbArticleId = $("#zxbArticleId").val();
    if(isNotNull(zxbRuleId)&&isNotNull(zxbArticleId)&&isNotNull(calendarDate)){
        $("#zxbRuleUrl").html(ruleBaseUrl+"/"+zxbRuleId+"/"+zxbArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#zxbRuleUrl").html("");
    }
}

function cybRule(e){
    var calendarDate =  $("#dateSelect").val();
    var cybRuleId = $("#cybRuleId").val();
    var cybArticleId = $("#cybArticleId").val();
    if(isNotNull(cybRuleId)&&isNotNull(cybArticleId)&&isNotNull(calendarDate)){
        $("#cybRuleUrl").html(ruleBaseUrl+"/"+cybRuleId+"/"+cybArticleId+"/"+calendarDate.replace(/-/g,'')+".html");
    }else {
        $("#cybRuleUrl").html("");
    }
}
function cancel(){
	popConfirm("您还未保存，是否确定放弃保存",function(){
		closeWin();
	});
}
//都不为空返回1
//一个为空一个不为空返回9
//两个都为空返回0
function checkGroup(obj1,obj2){
    if(isNotNull(obj1)&&isNotNull(obj2)){
        return 1;
    }
    if((isNotNull(obj1)&&!isNotNull(obj2))||(!isNotNull(obj1)&&isNotNull(obj2))){
        return 9
    }
    return 0;
}
//保存操作
function valueCalendarSave(){
    //增加校验法规信息
    //首先，必须成对出现
    //对数，不能超过3对
    var generalRuleId = $("#generalRuleId").val();
    var generalArticleId = $("#generalArticleId").val();
    var shjysRuleId = $("#shjysRuleId").val();
    var shjysArticleId = $("#shjysArticleId").val();
    var hzbRuleId = $("#hzbRuleId").val();
    var hzbArticleId = $("#hzbArticleId").val();
    var kcbRuleId = $("#kcbRuleId").val();
    var kcbArticleId = $("#kcbArticleId").val();
    var szjysRuleId = $("#szjysRuleId").val();
    var szjysArticleId = $("#szjysArticleId").val();
    var szbRuleId = $("#szbRuleId").val();
    var szbArticleId = $("#szbArticleId").val();
    var zxbRuleId = $("#zxbRuleId").val();
    var zxbArticleId = $("#zxbArticleId").val();
    var cybRuleId = $("#cybRuleId").val();
    var cybArticleId = $("#cybArticleId").val();
    var sumRule = 0;
    if(checkGroup(generalRuleId,generalArticleId)==1){
        sumRule++;
    }else if(checkGroup(generalRuleId,generalArticleId)==9){
        layer.msg("通用法规ID与通用条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(shjysRuleId,shjysArticleId)==1){
        sumRule++;
    }else if(checkGroup(shjysRuleId,shjysArticleId)==9){
        layer.msg("上交所法规ID与上交所条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(hzbRuleId,hzbArticleId)==1){
        sumRule++;
    }else if(checkGroup(hzbRuleId,hzbArticleId)==9){
        layer.msg("沪主板法规ID与沪主板条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(kcbRuleId,kcbArticleId)==1){
        sumRule++;
    }else if(checkGroup(kcbRuleId,kcbArticleId)==9){
        layer.msg("科创板法规ID与科创板条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(szjysRuleId,szjysArticleId)==1){
        sumRule++;
    }else if(checkGroup(szjysRuleId,szjysArticleId)==9){
        layer.msg("深交所法规ID与深交所条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(szbRuleId,szbArticleId)==1){
        sumRule++;
    }else if(checkGroup(szbRuleId,szbArticleId)==9){
        layer.msg("深主板法规ID与深主板条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(zxbRuleId,zxbArticleId)==1){
        sumRule++;
    }else if(checkGroup(zxbRuleId,zxbArticleId)==9){
        layer.msg("中小板法规ID与中小板条文ID必须都为空或都不为空!");
        return;
    }
    if(checkGroup(cybRuleId,cybArticleId)==1){
        sumRule++;
    }else if(checkGroup(cybRuleId,cybArticleId)==9){
        layer.msg("创业板法规ID与创业板条文ID必须都为空或都不为空!");
        return;
    }
    if(sumRule>3){
        layer.msg("法规条文数量配置不能超过3对！");
        return;
    }
	if($("#valueCalendarForm").valid()){
		if(($("#fileId").val() || $("#appBackground").val())&&($("#fileId2").val() || $("#webBackground").val())){
			ajaxSubmitForm("/calendar/valueCalendarSave", "", function(data){
				layer.msg("保存成功(￣▽￣＃) = ﹏﹏");
				window.parent.parent.themeRefresh();
				setTimeout(function(){
					closeWin();
				},2000);
			});
		}else{
			layer.msg("请选择图片!");
		}
	}
}


//-----------------------以下内容为保存图片相关代码--------------------------------
//上传app图片的清除
function clearImg(){
	popConfirm("确认删除", clear, null);	
}
//上传web图片的清除
function clearWebImg(){
    popConfirm("确认删除", clearWeb, null);
}
//清除图片回调函数
function clearWeb(){
    $("#imgWebId").css("display","none");
    if(document.getElementById("upWeb")){
        $("#upWeb").val("");
    }
    $("#fileId2").val("");
    $("#webBackground").val("");
    $("#addWebImg").css("display","block");
}

//清除图片回调函数
function clear(){
	$("#imgId").css("display","none");
	if(document.getElementById("up")){
		$("#up").val("");
	}
	$("#fileId").val("");
    $("#appBackground").val("");
	$("#addImg").css("display","block");
}
//上传文件到临时文件，并生成临时fileId
function generateFileId(){
	var url = contextPath + '/filetempupload';
	$('.newUp').fileupload({
		url : url,
		dataType : 'json',
		autoUpload: true,
		done : function(e, data) {
			$.each(data.result,function(index,file) {
				ImgType = ["gif", "jpeg", "jpg", "bmp", "png"];
				fileType = file.fileContentType;
				type = fileType.substring(fileType.indexOf('/')+1, fileType.length);
				if("jpeg"==type || "gif"==type || "jpg"==type || "bmp"==type || "png"==type){
                    $("#fileId").val(file.fileId);
                    layer.msg("图片上传完成！");
				}
			});
		}
	});
}
//上传文件到临时文件，并生成临时fileId
function generateFileId2(){
    var url = contextPath + '/filetempupload';
    $('.newWebUp').fileupload({
        url : url,
        dataType : 'json',
        autoUpload: true,
        done : function(e, data) {
            $.each(data.result,function(index,file) {
                ImgType = ["gif", "jpeg", "jpg", "bmp", "png"];
                fileType = file.fileContentType;
                type = fileType.substring(fileType.indexOf('/')+1, fileType.length);
                if("jpeg"==type || "gif"==type || "jpg"==type || "bmp"==type || "png"==type){
                    $("#fileId2").val(file.fileId);
                    layer.msg("图片上传完成！");
                }
            });
        }
    });
}

$.fn.extend({
    uploadPreview: function (opts) {
        var _self = this,
            _this = $(this);

        opts = jQuery.extend({
            Img: "ImgPr",
            Width: 400,
            Height: 250,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {}
        }, opts || {});
        
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
                       
            $("#imgId").css("display","block");
            $("#addImg").css("display","none");
            return url
        };

        _this.change(function () {
            if (this.value) {
                if (!RegExp("\.(" + opts.ImgType.join("|") + ")$", "i").test(this.value.toLowerCase())) {
                	layer.msg("选择文件错误,图片类型必须是" + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false;
                }
                $.browser=new Object(); 
                $.browser.msie = /msie/.test(navigator.userAgent.toLowerCase()); 
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img).attr('src', _self.getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj.parent("div").css({
                            'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                            'width': opts.Width + 'px',
                            'height': opts.Height + 'px'
                        });
                        div.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src', _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    },
    uploadPreview2: function (opts) {
        var _self = this,
            _this = $(this);

        opts = jQuery.extend({
            Img: "ImgWebPr",
            Width: 400,
            Height: 250,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {}
        }, opts || {});

        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }

            $("#imgWebId").css("display","block");
            $("#addWebImg").css("display","none");
            return url
        };

        _this.change(function () {
            if (this.value) {
                if (!RegExp("\.(" + opts.ImgType.join("|") + ")$", "i").test(this.value.toLowerCase())) {
                    layer.msg("选择文件错误,图片类型必须是" + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false;
                }
                $.browser=new Object();
                $.browser.msie = /msie/.test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img).attr('src', _self.getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj.parent("div").css({
                            'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                            'width': opts.Width + 'px',
                            'height': opts.Height + 'px'
                        });
                        div.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src', _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});
