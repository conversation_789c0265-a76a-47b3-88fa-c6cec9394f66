<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<e:base />
<e:js />
<style type="text/css">
.text1:HOVER{
    cursor:pointer;
    text-decoration: underline;
}
.top5px{
    margin-top: 5px;
    word-wrap:break-word;
}
.top10px{
    margin-top: 10px;
}
.pLine{
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 10px;
}
.leftAndRight0px{
    margin-left: 0px;
    margin-right: 0px;
    padding-left: 0px;
    padding-right: 0px;
}
.themeEdit{
    border-radius: 5%;
    color: white;
}
.black{
    border-radius: 5%;
    background-color: black;
}
.themeEdit:HOVER{
    cursor:pointer;
    opacity:0.5;
    filter(alpha=50)
}
.bordergroundEdit{
    cursor:pointer;
    position: absolute;
    color: white;
    left: 88%;
    top: 3%;
}
.labelSpan{
    font-weight: bold;
}
.myGutter
{
    margin-top: 5px;
    padding: 10px;
    box-shadow:2px 2px 5px #000;
}
.abc{
    margin-top: 3px;
    white-space: nowrap;
    width:80%;
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 1;  /*数字与之前的文字对齐*/
}
    /*#previewDiv{*/
        /*display: none;*/
        /*position: fixed;*/
        /*!*left: 500px;*!*/
        /*!*top:40px;*!*/
        /*!*width: 200px;*!*/
        /*!*height:100px;*!*/
        /*!*border: #0a001f 1px solid;*!*/
    /*}*/


</style>
</head>
<body style="background-color: white;height: 100%">
    <div class="example row leftAndRight0px">
        <div class="details_xq" id="showPicture" style="margin-top: 20px;">
            <!-- 此处用于加入图片元素 -->
        </div>
    </div>

<%--<div id="previewDiv">--%>
    <%--<p>hello</p>--%>
    <%--<p><input type="button" onclick="popPreview(0)" value="关闭"/>--%>
    <%--</p>--%>
<%--</div>--%>
</body>
<script id="calendarDateDetailTemp" type="text/html">
    <div class="col-md-4 col top10px"><div class="myGutter">
    <div class="top5px" style="text-align: center;">
    <span class="labelSpan" title="日期">日期:</span>
    <span>{{data.calendarDate}}</span>
    </div>
    <div class="black">
    <img alt="图片加载失败" src="{{data.webBackground}}"  onclick="valueCalendarEdit('{{data.id}}')" class="valueCalendarEdit" width="100%" height="168">
    </div>
    <div class="top5px">
    <span class="labelSpan " title="法文">法文:</span>
    <span class="abc" >{{data.copyWriter}}</span>
    </div>
    <div class="top5px">
    <span class="labelSpan" title="金句名言">金句名言：</span>
    <span class="abc" >{{data.quote}}</span>
    </div>
    <div class="top5px pLine row leftAndRight0px">
        <sec:authorize access="hasAuthority('RES_APPLICATION_VALUE_CALENDAR_INIT_AUTHORITY_2')" >
            <div class="col-md-4" ><input style="float:right;margin-left:20px;" type="button"  onclick="valueCalendarEdit('{{data.id}}')" value="查看" class="btn btn-primary" /></div>
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_APPLICATION_VALUE_CALENDAR_INIT_AUTHORITY_3')" >
                <div class="col-md-4" ><input style="float:right;margin-left:20px;" type="button"  onclick="valueCalendarEdit('{{data.id}}')" value="编辑" class="btn btn-primary" /></div>
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_APPLICATION_VALUE_CALENDAR_DELETE_AUTHORITY_3')" >
        <div class="col-md-4" ><input style="float:left;margin-right:20px;" type="button" onclick="del.call(this,'{{data.id}}')" value="删除" class="btn btn-default" /></div>
        </sec:authorize>
        <div class="col-md-4" ><input style="float:left;margin-right:20px;" type="button" onclick="popPreview(1,'{{data}}')" value="预览" class="btn btn-default" /></div>
    </div>
    </div>
    </div>
</script>
</html>
