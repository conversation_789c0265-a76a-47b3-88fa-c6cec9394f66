$(document).ready(function () {

    $('#btnCancel').click(function () {
        closeWin()
    });

    $('#btnSubmit').click(function () {
        submitOn()
    });

    dateInit();

});

function submitOn() {
    let time = $('#dateRangeStr').val()
    var times;
    var nowDate;
    var startDate;
    if (time) {
        var times = time.split(" 至 ");
        nowDate = new Date()
        startDate = new Date(times[0]);
    }
    if (times && startDate < nowDate){
        // 如果选了时间且开始时间小于当前时间
        popMsg("开始时间不能小于当前时间")
    }else {
        closeWinCallBack(times)
    }
}

function dateInit() {
    var dateRangeStr = $('#dateRangeStr').val();
    if (dateRangeStr.trim() != '') {
        var dateRangeStr = dateRangeStr.split(' 至 ');
        dataRangePickerInit($('#dateRangeStr'), dateRangeStr[0], dateRangeStr[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    } else {
        dataRangePickerInit($('#dateRangeStr'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    }
}