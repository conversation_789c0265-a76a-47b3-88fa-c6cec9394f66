<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <script type="text/javascript">
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <div class="" style="padding-top: 5px">
            <form:form  modelAttribute="capcoCostDto" id="capcoCostDto" >
                <input type="hidden" id="relationId" value="${capcoCostDto.relationId}"/>
                <div class="row" style="margin-top: 10px">
                    <div class="col-md-12">
                        <label class="col-md-1 control-label" style="width: 100px">模板名称：</label>
                        <div class="col-md-3">
                            <form:input path="templateName" placeholder="请输入模板名称" cssClass="form-control" />
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-12 text-right" >
                            <span id="btnClear" class="btn btn-default">清空</span>
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </div>
                    </div>
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/costTemplate/queryCostTemplate" cssClass="table table-striped table-hover">
                <e:gridColumn label="<input type='radio'  id='allCheck' disabled>"
                              renderColumn="rcIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:10%"/>
                    <e:gridColumn label="模板名称" displayColumn="templateName" orderable="false"
                                  cssClass="text-center" cssStyle="width:90%" />
            </e:grid>
        </div>
        <div class="row">
            <div class="col-xs-12 text-center" >
                <sapn id="btnSubmit" class=" btn btn-primary">选择</sapn>
            </div>
        </div>
    </div>
</div>
</body>
</html>