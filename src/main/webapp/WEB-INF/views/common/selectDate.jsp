<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
    </style>
</head>
<body>
<div class="panel">
    <div>
        <div class="" style="padding-top: 15px">
            <form:form id="editLiveInfoForm" modelAttribute="selectCostTemplateDto" cssClass="form-horizontal">
                <div class="control-group form-group">
                    <label class="col-md-2 no-padding control-label text-right" style="margin-left: 35px;margin-top: 5px;width: 100px;">改价时间区间：</label>
                    <div class="col-xs-3 daterange" style="width: 370px">
                        <form:input placeholder="选择改价时间区间" autocomplete="off" path="dateRangeStr" cssClass="jeinput form-control " readonly="true" />
                        <i class="glyphicon glyphicon-calendar fa fa-calendar daterangetag" style="bottom: 16px;right: 16px;"></i>
                    </div>
                    <label class="col-md-8 no-padding control-label" style="margin-top: 5px;color: red;text-align: left;margin-left: 150px;">
                        注：选择改价时间区间后，仅在时间区间内会修改人员类型，到期后自动恢复到原来的内容；若不选择，则立马修改人员类型且不会恢复</label>
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel-body">
        <div class="row" style="display: flex;justify-content: center">
            <div class="text-center" >
                <sapn id="btnCancel" class=" btn ">取消</sapn>
            </div>
            <div class="text-center" style="margin-left: 10px" >
                <sapn id="btnSubmit" class=" btn btn-primary">确定</sapn>
            </div>
        </div>
    </div>
</div>
</body>
</html>