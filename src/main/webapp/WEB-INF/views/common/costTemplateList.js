var selCostRelationId = '';

$(document).ready(function () {
    var selPackageId = $("#selPackageId").val();

    selCostRelationId = selPackageId

    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        document.getElementById("capcoCostDto").reset();
        selCostRelationId = '';
        search();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/costTemplate/queryCostTemplate", $("#capcoCostDto").formSerialize());
}

function selCost(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        selCostRelationId = id
    }
}
function submitOn() {
    if (selCostRelationId !== null && selCostRelationId !== ''){
        parent.popWin('选择到期日期','/costTemplate/selectDateInit', null,'100%', '100%', function (res){
            console.log(res)
            let param = {
                relationId : $("#relationId").val(),
                selRelationId : selCostRelationId,
                startDate : res ? res[0] : '',
                expireDate : res ? res[1] : ''
            }
            closeWinCallBack(param)
        })
    }else {
        popMsg("请选择价格模板")
    }
}

function rcIndex(data, type, row, meta) {
    var str = '';
    str += '<div class="classCheckBox case-opt"  onclick="selCost(this)">';
    if (selCostRelationId == '' || selCostRelationId != data.id) {
        str +=
            '<input type="radio" name="checkRow" class="selDeclare"  d-id="' + data.relationId + '"  value="' + data.relationId + '" class="hidden">';
    } else {
        str +=
            '<input checked="true" type="radio" name="checkRow" class="selDeclare"  d-id="' + data.relationId + '"  value="' + data.relationId + '" \ >';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="relationId" value="' + data.relationId + '">'
    str += '</div>';
    return str;
}