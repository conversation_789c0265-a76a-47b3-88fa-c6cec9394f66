<%--
/***************************************************************
* 程序名 :  fileview.jsp
* 日期  :  2015-7-31
* 作者  :  zhaoyang
* 模块  :  文件浏览
* 描述  :  office浏览
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
<script type="text/javascript">
// 文件ID
var fileId ="${fileId}";
// 文档编辑控件对象
var TANGER_OCX_OBJ;
$(document).ready(function() {
	// 取得文档编辑控件对象
	TANGER_OCX_OBJ = document.getElementById("TANGER_OCX");
	// 增加浏览器安全信任
    TANGER_OCX_OBJ.AddDomainToTrustSite(document.domain),
    TANGER_OCX_OBJ.AddDocTypePlugin(".pdf","PDF.NtkoDocument","4,0,0,5","static/lib/office/ntkooledocall.cab",51,true); 
    TANGER_OCX_OBJ.Caption="了解更多合规内容http://www.valueonline.cn";
    autoOCXObjectHeight();
	// 从服务器取得文档
	TANGER_OCX_OBJ.BeginOpenFromURL(contextPath+"/filedownload?fileId="+fileId);
    // 关闭编辑工具栏
    TANGER_OCX_OBJ.toolbars = 0;
});
//调整文档编辑控件对象高度
function autoOCXObjectHeight() {
	$("#ocxobject").height($(window).height()-5);
	document.getElementById("TANGER_OCX").height=$("#ocxobject").height()+'px';
}
</script>
<script language="JScript" for="TANGER_OCX" event="Ondocumentopened(str,doc)">
autoOCXObjectHeight();
</script>
</head>
<body>
<div id="ocxobject">
    <script src='<c:url value="/"/>static/lib/office/ntkoofficecontrol.js'></script>
</div>
</body>
</html>