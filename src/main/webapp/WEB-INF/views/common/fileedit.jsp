<%--
/***************************************************************
* 程序名 : fileedit.jsp
* 日期  :  2015-7-31
* 作者  :  zhaoyang
* 模块  :  文件编辑
* 描述  :  office编辑
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base/>
<script type="text/javascript">
//文件ID
var fileId ="${fileInfo.fileId}";
//文件名称
var fileName ="${fileInfo.fileName}";
// 文档编辑控件对象
var TANGER_OCX_OBJ;

$(document).ready(function() {
	// 取得文档编辑控件对象
	TANGER_OCX_OBJ = document.getElementById("TANGER_OCX");
	// 增加浏览器安全信任
    TANGER_OCX_OBJ.AddDomainToTrustSite(document.domain),
    TANGER_OCX_OBJ.AddDocTypePlugin(".pdf","PDF.NtkoDocument","4,0,0,5","static/lib/office/ntkooledocall.cab",51,true); 
    TANGER_OCX_OBJ.Caption="了解更多合规内容http://www.valueonline.cn";
    autoOCXObjectHeight();
	// 从服务器取得文档
	TANGER_OCX_OBJ.BeginOpenFromURL(contextPath+"/filedownload?fileId="+fileId);
});
//调整文档编辑控件对象高度
function autoOCXObjectHeight() {
	$("#ocxobject").height($(window).height()-5);
	document.getElementById("TANGER_OCX").height=$("#ocxobject").height()+'px';
}
// 显示工具栏
function officeToolBar() {
    TANGER_OCX_OBJ.toolbars = !TANGER_OCX_OBJ.toolbars
}
// 另存为PDF到本地
function objSaveAsPDFFile() {
    TANGER_OCX_OBJ.ShowDialog(3);
}
// 保存修改后的文件到服务器临时目录
function saveFileToServer() {
	var ecodeFileName = encodeURI(fileName);
	var response = TANGER_OCX_OBJ.SaveToURL(contextPath + "/filetempupload", "files", "", ecodeFileName, 0);
	// IE内核保存回调
	saveCallBack(response);
}
// 保存回调函数
function saveCallBack(response) {
	if (response == "") return;
	// 保存后的临时文件ID
	var tempFileId = $.parseJSON(response)[0].fileId;
	if (tempFileId != undefined) {
		popMsg("保存成功");
		var callBack = window.popCallBack;
		var paraCallBack = window.paraCallBack;
		parent.window[callBack(tempFileId, paraCallBack)];
	} else {
		popAlert("保存失败");
	}
}
</script>
<script language="JScript" for="TANGER_OCX" event="OnFileCommand(cmd,canceled)">
FileCommand(cmd,canceled);
</script>
<script language="JScript" for="TANGER_OCX" event="Ondocumentopened(str,doc)">
OnComplete3(str,doc);
</script>
</head>
<body>
<div id="ocxobject">
    <script src='<c:url value="/"/>static/lib/office/ntkoofficecontrol.js'></script>
</div>
</body>
</html>