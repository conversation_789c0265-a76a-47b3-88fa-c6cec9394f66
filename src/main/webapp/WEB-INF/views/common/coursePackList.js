var selPackage = '';
var selPackageName = ''

$(document).ready(function () {

    $(window).keydown( function(e) {
        var key = window.event?e.keyCode:e.which;
        if(key.toString() == "13"){
            return false;
        }
    });
    var selPackageId = $("#selPackageId").val();

    selPackage = selPackageId

    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        selPackage = '';
        search();
    });
});

function search() {
    let param = {
        packageName: $("#packageName").val(),
        releaseFlag: "1"
    }
    ajaxTableQuery("tableAll", "/packManagement/queryPackageList", param);
}

function coursePackName(data, type, row, meta) {
    var str = '<div style="cursor:pointer;width: 100%"">'+data.packageName+'</div>'
    return str;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        selPackage = id
        selPackageName = $(item).parent().parent().find('td').eq(1).children().html()
    }
}
function submitOn() {
    if (selPackage !== null && selPackage !== ''){
        var item = {
            selPackage:selPackage,
            selPackageName:selPackageName
        }
        closeWinCallBack(item)
    }else {
        popMsg("请选择课程包")
    }
}

function rcIndex(data, type, row, meta) {
    var str = '';
    str += '<div class="classCheckBox case-opt"  onclick="selCompany(this)">';
    if (selPackage == '' || selPackage != data.id) {
        str +=
            '<input type="radio" name="checkRow" class="selDeclare"  d-id="' + data.id + '"  value="' + data.id + '" class="hidden">';
    } else {
        str +=
            '<input checked="true" type="radio" name="checkRow" class="selDeclare"  d-id="' + data.id + '"  value="' + data.id + '" \ >';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="courseId" value="' + data.id + '">'
    str += '</div>';
    return str;
}