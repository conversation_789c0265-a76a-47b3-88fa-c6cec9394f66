<%--
  Created by IntelliJ IDEA.
  User: Zhaol
  Date: 2023/3/2
  Time: 10:29
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>易董 云端管理平台</title>
  <e:base />
  <e:js/>
  <style>
    label{
      padding-top: 7px;
    }
  </style>
</head>
<body>
<div class="panel">
  <div class="panel-heading">
    <form:form modelAttribute="schUserInfoDto" id="queryForm" >
      <input type="hidden" id="examRankUnifyId" name="examRankUnifyId" value="${examSpecialPersonDTO.examRankUnifyId}"/>
      <div class="row">
        <div class="col-md-12">
          <label class="col-md-1 control-label">用户类型</label>
          <div class="col-md-3">
            <input id="personType" type="text" class="t-select" json-data='${personTypeList}' placeholder="请输入用户类型" />
            <input name="personType" type="hidden"  />
          </div>
          <label class="col-md-1 control-label">证券代码</label>
          <div class="col-md-3">
            <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label">公司名称</label>
          <div class="col-md-3">
            <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <label class="col-md-1 control-label">姓名</label>
          <div class="col-md-3">
            <form:input path="realName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"/>
          </div>
          <label class="col-md-1 control-label">手机号</label>
          <div class="col-md-3">
            <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
          </div>
          <label class="col-md-1 control-label">邮箱</label>
          <div class="col-md-3">
            <form:input path="mail" placeholder="请输入邮箱" cssClass="form-control" autocomplete="off" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">所在辖区</label>
          <div class="col-md-3">
            <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
            <input name="belongCommission" type="hidden" />
          </div>
          <label class="col-md-1 control-label">人员标签</label>
          <div class="col-md-3">
            <input id="personLabel" type="text" class="t-select" json-data='${personLabelList}' placeholder="请输入人员标签"/>
            <input name="personLabel" type="hidden"  />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-right" >
          <sec:authorize access="hasAuthority('RES_INSERT_RELEVANCE_PERSON_AUTHORITY_3')" >
            <%--新增关联人员权限--%>
            <span id="saveAdd" class="btn btn-primary">添加</span>
          </sec:authorize>
          <sec:authorize access="hasAuthority('RES_TRAIN_USER_MANAGER_INFOLIST_AUTHORITY_3')" >
            <%--查询关联人员权限--%>
            <span id="btnQuery" class="btn btn-primary">查询</span>
          </sec:authorize>
          <span id="btnClear" class="btn btn-default">清空</span>
        </div>
      </div>
    </form:form>
  </div>
  <div class="panel-body">
    <div class="row" style="padding-left: 10px;padding-right: 10px">
      <e:grid id="table1" action="/trainUserManage/getUserInfoList"
              cssClass="table table-striped table-hover">
        <e:gridColumn label="<input type='checkbox'  id='allCheck'>"
                      renderColumn="rcIndex" orderable="false" cssClass="text-center"
                      cssStyle="width:5%" />
        <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                      cssStyle="width:5%" />
        <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
        <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                      cssClass="text-center" cssStyle="width:10%;" />
        <e:gridColumn label="证券代码" displayColumn="companyCode" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
        <e:gridColumn label="公司名称/学校" displayColumn="companyName" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="部门/年级" displayColumn="orgName" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
        <e:gridColumn label="职务" displayColumn="post" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
      </e:grid>
    </div>
  </div>
</div>
</body>
</html>

