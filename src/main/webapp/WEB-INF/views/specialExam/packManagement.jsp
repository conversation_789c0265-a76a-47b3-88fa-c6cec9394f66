<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">预约信息</i>
    </div>
    <div class="panel-body">
        <form:form action=""  modelAttribute="coursePackageDto" id="coursePackageDto">
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">打包名称：</label>
<%--                <div class="col-md-3">--%>
<%--                    <form:input path="packageName" autocomplete="off"  cssClass="form-control"/>--%>
<%--                </div>--%>
                <div class="col-md-3">
                    <input id="packageId" type="text" class="t-select" json-data='${coursePackageList}'
                           selected-ids="${coursePackageDto.packageId}"/>
                    <input name="packageId" type="hidden" placeholder="请选择课程包" value='${coursePackageDto.packageId}'/>
                </div>
                <label class="col-md-1 control-label" style="text-align:center">发布状态：</label>
                <div class="col-md-3">
                    <form:select path="releaseFlag" cssClass="form-control">
                        <form:option value="">全部</form:option>
                        <form:options items="${liveReleaseOptions}" itemLabel="label" itemValue="value" />
                    </form:select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-offset-9 col-md-3" align="right">
                    <input type="button" id="resetBtn" class="btn btn-default" value="清空">
                    <sec:authorize access="hasAuthority('RES_SCH_PACK_LIST_INIT_AUTHORITY_3')" >
                        <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_SCH_PACK_EDIT_INIT_AUTHORITY_3')" >
                        <input type="button" id="btnAdd" class="btn btn-primary" value="新建">
                    </sec:authorize>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_SCH_PACK_EDIT_INIT_AUTHORITY_3')" >
            <%--编辑权限--%>
            <input type="hidden" id="editAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_RELEASE_FLAG_AUTHORITY_3')" >
            <%--发布权限--%>
            <input type="hidden" id="releaseAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_PACK_DELET_PACK_AUTHORITY_3')" >
            <%--删除权限--%>
            <input type="hidden" id="delAuth" value="true">
        </sec:authorize>
        <div class="example">
            <hr>
        </div>
        <div class="ibox float-e-margins">
            <e:grid id="packInfoTable" action="/packManagement/queryPackageList" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:5%;"/>
                <e:gridColumn label="打包名称" displayColumn="packageName" orderable="false" cssClass="text-center" cssStyle="width:20%; word-wrap: break-word;"/>
                <e:gridColumn label="课程数量" displayColumn="courseNum" orderable="false" cssClass="text-center" cssStyle="width:20%; word-wrap: break-word;"/>
                <e:gridColumn label="发布状态" renderColumn="releaseColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:20%; word-wrap: break-word;"/>
                <e:gridColumn label="操作" renderColumn="sourceColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:35%; word-wrap: break-word;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
