let selectIds = []
$(document).ready(function() {
    // 下拉初始化
    tSelectInit();
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("queryForm").reset();
        $('input[name="personType"]').val("");
        $('input[name="belongCommission"]').val("");
        $('input[name="personLabel"]').val("");
        $('#personType').val("")
        $('#belongCommission').val("")
        //取消全选
        cancelAllCheck();
        search()
    });
    //查询
    $("#btnQuery").bind("click",function() {
        cancelAllCheck()
        search()
    });
    //保存
    $("#saveAdd").bind("click", function () {
        let param = {
            selectedIdList: selectIds.toString(),
            examRankUnifyId: $("#examRankUnifyId").val()
        };
        if (selectIds.length === 0){
            popMsg("请选择关联人员!")
            return
        }
        ajaxData("/examConfig/insertRelevancePerson", param, function (data) {
            if (data === -1){
                popMsg("操作失败，人员已存在");
                return;
            }
            if (data > 0) {
                closeWinCallBack()
                return
            }
            popMsg("操作失败，请稍后再试");
        });
    })
    //全选
    let checked = true
    $("#allCheck").click(function () {
        $('input[name="selectIds"]').prop("checked", checked);
        checked = !checked;
        selectRelevancePerson()
    });
});

//取消全选
function cancelAllCheck() {
    $('input[name="selectIds"]').prop("checked", false);
    let allCheck = document.getElementById("allCheck");
    allCheck.checked = false
    selectRelevancePerson()
}
function tSelectInit() {
    let teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#personLabel').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function search(){
    ajaxTableQuery("table1", "/trainUserManage/getUserInfoList",$("#queryForm").formSerialize());
}
function callBackAddUser() {
    ajaxTableReload("table1",false);
}
function rcIndex(data) {
    let str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;">';
    str += '<input type="checkbox" name="selectIds" class="selDeclare" value="'+ data.id +'"';
    str += ' onclick="selectRelevancePerson()" class="hidden">';
    str += '<label></label>';
    str += '<input type="hidden" name="id" value="' + data.id + '">'
    str += '</div>';
    return str;
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

function selectRelevancePerson() {
    const obj = document.getElementsByName("selectIds");
    const checkVal = [];//获取选中的id
    for(const k in obj){
        if(obj[k].checked){
            checkVal.push(obj[k].value);
        }
    }
    selectIds = checkVal
}