<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <style>
        #gradePicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
    </style>
    <e:base/>
    <e:js/>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="specialGradeDto" id="queryForm" autocomplete="off">
            <form:hidden path="gradeId"/>
            <div class="row">
                <div class="col-md-12"  >
                    <label class="col-md-3 control-label" style="margin-top: 5px;">级别描述</label>
                    <div class="col-md-8 ">
                        <form:textarea  rows="8" path="gradeDescribe" cssClass="form-control" ></form:textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-xs-2 control-label">专题图片</label>
                    <div class="col-xs-6" style="margin: 10px 0">
                        <c:choose>
                            <c:when test="${specialGradeDto.gradeImageUrl == '' || specialGradeDto.gradeImageUrl==null}">
                                <div id="gradePicDiv" class="col-xs-12 no-padding" style="display: none;">
                                    <img id="gradeImageUrlSrc" src="${specialGradeDto.gradeImageUrl}" style="width: 320px;height: 180px;"/>
                                    <input type="hidden" name="gradePicFileId" id="gradePicFileId"/>
                                    <form:hidden path="gradeImageUrl"/>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div id="sgradePicDiv" class="col-xs-12 no-padding" style="display: block;">
                                    <img id="gradeImageUrlSrc" src="${specialGradeDto.gradeImageUrl}" style="width: 320px;height: 180px;"/>
                                    <input type="hidden" name="gradePicFileId" id="gradePicFileId"/>
                                    <form:hidden path="gradeImageUrl"/>
                                </div>
                            </c:otherwise>
                        </c:choose>
                        <div class="col-xs-12 no-padding controls live-pic-btn-row">
                            <a href="javascript:void(0);" class="file btn btn-warning btn-facebook btn-outline">
                                <i class="fa fa-upload"> </i> 上传图片
                                <input id="gradePicFile" type="file" name="files"/>
                            </a>
                            <input type="button" id="gradePicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top: 20px;text-align: center;">
                <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存" />
                <input type="button" id="btnCancel" class="btn btn-default" value="取消" />
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
