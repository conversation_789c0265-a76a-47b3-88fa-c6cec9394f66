<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .pictureTypeTitle{
            width: 110px;
            height: 25px;
            background-color: #00a0e9;
            color: white;
            margin-left: -25px;
            padding-left: 25px;
            line-height: 25px;
        }
        .verificationPictureDiv{
            width: 250px;
            /*height: 270px;*/
            font-size: 9px;
            margin-left: 30px;
            float: left;
        }
        .examPictureDiv{
            width: 200px;
            margin-left: 30px;
            float: left;
        }
        .adoptButton{
            background-color: #00a0e9;
            border-radius: 5px;
            color: white;
            width: 150px;
            height: 35px;
            font-size: 12px;
            padding-left: 25px;
            line-height: 35px;
            margin-top: 5px;
            margin-left: 15px;
        }
        .recognitionResultTitle{
            width: 120px;
            height: 25px;
            font-size: 12px;
            padding-left: 25px;
            line-height: 25px;
            color: white;
            border-bottom-left-radius: 10px;
            position: relative;
            top: -208px;
            left: 88px;
        }
        .panel-body{
            min-height: 880px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="examMonitoringDto" id="examMonitoringDto" autocomplete="off">
            <form:hidden path="recordId"/>
            <div id="verificationPictureTitle" class="col-md-12" style="margin-top: 20px" >
                <div class="pictureTypeTitle">人脸验证</div>
            </div>
            <div id="verificationPicture" class="col-md-12" style="margin-top: 20px" >
                <c:forEach items="${faceVerificationList}" var="item" varStatus="status">
                    <div class="verificationPictureDiv">
                        <img src="${item.osAttUrl}"/>
                        <div class="verificationFailStatus_${status.index}">
                            <c:if test="${item.recognitionResults == '0'}">
                                <div class="recognitionResultTitle" style="background-color: crimson;">人脸验证失败</div>
                            </c:if>
                            <c:if test="${item.recognitionResults == '1'}">
                                <div class="recognitionResultTitle" style="background-color: #00a0e9;">人脸验证通过</div>
                            </c:if>
                        </div>
                        <c:if test="${item.recognitionResults == '0'}">
                            <div class="verificationFailInfo_${status.index}">
                                <div class="col-md-12" style="margin-left: -15px">
                                    <div class="col-md-12">姓名：${item.personName}</div>
                                </div>
                                <div class="col-md-12" style="margin-left: -15px">
                                    <div class="col-md-12">身份证号：${item.idNumber}</div>
                                </div>
                                <div class="col-md-12" style="margin-left: -15px">
                                    <div class="col-md-12">验证时间：${item.createTime}</div>
                                </div>
                                <div class="col-md-12" style="margin-left: -15px">
                                    <div class="col-md-12">失败原因：${item.failureReason}</div>
                                </div>
                            </div>
                            <div class="col-md-12" style="margin-top: 20px;margin-left: 15px" >
                                <sec:authorize access="hasAuthority('RES_UPDATE_VERIFICATION_STATUS_AUTHORITY_3')" >
                                    <%--更新人脸验证状态权限--%>
                                    <c:if test="${status.index+1 == faceVerificationList.size()}">
                                        <div class="adoptButton" onclick="adoptButton('${item.faceId}','${status.index}')">设为通过人脸验证</div>
                                    </c:if>
                                </sec:authorize>
                            </div>
                        </c:if>
                    </div>
                </c:forEach>
            </div>
            <c:if test="${certificatesPic != null}">
                <div id="verificationPictureTitle" class="col-md-12" style="margin-top: 20px" >
                    <div class="pictureTypeTitle">证书照片</div>
                </div>
                <div id="verificationPicture" class="col-md-12" style="margin-top: 20px" >
                    <div>(注：学员已经上传证件照片后，下发证书默认使用证件照。没上传使用的是人脸验证的照片)</div>
                    <div class="verificationPictureDiv">
                        <img src="${certificatesPic}"/>
                    </div>
                </div>
            </c:if>
            <c:if test="${examGradList!=null}">
                <div id="examPictureTitle" class="col-md-12" style="margin-top: 80px" >
                    <div class="pictureTypeTitle">考试中抓拍</div>
                </div>
                <div id="examPicture" class="col-md-12" style="margin-top: 20px" >
                    <c:forEach items="${examGradList}" var="item" varStatus="status">
                        <div class="examPictureDiv" align="center">
                            <img src="${item.osAttUrl}"/>
                            <div class="col-md-12" style="margin-top: 10px">
                                <div class="col-md-12">${item.createTime}</div>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </c:if>
            <c:if test="${videoList!=null}">
                <div id="examPictureTitle" class="col-md-12" style="margin-top: 80px" >
                    <div class="pictureTypeTitle">视频监控</div>
                </div>
                <div id="examPicture" class="col-md-12" style="margin-top: 20px" >
                    <%--<c:forEach items="${videoList}" var="item" varStatus="status">
                        <video src="${item.downloadUrl}"></video>
                    </c:forEach>--%>
                </div>
            </c:if>
            <c:if test="${examResult!=null}">
                <div id="examPictureTitle" class="col-md-12" style="margin-top: 40px" >
                    <div class="pictureTypeTitle">考试结果</div>
                </div>
                <div class="col-md-12" style="margin-top: 20px" >
                    <label class="col-md-2 control-label">考试分数：${examResult.capcoExamScore}分</label>
                    <label class="col-md-2 control-label">是否通过：
                        <c:if test="${examResult.examQualifiedFlag=='0'}">
                            否
                        </c:if>
                        <c:if test="${examResult.examQualifiedFlag=='1'}">
                            是
                        </c:if>
                    </label>
                    <c:if test="${examMonitoringDto.examType=='111111'}">
                        <label class="col-md-2 control-label">是否下发证书：
                            <c:if test="${examResult.ifCertificate=='0'}">
                                否
                            </c:if>
                            <c:if test="${examResult.ifCertificate=='1'}">
                                是
                            </c:if>
                        </label>
                    </c:if>
                </div>


                <c:if test="${examResult.examQualifiedFlag=='1'}">
                    <div id="examPictureTitle" class="col-md-12" style="margin-top: 40px" >
                    <c:if test="${examMonitoringDto.examType=='111111'}">
                        <div class="pictureTypeTitle">证书审核</div>
                    </c:if>
                    <c:if test="${examMonitoringDto.examType=='444444'}">
                        <div class="pictureTypeTitle">考试审核</div>
                    </c:if>
                    </div>
                    <div>
                        注：审核通过不发送短信通知，审核不通过发送短信通知。 复核通过发送短信通知，复核不通过发送短信通知。
                    </div>
                    <c:choose>
                        <c:when test="${examResult.examineList != null && examResult.examineList.size()>0}">
                            <c:forEach items="${examResult.examineList}" var="item" varStatus="status">
                                <div class="col-md-12">
                                    <c:if test="${item.examineStatus =='0'}">
                                        <label class="col-md-2 control-label">审核人:${item.examinePerson}</label>
                                        <label class="col-md-2 control-label">审核时间:${item.examineTime}</label>
                                        <label class="col-md-2 control-label">审核结果：${item.adoptFlag}</label>
                                    </c:if>
                                    <c:if test="${item.examineStatus =='1'}">
                                        <label class="col-md-2 control-label">复核人:${item.examinePerson}</label>
                                        <label class="col-md-2 control-label">复核时间:${item.examineTime}</label>
                                        <label class="col-md-2 control-label">复核结果：${item.adoptFlag}</label>
                                    </c:if>
                                </div>
                            </c:forEach>
                            <c:if test="${examResult.ifCertificate == '0' && examResult.examineList[0].adoptFlag == '通过' && examResult.examineList.size()==1}">

                                <sec:authorize access="hasAuthority('RES_EXAMINE_ABOPT_AUTHORITY_3')" >
                                    <%--审核考试权限--%>
                                    <sec:authorize access="hasAuthority('RES_SEND_CERTIFICATE_AUTHORITY_3')" >
                                        <%--下发证书权限--%>
                                        <div class="col-md-12">
                                            <label class="col-md-2 control-label sendCertificate">
                                                <span style="cursor: pointer;color: #00a0e9;font-size: 20px;" onclick="sendCertificate('${examMonitoringDto.recordId}','${examMonitoringDto.examRank}','1',${examMonitoringDto.examType})">
                                                    <c:if test="${examMonitoringDto.examType=='111111' || examMonitoringDto.examType=='777777'}">
                                                        复核通过并下发证书
                                                    </c:if>
                                                    <c:if test="${examMonitoringDto.examType=='444444'}">
                                                        复核通过并发送短信
                                                    </c:if>
                                                </span>
                                            </label>
                                            <label class="col-md-2 control-label sendCertificate">
                                                <span style="cursor: pointer;color: #00a0e9;font-size: 20px;" onclick="sendCertificate('${examMonitoringDto.recordId}','${examMonitoringDto.examRank}','0',${examMonitoringDto.examType})">复核不通过</span>
                                            </label>
                                        </div>
                                    </sec:authorize>
                                </sec:authorize>
                            </c:if>
                        </c:when>
                        <c:otherwise>
                            <sec:authorize access="hasAuthority('RES_EXAMINE_ABOPT_AUTHORITY_3')" >
                                <%--审核考试权限--%>
                                <label class="col-md-2 control-label sendCertificate">
                                    <span style="cursor: pointer;color: #00a0e9;font-size: 20px;" onclick="examineAdopt('${examMonitoringDto.recordId}','1',${examMonitoringDto.examType})">审核通过</span>
                                </label>
                                <label class="col-md-2 control-label sendCertificate">
                                    <span style="cursor: pointer;color: #00a0e9;font-size: 20px;" onclick="examineAdopt('${examMonitoringDto.recordId}','0',${examMonitoringDto.examType})">审核不通过</span>
                                </label>
                            </sec:authorize>
                        </c:otherwise>
                    </c:choose>
<%--                        <label class="col-md-2 control-label sendCertificate">--%>
<%--                            <span style="cursor: pointer;color: #00a0e9;" onclick="sendCertificate('${examMonitoringDto.recordId}','${examMonitoringDto.examRank}')">下发证书</span>--%>
<%--                        </label>--%>
                </c:if>

            </c:if>
            <div class="col-md-12" style="text-align: center;position: absolute;bottom: 0px;left: 0px">
                <input type="button" id="closeBtn" class="btn btn-default" value="关闭">
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
