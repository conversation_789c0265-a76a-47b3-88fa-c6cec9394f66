
$(document).ready(function() {

    $("#queryBtn").bind("click", function() {
        tableQuery();
    });

    $("#resetBtn").bind("click", function() {
        document.getElementById("coursePackageDto").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery();
    });

    $("#btnAdd").bind("click", function() {
        popWin('新增打包','/packManagement/editPackInit', {},'100%', '100%', callBackAddUser);
    });

    tSelectInit(); //下拉初始化
});


/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("packInfoTable", "/packManagement/queryPackageList", $("#coursePackageDto").formSerialize());
}

//下拉初始化
function tSelectInit() {
    var pagSelectOptions = {
        id: 'packageId',
        name: 'packageName',
        value: 'packageId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#packageId').tselectInit(null, pagSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function saveCallBack() {
    popMsg('保存成功！');
    tableReload(false);
}

function callBackAddUser() {
    tableReload(false);
}

function tableReload(resetPaging) {
    ajaxTableReload("packInfoTable", resetPaging);
}

function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}


function releaseColumnIndex(data, type, row, meta) {
    if(data.releaseFlag =='0'){
        return '未发布'
    }else {
        return '已发布'
    }
}

function sourceColumnIndex(data, type, row, meta) {
    let content = '';

    if (document.getElementById("editAuth")) {
        content += '<a href="javascript:void(0)" onclick="editPackInfo(\''+ data.id +'\')" title="编辑">编辑</a>';
    }

    if (document.getElementById("releaseAuth")) {
        content += ' | ';
        if(data.releaseFlag == '0'){
            content += '<a href="javascript:void(0)" onclick="releasePack(\''+ data.id +'\',1)" title="发布">发布</a>';
            content += ' | ';
        }else {
            content += '<a href="javascript:void(0)" onclick="releasePack(\''+ data.id +'\',0)" title="取消发布">取消发布</a>';
            content += ' | ';
        }
    }

    if (document.getElementById("delAuth")) {
        content += '<a href="javascript:void(0)" onclick="deletePack(\''+ getValue(data.id) +'\')" title="删除">删除</a>';
    }

    if (!content) {
        content += '-';
    }

    return content;
}

function editPackInfo(id){
    let param = {
        id:id
    }
    popWin('编辑打包', '/packManagement/editPackInit', param, '100%', '100%', saveCallBack, '', callBackAddUser);
}

//发布
function releasePack(id,releaseFlag){
    var param ={
        id:id,
        releaseFlag:releaseFlag
    };
    ajaxData('/packManagement/releasePack', param, callBackAddUser);
}

//删除
function deletePack(id){
    popConfirm("确认删除?", function () {
        var param ={
            id:id
        };
        ajaxData('/packManagement/deletePack', param, callBackAddUser);
    });
}