<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <script type="text/javascript">
        var examBaseUrl = "${examBaseUrl}";
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="examMonitoringDto" id="examMonitoringDto" >
            <div class="row" style="margin-top: 10px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">考试类型</label>
                    <div class="col-md-3">
                        <form:select path="examId" cssClass="form-control" itemValue="9250280212976152490">
                            <form:option value="">全部</form:option>
                            <form:options items="${examTypeList}" itemLabel="examName" itemValue="examId" />
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">考试级别类型</label>
                    <div class="col-md-3">
                        <form:select path="examGradeType" cssClass="form-control" disabled="true">
                            <form:option value="">全部</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">考试状态</label>
                    <div class="col-md-3">
                        <form:select path="examStatusType" cssClass="form-control" itemValue="5">
                            <form:option value="">全部</form:option>
                            <form:options items="${examStatusList}" itemLabel="label" itemValue="value" />
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top: 15px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="personName" placeholder="请输入姓名" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">公司简称</label>
                    <div class="col-md-3">
                        <form:input path="companyCodeAndName" placeholder="请输入公司简称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">手机号</label>
                    <div class="col-md-3">
                        <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                    </div>



                </div>
            </div>
            <div class="row" style="margin-top: 15px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">证券代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">所在辖区</label>
                    <div class="col-md-3">
                        <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
                        <input name="belongCommission" type="hidden" />
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top: 15px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">是否下发证书</label>
                    <div class="col-md-3">
                        <form:select path="certificateStatus" cssClass="form-control">
                            <form:option value="">全部</form:option>
                            <form:option value="1">是</form:option>
                            <form:option value="0" selected="true">否</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">证书下发时间</label>
                    <div class="col-md-3 daterange">
                        <form:input path="examineTime" placeholder="请选择证书下发时间" cssClass="form-control" />
                    </div>
                </div>
            </div>
    </div>

            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_QUERY_EXAM_MONITORING_INFO_AUTHORITY_3')" >
                        <%--查询考试监控列表权限--%>
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_MONITORING_EXPORT_AUTHORITY_3')" >
                        <%--查询考试监控列表权限--%>
                        <span id="exportTableBtn" class="btn btn-primary">导出</span>
                    </sec:authorize>
                    <span id="btnClear" class="btn btn-default">清空全部</span>
                </div>
            </div>
        </form:form>

        <sec:authorize access="hasAuthority('RES_VIEW_MONITORING_INFO_AUTHORITY_3')" >
            <%--查看监控信息权限--%>
            <input type="hidden" id="viewMonitoringAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_VIEW_HISTORY_MONITORING_INFO_AUTHORITY_3')" >
            <%--查看历史监控信息权限--%>
            <input type="hidden" id="viewHistoryMonitoringAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_VIEW_EXAM_INFO_AUTHORITY_3')" >
            <%--查看试卷权限--%>
            <input type="hidden" id="viewExamAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_PAPER_MARKING_AUTHORITY_3')" >
            <%--批卷权限--%>
            <input type="hidden" id="paperMarkingAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SELECT_MONITOR_AUTHORITY_3')" >
            <%--查看认证信息权限--%>
            <input type="hidden" id="selectMonitorInfoAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_DEL_EXAM_RECORD_AUTHORITY_3')" >
            <%--删除考试记录权限--%>
            <input type="hidden" id="delExamRecordAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/examMonitoring/queryExamMonitoringList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:4%" />
                <e:gridColumn label="姓名" displayColumn="personName" orderable="false"
                              cssClass="text-center" cssStyle="width:4%" />
                <e:gridColumn label="考试名称" displayColumn="examName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="考试级别" displayColumn="examRankStr" orderable="false"
                              cssClass="text-center" cssStyle="width:5%" />
                <e:gridColumn label="证券代码" renderColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:6%" />
                <e:gridColumn label="公司简称" displayColumn="companyName"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="考试状态" displayColumn="examStatus" orderable="false" cssClass="text-center"
                              cssStyle="width:6%" />
                <e:gridColumn label="证书状态" renderColumn="ifCertificate" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="交卷时间" renderColumn="submitTime" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="证书下发时间" renderColumn="examineTime" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="作答时间" renderColumn="timeSlot" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="分数" renderColumn="score" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:18%" />
            </e:grid>
        </div>
    </div>
</div>

</body>
</html>
