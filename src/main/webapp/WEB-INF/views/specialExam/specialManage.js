$(document).ready(function () {

    $('#btnQuery').click(function () {
        search();
    });

    $("#addSpecial").click(function (){
        parent.popWin('新增考核信息', '/specialConfig/addSpecial', "", '100%', '100%', callBackAddSpecial, '', callBackAddSpecial);
    })
})
function search(){
    ajaxTableQuery("tableAll", "/specialConfig/querySpecialInfoList",
        $("#queryForm").formSerialize());
}
function callBackAddSpecial(){
    ajaxTableReload("tableAll", false);
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function columnRelease(data, type, row, meta) {
    if (data.releaseFlag=='0'){
        return '否'
    }else if (data.releaseFlag=='1'){
        return '是'
    }else {
        return ''
    }
}
function columnExam(data, type, row, meta) {
    if (data.examId != null && data.examId != ''){
        return '是'
    }else {
        return '否'
    }
}
function columnOperation(data, type, row, meta) {
    var str = '';
    if (document.getElementById("editAuth")) {
        str += '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editSpecial(\'' + data.specialId + '\')"></i>';
    }

    if (document.getElementById("delAuth")) {
        if (data.releaseFlag=='0') {
            str += '<i class="fa fa-trash-o" style="cursor: pointer;margin: 0 5px;" title="删除" onclick="deleteSpecial(\'' + data.specialId + '\')"></i>';
        }
    }

    if (!str) {
        str += '-';
    }
    return str;
}

function editSpecial(specialId){
    var param = {
        specialId:specialId,
    }
    parent.popWin('编辑专题信息', '/specialConfig/addSpecial', param, '100%', '100%', callBackAddSpecial, '', callBackAddSpecial);
}

function deleteSpecial(specialId){
    popConfirm("当前操作不可恢复，确认删除?", function () {
        var param = {
            specialId:specialId,
            status:"0"
        }
        ajaxData("/specialConfig/releaseSpecialInfo",param, function (res) {
            popMsg("删除成功");
            callBackAddSpecial()
        })
    });
}
