<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <div class="panel-heading">
            <form:form modelAttribute="specialDto" id="queryForm" >
                <div class="row">
                    <div class="col-md-12 text-right" >
                        <sec:authorize access="hasAuthority('RES_QUERY_SPECIAL_INFO_AUTHORITY_3')" >
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空全部</span>
                        <sec:authorize access="hasAuthority('RES_ADD_SPECIAL_AUTHORITY_3')" >
                            <span id="addSpecial" class="btn btn-primary">新建专题</span>
                        </sec:authorize>
                    </div>
                </div>
            </form:form>
            <sec:authorize access="hasAuthority('RES_ADD_SPECIAL_AUTHORITY_3')" >
                <%--编辑权限--%>
                <input type="hidden" id="editAuth" value="true">
            </sec:authorize>
            <sec:authorize access="hasAuthority('RES_RELEASE_SPECIAL_INFO_AUTHORITY_3')" >
                <%--删除权限--%>
                <input type="hidden" id="delAuth" value="true">
            </sec:authorize>
        </div>

        <div class="panel-body">
            <div class="row">
                <e:grid id="tableAll" action="/specialConfig/querySpecialInfoList" cssClass="table table-striped table-hover">
                    <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                                  cssStyle="width:5%" />
                    <e:gridColumn label="专题名称" displayColumn="specialName" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="专题类型" displayColumn="specialType" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="级别数量" displayColumn="gradeNum" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="课程数量" displayColumn="courseNum"  orderable="false" cssClass="text-center"
                                  cssStyle="width:8%" />
                    <e:gridColumn label="关联考试" renderColumn="columnExam" orderable="false" cssClass="text-center"
                                  cssStyle="width:10%" />
                    <e:gridColumn label="发布状态" renderColumn="columnRelease" orderable="false" cssClass="text-center"
                                  cssStyle="width:10%" />
                    <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                                  cssStyle="width:8%" />
                </e:grid>
            </div>
        </div>

    </div>
</div>
</body>
</html>
