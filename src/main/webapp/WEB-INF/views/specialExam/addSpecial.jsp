<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <style>
        #specialPicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .grade-class{
            margin: 40px 10px;
            padding: 20px 40px;
            border: 1px solid black;
        }
        .typeButt-class{
            margin-bottom: 6px;
            margin-left: 20px;
            float: right;
        }
        .row{
            margin-top: 20px
        }
    </style>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <input type="hidden" value="${courseTypeList}" id="courseTypeList">
    <div class="panel-heading" style="min-height: 900px">
        <div class="row" style="padding-top: 5px;padding-bottom: 10px">
            <div class="col-md-12">
                <div class="col-md-6 text-left">
                    <div style="font-size: 20px;font-weight: bold">专题基本信息</div>
                </div>
                <div class="col-md-6 text-right">
                    <span id="btnRelease" class="identity-limit btn btn-primary">发布</span>
                    <span id="btnCancel" class="identity-limit btn btn-primary">取消发布</span>
                    <span id="btnSaveSpecial" class="identity-limit btn btn-primary">保存基本信息</span>
                    <span id="btnClose" class="btn">关闭</span>
                </div>
            </div>
        </div>
        <form:form modelAttribute="specialDto" id="queryForm" autocomplete="off">
            <form:hidden path="specialId"/>
            <form:hidden path="releaseFlag"/>
            <form:hidden path="examId"/>
            <form:hidden path="packageId"/>

            <div class="row" style="">
                <div class="col-md-12">
                    <label class="col-md-1 control-label c-label"><span style="color: #FF0000;font-weight: bold;">*</span>专题类型：</label>
                    <div class="col-md-7">
                        <form:select path="specialType" cssClass="form-control" onchange="rem()">
                            <form:option value="">请选择</form:option>
                            <c:forEach items="${specialType}" var="item" varStatus="status">
                                <form:option value="${item.codeValue}">${item.codeName}</form:option>
                            </c:forEach>
                        </form:select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label c-label"><span style="color: #FF0000;font-weight: bold;">*</span>专题名称：</label>
                    <div class="col-md-7">
                        <form:input path="specialName" placeholder="请输入专题名称" cssClass="form-control"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>关联考试：</label>
                    <input type="button" style="margin-right: 15px;" class="btn col-md-1"  value="选择考试" onclick="selectExam()"/>
                    <span class="col-md-2 selectExam-class" style="font-size: 16px">
                        <span style=" color: #00b7ee" id="examName">${specialDto.examName}</span>
                        <span style="color: #00b7ee;cursor: pointer;margin-left: 50px" onclick="delExamName(this)">删除</span>
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-xs-1 control-label">专题图片</label>
                    <div class="col-xs-6" style="margin: 10px 0">
                        <c:choose>
                            <c:when test="${specialDto.specialImageUrl == '' || specialDto.specialImageUrl==null}">
                                <div id="specialPicDiv" class="col-xs-12 no-padding" style="display: none;">
                                    <img id="specialImageUrlSrc" src="${specialDto.specialImageUrl}" style="width: 320px;height: 180px;"/>
                                    <input type="hidden" name="specialPicFileId" id="specialPicFileId"/>
                                    <form:hidden path="specialImageUrl"/>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div id="specialPicDiv" class="col-xs-12 no-padding" style="display: block;">
                                    <img id="specialImageUrlSrc" src="${specialDto.specialImageUrl}" style="width: 320px;height: 180px;"/>
                                    <input type="hidden" name="specialPicFileId" id="specialPicFileId"/>
                                    <form:hidden path="specialImageUrl"/>
                                </div>
                            </c:otherwise>
                        </c:choose>
                        <div class="col-xs-12 no-padding controls live-pic-btn-row">
                            <a href="javascript:void(0);" class="file btn btn-warning btn-facebook btn-outline">
                                <i class="fa fa-upload"> </i> 上传图片
                                <input id="specialPicFile" type="file" name="files"/>
                            </a>
                            <input type="button" id="specialPicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" id="realPackage">
                <div class="col-md-12">
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>关联课程包：</label>
                    <input type="button" style="margin-right: 15px;" class="btn col-md-1"  value="选择课程包" onclick="selectCoursePackage()"/>
                    <span class="col-md-2 selectPackage-class" style="font-size: 16px">
                        <span style=" color: #00b7ee" id="packageName">${specialDto.packageName}</span>
                        <span style="color: #00b7ee;cursor: pointer;margin-left: 50px" onclick="delPackageName(this)">删除</span>
                    </span>
                </div>
            </div>

            <div class="row" id="addGrade">
                <span class="btn btn-primary" style="margin-bottom: 6px;margin-left: 20px"  onclick="addGrade()">新增级别</span>
            </div>
            <div id="gradeDivId">
                <c:forEach items="${specialDto.specialGradeDtoList}" var="item" varStatus="status">
                    <div class="grade-class">
                        <div class="row">
                            <label class="col-md-1 control-label c-label">级别名称：</label>
                            <span class="col-md-4">
                                <span>${item.gradeName}</span>
                                <input type="hidden" id="gradeId_${status.index}" value="${item.gradeId}">
                            </span>
                            <span class="btn btn-primary typeButt-class" onclick="addSpecialType('','${status.index}')">新增分类</span>
                            <span class="btn btn-primary typeButt-class" onclick="deleteSpecialGrade('${item.gradeId}')">删除级别</span>
                            <span class="btn btn-primary typeButt-class" onclick="editGrade('${item.gradeId}')">编辑级别</span>
                            <span class="btn btn-primary typeButt-class" onclick="editSpecialPrice('${item.gradeId}')">收费设置</span>
                        </div>
                        <div class="row">
                            <div class="col-xs-12">
                                <table class="table table-bordered no-margin" style="text-align: center;float:left;" id="typeTableId">
                                    <thead>
                                    <tr role="row">
                                        <th class="text-center" style="width: 5%;">序号</th>
                                        <th class="text-center" style="width: 10%;">分类名称</th>
                                        <th class="text-center" style="width: 55%;">分类描述</th>
                                        <th class="text-center" style="width: 10%;">是否打包售卖</th>
                                        <th class="text-center" style="width: 20%;">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody id="typeTbodyId_${status.index}">
                                        <c:forEach items="${item.specialTypeDtoList}" var="item1" varStatus="status1">
                                            <tr>
                                                <td class="sort-no">${item1.sort}</td>
                                                <td>
                                                    ${item1.specialTypeName}
                                                    <input type="hidden" id="specialTypeId_${status.index}_${status1.index}" value="${item1.specialTypeId}">
                                                </td>
                                                <td style="word-break:break-all"><span>${item1.specialTypeDescribe}</span></td>
                                                <td>
                                                    <c:if test="${item1.packageFlag == '1'}">
                                                        是
                                                    </c:if>
                                                    <c:if test="${item1.packageFlag != '1'}">
                                                        否
                                                    </c:if>
                                                </td>
                                                <td>
                                                    <a href="javascript:void(0)" onclick="addSpecialType('${item1.specialTypeId}','${status1.index}')" title="编辑分类">编辑</a>
                                                    |
                                                    <a href="javascript:void(0)" onclick="relationSpecialType('${item1.specialTypeId}','${item1.packageId}')" title="关联课程">关联课程</a>
                                                    |
                                                    <a href="javascript:void(0)" onclick="deleteSpecialType('${item1.specialTypeId}')" title="删除分类">删除</a>
                                                    |
                                                    <a href="javascript:void(0)" onclick="upSpecialType('${item.gradeId}','${item1.specialTypeId}','${item1.sort}','${status.index}')" title="上移分类">上移</a>
                                                    |
                                                    <a href="javascript:void(0)" onclick="downSpecialType('${item.gradeId}','${item1.specialTypeId}','${item1.sort}','${status.index}')" title="下移分类">下移</a>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </c:forEach>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
