$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
});

function tSelectInit() {
    const liveNameSelectOptions = {
        id: 'liveId',
        name: 'liveName',
        value: 'liveId',
        grade: 1,
        resultType: 'all',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        inputType: 'radio', // 单选
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#liveId').tselectInit(null, liveNameSelectOptions);
}

function relation() {
    let param = {
        liveId: $('input[name="liveId"]').val(),
        courseId: $("#courseId").val()
    }
    if (!param.liveId) {
        popMsg("请选择直播")
        return
    }
    ajaxData("/basicInformation/relationLive", param, function (res) {
        closeWinCallBack(res);
    });
}

function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}