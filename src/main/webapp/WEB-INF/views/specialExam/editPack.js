let _ImgUploadUrl = contextPath + '/filetempupload';
$(document).ready(function () {

    //编辑分类进时判断是否打包售卖
    packFlagInit();

    livePicInit();

    associationTypeInit();

    tSelectInit();

    save();


    $('#closeBtn').bind('click', function() {
        popConfirm('确定放弃吗？', function () {
            closeWin();
        });
    });

    $("#btnSelectCost").bind("click", function() {
        var relationId = $("#id").val()
        if(getValue(relationId) == ""){
            popMsg("请先保存数据")
            return ;
        }
        let personTypeList = $("#personTypeList").val()
        var param = {
            existPersonType:true,
            existIfFree:true,
            existIfDiscount:true,
            existDiscount:true,
            existPrice:true,
            relationId:relationId
        }
        popWin("选择用户类型", "/selectTrainTypeCost", param, "80%", "80%");
    });
})


function livePicInit() {
    $('#livePicRemoveBtn').bind('click', function () {
        clearLivePic();
    });
    $('#livePicFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#livePicFileId").val(file.fileRelaId);
                $("#imageUrl").val(file.fileRelaId);
                $("#picAttId").val("");
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });

    $("#livePicFile").imageUrlUploadPreview({
        Img: "livePicImgSrc",
        Width: 50,
        Height: 50
    });
}

function clearLivePic() {
    popConfirm("确认删除图片吗？", function () {
        $("#livePicFileId").val("");
        $("#livePicDiv").css("display", "none");
        $("#livePicFile").val("");
        $("#imageUrl").val("");
    });
}

//选择课程
function chooseCourse() {
    let params = {
        id:$("#id").val(),
        courseTagType:'1'
    }
    parent.popWin("选择课程", "/packManagement/selectPackCourse", params, "70%", "80%", function (data) {
        let param = {
            coursePackageMapDtos:data
        }
        ajaxData("/packManagement/getCourseInfo",param,function (data1) {
            if(data1){
                $("#courseTBody").empty()
                data1.some((item,index)=>{
                    var sort = index+1
                    var html = '<tr><input type="hidden" value="'+item.courseId+'"/>'
                        + '<td class="serialNumber">'+sort+'</td>'
                        + '<td>'+item.courseName+'</td>'
                        + '<td>'+item.courseTime+'分钟</td>'
                        + '<td>'+item.originalPrice+'</td>'
                        + '<td><i class="icon-arrow-up" title="上移" onclick="moveUp(this)"></i>\n' +
                        ' <i class="icon-arrow-down" title="下移" onclick="moveDown(this)"></i>\n' +
                        ' <i class="icon icon-trash" title="删除" onclick="deleteCourse(this)"></i></td></tr>'
                    $("#courseTBody").append(html)
                })
            }
        })
    })
}

//删除关联课程
function deleteCourse(obj){
    popConfirm("确认删除?",function() {
        $(obj).parent().parent().remove();//移除行
        sortMyTable()
        });
}

function sortMyTable(){
    $.each($("#courseTBody tr"),function(index,item){
        $(this).find(".serialNumber").html(index+1);     //列表重排序号
    })
}

//上移
function moveUp(obj){
     if($(obj).parents("tr").index() != 0){
         let obj1 = $(obj).parent().parent()
         let obj2 = $(obj1).prev()
         changeDoms(obj1,obj2)
     }else {
         popMsg("无法上移")
     }
}

//下移
function moveDown(obj){
    var tr = $(obj).parents("tr");
    var trLength = $('#courseTBody').children('tr').length;
    if (tr.index() != trLength - 1) {
        let obj1 = $(obj).parent().parent()
        let obj2 = $(obj1).next()
        changeDoms(obj1,obj2)
    }else {
        popMsg("无法下移")
    }
}

//交换两个节点
function changeDoms(d1,d2){
    var d11 = $('<hr/>')
    var d22 = $('<hr/>')
    $(d1).before(d11)
    $(d2).before(d22)

    $(d22).after(d1)
    $(d11).after(d2)

    $(d11).remove()
    $(d22).remove()

    sortMyTable()
}
function save(){
    $('#saveBtn').bind('click', function() {
        if(getValue($("#packageName").val()) == ""){
            popMsg("请填写打包名称");
            return;
        }
        if(getValue($("#imageUrl").val()) == "") {
            popMsg("请上传课程图片");
            return;
        }
        // if(getValue($("#originalPrice").val()) == "") {
        //     popMsg("请填写打包原价");
        //     return;
        // }
        var flag = false;
        $("#discountTableId").find("tr").each(function (n,obj){
            var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
            if (checkFlag){
                var discount = $(obj).find('td').eq(2).find('input').val()
                var price = $(obj).find('td').eq(3).find('input').val()
                if (discount == '' || price == ''){
                    flag = true
                }
            }
        })
        if (flag){
            popMsg("折扣和折后价不能为空");
            return;
        }
        popConfirm("确认保存?", function () {
            savePackInfo("1");
        });

    });

    $('#saveBtn2').bind('click', function() {
        if(getValue($("#packageName").val()) == ""){
            popMsg("请填写打包名称");
            return;
        }
        if(getValue($("#imageUrl").val()) == "") {
            popMsg("请上传课程图片");
            return;
        }
        // if(getValue($("#originalPrice").val()) == "") {
        //     popMsg("请填写打包原价");
        //     return;
        // }
        var flag = false;
        $("#discountTableId").find("tr").each(function (n,obj){
            var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
            if (checkFlag){
                var discount = $(obj).find('td').eq(2).find('input').val()
                var price = $(obj).find('td').eq(3).find('input').val()
                if (discount == '' || price == ''){
                    flag = true
                }
            }
        })
        if (flag){
            popMsg("折扣和折后价不能为空");
            return;
        }
        popConfirm("确认保存?", function () {
            savePackInfo("2");
        });

    });
}

function savePackInfo(val) {
    //关联课程
    var coursePackageMapDtoList = []
    $("#courseTBody").find("tr").each(function (n,obj){
        var courseId = $(obj).find("input").val()
        var sort = $(obj).find(".serialNumber").text()
        var coursePackageMapDto = {
            courseId:courseId,
            sort:sort
        }
        coursePackageMapDtoList.push(coursePackageMapDto)
    })

    var packageFlag = $('input:radio[name="packageFlag"]:checked').val()
    var releaseFlag = '1'
    if (packageFlag == '0'){
        releaseFlag = '0'
    }
    let param = {
        id:$("#id").val(),
        specialTypeId:$("#specialTypeId").val(),
        packageFlag : packageFlag,
        releaseFlag: releaseFlag,
        originalPrice:$("#originalPrice").val(),
        packageName:$("#packageName").val(),
        imageUrl:$("#imageUrl").val(),
        coursePackageMapDtoList:coursePackageMapDtoList,
        associationType:$('input:radio[name="associationType"]:checked').val(),
        capcoCourseFlag:$('input:radio[name="capcoCourseFlag"]:checked').val(),
        courseType:$('input[name=courseType]').val(),
        teacher:$('input[name=teacher]').val(),
        subject:$('input[name=subject]').val(),
        applyPlate:$('input[name=applyPlate]').val(),
        applyMechanism:$('input[name=applyMechanism]').val(),
        applyPerson:$('input[name=applyPerson]').val(),
        saveFlag:val

    }
    $.ajax({
        url: contextPath + '/packManagement/savePackInfo',
        data: JSON.stringify(param),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (req) {
            if (req.success) {
                closeWinCallBack(req);
            }
        },
        error: function () {
            popMsg("保存失败");
        }
    });
}

$.fn.extend({
    imageUrlUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "livePicImgSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#imageUrl").val("");
            $("#livePicDiv").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});

function packFlagInit(){
    if ($("#specialTypeId").val() != null && $("#specialTypeId").val() != ''){
        if ($('input:radio[name="packageFlag"]:checked').val() == null){
            $("#packageFlag1").attr('checked','true');
        }
        $("#packFlagId").css("display", "block");
        var packageFlag = $('input:radio[name="packageFlag"]:checked').val()
        if (packageFlag == '0'){
            $("#chargeId").css("display", "none");
        }else {
            $("#chargeId").css("display", "block");
        }
    }else {
        $("#packFlagId").css("display", "none");
    }
}

function associationTypeChange (item){
    var val = $(item).val()
    if (val == '1' || val == '2'){
        $("#selectCourseId").css("display", "none")
        $("#selectCourseTypeId").css("display", "block")
    }else {
        $("#selectCourseId").css("display", "block")
        $("#selectCourseTypeId").css("display", "none")
    }
}

function associationTypeInit(){
    var associationType = $("#associationType").val()
    if (associationType == '1'){
        $("#associationType1").prop('checked',true);
        $("#selectCourseId").css("display", "none")
    }else if (associationType == '2'){
        $("#associationType2").prop('checked',true);
        $("#selectCourseId").css("display", "none")
    }else {
        $("#associationType3").prop('checked',true);
        $("#selectCourseTypeId").css("display", "none")
    }

    var capcoCourseFlag = $("#capcoCourseFlag").val()
    if (capcoCourseFlag == '1'){
        $("#capcoCourseFlag2").prop('checked',true);
    }else {
        $("#capcoCourseFlag1").prop('checked',true);
    }
}

function capcoCourseFlagInit(){
    var capcoCourseFlag = $("#capcoCourseFlag").val()
    if (capcoCourseFlag == '0'){
        $("#capcoCourseFlag1").prop('checked',true);
    }else if (associationType == '1'){
        $("#capcoCourseFlag2").prop('checked',true);
    }
}

//下拉初始化
function tSelectInit() {
    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'all',
        inputSearch: true,
        openDown: false,//是否初始展开tree
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };

    var tSelectOptionType = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        useFooter: true,
        hiddenInput: true,
        grade: 1,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };

    $('#courseType').tselectInit(null, tSelectOptionType);
    $('#applyPlate').tselectInit(null, tSelectOptions);
    $('#applyPerson').tselectInit(null, tSelectOptions);
    $('#applyMechanism').tselectInit(null, tSelectOptions);
    $('#subject').tselectInit(null, tSelectOptions);

    var teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}