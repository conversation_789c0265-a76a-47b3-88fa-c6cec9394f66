$(document).ready(function () {

    $("#closeBtn").click(function () {
        closeBtn();
    });

})

function adoptButton(faceId,index) {
    popConfirm("确定通过当前验证?", function () {
        let param = {
            recognitionResults: '1',
            ifQualified : '2',
            faceId:faceId
        }
        ajaxData("/examMonitoring/updateVerificationStatus", param, function (res) {
            if (res) {
                popMsg("设定成功");
                refreshMonitor(index);
            } else {
                popMsg("设定失败");
            }
        })
    })
}

function refreshMonitor(index) {
    $(".adoptButton").remove()
    $(".verificationFailStatus_"+index+"").empty()
    $(".verificationFailInfo_"+index+"").empty()
    let html = '<div class="recognitionResultTitle" style="background-color: #00a0e9;">人脸验证通过</div>'
    $(".verificationFailStatus_"+index+"").append(html)
}

function closeBtn() {
    closeWin();
}

//审核通过
function examineAdopt(recordId,adoptFlag,examType){
    if (adoptFlag == '1'){
        popConfirm("确定通过审核?", function () {
            var param = {
                businessId:recordId,
                examineStatus:'0',//审核
                adoptFlag:adoptFlag//是否通过
            }
            ajaxData("/examMonitoring/examineAdopt", param, function (res) {
                popMsg("审核通过")
                closeWin();
            })
        })
    }else {
        var content =  '<textarea rows="4" id="sendMessage" class="form-control" style="width: 100%"></textarea>'
        if (examType=='444444'){
            content =  '<textarea rows="4" id="sendMessage" class="form-control" style="width: 100%">您的违法违规试卷，考试成绩不合格。请于2023年6月30前达到成绩合格，此期间可不限次重考。</textarea>'
        }else if (examType=='111111'){
            content =  '<textarea rows="4" id="sendMessage" class="form-control" style="width: 100%">学员您好！由于您在作答过程中存在违反考试纪律的行为，本次成绩无效。请您仔细阅读测试须知，重新测试。预祝您水平测试成功！</textarea>'
        }
        var layerIndex = layer.open({
            type:'1',
            title: '短信内容:审核不通过原因(不填内容默认不发送短信)',
            content:content,
            btn: ['确认', '取消'],
            area: ["660px", "460px"],
            yes: function(index, layero){
                var param = {
                    sendMessage:$("#sendMessage").val(),
                    businessId:recordId,
                    examineStatus:'0',//审核
                    adoptFlag:adoptFlag//是否通过
                };
                ajaxData('/examMonitoring/examineAdopt',param,function (res){
                   popMsg("短信发送成功")
                   layer.close(layerIndex);
                   closeWin();
                })
            },
            btn2: function(index, layero){

            }
        });
    }

}

//复核通过并下发证书
function sendCertificate(recordId,examRank,adoptFlag,examType){
    var certificateName = '01'
    if (examType == '777777'){
        certificateName = '02'
    }
    if (adoptFlag == '1'){
        var title = "确定下发证书并短信通知?"
        if (examType=='444444'){
            title = "确定复核通过并发送短信通知?"
        }
        popConfirm(title, function () {
            var param = {
                businessId:recordId,
                examRank:examRank,
                certificateName:certificateName,//证书类型：01：证代分级 02：董秘
                examineStatus:'1',//复核,
                adoptFlag:adoptFlag,
                examType:examType
            }
            ajaxData("/examMonitoring/sendCertificate", param, function (res) {
                if (res != "0") {
                    if(examType == '111111' || examType == '777777'){
                        popMsg("复核成功并已下发证书");
                    }else {
                        popMsg("复核成功并已发送短信通知");
                    }
                    closeWin();
                } else {
                    popMsg("当前复核人员与审核人员一致");
                }
            })
        })
    }else {
        var content =  '<textarea rows="4" id="sendMessage" class="form-control" style="width: 100%"></textarea>'
        if (examType=='444444'){
            content =  '<textarea rows="4" id="sendMessage" class="form-control" style="width: 100%">您的违法违规试卷，考试成绩不合格。请于2023年6月30前达到成绩合格，此期间可不限次重考。</textarea>'
        }else if (examType=='111111'){
            content =  '<textarea rows="4" id="sendMessage" class="form-control" style="width: 100%">学员您好！由于您在作答过程中存在违反考试纪律的行为，本次成绩无效。请您仔细阅读测试须知，重新测试。预祝您水平测试成功！</textarea>'
        }
        var layerIndex = layer.open({
            type:'1',
            title: '短信内容:复核不通过原因(不填内容默认不发送短信)',
            content:content,
            btn: ['确认', '取消'],
            area: ["660px", "460px"],
            yes: function(index, layero){
                var param = {
                    sendMessage:$("#sendMessage").val(),
                    businessId:recordId,
                    examineStatus:'1',//复核
                    adoptFlag:adoptFlag//是否通过
                };
                ajaxData('/examMonitoring/examineAdopt',param,function (res){
                    if (res != "0") {
                        popMsg("短信发送成功")
                        layer.close(layerIndex);
                        closeWin();
                    }else {
                        popMsg("当前复核人员与审核人员一致");

                    }
                })
            },
            btn2: function(index, layero){

            }
        });
    }
}