$(document).ready(function () {

    $("#btnCheck").bind("click", function () {
        checkFirm();
    });


})

function checkFirm(){
    
    var checkState = $('#checkState').val()
    if (checkState != ''){
        applyCertificate(checkState);
    }else {
        popMsg("请选择审核结果")
    }
}

function applyCertificate(checkState){
    var message = ""
    if (checkState == '2'){
        message = "确定审核通过并更换证书照片?"
    }else {
        message = "确定审核不通过？"
    }
    popConfirm(message, function () {
        var param = {
            ifApplyCertificate:checkState,
            remindContent:$("#remindContent").val(),
            businessId:$("#businessId").val(),
            examRank:$("#examRank").val(),
            certificateName:$("#examRank").val(),
            certificateNumber:$("#certificateNumber").val(),
            createTime:$("#createTime").val(),
        }
        ajaxData("/certificateConfig/applyCertificate", param, function (res) {
            closeWinCallBack()
            popMsg("审核成功");
        })
    })
}


function imageClick(index) {
    const viewer = new Viewer(document.querySelectorAll(".ImgPr")[index], {
        inline: false
    });
}
