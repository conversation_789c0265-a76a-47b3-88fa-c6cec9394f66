$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });
})

function search(){
    ajaxTableQuery("tableAll", "/examConfig/getPaperList",
        $("#queryForm").formSerialize());
}

// 列表索引列
function questionOrganizeTypeOperation(data, type, row, meta) {
    if (data.questionOrganizeType == '2'){
        return '随机试卷'
    }else {
        return '固定试卷'
    }
}

function columnOperation(data, type, row, meta) {
    var str = '';
    str = '<a href="javascript:void(0)" onClick="selectPaper(\'' + data.paperId + '\',\'' + data.paperName + '\')" title="选择试卷">选择</a>';
    return str;
}

function selectPaper(paperId,paperName){
    var data = {
        paperId:paperId,
        paperName:paperName
    }
    closeWinCallBack(data)
}