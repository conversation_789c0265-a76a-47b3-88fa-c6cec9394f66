<%--
  Created by IntelliJ IDEA.
  User: 86156
  Date: 2023/3/4
  Time: 14:42
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            margin-top: 6px;
        }
        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <h2 class="sub-title">统一考试</h2>
        <div class="row" >
            <label class="col-xs-1 control-label">
            </label>
            <div class="col-xs-11">
                <table class="table table-bordered no-margin" style="text-align: center;float:left;" id="examTableId">
                    <thead>
                    <div class="col-md-12">
                        <div style="margin-bottom: 8px;margin-right: 8px;float: right;">
                            <c:if test="${examRankUnifyMapDTO.examType == '111111' && examRankUnifyMapDTO.examRank == '03'}">
                                <span id="downloadTemplate" class="identity-limit btn btn-primary">下载导入模板</span>
                            </c:if>
                            <sec:authorize access="hasAuthority('RES_UNIFY_EXAM_EDIT_AUTHORITY_3')" >
                                <%--统一考试编辑权限--%>
                                <span id="addUnifyExam" class="btn btn-primary" onclick="editUnifyExam('','${examRankUnifyMapDTO.examRankId}');">新建</span>
                            </sec:authorize>
<%--                            <input type="hidden" id="examRankId" name="examRankId" value="${examRankUnifyMapDTO.examRankId}" class="c-input">--%>
                        </div>
                    </div>
                    <tr role="row">
                        <th class="text-center" style="width: 8%;">序号</th>
                        <th class="text-center" style="width: 10%;">考试名称</th>
                        <th class="text-center" style="width: 10%;">考试开始时间</th>
                        <th class="text-center" style="width: 10%;">考试结束时间</th>
                        <c:if test="${examRankUnifyMapDTO.examType == '111111' && examRankUnifyMapDTO.examRank == '03'}">
                            <th class="text-center" style="width: 10%;">导入线下考试列表</th>
                        </c:if>
                        <th class="text-center" style="width: 10%;">操作</th>
                    </tr>
                    </thead>
                    <tbody id="examTbodyId">
                    <c:forEach items="${unifyExamList}" var="item" varStatus="status">
                        <tr>
                            <td class="sort-no">
                                <div style="margin-top: 5px">${item.sort}</div>
                            </td>
                            <td>
                                <div style="margin-top: 5px">${item.paperName}</div>
                            </td>
                            <td>
                                <div style="margin-top: 5px">${item.startTime}</div>
                            </td>
                            <td>
                                <div style="margin-top: 5px">${item.endTime}</div>
                            </td>
                            <c:if test="${examRankUnifyMapDTO.examType == '111111' && examRankUnifyMapDTO.examRank == '03'}">
                                <td>
                                    <div class="text-center" style="margin-bottom: 5px">
                                        <sec:authorize access="hasAuthority('RES_IMPORT_OFFLINE_EXAM_EXCEL_PERSON_AUTHORITY_3')" >
                                            <%--导入线下考试列表权限--%>
                                            <span id="importExamListButton" onclick="importExamList(this,'${item.id}','${item.examRankId}')" class="btn btn-primary fileinput-button">
                                                <span>导入线下考试列表</span>
                                                <input id="importExamList" type="file" name="files" class="identity-limit btn btn-primary"/>
                                            </span>
                                        </sec:authorize>
                                    </div>
                                </td>
                            </c:if>
                            <td>
                                <div style="margin-top: 5px">
                                            <span>
                                                <sec:authorize access="hasAuthority('RES_UNIFY_EXAM_EDIT_AUTHORITY_3')" >
                                                    <%--统一考试编辑权限--%>
                                                    <a href="#" onclick="editUnifyExam('${item.id}','${item.examRankId}')">编辑</a>
                                                </sec:authorize>
                                                <sec:authorize access="hasAuthority('RES_RELEVANCE_PERSON_INIT_AUTHORITY_3')" >
                                                    <%--关联人员页面初始化权限--%>
                                                    <a href="#" onclick="relevancePerson('${item.id}')">|关联人员</a>
                                                </sec:authorize>
                                                <sec:authorize access="hasAuthority('RES_SELECT_PAPER_INFO_AUTHORITY_3')" >
                                                    <%--选择试卷列表页初始化权限--%>
                                                    <a href="#" onclick="addExamRank('${item.id}')">|选择试卷</a>
                                                </sec:authorize>
                                                <sec:authorize access="hasAuthority('RES_DELETE_UNIFY_EXAM_AUTHORITY_3')" >
                                                    <%--删除统一考试权限--%>
                                                    <a href="#" onclick="deleteUnifyExam('${item.id}')">|删除</a>
                                                </sec:authorize>
                                            </span>
                                </div>
                            </td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
</html>

