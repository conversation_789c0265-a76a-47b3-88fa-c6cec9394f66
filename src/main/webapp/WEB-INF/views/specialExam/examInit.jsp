<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <script type="text/javascript">
        var userId = "${userId}";
        var personName = "${personName}";
        var orgId = "${orgId}";
        var examBaseUrl = "${examBaseUrl}";
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="examDto" id="queryForm" >
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">考试名称</label>
                    <div class="col-md-3">
                        <form:input path="examName" placeholder="请输入考试名称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">是否发布</label>
                    <div class="col-md-3">
                        <form:select path="releaseFlag" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未发布</form:option>
                            <form:option value="1">已发布</form:option>
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">发布时间</label>
                    <div class="col-md-3 daterange">
                        <form:input path="releaseTime" placeholder="请输发布时间" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">业务类型</label>
                    <div class="col-md-3">
                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' />
                        <input name="courseType" type="hidden" placeholder="请选择课程类型" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_QUERY_EXAM_INFO_AUTHORITY_3')" >
                        <%--查询考核列表权限--%>
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <span id="btnClear" class="btn btn-default">清空全部</span>
                    <sec:authorize access="hasAuthority('RES_ADD_EXAM_AUTHORITY_3')" >
                        <%--新增考核初始化权限--%>
                        <span id="addExam" class="btn btn-primary">新增考核</span>
                    </sec:authorize>
                        <%--访问题库管理权限--%>
                        <span id="examManage" class="btn btn-primary">题库管理</span>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_ADD_EXAM_AUTHORITY_3')" >
            <%--编辑考核初始化权限--%>
            <input type="hidden" id="editExamAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_UPDSTE_EXAM_STATUS_AUTHORITY_3')" >
            <%--修改考核状态（删除考核）权限--%>
            <input type="hidden" id="updateExamAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/examConfig/queryExamInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="考核名称" displayColumn="examName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="业务分类" displayColumn="courseType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否发布" renderColumn="columnRelease"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="发布时间" displayColumn="releaseTime" orderColumn="release_time" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="创建人" displayColumn="createUser" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>

</body>
</html>
