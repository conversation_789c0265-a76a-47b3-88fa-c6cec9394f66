var selCourse = [];

$(document).ready(function () {

    tSelectInit();

    $(window).keydown( function(e) {
        var key = window.event?e.keyCode:e.which;
        if(key.toString() == "13"){
            return false;
        }
    });

    //查询已被选择的包
    ajaxData("/packManagement/getPackCourseMap",{id:$('#id').val()},function (res) {
        selCourse = res
    })

    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        tSelectInit();
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        selCourse = [];
        search();
    });
    $('#allCheck').click(function () {
        let nodes = document.getElementsByClassName("selDeclare")
        let flag = false
        if(this.checked){
            flag = true
        }
        for (let i = 0; i < nodes.length; i++) {
            var id = nodes[i].attributes.getNamedItem("d-id").nodeValue;//通过选择的input按钮获取ID
            if (flag) {
                nodes[i].checked = true
            } else {
                nodes[i].checked = false
            }
            if (nodes[i].checked) {
                if (selCourse.indexOf(id) === -1) {
                    selCourse.push(id)
                }
            } else {
                selCourse.some((item,i)=>{
                    if(item === id){
                        selCourse.splice(i,1)
                    }
                })
            }
        }
    })

});

//下拉初始化
function tSelectInit() {

    var businessSpecialTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, businessSpecialTypeSelectOptions);

    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };

    $('#applyMechanism').tselectInit(null, tSelectOptions);

}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function search() {
    ajaxTableQuery("tableAll", "/homepageConfig/queryChooseCourse", $("#queryForm").formSerialize());
}
function rcIndex(data, type, row, meta) {
    var flag = false;

    selCourse.forEach((item)=>{
        if(item == data.id){
            flag = true
        }
    })

    var str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;" onclick="selCompany(this)">';
    if(flag){
        str += '<input  type="checkbox" checked name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }else {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="courseId" value="' + data.id + '">'
    str += '</div>';
    return str;
}

function courseName(data, type, row, meta) {
    var str = '<div style="cursor:pointer;width: 100%" onclick="selCourseName(this)">'+data.courseName+'</div>'
    return str;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        if (selCourse.indexOf(id) === -1) {
            selCourse.push(id)
        }
    } else {
        selCourse.some((item,i)=>{
            if(item === id){
                selCourse.splice(i,1)
            }
        })
    }
}
function submitOn() {
    if (selCourse.length >0){
        closeWinCallBack(selCourse.toString())
    }else {
        popMsg("请选择课程")
    }

}
