<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="paperDto" id="queryForm" >
            <form:hidden path="id"/>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">试卷名称</label>
                    <div class="col-md-3">
                        <form:input path="paperName" placeholder="请输入试卷名称" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_GET_PAPER_LIST_AUTHORITY_3')" >
                        <%--查询试卷列表权限--%>
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_ADD_EXAM_AUTHORITY_3')" >
            <%--编辑考核初始化权限--%>
            <input type="hidden" id="editExamAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/examConfig/getPaperList?id=${paperDto.id}" cssClass="table table-striped table-hover">
                <e:gridColumn label="试卷编号" displayColumn="paperNumber" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="试卷名称" displayColumn="paperName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="试卷类型" renderColumn="questionOrganizeTypeOperation" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="试卷题数" displayColumn="paperQuestionNum"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="试卷分数" displayColumn="paperScore" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="创建人" displayColumn="createUser" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="创建时间" displayColumn="createDate" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>

</body>
</html>
