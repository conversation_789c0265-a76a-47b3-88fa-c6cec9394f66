<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <script type="text/javascript">
        const examBaseUrl = "${examBaseUrl}";
    </script>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            margin-top: 6px;
        }
        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="examSpecialPersonDTO" id="examSpecialPersonForm">
        <input type="hidden" id="examRankUnifyId" name="examRankUnifyId" value="${examSpecialPersonDTO.examRankUnifyId}"/>
            <input type="hidden" id="startTime" name="startTime" value="${examRankUnifyMapDTO.startTime}"/>
            <input type="hidden" id="endTime" name="endTime" value="${examRankUnifyMapDTO.endTime}"/>
        <div class="row">
            <div class="col-md-12">
                <label class=" col-md-1 control-label">姓名</label>
                <div class="col-md-3">
                    <form:input path="realName" placeholder="请输入姓名" cssClass="form-control"/>
                </div>
                <label class=" col-md-1 control-label">手机号</label>
                <div class="col-md-3">
                    <form:input path="phone" placeholder="请输入手机号" cssClass="form-control"/>
                </div>
                <label class=" col-md-1 control-label">证券代码</label>
                <div class="col-md-3">
                    <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control"/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <label class=" col-md-1 control-label">公司简称</label>
                <div class="col-md-3">
                    <form:input path="companyName" placeholder="请输入公司简称" cssClass="form-control"/>
                </div>
                <label class="col-md-1 control-label">用户类型</label>
                <div class="col-md-3">
                    <input id="personType" type="text" class="t-select" json-data='${personTypeList}' placeholder="请输入用户类型" />
                    <input name="personType" type="hidden"  />
                </div>
                <label class="col-md-1 control-label">所在辖区</label>
                <div class="col-md-3">
                    <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
                    <input name="belongCommission" type="hidden" />
                </div>
            </div>
        </div>
            <div class="row">
                <div class="col-md-12 text-right">
                    <div style="margin-bottom: 8px;margin-right: 8px;float: right;">
                        <span id="btnClear" class="btn btn-default">清空条件</span>
                        <sec:authorize access="hasAuthority('RES_QUERY_RELEVANCE_PERSON_LIST_AUTHORITY_3')" >
                            <%--查询关联人员列表权限--%>
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_RELEVANCE_PERSON_ADD_INIT_AUTHORITY_3')" >
                            <%--新增关联人员初始化权限--%>
                            <span id="addPerson" class="btn btn-primary">新增</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_VIEW_MONITORING_INFO_AUTHORITY_3')" >
                            <%--查看监控信息权限--%>
                            <span id="viewMonitoring" class="btn btn-primary">查看监控</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_EXPORT_RELEVANCE_PERSON_AUTHORITY_3')" >
                            <%--导出关联人员权限--%>
                            <span id="export" class="btn btn-primary">导出</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_IMPORT_RELEVANCE_PERSON_EXCEL_AUTHORITY_3')" >
                            <%--导入关联人员权限--%>
                            <span class="btn btn-primary fileinput-button">
                            <span class="">导入</span><input id="importRelevancePersonExcel" type="file" name="files"
                                                             class="btn btn-primary"/>
                        </span>
                        </sec:authorize>
                        <a href="#" id="downloadTemplate" style="margin-top: 5px">下载导入模板</a>
                    </div>
                </div>
            </div>
        </div>
    </form:form>
    <sec:authorize access="hasAuthority('RES_SELECT_MONITOR_AUTHORITY_3')" >
        <%--查看认证信息权限--%>
        <input type="hidden" id="selectMonitorInfoAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_VIEW_EXAM_INFO_AUTHORITY_3')" >
        <%--查看试卷权限--%>
        <input type="hidden" id="viewExamAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_DELETE_RELEVANCE_PERSON_AUTHORITY_3')" >
        <%--删除关联人员权限--%>
        <input type="hidden" id="delRelevancePersonAuth" value="true">
    </sec:authorize>
    <div class="row" style="padding-left: 10px;padding-right: 10px">
        <e:grid id="table1" action="/examConfig/queryRelevancePersonList?examRankUnifyId=${examSpecialPersonDTO.examRankUnifyId}"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:3%"/>
            <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                          cssClass="text-center" cssStyle="width:4%"/>
            <e:gridColumn label="证券代码" displayColumn="companyCode" orderable="false"
                          cssClass="text-center" cssStyle="width:4%"/>
            <e:gridColumn label="公司简称" displayColumn="companyName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="考试状态" displayColumn="examStatus" orderable="false"
                          cssClass="text-center" cssStyle="width:4%"/>
            <e:gridColumn label="证书状态" displayColumn="ifCertificate" orderable="false"
                          cssClass="text-center" cssStyle="width:3%"/>
            <e:gridColumn label="交卷时间" displayColumn="submitTime" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="证书下发时间" displayColumn="examineTime" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="作答时间" displayColumn="timeSlot" orderable="false"
                          cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="分数" displayColumn="score" orderable="false"
                          cssClass="text-center" cssStyle="width:4%"/>
            <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                          cssClass="text-center" cssStyle="width:12%;"/>
        </e:grid>
    </div>
</div>
</body>
</html>

