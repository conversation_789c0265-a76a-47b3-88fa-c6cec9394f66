<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <style>
        .c-label {
            padding-top: 6px;
            text-align: right;
            padding-right: 0 !important;
        }
        .c-input {
            border-radius: 0;
            border: 1px solid #e1e1e1;
            height: 30px;
            width: 80px;
        }
    </style>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">

    <div class="panel-heading" style="min-height: 900px">

        <div class="row" style="padding-top: 5px;padding-bottom: 10px">
            <div class="col-md-12">
                <div class="col-md-6 text-left">
                    <div style="font-size: 20px;font-weight: bold">考试基本信息</div>
                </div>

                <div class="col-md-6 text-right">
                    <sec:authorize access="hasAuthority('RES_UPDSTE_EXAM_STATUS_AUTHORITY_3')" >
                        <%--修改考核状态权限--%>
                        <span id="btnRelease" class="identity-limit btn btn-primary">发布</span>
                        <span id="btnCancel" class="identity-limit btn btn-primary">取消发布</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_SAVE_EXAM_AUTHORITY_3')" >
                        <%--保存考核基本信息权限--%>
                        <span id="btnSaveExam" class="identity-limit btn btn-primary">保存基本信息</span>
                    </sec:authorize>
                    <span id="btnClose" class="btn">关闭</span>
                </div>
            </div>
        </div>
        <form:form modelAttribute="examDto" id="queryForm" autocomplete="off">
            <form:hidden path="id"/>
            <form:hidden path="releaseFlag"/>
            <form:hidden path="examRanks"/>

            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label c-label"><span style="color: #FF0000;font-weight: bold;">*</span>考试名称</label>
                    <div class="col-md-7">
                        <form:input path="examName" placeholder="请输入考试名称" cssClass="form-control"/>
                    </div>
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>考试发布时间</label>
                    <div class="col-md-3 daterange">
                        <e:date cssClass="form-control" path="releaseTime" format="yyyy-mm-dd hh:ii:ss" placeholder="请选择考试发布时间" cssStyle="background-color: #e5e5e5;"/>
                        <i class="glyphicon glyphicon-calendar fa fa-calendar daterangetag" style="right: 18px; top: 7px;"></i>
                    </div>
                </div>
            </div>
            <div class="row" >
                <div class="col-md-12">
                    <label class="col-md-1 control-label c-label"><span style="color: #FF0000;font-weight: bold;">*</span>适用考试</label>
                    <div class="col-md-5">
                        <form:select path="examType" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:options items="${examTypeList}" itemLabel="codeName" itemValue="codeValue" />
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row" >
                <div class="col-md-12">
                    <label class="col-md-1 control-label c-label">考试描述</label>
                    <div class="col-md-5">
                        <form:textarea path="examDescribe"  cssClass="form-control" placeholder=""/>
                    </div>
                </div>
            </div>
<%--            <div class="row" >--%>
<%--                <div class="col-md-12">--%>
<%--                    <label class="col-md-1 control-label c-label">业务类型</label>--%>
<%--                    <div class="col-md-5">--%>
<%--                        <input id="courseType" type="text" json-data='${courseTypeSelect001}' class="t-select isRequired"selected-ids="${examDto.courseType}" placeholder="请选择课程类型"/>--%>
<%--                    </div>--%>
<%--                    <label class="col-md-1 control-label c-label" id="courseHours" style="text-align: left">(共${courseHours}学时)</label>--%>
<%--                </div>--%>
<%--            </div>--%>
            <h2 class="sub-title">选择试卷</h2>
            <%--<div class="text-right" style="margin-bottom: 5px">
                <span id="importExamListButton" class="btn btn-primary fileinput-button">
                            <span class="">导入线下考试列表</span>
                    <input id="importExamList" type="file" name="files" class="identity-limit btn btn-primary"/>
                </span>
                <span id="downloadTemplate" class="identity-limit btn btn-primary">下载导入模板</span>
            </div>--%>
            <div class="row" >
                <label class="col-xs-1 control-label">
                </label>
                <div class="col-xs-11">
                    <table class="table table-bordered no-margin" style="text-align: center;float:left;" id="examTableId">
                        <thead>
                            <tr role="row">
                                <th class="text-center" style="width: 8%;">序号</th>
                                <th class="text-center" style="width: 10%;">考试级别</th>
                                <th class="text-center" style="width: 10%;">所需学时</th>
                                <th class="text-center" style="width: 10%;">作答间隔</th>
                                <th class="text-center" style="width: 10%;">是否允许单人考试</th>
                                <th class="text-center" style="width: 15%;">选择试卷</th>
                                <th class="text-center" style="width: 10%;">
                                    <i class="fa fa-plus-square-o btn-row-add" title="新增"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="examTbodyId">
                            <c:forEach items="${examDto.examRankList}" var="item" varStatus="status">
                                <tr>
                                    <td class="sort-no">${item.sort}</td>
                                    <td>
                                        <select id="examRank" name="examRank" class="form-control" >
                                            <option value="">请选择</option>
                                            <option value="01"
                                                    <c:if test="${item.examRank == '01'}">selected</c:if>
                                            >初级</option>
                                            <option value="02"
                                                    <c:if test="${item.examRank == '02'}">selected</c:if>
                                            >中级</option>
                                            <option value="03"
                                                    <c:if test="${item.examRank == '03'}">selected</c:if>
                                            >高级</option>
                                            <option value="04"
                                                    <c:if test="${item.examRank == '04'}">selected</c:if>
                                            >种子期</option>
                                            <option value="05"
                                                    <c:if test="${item.examRank == '05'}">selected</c:if>
                                            >培育期</option>
                                            <option value="06"
                                                    <c:if test="${item.examRank == '06'}">selected</c:if>
                                            >辅导期</option>
                                            <option value="07"
                                                    <c:if test="${item.examRank == '07'}">selected</c:if>
                                            >初阶</option>
                                            <option value="08"
                                                    <c:if test="${item.examRank == '08'}">selected</c:if>
                                            >进阶</option>
                                            <option value="09"
                                                    <c:if test="${item.examRank == '09'}">selected</c:if>
                                            >高阶</option>
                                        </select>
                                    </td>

                                    <td>
                                        <input type="text" value="${item.courseTime}" class="c-input">学时
                                    </td>
                                    <td>
                                        <input type="text" value="${item.answerInterval}" class="c-input">天
                                    </td>
                                    <td>
                                        <select id="ifSingle" name="ifSingle" class="form-control" >
                                            <option value="">请选择</option>
                                            <option value="1"
                                                    <c:if test="${item.ifSingle == '1'}">selected</c:if>
                                            >是</option>
                                            <option value="0"
                                                    <c:if test="${item.ifSingle == '0'}">selected</c:if>
                                            >否</option>
                                        </select>
                                    </td>
                                    <td>
                                        <sec:authorize access="hasAuthority('RES_SELECT_PAPER_INFO_AUTHORITY_3')" >
                                            <%--选择试卷列表页权限--%>
                                            <span class="btn btn-primary" onclick="addExamRank(this,'${item.id}')">选择试卷</span>
                                        </sec:authorize>
                                        <span class="paperName">${item.paperName}</span>
                                        <input name="paperId" type="hidden" value="${item.paperId}">
                                    </td>
                                    <td>
                                        <div style="margin-top: 8px">
                                            <span>
                                                <input type="hidden" id="examRankId" name="examRankId" value="${item.id}" class="c-input">
                                                <sec:authorize access="hasAuthority('RES_UNIFY_EXAM_INIT_AUTHORITY_3')" >
                                                    <%--选择试卷列表页权限--%>
                                                    <a href="#" onclick="addUnifyExam(this)">统一考试</a>
                                                </sec:authorize>
                                                |
                                                <sec:authorize access="hasAuthority('RES_DELETE_EXAM_RANK_AUTHORITY_3')" >
                                                    <%--删除考试级别权限--%>
                                                    <a href="#" onclick="removeExam(this,'${item.id}')">刪除</a>
                                                </sec:authorize>
                                            </span>
                                        </div>
                                    </td>
                                </tr>

                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
