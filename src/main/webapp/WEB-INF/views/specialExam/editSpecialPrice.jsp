<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        #livePicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .isNumber{
            border: 1px solid #ccc;
            height: 30px;
            width: 65%;
            background: #F5F5F5;
        }
    </style>
</head>
<body>

<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="specialGradeDto" id="queryForm" autocomplete="off">
            <form:hidden path="gradeId"/>
            <form:hidden path="personTypes"/>
            <div class="col-md-12" style="padding-top: 20px">
                <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>打包原价：</label>
                <div class="col-md-5">
                    <form:input path="originalPrice" placeholder="打包原价" onkeyup="value=value.replace(/[^\-?\d.]/g,'')" cssClass="form-control" onblur="calculatePrice()"/>
                </div>
            </div>
            <div id="chargeId" class="col-md-12" style="margin: 10px 0">
                <label class="col-xs-1 control-label">
                    收费设置
                </label>
                <div class="col-xs-10">
                    <c:if test="${mechanismList != null}">
                        <table class="table table-bordered no-margin" style="text-align: center;width: 80%;float:left;" id="configCourseType">
                            <thead>
                            <tr>
                                <td width="40%" class="sch-td">机构名称</td>
                                <td width="15%" class="sch-td">打折类型</td>
                                <td width="15%" class="sch-td">折数</td>
                                <td width="15%" class="sch-td">折后价格</td>
                            </tr>
                            </thead>
                            <tbody id="discountTableId">
                            <c:forEach items="${mechanismList}" var="item" varStatus="status">
                                <tr>
                                    <td>
                                        <input type="checkbox" name="interest_${status.index}" id="codeValue_${status.index}" value="${item.codeValue}" onchange="chargeChange(this,${status.index})"
                                                <c:if test="${item.relationId !='' && item.relationId != null}">
                                                    checked="checked"
                                                </c:if>
                                        />
                                        <label>${item.codeName}</label>
                                    </td>
                                    <td class="ifFreeClass">
                                        <c:choose>
                                            <c:when test="${item.relationId !='' && item.relationId != null}">
                                                <input type="radio" name="ifFree_${status.index}" id="ifFree_${status.index}" value="0" onchange="freeChange(this,${status.index})"
                                                        <c:if test="${item.ifDiscount == '0'}">
                                                            checked="checked"
                                                        </c:if>
                                                />
                                                <label for="ifFree_${status.index}">不打折</label>
                                                <input type="radio" name="ifFree_${status.index}" id="ifNotFree_${status.index}" value="1" style="margin-left: 15px" onchange="notFreeChange(this,${status.index})"
                                                        <c:if test="${item.ifDiscount == '1'}">
                                                            checked="checked"
                                                        </c:if>
                                                />
                                                <label for="ifNotFree_${status.index}">打折</label>
                                            </c:when>
                                            <c:otherwise>
                                                --
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="discountClass">
                                        <c:choose>
                                            <c:when test="${item.ifDiscount == '1'}">
                                                <input type="text" name="discount_${status.index}" onkeyup="value=value.replace(/[^\-?\d.]/g,'')"  value="${item.discount}" class="isNumber" onblur="discountCal(this)"/>
                                                <label>折</label>
                                            </c:when>
                                            <c:otherwise>
                                                --
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="priceClass">
                                        <c:choose>
                                            <c:when test="${item.ifDiscount == '1'}">
                                                <input type="text" name="price_${status.index}" onkeyup="value=value.replace(/[^\-?\d.]/g,'')" placeholder="只能输入数字" value="${item.price}" class="isNumber" onblur="priceCal(this)"/>
                                                <label>元</label>
                                            </c:when>
                                            <c:otherwise>
                                                --
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </c:if>
                </div>
            </div>
            <div class="col-md-12" style="text-align: center;padding-top: 30px">
                <input type="button" id="closeBtn" class="btn btn-default" value="关闭">
                <input type="button" id="saveBtn" class="btn btn-primary" value="保存">
            </div>
        </form:form>
    </div>
</div>

</body>
</html>
