$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("examSpecialPersonForm").reset();
        $('input[name="personType"]').val("");
        $('input[name="belongCommission"]').val("");
        $('input[name="userName"]').val("");
        $('input[name="phone"]').val("");
        search()
    });
    //查询
    $("#btnQuery").bind("click", function () {
        search()
    });
    //新增
    $("#addPerson").bind("click", function () {
        let param = {
            examRankUnifyId: $("#examRankUnifyId").val()
        }
        parent.popWin('新增关联人员', "/examConfig/relevancePersonAddInit", param, "100%", "100%", callBackAddUser);
    })
    //查看监控
    $("#viewMonitoring").bind("click", function () {
        let now = (new Date()).valueOf();
        let startTime = new Date($("#startTime").val()).valueOf()
        let endTime = new Date($("#endTime").val()).valueOf()
        if (now < startTime){
            popMsg("考试还未开始!")
            return;
        }
        let examRankUnifyId = $("#examRankUnifyId").val()
        let url;
        if (now > endTime) {
            url = examBaseUrl + 'ui/exam/capcoExam/rtc/roomHistory?roomId=' + examRankUnifyId
            window.open(url);
            return
        }
        url = examBaseUrl + 'ui/exam/capcoExam/rtc/room?roomId=' + examRankUnifyId
        window.open(url);
    });
    //导入
    $("#importRelevancePersonExcel").bind("click", function () {
        importRelevancePersonExcel()
    });
    //导出
    $("#export").bind("click", function () {
        window.open(contextPath + "/examConfig/exportRelevancePerson?" + $("#examSpecialPersonForm").serialize());
    });
    //下载导入模板
    $("#downloadTemplate").bind("click", function () {
        window.open(contextPath + "/examConfig/exportRelevancePerson?");
    });
})

function search() {
    ajaxTableQuery("table1", "/examConfig/queryRelevancePersonList", $("#examSpecialPersonForm").formSerialize());
}
function tSelectInit(){
    let teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function callBackAddUser() {
    popMsg("新增成功!");
    ajaxTableReload("table1", false);
}

function importRelevancePersonExcel() {
    $("#importRelevancePersonExcel").fileupload({
        url: contextPath + "/examConfig/importRelevancePersonExcel",
        dataType: "json",
        autoUpload: true,
        formData: {examRankUnifyId : $("#examRankUnifyId").val()},
        add: function (e, data) {
            if (data.files[0].name.indexOf("xlsx") === -1) {
                popMsg("只支持.xlsx格式文件上传");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            layer.close(index);
            const rtnRlt = data.result;
            if (rtnRlt != null) {
                _cmImportResultDto = rtnRlt.result;
                if (_cmImportResultDto.importStatus === '0'){
                    
                    if( _cmImportResultDto.realName){
                        popAlert("导入失败,请检查姓名为“" + _cmImportResultDto.realName + "”的数据是否有误");
                    }else {
                        popAlert("导入失败,导入文件不能为空");
                    }
                    return
                }
                if (_cmImportResultDto.importStatus === '1') {
                    popAlert("导入成功");
                    // 刷新数据列表
                    ajaxTableReload("table1", false);
                }
            }
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("导入失败,请检查导入数据");
        }
    });
}

//操作-查看认证
function viewMonitoring(recordId,answerId,examRank,createUser){
    let now = (new Date()).valueOf();
    let startTime = new Date($("#startTime").val()).valueOf()
    let endTime = new Date($("#endTime").val()).valueOf()
    if (now < startTime){
        popMsg("考试还未开始!")
        return
    }
    if (now > endTime){
        popMsg("考试已结束!")
        return;
    }
    let param = {
        recordId:recordId,
        answerId:answerId,
        examRank:examRank,
        createUser:createUser
    }
    parent.popWin('考试认证', "/examMonitoring/selectMonitorInit", param, "98%", "98%", callBackViewMonitoring);

}
function callBackViewMonitoring(){
}

//操作-查看试卷
function viewExam(answerId,paperId,createUser){
    let params = {
        createUser : createUser
    }
    ajaxData("/examMonitoring/getUserType",params, function (res){
        let url = examBaseUrl + 'ui/exam/finishLook?answerId='+answerId+'&paperLook=1&comeType=1&status=1&indexType=0&userType='+res+'&paperId='+paperId;
        window.open(encodeURI(url));
    })

}

function deleteRelevance(id) {
    popConfirm("确认取消该关联人员吗？", function () {
        let param = {
            id: id
        };
        ajaxData("/examConfig/deleteRelevancePerson", param, function (data) {
            if (data > 0) {
                popMsg("删除成功!");
                ajaxTableReload("table1", false);
                return
            }
            popMsg("操作失败，请稍后再试");
        });
    })
}

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function renderColumnOperation(data) {
    let delRelevancePersonAuth = false;
    let viewExamAuth = false;
    let selectMonitorInfoAuth = false;

    if (document.getElementById("viewExamAuth")) {
        viewExamAuth = true;
    }

    if (document.getElementById("selectMonitorInfoAuth")) {
        selectMonitorInfoAuth = true;
    }

    if (document.getElementById("delRelevancePersonAuth")) {
        delRelevancePersonAuth = true;
    }

    //examStatusValue = 0:未开始 1:人脸验证失败 2:人脸验证通过 3:考试中 4:未通过 5:通过
    let str = ''
    if (selectMonitorInfoAuth) {
        str += '<a href="#" onClick="viewMonitoring(\'' + data.recordId + '\',\'' + data.answerId + '\',\'' + data.examRank + '\',\'' + data.createUser + '\')" style="margin-right:4px;">查看认证</a>';
        str += '<span style="color: #00a0e9">| </span>';
    }
    if ((data.examStatusValue === '4' || data.examStatusValue === '5') && viewExamAuth) {
        str += '<a href="#" onClick="viewExam(\'' + data.answerId + '\',\'' + data.paperId + '\',\'' + data.createUser + '\')" style="margin-right:4px;">查看试卷</a>';
        str += '<span style="color: #00a0e9">| </span>';
    }

    if (delRelevancePersonAuth) {
        str += '<a href="#" onClick="deleteRelevance(\'' + data.id + '\')" style="margin-right:4px;">删除</a>';
    }

    if (!str) {
        str += '--'
    }
    return str;
}
