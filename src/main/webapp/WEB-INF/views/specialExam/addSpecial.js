var _ImgUploadUrl = contextPath + '/filetempupload';
$(document).ready(function () {
    releaseOperation()
    $("#btnSaveSpecial").click(function (){
        if ($("#queryForm").valid()) {
            btnSaveSpecial();
        }
    })
    if ($("#examId").val() !=null && $("#examId").val() != ''){
        $('.selectExam-class').css('display', 'block');
    }else {
        $('.selectExam-class').css('display', 'none');
    }

    if ($("#packageId").val() !=null && $("#packageId").val() != ''){
        $('.selectPackage-class').css('display', 'block');
    }else {
        $('.selectPackage-class').css('display', 'none');
    }


    $("#btnClose").click(function () {
        popConfirm("确认关闭?", function () {
            btnClose();
        });
    });

    $("#btnRelease").click(function (){
        popConfirm('确定发布？', function () {
            releaseSpecialInfo("1")
        })
    })

    $("#btnCancel").click(function (){
        popConfirm('确定取消发布？', function () {
            releaseSpecialInfo("0")
        })
    })


    $("#queryForm").validate({
        ignore:"",
        rules: {
            "specialName": {
                required: true,
            },
            "specialType": {
                required: true,
            },

        },
        messages: {
            "specialName": {
                required: "请填写专题名称"
            },
            "specialType": {
                required: "请选择专题类型"
            },
        }
    });

    rem()

    specialPicInit();
})

function specialPicInit(){
    $('#specialPicRemoveBtn').bind('click', function () {
        clearSpecialPic();
    });
    $('#specialPicFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                
                $("#specialPicFileId").val(file.fileRelaId);
                $("#specialImageUrl").val(file.fileRelaId);
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });
    $("#specialPicFile").specialPicFilePicUploadPreview({
        Img: "specialImageUrlSrc",
        Width: 50,
        Height: 50
    });

}

$.fn.extend({
    specialPicFilePicUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "specialImageUrlSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#specialImageUrl").val("");
            $("#specialPicDiv").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});

function clearSpecialPic() {
    popConfirm("确认删除图片吗？", function () {
        $("#specialPicFileId").val("");
        $("#specialPicDiv").css("display", "none");
        $("#specialPicFile").val("");
        $("#specialImageUrl").val("");
    });
}



function btnClose() {
    closeWinCallBack();
}

function releaseOperation(){
    var releaseFlag = $("#releaseFlag").val()
    if (releaseFlag == '0'){
        $("#btnRelease").css('display','inline-block')
        $("#btnCancel").css('display','none')
    }else if (releaseFlag == '1'){
        $("#btnRelease").css('display','none')
        $("#btnCancel").css('display','inline-block')
    }else {
        $("#btnRelease").css('display','none')
        $("#btnCancel").css('display','none')
    }
}

function btnSaveSpecial(){
    if ($("#queryForm").valid()) {
        popConfirm('确定保存？', function () {
            ajaxData("/specialConfig/saveSpecialInfo", $("#queryForm").formSerialize(), function (res) {
                popMsg("保存成功");
                $("#specialId").val(res.specialId);
            })
        })
    }
}

function releaseSpecialInfo(releaseFlag){
        var param = {
            specialId:$("#specialId").val(),
            releaseFlag:releaseFlag
        }
        ajaxData("/specialConfig/releaseSpecialInfo",param,function (){
            popMsg("发布成功")
            $("#releaseFlag").val(releaseFlag)
            releaseOperation()
            if (releaseFlag == '1'){
                popMsg("发布成功");
            }else {
                popMsg("取消发布成功");
            }
        })
}

function selectExam(){
    var params = {
        //examType:'111111'
    }
    popWin("选择考试", "/specialConfig/selectExamPop", params, "90%", "90%", function (data) {
        $('.selectExam-class').css('display', 'block');
        $("#examId").val(data.examId);
        $("#examName").html(data.examName);
    })
}

function delExamName(obj) {
    $("#examId").val("");
    $(".selectExam-class").css('display', 'none')
}

function addGrade(){
    var gradeLen = $("#gradeDivId").children().length//级别数量
    let specialId = $("#specialId").val();
    if (specialId != null && specialId != ''){
        var content =
            "<div>" +
            "<div class=\"row\">\n" +
            "    <label class=\"col-md-3 control-label c-label\">级别名称：</label>\n" +
            "    <div class=\"col-md-8\">\n" +
            "    <select id=\"gradeName\" name=\"gradeName\" class=\"form-control\">\n" +
            "         <option value=\"\">请选择</option>\n" +
            "         <option value=\"01\">证代初级</option>\n" +
            "         <option value=\"02\">证代中级</option>\n" +
            "         <option value=\"03\">证代高级</option>\n" +
            "         <option value=\"04\">基础篇</option>\n" +
            "         <option value=\"05\">进阶篇</option>\n" +
            "         <option value=\"06\">辅导篇</option>\n" +
            "         <option value=\"07\">董秘初阶</option>\n" +
            "         <option value=\"08\">董秘进阶</option>\n" +
            "         <option value=\"09\">董秘高阶</option>\n" +
            "    </select>" +
            "    </div>\n" +
            "</div>\n" +
            "<div  class=\"row\">" +
            "   <label class=\"col-md-3 control-label c-label\">级别描述：</label>" +
            "   <div class=\"col-md-8\">" +
            "       <textarea id=\"gradeDescribe\" name=\"gradeDescribe\" class=\"form-control\" rows=\"3\"   ></textarea>" +
            "   </div>" +
            "</div>" +
            "</div>"

        var layerIndex = layer.open({
            title:'新建级别',
            content: content,
            btn: ['确定', '关闭'],
            area:["400px","500px"],
            yes: function(index){
                var gradeName = $("#gradeName").val()
                var param = {
                    specialId : $("#specialId").val(),
                    gradeName : gradeName,
                    sort:gradeLen,
                    gradeDescribe:$("#gradeDescribe").val()
                }
                ajaxData("/specialConfig/saveSpecialGrade",param,function (res){
                    refreshSpecial()
                    layer.close(layerIndex);
                })
            },
            no:function (index,) {
                layer.close(layerIndex);
            }
        })
    }else {
        popMsg("请先保存专题基本信息")
    }
}


function addSpecialType(specialTypeId,gradeLen){
    var typeLen = $("#typeTbodyId_"+gradeLen+"").children().length//分类数量
    var param = {
        specialTypeId : specialTypeId,
        specialId : $("#specialId").val(),
        gradeId : $("#gradeId_"+gradeLen+"").val(),
        sort : parseInt(typeLen)+1
    }

    parent.popWin("新建专题分类","/specialConfig/addSpecialType",param,'50%', '80%', function (){
        refreshSpecial()
        popMsg("保存成功")
    })
}

function editGrade(gradeId){
    var param = {
        gradeId : gradeId,
    }
    parent.popWin("编辑级别描述","/specialConfig/addSpecialGrade",param,'50%', '80%', function (){
        refreshSpecial()
        popMsg("保存成功")
    })
}

function editSpecialPrice(gradeId){
    // var param = {
    //     gradeId : gradeId,
    // }
    // parent.popWin("收费设置","/specialConfig/editSpecialPrice",param,'98%', '98%', function (){
    //     refreshSpecial()
    //     popMsg("保存成功")
    // })

    var param = {
        existPersonType:true,
        existIfFree:true,
        existIfDiscount:true,
        existDiscount:true,
        existPrice:true,
        relationId:gradeId
    }
    parent.popWin("选择用户类型", "/selectTrainTypeCost", param, "98%", "98%");

}

function refreshSpecial() {
    var param = {
        specialId : $("#specialId").val()
    }
    ajaxData("/specialConfig/selectSpecialDetailed",param,function (res){
        var gradeHtml = ""
        $("#gradeDivId").empty()
        if (res.specialGradeDtoList != null && res.specialGradeDtoList.length>0){
            for (var i=0;i<res.specialGradeDtoList.length;i++){
                var typeHtml = ""
                if (res.specialGradeDtoList[i].specialTypeDtoList != null && res.specialGradeDtoList[i].specialTypeDtoList.length>0){
                    for (var j=0;j<res.specialGradeDtoList[i].specialTypeDtoList.length;j++){
                        var packageFlag = "否"
                        if (res.specialGradeDtoList[i].specialTypeDtoList[j].packageFlag == '1'){
                            packageFlag = "是"
                        }
                        var specialTypeId = res.specialGradeDtoList[i].specialTypeDtoList[j].specialTypeId
                        var packageId = res.specialGradeDtoList[i].specialTypeDtoList[j].packageId ? res.specialGradeDtoList[i].specialTypeDtoList[j].packageId : ''
                        typeHtml +=
                            "<tr>\n" +
                            "<td class=\"sort-no\">"+res.specialGradeDtoList[i].specialTypeDtoList[j].sort+"</td>\n" +
                            "<td>" +
                            ""+res.specialGradeDtoList[i].specialTypeDtoList[j].specialTypeName+"" +
                            "<input type='hidden' id='specialTypeId_"+i+"_"+j+"' value='"+specialTypeId+"'>" +
                            "</td>\n" +
                            "<td style=\"word-break:break-all\">"+res.specialGradeDtoList[i].specialTypeDtoList[j].specialTypeDescribe+"</td>\n" +
                            "<td>"+packageFlag+"</td>\n" +
                            "<td>" +
                            "<a href=\"javascript:void(0)\" onclick=\"addSpecialType('"+specialTypeId+"',"+i+")\" title=\"编辑分类\">编辑</a>\n" +
                            "|\n" +
                            "<a href=\"javascript:void(0)\" onclick=\"relationSpecialType('"+specialTypeId+"','"+packageId+"')\" title=\"关联课程\">关联课程</a>\n" +
                            "|\n" +
                            "<a href=\"javascript:void(0)\" onclick=\"deleteSpecialType('"+specialTypeId+"')\" title=\"删除分类\">删除</a>" +
                            "|\n" +
                            "<a href=\"javascript:void(0)\" onclick=\"upSpecialType('"+res.specialGradeDtoList[i].gradeId+"','"+specialTypeId+"','"+res.specialGradeDtoList[i].specialTypeDtoList[j].sort+"',"+i+")\" title=\"上移分类\">上移</a>" +
                            "|\n" +
                            "<a href=\"javascript:void(0)\" onclick=\"downSpecialType('"+res.specialGradeDtoList[i].gradeId+"','"+specialTypeId+"','"+res.specialGradeDtoList[i].specialTypeDtoList[j].sort+"',"+i+")\" title=\"下移分类\">下移</a>" +
                            "</td>\n" +
                            "</tr>"
                    }
                }
                gradeHtml += "<div class=\"grade-class\">\n" +
                    "<div class=\"row\">\n" +
                    "    <label class=\"col-md-1 control-label c-label\">级别名称：</label>\n" +
                    "    <span class=\"col-md-4\">\n" +
                    "<span>"+res.specialGradeDtoList[i].gradeName+"</span>" +
                    "<input type='hidden' id='gradeId_"+i+"' value='"+res.specialGradeDtoList[i].gradeId+"'>" +
                    "    </span>\n" +
                    "<span class=\"btn btn-primary typeButt-class\" onclick=\"addSpecialType('',"+i+")\">新增分类</span>" +
                    "<span class=\"btn btn-primary typeButt-class\" onclick=\"deleteSpecialGrade('"+res.specialGradeDtoList[i].gradeId+"')\">删除级别</span>\n" +
                    "<span class=\"btn btn-primary typeButt-class\" onclick=\"editGrade('"+res.specialGradeDtoList[i].gradeId+"')\">编辑描述</span>" +
                    "<span class=\"btn btn-primary typeButt-class\" onclick=\"editSpecialPrice('"+res.specialGradeDtoList[i].gradeId+"')\">收费设置</span>" +
                    "</div>\n" +
                    "<div class=\"row\">\n" +
                    "    <div class=\"col-xs-12\">\n" +
                    "    <table class=\"table table-bordered no-margin\" style=\"text-align: center;float:left;\" id=\"typeTableId\">\n" +
                    "        <thead>\n" +
                    "            <tr role=\"row\">\n" +
                    "                <th class=\"text-center\" style=\"width: 5%;\">序号</th>\n" +
                    "                <th class=\"text-center\" style=\"width: 10%;\">分类名称</th>\n" +
                    "                <th class=\"text-center\" style=\"width: 55%;\">分类描述</th>\n" +
                    "                <th class=\"text-center\" style=\"width: 10%;\">是否打包售卖</th>\n" +
                    "                <th class=\"text-center\" style=\"width: 20%;\">操作</th>\n" +
                    "            </tr>\n" +
                    "        </thead>\n" +
                    "        <tbody id=\"typeTbodyId_"+i+"\">\n" +
                    ""+typeHtml+"" +
                    "        </tbody>\n" +
                    "    </table>\n" +
                    "    </div>\n" +
                    "</div>\n" +
                    "</div>"
            }
        }
        $("#gradeDivId").append(gradeHtml)
    })
}

function relationSpecialType(specialTypeId,packageId){
    var param = {
        id : packageId,
        specialTypeId : specialTypeId
    }
    parent.popWin('关联课程','/packManagement/editPackInit', param,'100%', '100%', function (){
        refreshSpecial()
        popMsg("保存课程")
    });
}

function deleteSpecialType(specialTypeId){
    popConfirm('删除后不可恢复，确定删除？', function () {
        var param = {
            specialTypeId : specialTypeId
        }
        ajaxData("/specialConfig/deleteSpecialType",param,function (){
            popMsg("删除成功")
            refreshSpecial()
        })
    })
}

function deleteSpecialGrade(gradeId){
    popConfirm('删除后不可恢复，确定删除？', function () {
        var param = {
            gradeId : gradeId
        }
        ajaxData("/specialConfig/deleteSpecialGrade",param,function (){
            popMsg("删除成功")
            refreshSpecial()
        })
    })
}

function upSpecialType(gradeId,specialTypeId,sort,index){
    if(sort<=1){
        popMsg("当前分类不可上移")
    }else {
        popConfirm('确定上移？', function () {
            var param = {
                gradeId:gradeId,
                specialTypeId:specialTypeId,
                sort:sort,
                sortFlag : "1"
            }
            ajaxData("/specialConfig/sortSpecialType",param,function (){
                popMsg("上移成功")
                refreshSpecial()
            })
        })
    }

}
function downSpecialType(gradeId,specialTypeId,sort,index){
    var typeLen = $("#typeTbodyId_"+index+"").children().length//分类数量
    if(sort>=typeLen){
        popMsg("当前分类不可下移")
    }else {
        popConfirm('确定下移？', function () {
            var param = {
                gradeId:gradeId,
                specialTypeId:specialTypeId,
                sort:sort,
                sortFlag : "2"
            }
            ajaxData("/specialConfig/sortSpecialType",param,function (){
                popMsg("下移成功")
                refreshSpecial()
            })
        })
    }
}

function rem(){
    if(getValue($("#specialType").val()) == '1'){
        $("#addGrade").attr({"style":"display:none"})
        $("#gradeDivId").attr({"style":"display:none"})
        $("#realPackage").attr({"style":"display:black"})
    }else {
        $("#addGrade").attr({"style":"display:black"})
        $("#gradeDivId").attr({"style":"display:black"})
        $("#realPackage").attr({"style":"display:none"})
    }
}

function selectCoursePackage (){
    popWin("选择课程包", "/selectCoursePackage", '', "80%", "80%",function (data){
        $('.selectPackage-class').css('display', 'block');
        $("#packageId").val(data.selPackage);
        $("#packageName").html(data.selPackageName);
    });
}

function delPackageName(obj) {
    $("#packageId").val("");
    $(".selectPackage-class").css('display', 'none')
}