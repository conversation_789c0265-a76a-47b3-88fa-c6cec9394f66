$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });
    //清空条件
    $('#btnClear').click(function () {
        document.getElementById("examMonitoringDto").reset();
        search();
    });
})

function search(){
    ajaxTableQuery("tableAll", "/certificateConfig/queryCertificateList", $("#examMonitoringDto").formSerialize()+'&examType=111111');
}


// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
function submitTime(data, type, row, meta) {
    if(data.submitTime === '' || data.submitTime === null){
        return "- -";
    }else{
        return data.submitTime;
    }
}
function score(data, type, row, meta) {
    if(data.score === '' || data.score === null){
        return "- -";
    }else{
        return data.score;
    }
}

function companyCode(data, type, row, meta) {
    if(data.companyCode === '' || data.companyCode === null){
        return "- -";
    }else{
        return data.companyCode;
    }
}

function columnOperation(data, type, row, meta) {
    let str = '';
    let viewCertificateAuth = false;

    if (document.getElementById("viewCertificateAuth")) {
        viewCertificateAuth = true;
    }

    if (viewCertificateAuth) {
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="viewCertificate(\'' + data.recordId + '\',\'' + data.createUser + '\')">查看</span>';
    }

    if (!str) {
        str += '--'
    }
    return str;
}

function ifApplyCertificate(data, type, row, meta) {
    let str = '';
    if (data.ifApplyCertificate == '0'){
        str = '未申请'
    }else if (data.ifApplyCertificate == '1'){
        str = '已申请'
    }else if (data.ifApplyCertificate == '2'){
        str = '已通过'
    }else if(data.ifApplyCertificate == '3') {
        str = '未通过'
    }else{
        str = '--'
    }
    return str
}

function viewCertificate(recordId,createUser){
    var param = {
        businessId:recordId,
        userId:createUser
    }
    parent.popWin("证书详情","/certificateConfig/viewCertificate",param,'90%', '90%', callBack,'',callBack)
}

function callBack() {
    ajaxTableReload("tableAll",false);
}
