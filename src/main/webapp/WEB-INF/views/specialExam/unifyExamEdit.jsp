<%--
  Created by IntelliJ IDEA.
  User: 86156
  Date: 2023/3/7
  Time: 15:53
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .control-label {
            margin-top: 6px;
        }
        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel" style="height: 95%">
    <div class="panel-body">
        <form:form id="unifyExamAddForm" modelAttribute="examRankUnifyMapDTO" cssClass="form-horizontal">
            <form:hidden path="examRankId" value="${examRankUnifyMapDTO.examRankId}"/>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-1 control-label" style="margin-top: 5px;width: 100px;">
                    <span style="color: #FF0000; ">*</span>
                    考试名称:
                </label>
                <div class="col-md-4 daterange">
                    <sec:authorize access="hasAuthority('RES_SELECT_PAPER_INFO_AUTHORITY_3')" >
                        <%--选择试卷列表页初始化权限--%>
                        <span id="addExamRank" class="btn btn-primary">选择试卷</span>
                    </sec:authorize>
                    <span id="paperName">${examRankUnifyMapDTO.paperName}</span>
                </div>
            </div>
            <br style="height: 10px">
            <div class="col-md-12" style="display: flex">
                <label class="col-md-1 control-label">
                    <span style="color: #FF0000;">*</span>
                    开始时间:
                </label>
                <div class="col-md-4 daterange">
                    <e:date cssClass="form-control" path="startTime" format="yyyy-mm-dd hh:ii:ss" placeholder="请选择考试开始时间" cssStyle="background-color: #e5e5e5;"/>
                    <i class="glyphicon glyphicon-calendar fa fa-calendar daterangetag" style="right: 18px; top: 7px;"></i>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-1 control-label">
                    <span style="color: #FF0000;">*</span>
                    结束时间:
                </label>
                <div class="col-md-4 daterange">
                    <e:date cssClass="form-control" path="endTime" format="yyyy-mm-dd hh:ii:ss" placeholder="请选择考试结束时间" cssStyle="background-color: #e5e5e5;"/>
                    <i class="glyphicon glyphicon-calendar fa fa-calendar daterangetag" style="right: 18px; top: 7px;"></i>
                </div>
            </div>
            <div class="col-md-12">
                <div style="text-align:center; margin-top: 60px">
                    <span id="close" class="btn btn-default">关闭</span>
                    <sec:authorize access="hasAuthority('RES_SAVE_UNIFY_EXAM_EDIT_AUTHORITY_3')" >
                        <%--保存新增/编辑统一考试权限--%>
                        <span id="saveEdit" class="btn btn-primary" onclick="saveEdit('${examRankUnifyMapDTO.id}')">确定</span>
                    </sec:authorize>
                </div>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
