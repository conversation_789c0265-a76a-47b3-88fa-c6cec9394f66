$(document).ready(function () {
    $("#addExamRank").click(function () {
        addExamRank();
    })
    $("#close").click(function () {
        closeWin()
    })
})

let paperName;
let paperId;

function addExamRank() {
    parent.popWin("选择试卷", "/examConfig/selectPaperInfo", null, '98%', '98%', function (paper) {
        paperName = paper.paperName;
        paperId = paper.paperId;
        document.getElementById("paperName").innerText = paperName

    }, "", "")
}

function saveEdit(id) {
    let param = {
        id: id,
        paperName: paperName,
        paperId: paperId,
        examRankId: $("#examRankId").val(),
        startTime: $("#startTime").val(),
        endTime: $("#endTime").val()
    }
    if (!document.getElementById("paperName").innerText) {
        popMsg("请选择考试")
        return
    }
    if (!param.startTime) {
        popMsg("请填写考试开始时间")
        return;
    }
    if (!param.endTime) {
        popMsg("请填写考试结束时间")
        return;
    }
    if (param.startTime > param.endTime) {
        popMsg("请输入正确的考试时间")
        return;
    }
    ajaxData("/examConfig/saveUnifyExamEdit", param, function (res) {
        if (res) {
            closeWinCallBack();
        }
    })
}