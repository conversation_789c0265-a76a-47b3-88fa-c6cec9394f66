<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        #livePicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        table tbody {
            max-height: 300px;
            display: block;
            overflow-y: auto;
        }
        table thead, table tbody tr {
            width: 100%;
            display: table;
            table-layout: fixed;
        }
        table thead {
            width: calc(100% - 10px); /*减去默认滚动条的宽度，让thead 与tbody 对齐*/
        }
        table tbody::-webkit-scrollbar { width: 10px;}  /*设置滚动条的宽度，让thead 与tbody 对齐*/

    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
    <form:form modelAttribute="coursePackageDto" id="queryForm" autocomplete="off">
        <form:hidden path="id"/>
        <form:hidden path="specialTypeId"/>
        <form:hidden path="associationType"/>
        <form:hidden path="capcoCourseFlag"/>

        <div id="packFlagId" class="col-md-12" style="padding-top: 20px" >
            <label class="col-md-1 control-label">是否打包售卖</label>
            <div class="col-md-2 no-padding">
                <form:radiobutton path="packageFlag" value="0" id="packageFlag1"  onchange="packFlagInit()"/>
                <label for="packageFlag1">否</label>
                <form:radiobutton path="packageFlag" value="1" id="packageFlag2" onchange="packFlagInit()" cssStyle="margin-left:6px"/>
                <label for="packageFlag2">是</label>
            </div>
        </div>

        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>打包名称：</label>
            <div class="col-md-7">
                <form:input path="packageName" placeholder="打包名称" cssClass="form-control"/>
            </div>
        </div>
        <div class="col-md-12">
            <label class="col-xs-1 control-label">
                <span style="color: #FF0000;font-weight: bold;">*</span>背景图片：
                <div style="font-size: 12px;color: red;">（请上传16:9的图片）</div>
            </label>
            <div class="col-xs-6" style="margin: 10px 0">
                <div id="livePicDiv" class="col-xs-12 no-padding"
                     <c:if test="${empty coursePackageDto.imageUrl}" >style="display: none;" </c:if>
                >
                    <img id="livePicImgSrc" src="${coursePackageDto.imageUrl}" style="width: 320px;height: 180px;" alt=""/>
                    <input type="hidden" name="livePicFileId" id="livePicFileId" value="${coursePackageDto.imageUrl}"/>
                    <form:hidden path="imageUrl"/>
                </div>
                <div class="col-xs-12 no-padding controls live-pic-btn-row">
                    <a href="javascript:void(0);" id="livePicUploadBtn" class="file btn btn-warning btn-facebook btn-outline">
                        <i class="fa fa-upload"> </i> 上传图片
                        <input id="livePicFile" type="file" name="files"/>
                    </a>
                    <input type="button" id="livePicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                </div>
            </div>
        </div>
<%--        <div class="col-md-12" style="padding-top: 20px">--%>
<%--            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>打包原价：</label>--%>
<%--            <div class="col-md-5">--%>
<%--                <form:input path="originalPrice" placeholder="打包原价" onkeyup="value=value.replace(/[^\-?\d.]/g,'')" cssClass="form-control" onblur="calculatePrice()"/>--%>
<%--            </div>--%>
<%--        </div>--%>
        <div id="chargeId" class="col-md-12" style="margin: 10px 0">
            <label class="col-xs-1 control-label">
                收费设置
            </label>
            <div class="col-xs-10">
                <input id="btnSelectCost" type="button" style="margin-right: 15px;" class="btn" value="选择用户类型"/>
                <form:hidden path="personTypes" value="${coursePackageDto.personTypes}"/>
            </div>
        </div>

        <div class="col-md-12" style="border: 1px solid black">

        <div class="col-md-12" style="margin: 10px 0">
            <label class="col-xs-1 control-label">
                关联课程类型
            </label>
            <div class="col-xs-6">
                <input type="radio" name="associationType" id="associationType1" value="1" onchange="associationTypeChange(this)"/>
                <label for="associationType1">根据条件更新</label>
                <input type="radio" name="associationType" id="associationType2" value="2" onchange="associationTypeChange(this)" style="margin-left: 15px" />
                <label for="associationType2">根据条件不更新</label>
                <input type="radio" name="associationType" id="associationType3" value="3" onchange="associationTypeChange(this)" style="margin-left: 15px" />
                <label for="associationType3">精确课程</label>
            </div>
        </div>

        <div class="col-md-12" id="selectCourseId" style="margin: 10px 0">
            <label class="col-xs-1 control-label">
                选择课程
            </label>
            <div class="col-xs-1">
                <input type="button" value="选择课程" onclick="chooseCourse()"/>
            </div>
            <div class="col-xs-6">
                <table id="courseTable" class="table table-bordered no-margin">
                    <thead>
                    <tr>
                        <th style="text-align: center" width="10%">序号</th>
                        <th style="text-align: center" width="55%">课程名称</th>
                        <th style="text-align: center" width="15%">课程时长</th>
                        <th style="text-align: center" width="10%">原价</th>
                        <th style="text-align: center" width="10%">操作</th>
                    </tr>
                    </thead>
                    <tbody id="courseTBody">
                    <c:forEach items="${coursePackageDto.coursePackageMapDtoList}" var="item" varStatus="status">
                        <tr>
                            <input type="hidden" value="${item.courseId}"/>
                            <td class="serialNumber" style="text-align: center" width="10%">${status.index+1}</td>
                            <td style="text-align: center" width="55%">${item.courseName}</td>
                            <td style="text-align: center" width="15%">${item.courseTime}分钟</td>
                            <td style="text-align: center" width="10%">${item.originalPrice}</td>
                            <td style="text-align: center" width="10%">
                                <i class="icon-arrow-up" title="上移" onclick="moveUp(this)"></i>
                                <i class="icon-arrow-down" title="下移" onclick="moveDown(this)"></i>
                                <i class="icon icon-trash" title="删除" onclick="deleteCourse(this)"></i>
                            </td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="col-md-12" id="selectCourseTypeId" style="margin: 10px 0;min-height: 200px">
            <div class="col-md-12">
                <label class="col-md-1 control-label">仅面向中上协课程</label>
                <div class="col-md-3">
                    <input type="radio" name="capcoCourseFlag" id="capcoCourseFlag1" value="0" onchange="capcoCourseFlagChange(this)"/>
                    <label for="capcoCourseFlag1">不包含</label>
                    <input type="radio" name="capcoCourseFlag" id="capcoCourseFlag2" value="1" onchange="capcoCourseFlagChange(this)" style="margin-left: 15px" />
                    <label for="capcoCourseFlag2">包含</label>
                </div>
            </div>
            <div class="col-md-12" style="margin: 10px 0">
                <label class="col-md-1 control-label">业务专题</label>
                <div class="col-md-3">
                    <input id="courseType" type="text" json-data='${courseTypeSelect001}' selected-ids="${coursePackageDto.courseType}" />
                    <input name="courseType" type="hidden" value='${coursePackageDto.courseType}'/>
                </div>
                <label class="col-md-1 control-label">讲师</label>
                <div class="col-md-3">
                    <input id="teacher" type="text" class="t-select" json-data='${teacherSelect}' selected-ids="${coursePackageDto.teacher}"/>
                    <input name="teacher" type="hidden" value='${coursePackageDto.teacher}'/>
                </div>
                <label class="col-md-1 control-label">职务分类</label>
                <div class="col-md-3">
                    <input id="subject" type="text" class="t-select" json-data='${courseTypeSelect005}' selected-ids="${coursePackageDto.subject}"/>
                    <input name="subject" type="hidden" value='${coursePackageDto.subject}'/>
                </div>
            </div>
            <div class="col-md-12">
                <label class="col-md-1 control-label">适用板块</label>
                <div class="col-md-3">
                    <input id="applyPlate" type="text" class="t-select" json-data='${courseTypeSelect003}' selected-ids="${coursePackageDto.applyPlate}"/>
                    <input name="applyPlate" type="hidden" value='${coursePackageDto.applyPlate}'/>
                </div>
                <label class="col-md-1 control-label">课程类型</label>
                <div class="col-md-3">
                    <input id="applyMechanism" type="text" class="t-select" json-data='${courseTypeSelect004}' selected-ids="${coursePackageDto.applyMechanism}"/>
                    <input name="applyMechanism" type="hidden" value='${coursePackageDto.applyMechanism}'/>
                </div>
                <label class="col-md-1 control-label">适用人群</label>
                <div class="col-md-3 ">
                    <input id="applyPerson" type="text" class="t-select" json-data='${courseTypeSelect002}' selected-ids="${coursePackageDto.applyPerson}"/>
                    <input name="applyPerson" type="hidden" value='${coursePackageDto.applyPerson}'/>
                </div>
            </div>
        </div>
        </div>
        <div class="col-md-12" style="text-align: center;padding-top: 30px">
            <input type="button" id="closeBtn" class="btn btn-default" value="关闭">
            <input type="button" id="saveBtn" class="btn btn-primary" value="保存基本信息">
            <input type="button" id="saveBtn2" class="btn btn-primary" value="保存基本信息&课程关联">
            <div>注：保存基本信息：只保存课程包的基本信息，不包括课程关联的设置；保存基本信息&课程关联：保存基本信息和课程关联的设置</div>
        </div>
    </form:form>
    </div>
</div>
</body>
</html>
