$(document).ready(function () {
    //下载导入模板
    $("#downloadTemplate").click(function (){
        window.open(contextPath + "/examConfig/exportOfflineExamExcel");
    })
})
//导入线下考试列表
function importExamList(that,examRankUnifyId,examRankId){
    let importExamList = document.getElementById("importExamList")
    importExamList.type = 'file'
    $(that).find("input[id='importExamList']").fileupload({
        url: contextPath + "/examConfig/importOfflineExamExcel",
        dataType: "json",
        autoUpload: true,
        formData: {
            examRankUnifyId : examRankUnifyId,
            examRankId: examRankId
        },
        add: function (e, data) {
            if (data.files[0].name.indexOf("xlsx") === -1) {
                popMsg("只支持.xlsx格式文件上传");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            layer.close(index);
            let importResult;
            if (data.result != null) {
                importResult = data.result.result;
                if (importResult.importStatus === '0') {
                    popAlert("导入失败，请检查姓名为“" + importResult.userName + "”的数据是否有误，或与系统内重复，请确认后重新上传");
                    return
                }
                if (importResult.importStatus === '1') {
                    popAlert("导入成功");
                    // 刷新数据列表
                    ajaxTableReload("table1", false);
                }
            }
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("导入失败,请检查导入数据");
        }
    });
}

function callBack() {
    ajaxTableReload("table1", false);
}

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function renderColumnOperation(data) {
    return '<a href="#" onClick="relevancePerson(\'' + data.id + '\')" style="margin-right:4px;">关联人员</a>';
}

function addExamRank(id) {
    let param = {
        id: id
    }
    parent.popWin("选择试卷", "/examConfig/selectPaperInfo", param, '98%', '98%', function (paper) {

        let param = {
            id: id,
            paperName: paper.paperName,
            paperId: paper.paperId
        }
        ajaxData("/examConfig/updateUnifyExamPaper", param, function (res) {
            if (res > 0) {
                //刷新当前页面
                location.reload()
            }
        })
    }, "", "")

}

function relevancePerson(id) {
    let param = {
        examRankUnifyId: id,
    }
    popWin('关联人员', '/examConfig/relevancePersonInit', param, '85%', '85%', callBack, '', callBack);
}

function editUnifyExam(id, examRankId) {
    let title = id ? '编辑统一考试' : '新增统一考试'
    let param = {
        id: id ? id : '',
        examRankId: examRankId
    }
    popWin(title, '/examConfig/unifyExamEdit', param, '50%', '75%', editUnifyExamCallBack, '', cancelCallBack);
}

function editUnifyExamCallBack() {
    popMsg("保存成功!")
    location.reload()
}

function cancelCallBack() {
}

function deleteUnifyExam(id) {
    popConfirm('确定删除？', function () {
        let param = {
            id: id
        }
        ajaxData("/examConfig/deleteUnifyExam", param, function (res) {
            if (res > 0) {
                //刷新当前页面
                location.reload()
            }
        })
    })
}