<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form modelAttribute="specialTypeDto" id="specialTypeDto">
            <form:hidden path="specialTypeId" />
            <form:hidden path="specialId" />
            <form:hidden path="gradeId" />
            <form:hidden path="sort" />
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>分类名称</label>
                <div class="col-md-10 no-padding" style="width: 600px;">
                    <form:input path="specialTypeName" placeholder="请输入分类名称" cssClass="form-control"/>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">分类描述</label>
                <div class="col-md-10 no-padding" style="width: 600px;">
                    <form:textarea cols="60" rows="3" path="specialTypeDescribe" cssClass="form-control" placeholder="请输入分类描述（最多输入200个字符）" maxlength="200"></form:textarea>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>背景图片</label>
                <div class="col-md-6 no-padding" style="width: 50%">
                    <div id="imgId" class="col-md-5 no-padding" >
                        <img id="ImgPr" src="${specialTypeDto.imageUrl}" style="width: 200px"/>
                        <input type="hidden" name="fileId" id="fileId" value="${specialTypeDto.imageUrl}"/>
                        <input type="hidden" name="delFileId" id="delFileId" value="${specialTypeDto.imageUrl}"/>
                        <form:hidden path="imageUrl" />
                    </div>
                    <div class="col-md-4 no-padding" style="display: inline-block;margin-top: 10px">
                <span class="btn btn-sm btn-success fileinput-button">
                    <span>上传</span>
                    <input id="up" type="file" name="files">
                </span>
                        <span onclick="clearImg()" class="btn btn-sm" style="margin-left: 20px;">删除</span>
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top: 20px;text-align: center;">
                <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存" />
                <input type="button" id="btnCancel" class="btn btn-default" value="取消" />
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
