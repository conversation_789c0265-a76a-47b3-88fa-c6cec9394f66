$(document).ready(function () {

    $("#btnSave").bind('click',function () {
        saveSpecialType();
    })
    $("#btnCancel").bind('click',function () {
        closeWin()
    })

    uploadFile()


})

function saveSpecialType(){
    if ($("#imageUrl").val() == null || $("#imageUrl").val() == ''){
        popMsg("请上传背景图片")
    }else {
        var param = {
            specialTypeId : $("#specialTypeId").val(),
            specialTypeName : $("#specialTypeName").val(),
            specialTypeDescribe : $("#specialTypeDescribe").val(),
            specialId : $("#specialId").val(),
            gradeId : $("#gradeId").val(),
            sort : $("#sort").val(),
            imageUrl : $("#imageUrl").val()
        }
        ajaxData("/specialConfig/saveSpecialType",param,function (data) {
            closeWinCallBack(data);
        })
    }
}
//删除图片
function clearImg() {
    popConfirm("确认删除图片", function () {
        $("#fileId").val("");
        $("#imageUrl").val("");
        $("#imgId").css("display", "none");
        $("#up").val("");
    });
}

function uploadFile(){
    var url = contextPath + '/filetempupload';
    $('#up').fileupload({
        url : url,
        dataType : 'json',
        autoUpload : true,
        submit : function(e, data) {
            index = layer.load(1, {
                shade : [ 0.1, '#fff' ]
                // 0.1透明度的白色背景
            });
        },
        done : function(e, data) {
            $.each(data.result, function(index, file) {
                $("#fileId").val(file.fileRelaId);
                $("#imageUrl").val(file.fileRelaId);
            });
            layer.close(index);
        }
    });

    $("#up").uploadPreview({
        Img : "ImgPr",
        Width : 50,
        Height : 50
    });
}
$.fn
    .extend({
        uploadPreview : function(opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img : "ImgPr",
                Width : 100,
                Height : 100,
                ImgType : [ "gif", "jpeg", "jpg", "bmp", "png" ],
                Callback : function() {
                }
            }, opts || {});
            _self.getObjectURL = function(file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }
                if ($("#imageUrl").val() != "") {
                    $(
                        '<input type="hidden" name="delFileId" value="'
                        + $("#imageUrl").val() + '"/>').appendTo(
                        '#delFileIdList');
                    $("#imageUrl").val("");
                }

                $("#imgId").css("display", "block");
                return url
            };

            _this.change(function() {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter' : 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width' : opts.Width
                                            + 'px',
                                        'height' : opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })
