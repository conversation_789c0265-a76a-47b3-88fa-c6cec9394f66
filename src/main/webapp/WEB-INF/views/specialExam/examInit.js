$(document).ready(function () {
    btnManage();
    // 下拉初始化
    tSelectInit();
    dateInit();
    $('#btnQuery').click(function () {
        search();
    });
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        search();
    });
})
function search(){
    ajaxTableQuery("tableAll", "/examConfig/queryExamInfoList",
        $("#queryForm").formSerialize());
}
function btnManage(){
    $('#examManage').click(function () {
        examManage()
    });
    $("#addExam").click(function (){
        parent.popWin('新增考核信息', '/examConfig/addExamManageInit', "", '98%', '98%', callBackAddExam, '', callBackAddExam);
    })
}
function callBackAddExam(){
    ajaxTableReload("tableAll", false);
}
function examManage(){
    var url = examBaseUrl + 'ui/exam/index?comeFrom=capco&companyCode=exam_system&username='+userId+'&personName='+personName+'&orgId='+orgId;
    window.open(encodeURI(url));
}
//下拉初始化
function tSelectInit() {
    var courseTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, courseTypeSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function dateInit() {
    dataRangePickerInit($('#releaseTime'), null, null, function () {}, function () {});
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
function columnRelease(data, type, row, meta) {
    if (data.releaseFlag=='0'){
        return '否'
    }else if (data.releaseFlag=='1'){
        return '是'
    }else {
        return ''
    }
}
function columnOperation(data, type, row, meta) {
    var str = '';
    let editExamAuth = false;
    let updateExamAuth = false;

    if (document.getElementById("editExamAuth")) {
        editExamAuth = true;
    }

    if (document.getElementById("updateExamAuth")) {
        updateExamAuth = true;
    }

    if (editExamAuth) {
        str += '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editExam(\'' + data.id + '\')"></i>';
    }
    if (data.releaseFlag=='0' && updateExamAuth) {
        str += '<i class="fa fa-trash-o" style="cursor: pointer;margin: 0 5px;" title="删除" onclick="deleteExam(\'' + data.id + '\')"></i>';
    }

    if (!str) {
        str = '--';
    }
    return str;
}
function editExam(id){
    var param = {
        id:id,
    }
    parent.popWin('编辑考核信息', '/examConfig/addExamManageInit', param, '98%', '98%', callBackAddExam, '', callBackAddExam);
}

function deleteExam(id){
    popConfirm("当前操作不可恢复，确认删除?", function () {
        var param = {
            id:id,
            status:"0"
        }
        ajaxData("/examConfig/updateExamStatus",param, function (res) {
            popMsg("删除成功");
            callBackAddExam()
        })
    });

}