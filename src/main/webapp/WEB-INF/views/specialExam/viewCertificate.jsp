<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/viewer.js"></script>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/viewer.css">
    <style>
    </style>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="certificateDtoForm" modelAttribute="certificateDto" cssClass="form-horizontal">
            <input type="hidden" id="businessId" value="${certificateDto.businessId}"/>
            <input type="hidden" id="examRank" value="${certificateDto.examRank}"/>
            <input type="hidden" id="certificateNumber" value="${certificateDto.certificateNumber}"/>
            <input type="hidden" id="createTime" value="${certificateDto.createTime}"/>
            <div class="row" style="padding-bottom: 5px">
                <div class="col-md-12 text-center">
                    <div class="col-md-6">
                        <label class="col-xs-2 no-padding control-label text-right">证书照片:</label>
                        <div class="col-xs-4">
                            <div class="ImgPr" onclick="imageClick(0)" >
                                <img src="${certificateDto.certificateImgUrl}" style="position: relative;cursor: pointer"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="col-xs-2 no-padding control-label text-right">证件照片:</label>
                        <div class="col-xs-4">
                            <img src="${certificateDto.certificatesPic}">
                        </div>
                    </div>
                </div>
            </div>
            <c:if test="${certificateDto.ifApplyCertificate == '1'}">
                <div class="row">
                    <div class="col-md-12 text-center" >
                        <input id="Button1" type="button" value="审核" class="btn btn-primary" data-toggle="modal" data-target="#applyCertificateModal"/>
                    </div>
                </div>
            </c:if>
        </form:form>
    </div>


    <div class="modal fade" id="applyCertificateModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close">
                    </button>
                    <h4 class="modal-title" id="myModalLabel" style="text-align: center">
                        审核
                    </h4>
                </div>
                <div class="modal-body">
                    <label class="col-md-12  control-label text-left" style="margin-top: 5px;">审核结果:</label>
                    <div id="selectCheck" class="col-md-12 no-padding text-center" style="width: 300px;">
                        <select id="checkState" name="" class="form-control">
                            <option value="">请选择</option>
                            <option value="2">通过</option>
                            <option value="3">未通过</option>
                        </select>
                    </div>
                    <label class="col-md-12  control-label text-left" style="margin-top: 5px;">短信内容（审核内容为空，默认不发送短信）:</label>
                    <textarea id="remindContent" class="form-control"></textarea>
                </div>

                <div class="modal-footer">
                    <div class="col-md-12 text-center" style="margin-top: 8px; display: inline-block">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        <sec:authorize access="hasAuthority('RES_APPLY_CERTIFICATE_AUTHORITY_3')" >
                            <%--审核证书照片更换权限--%>
                            <button type="button" id="btnCheck" class="btn btn-primary">确认</button>
                        </sec:authorize>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
</body>
</html>
