    $(document).ready(function () {
    tSelectInit();

    $("#btnSaveExam").click(function (){
        if ($("#queryForm").valid()) {
            saveExam();
        }
    })

    $("#btnClose").click(function () {
        popConfirm("确认关闭?", function () {
            btnClose();
        });
    });

    $("#btnRelease").click(function (){
        popConfirm('确定发布？', function () {
            updateStatus("1")
        })
    })

    $("#btnCancel").click(function (){
        popConfirm('确定取消发布？', function () {
            updateStatus("0")
        })
    })
        //导入线下考试列表
        $("#importExamListButton").click(function (){
            let importExamListDocument = document.getElementById("importExamList")
            if ($("#examType").val() !== '111111'){
                importExamListDocument.type = 'button'
                popMsg("只允许在正代分级高级考试中导入线下考试列表")
                return
            }
            importExamList()
        })
        //下载导入模板
        $("#downloadTemplate").click(function (){
            window.open(contextPath + "/examConfig/exportOfflineExamExcel");
        })

    addTable();
    examValidate()
    releaseOperation()

})
function releaseOperation(){
    var releaseFlag = $("#releaseFlag").val()
    if (releaseFlag == '0'){
        $("#btnRelease").css('display','inline-block')
        $("#btnCancel").css('display','none')
    }else if (releaseFlag == '1'){
        $("#btnRelease").css('display','none')
        $("#btnCancel").css('display','inline-block')
    }else {
        $("#btnRelease").css('display','none')
        $("#btnCancel").css('display','none')
    }
}
function examValidate(){
    $("#queryForm").validate({
        rules: {
            "examName": {
                required: true,
            },
            "releaseTime": {
                required: true,
            },
            "examType": {
                required: true,
            },
        },
        messages: {
            "examName": {
                required: "请填写考试名称"
            },
            "releaseTime": {
                required: "请选择发布时间",
            },
            "examType": {
                required: "请选择适用机构",
            },
        }
    });
}

function updateStatus(releaseFlag){
    var param = {
        id : $("#id").val(),
        releaseFlag : releaseFlag
    }
    ajaxData("/examConfig/updateExamStatus",param, function (res) {
        $("#releaseFlag").val(releaseFlag)
        releaseOperation()
        if (releaseFlag == '1'){
            popMsg("发布成功");
        }else {
            popMsg("取消发布成功");
        }
    })
}

function saveExam(){
    var examRankList = []
    var num = 0;
    $("#examTbodyId").find("tr").each(function (n,obj){
        var sort = $(obj).find('td').eq(0).html()
        var examRank = $(obj).find('td').eq(1).find('select').val()
        var courseTime = $(obj).find('td').eq(2).find('input').val()
        var answerInterval = $(obj).find('td').eq(3).find('input').val()
        let ifSingle = $(obj).find('td').eq(4).find('select').val()
        let paperName = $(obj).find('td').eq(5).find("span").eq(1).html()
        let paperId = $(obj).find('td').eq(5).find("input[name='paperId']").val()
        var examRankId = $(obj).find('td').eq(6).find("input[name='examRankId']").val()
        if (examRank === ''  || paperId === '' || isNaN(paperId)){
            num++
        }
        var examDto = {
            id:examRankId,
            sort:sort,
            examRank:examRank,
            courseTime:courseTime,
            answerInterval : answerInterval,
            ifSingle: ifSingle,
            paperId:paperId,
            paperName:paperName
        }
        examRankList.push(examDto)
    })
    $("#examRanks").val(JSON.stringify(examRankList))

    if (num>0){
        popMsg("请完善试卷信息")
    }else {
        popConfirm('确定保存？', function () {
            ajaxData("/examConfig/saveExam?", $("#queryForm").formSerialize(), function (res) {
                $("#id").val(res.id);
                $("#releaseFlag").val(res.releaseFlag);
                releaseOperation()
                popMsg("保存成功");
                closeWinCallBack();
            })
        })
    }

}

function btnClose() {
    closeWinCallBack();
}
//下拉初始化
function tSelectInit() {
    var tSelectOptionType = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        useFooter: true,
        hiddenInput: true,
        grade: 1,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, tSelectOptionType);

}

function importExamList(){
    let importExamList = document.getElementById("importExamList")
    importExamList.type = 'file'
    $("#importExamList").fileupload({
        url: contextPath + "/examConfig/importOfflineExamExcel",
        dataType: "json",
        autoUpload: true,
        formData: {examId : $("#examRankId").val()},
        add: function (e, data) {
            if (data.files[0].name.indexOf("xlsx") === -1) {
                popMsg("只支持.xlsx格式文件上传");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            layer.close(index);
            let importResult;
            if (data.result != null) {
                importResult = data.result.result;
                if (importResult.importStatus === '0') {
                    popAlert("导入失败,请检查姓名为“" + importResult.realName + "”的数据是否有误");
                    return
                }
                if (importResult.importStatus === '1') {
                    popAlert("导入成功");
                    // 刷新数据列表
                    ajaxTableReload("table1", false);
                }
            }
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("导入失败,请检查导入数据");
        }
    });
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
    if (d.value != null && d.value != ''){
        var param = {
            courseType : d.value
        }
        ajaxData("/examConfig/getCourseHours",param,function (res){
            var courseHours = "(共"+res+"学时)"
            $("#courseHours").html(courseHours)
        })
    }else {
        $("#courseHours").html("")
    }
}

function addExamRank (that){
    var id = $("#id").val()
    if (id === '' || id == null || isNaN(id)){
        popMsg("请先保存试卷基本信息")
    }else {
        var param = {
            id:id
        }
        parent.popWin("选择试卷","/examConfig/selectPaperInfo",param, '98%', '98%',function (paper){
            $(that).parent().find("span").eq(1).html(paper.paperName)
            $(that).parent().find("input[name='paperId']").val(paper.paperId)
        },"","")
    }
}

function addTable(){
    $(".btn-row-add").click(function (){
        if ($("#id").val() == '' || $("#id").val() == null){
            popMsg("请先保存考试基本信息再添加试卷")
            return
        }
        let sign = $("#examTbodyId").find("tr").length;
        let sort = parseInt(sign)+ 1
        var html = "<tr>" +
            "<td class='sort-no'>"+sort+"</td>" +
            "<td>" +
            "    <select id=\"examRank\" name=\"examRank\" class=\"form-control\">" +
            "        <option value=\"\">请选择</option>" +
            "        <option value=\"01\">初级</option>" +
            "        <option value=\"02\">中级</option>" +
            "        <option value=\"03\">高级</option>" +
            "        <option value=\"04\">种子期</option>" +
            "        <option value=\"05\">培育期</option>" +
            "        <option value=\"06\">辅导期</option>" +
            "         <option value=\"07\">董秘初阶</option>\n" +
            "         <option value=\"08\">董秘进阶</option>\n" +
            "         <option value=\"09\">董秘高阶</option>\n" +
            "    </select>" +
            "</td>" +
            "<td>" +
            "    <input type=\"text\" class=\"c-input\">学时" +
            "</td>" +
            "<td>" +
            "    <input type=\"text\" class=\"c-input\">天" +
            "</td>" +
            "<td>" +
            "    <select id=\"ifSingle\" name=\"ifSingle\" class=\"form-control\">" +
            "        <option value=\"\">请选择</option>" +
            "        <option value=\"1\">是</option>" +
            "        <option value=\"0\">否</option>" +
            "    </select>" +
            "</td>" +
            "<td>" +
            "    <span class=\"btn btn-primary\" onclick=\"addExamRank(this)\">选择试卷</span>" +
            "    <span class=\"paperName\"></span>" +
            "    <input name=\"paperId\" type=\"hidden\" value=\"${item.paperId}\">" +
            "</td>" +
            "<td>" +
            "    <div style=\"margin-top: 8px\">" +
            "        <span>" +
            "            <a href=\"#\" onclick=\"addUnifyExam(this)\">统一考试</a> |" +
            "            <a href=\"#\" onClick=\"removeExam(this)\">刪除</a>"+
            "        </span>" +
            "    </div>" +
            "</td>" +
            "</tr>"
        $("#examTbodyId").append(html)
    })
}
function removeExam(data,id){
    popConfirm('删除后不可恢复，确定删除？', function () {
        if (id != null){
            ajaxData("/examConfig/deleteExamRank",{id:id},function (res){

            })
        }
        var tbody = $(data).parents('tbody').eq(0);
        $(data).parents('tr').eq(0).remove();
        var trs = tbody.children('tr');
        trs.each(function (i, o) {
            $(o).children('.sort-no').text(i + 1);
        });
    })
}

function relevancePerson(id){
    let param = {
        examRankId:id,
    }
    popWin('关联人员', '/examConfig/relevancePersonInit', param, '75%', '75%', callBack, '', callBack);

}
function callBack(){
    ajaxTableReload("tableAll", false);
}

function addUnifyExam(that) {
   let examRankId = $(that).parent().find("input[name='examRankId']").val()
    //考试级别
    let examRank = $(that).parent().parent().parent().parent().find('td').eq(1).find('select').val()
    //考试类型
    let examType = $("#examType").val()
    if (isNaN(examRankId)){
        popMsg("请先保存基本信息")
        return
    }
    let param = {
        examRankId: examRankId,
        examRank: examRank,
        examType: examType
    }
    popWin('统一考试', '/examConfig/unifyExamInit', param, '100%', '100%', callBack, '', callBack);
}