<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        label{
            padding-top: 7px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body" style="padding-top: 5px;">

        <form:form modelAttribute="liveInfoParamDto" id="queryForm" >
            <div class="row">
                <label class="col-md-1">关联直播：</label>
                <div class="col-md-10">
                    <input id="liveId" type="text" class="t-select" json-data='${liveInfoList}' selected-ids = "${relationLiveInfo.liveId}" placeholder="请选择直播" />
                    <input name="liveId" type="hidden" />
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 text-right" style="margin-top: 5px" onclick="relation()" >
                    <sapn id="relation" class=" btn btn-primary">关联</sapn>
                </div>
            </div>
        </form:form>
        <form:form modelAttribute="courseDto" >
            <form:hidden id="courseId" path="id"/>
        </form:form>
    </div>
</div>
</body>