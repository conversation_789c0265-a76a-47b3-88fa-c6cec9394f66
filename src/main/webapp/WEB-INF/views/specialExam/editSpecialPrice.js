$(document).ready(function () {

    $('#saveBtn').bind('click', function() {
        if(getValue($("#originalPrice").val()) == "") {
            popMsg("请填写打包原价");
            return;
        }
        var flag = false;
        $("#discountTableId").find("tr").each(function (n,obj){
            var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
            if (checkFlag){
                var discount = $(obj).find('td').eq(2).find('input').val()
                var price = $(obj).find('td').eq(3).find('input').val()
                if (discount == '' || price == ''){
                    flag = true
                }
            }
        })
        if (flag){
            popMsg("折扣和折后价不能为空");
            return;
        }
        popConfirm("确认保存?", function () {
            savePackInfo();
        });

    });

    $('#closeBtn').bind('click', function() {
        popConfirm('确定关闭？', function () {
            closeWin();
        });
    });

})

function savePackInfo() {
    //收费管理
    let personTypes = []
    $("#discountTableId").find("tr").each(function (n,obj){
        var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
        if (checkFlag){
            var personType = $(obj).find('td').eq(0).find('input').val()
            var ifDiscount = $(obj).find('td').eq(1).find('input:radio:checked').val();
            var price,discount = ''
            if (ifDiscount === '1'){
                price = $(obj).find('td').eq(3).find('input').val()
                discount = $(obj).find('td').eq(2).find('input').val()
            }
            var personTypeDto = {
                personType : personType,
                ifDiscount : ifDiscount,
                price : price,
                discount:discount
            }
            personTypes.push(personTypeDto)
        }
    })
    $("#personTypes").val(JSON.stringify(personTypes))
    ajaxData("/specialConfig/saveSpecialPrice?", $("#queryForm").formSerialize(), function (res) {
        popMsg("保存成功");
    })
}

function chargeChange(ele,index){
    var checkFlag = $('input[name="'+ele.name+'"]').is(':checked')
    if (checkFlag){
        var html =
            '<input type="radio" name="ifFree_'+index+'" id="ifFree_'+index+'" value="0" checked="checked" onchange="freeChange(this,'+index+')" /> <label for="ifFree_'+index+'"> 不打折 </label>' +
            '<input type="radio" name="ifFree_'+index+'" id="ifNotFree_'+index+'" value="1" style="margin-left: 15px" onchange="notFreeChange(this,'+index+')" /> <label for="ifNotFree_'+index+'"> 打折 </label>'
        $('input[name="'+ele.name+'"]').parent().parent().find('.ifFreeClass').html(html)
    }else {
        $('input[name="'+ele.name+'"]').parent().parent().find('.ifFreeClass').html("--")
        $('input[name="'+ele.name+'"]').parent().parent().find('.priceClass').html("--")
        $('input[name="'+ele.name+'"]').parent().parent().find('.discountClass').html("--")
    }
}

function freeChange(ele,index){
    $('input[name="'+ele.name+'"]').parent().parent().find('.priceClass').html('--')
    $('input[name="'+ele.name+'"]').parent().parent().find('.discountClass').html("--")
}

function notFreeChange(ele,index){
    var html = '<input type="text" name="price_'+index+'" onkeyup="value=value.replace(/[^\\-?\\d.]/g,\'\')" value="" class="isNumber" onblur="priceCal(this)"/><label>元</label>'
    $('input[name="'+ele.name+'"]').parent().parent().find('.priceClass').html(html)
    var html1 = '<input type="text" name="discount_${status.index}" onkeyup="value=value.replace(/[^\\-?\\d.]/g,\'\')" value="" class="isNumber" onblur="discountCal(this)"/><label>折</label>'
    $('input[name="'+ele.name+'"]').parent().parent().find('.discountClass').html(html1)
}

//根据折扣计算价格
function discountCal(obj){
    if(getValue($("#originalPrice").val()) == "") {
        popMsg("请填写原价");
        return;
    }
    var originalPrice =  $("#originalPrice").val()
    var discount =  $(obj).parent().parent().find("input[name^='discount']").val()
    var price =  Number(originalPrice) * Number(discount) / 10
    $(obj).parent().parent().find("input[name^='price']").val(price.toFixed(2))
}

//根据价格计算折扣
function priceCal(obj) {
    if(getValue($("#originalPrice").val()) == "") {
        popMsg("请填写打包原价");
        return;
    }
    var originalPrice =  $("#originalPrice").val()
    var price =  $(obj).parent().parent().find("input[name^='price']").val()
    var discount = Number(price) / Number(originalPrice)  * 10
    $(obj).parent().parent().find("input[name^='discount']").val(discount.toFixed(2))
}

//刷新总价
function calculatePrice(){
    var originalPrice =  $("#originalPrice").val()
    if(!isNaN(originalPrice) && originalPrice != ''){
        $("#discountTableId").find('tr').each(function (n,obj){
            if($(obj).find('td').eq(2).find('input').val() != ''){
                discountCal($(obj).find('td').eq(2).find('input'))
            }
        })
    }
}