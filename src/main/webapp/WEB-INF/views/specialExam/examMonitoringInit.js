$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // 日期初始化
    dateInit();
    // 初始化页面监控考试类型下拉框(用于考试类型,默认选择证代)
    getSpecialGradeList();
    // 默认选中证代分级水平测试,此id为生产id
    $("#examId").val('9250280212976152490');
    // 默认选中考试通过的
    $("#examStatusType").val('5');

    $('#btnQuery').click(function () {
        search();
    });
    $("#exportTableBtn").bind("click", function () {
        exportTableData();
    });
    $("#btnClear").bind("click", function () {
        document.getElementById("examMonitoringDto").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        let str = '<option value="">全部</option>';
        $("#examGradeType").attr("disabled", true);
        $('#examGradeType').append(str)
        $("#examId").val("");
        $("#examStatusType").val("");
        $("#certificateStatus").val("");
        // 监控下拉框
        getSpecialGradeList();
        search("0");
    });

    $('#examId').change(function () {
        getSpecialGradeList();
    });
})

function search(item) {
    if (item == '0') {
        let param = {
            ifInitialization:0
        }
        ajaxTableQuery("tableAll", "/examMonitoring/queryExamMonitoringList", param);
    } else {
        ajaxTableQuery("tableAll", "/examMonitoring/queryExamMonitoringList", $("#examMonitoringDto").formSerialize());
    }
}

function exportTableData() {
    window.open(contextPath + "/examMonitoring/examMonitoringExport?" + $("#examMonitoringDto").formSerialize());
}

function getSpecialGradeList() {
    let params = {
        examId: $('#examId').val(),
    }
    ajaxData("/examMonitoring/getExamGradeTypeList", params, function (res) {
        $('#examGradeType').empty();
        let str = '<option value="">全部</option>';
        if ($('#examId').val() === "" || $('#examId').val() === null) {
            $("#examGradeType").attr("disabled", true);
            $('#examGradeType').append(str)
        } else {
            res.forEach(item => {
                str += '<option value="' + item.gradeId + '">' + item.gradeName + '</option>'
            })
            $("#examGradeType").attr("disabled", false);
            $('#examGradeType').append(str)
        }
    })
}


// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function companyCode(data, type, row, meta) {
    if (data.companyCode === '' || data.companyCode === null) {
        return "- -";
    } else {
        return data.companyCode;
    }
}

function ifCertificate(data, type, row, meta) {
    if (data.ifCertificate === '1') {
        return "是";
    } else {
        return "否";
    }
}

function examineTime(data, type, row, meta) {
    if (data.examineTime === '' || data.examineTime === null) {
        return "- -";
    } else {
        return data.examineTime;
    }
}

function submitTime(data, type, row, meta) {
    if (data.submitTime === '' || data.submitTime === null) {
        return "- -";
    } else {
        return data.submitTime;
    }
}


function timeSlot(data, type, row, meta) {
    if (data.timeSlot === 0 || data.timeSlot === null) {
        return "- -";
    } else {

        return timeCalculation(data.timeSlot);
    }
}

//时间计算
function timeCalculation(v) {
    // 将时间转为年月日时分秒
    let time = '';
    if (v / 60 >= 1) {
        time = Math.floor(v / 60) + '小时'
        v = v % 60
    }
    if (v / 1 >= 1) {
        time += Math.floor(v / 1) + '分'
        v = v % 1
    }
    return time
}

function score(data, type, row, meta) {
    if (data.score === '' || data.score === null) {
        return "- -";
    } else {
        return data.score;
    }
}

function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        inputSearch: true,
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#belongCommission').tselectInit(null, teaSelectOptions);
}

function dateInit() {
    dataRangePickerInit($('#examineTime'), null, null, function () {
    }, function () {
    });
}

function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function columnOperation(data, type, row, meta) {
    //examStatusValue = 0:未开始 1:人脸验证失败 2:人脸验证通过 3:考试中 4:未通过 5:通过
    let viewMonitoringAuth = false;
    let viewHistoryMonitoringAuth = false;
    let viewExamAuth = false;
    let paperMarkingAuth = false;
    let selectMonitorInfoAuth = false;
    let delExamRecordAuth = false;

    if (document.getElementById("viewMonitoringAuth")) {
        viewMonitoringAuth = true;
    }

    if (document.getElementById("viewHistoryMonitoringAuth")) {
        viewHistoryMonitoringAuth = true;
    }

    if (document.getElementById("viewExamAuth")) {
        viewExamAuth = true;
    }

    if (document.getElementById("paperMarkingAuth")) {
        paperMarkingAuth = true;
    }

    if (document.getElementById("selectMonitorInfoAuth")) {
        selectMonitorInfoAuth = true;
    }

    if (document.getElementById("delExamRecordAuth")) {
        delExamRecordAuth = true;
    }

    let str = '';
    if (data.examStatusValue === '0') {
        str += '<span style="color: #00a0e9">--</span>';
        return str
    }
    //examType = '444444' : 违法违规考试   '333333' : 辅导验收考试  '111111' : 证代分级考试
    if (data.examType !== '111111' && data.examStatusValue === '3') {
        if (viewMonitoringAuth) {
            str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="viewMonitoring(\'' + data.examRoomId + '\')">查看监控</span>';
            str += '<span style="color: #00a0e9">|</span>';
        }
    }
    if (data.examStatusValue === '4' || data.examStatusValue === '5') {
        if (data.examType !== '111111'){
            if (viewHistoryMonitoringAuth) {
                str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="viewHistoryMonitoring(\'' + data.separateRoomId + '\',\'' + data.examRoomId + '\',\'' + data.createUser + '\')">历史监控</span>';
                str += '<span style="color: #00a0e9">|</span>';
            }
        }
        if (viewExamAuth) {
            str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="viewExam(\'' + data.answerId + '\',\'' + data.paperId + '\',\'' + data.createUser + '\')">查看试卷</span>';
            str += '<span style="color: #00a0e9">|</span>';
        }
        if (paperMarkingAuth) {
            str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="paperMarking(\'' + data.answerId + '\',\'' + data.createUser + '\',\'' + data.examId + '\',\'' + data.recordId + '\')">批卷</span>';
            str += '<span style="color: #00a0e9">|</span>';
        }
    }
    if (selectMonitorInfoAuth) {
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="selectMonitor(\'' + data.recordId + '\',\'' + data.answerId + '\',\'' + data.examRank + '\',\'' + data.createUser + '\',' +
            '\'' + data.separateRoomId + '\',\'' + data.examRoomId + '\',\'' + data.examType + '\')">查看认证</span>';
        str += '<span style="color: #00a0e9">|</span>';
    }
    if (delExamRecordAuth) {
        str += '<span  style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="deleteExam(\'' + data.recordId + '\')">删除</span>';
    }

    if (!str) {
        str += '--'
    }
    return str;
}



function viewMonitoring(roomId) {
    window.open(examBaseUrl + 'ui/exam/capcoExam/rtc/room?roomId=' + roomId);
}

function viewHistoryMonitoring(separateRoomId,roomId, userId) {
    let examRoomId = (separateRoomId !== "null") ? separateRoomId : roomId
    window.open(examBaseUrl + 'ui/exam/capcoExam/rtc/roomHistory?roomId=' + examRoomId + '&userId=' + userId);
}

function paperMarking(answerId, createUser, examId, recordId){
    let params = {
        userSource: 'capco'
    }
    ajaxData("/examApiConfig/loginToExam", params, function (res) {
        var url = examBaseUrl + 'ui/exam/paperMarking?EXAM_TOKEN=' + res + '&answerId=' + answerId + '&examId=' + examId + '&recordId=' + recordId + '&createUser=' + createUser;
        window.open(encodeURI(url));
    })
}

function viewExam(answerId, paperId, createUser) {
    let params = {
        createUser: createUser
    }
    ajaxData("/examMonitoring/getUserType", params, function (res) {
        var userType = res;
        var url = examBaseUrl + 'ui/exam/capcoExam/capcoExamLook?answerId=' + answerId + '&paperLook=1&comeType=1&status=1&indexType=0&userType=' + userType + '&paperId=' + paperId;
        window.open(encodeURI(url));
    })

}

function deleteExam(recordId) {
    popConfirm("当前操作不可恢复，确认删除?", function () {
        let param = {
            recordId: recordId,
            status: "0"
        }
        ajaxData("/examMonitoring/updateExamMonitoringStatus", param, function (res) {
            if (res) {
                popMsg("删除成功");
            } else {
                popMsg("删除失败");
            }
            callBackAddExam();
        })
    });
}

function callBackAddExam() {
    ajaxTableReload("tableAll", false);
}

function selectMonitor(recordId, answerId, examRank,createUser,separateRoomId,roomId,examType) {
    let param = {
        recordId: recordId,
        answerId: answerId,
        examRank: examRank,
        createUser: createUser,
        examRoomId : (separateRoomId !== "null") ? separateRoomId : roomId,
        examType:examType
    }
    popWin('查看认证', '/examMonitoring/selectMonitorInit', param, '98%', '98%', callBackAddExam, '', callBackAddExam);
}
