<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
    </style>
</head>
<body>
<div class="panel" style="min-height: 600px;">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">  直播签到记录</i>
    </div>
    <div class="panel-body" style="min-height: 600px;">
        <form:form action=""  modelAttribute="liveShareSignParam" id="liveShareSignParam">
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">直播名称：</label>
                <div class="col-md-3">
                    <input id="liveId" type="text" class="t-select" json-data='${liveNameList}' placeholder="请选择直播名称" />
                    <input name="liveId" type="hidden" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center;margin-top: 5px">姓名：</label>
                <div class="col-md-3">
                    <form:input path="userName" autocomplete="off"  cssClass="form-control" placeholder="请输入姓名"/>
                </div>
                <label class="col-md-1 no-padding control-label text-right" style="text-align:center;margin-top: 5px">股票代码：</label>
                <div class="col-md-3" >
                    <form:input path="companyCode" cssClass="form-control"  placeholder="请输入股票代码" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 no-padding control-label text-right" style="text-align:center;margin-top: 5px" id="companyNameLabel">公司名称：</label>
                <div class="col-md-3">
                    <form:input path="companyName" cssClass="form-control"  placeholder="请输入公司名称" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3" align="right" style="float: right">
                    <span id="resetBtn" class="btn btn-default">清空条件</span>
                    <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
                    <input type="button" id="export" class="btn btn-primary" value="导出">

                </div>
            </div>
        </form:form>
        <div class="example">
            <hr>
        </div>
        <div class="ibox float-e-margins">
            <e:grid id="shareSignTable" action="/liveShareSign/queryShareTable" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="姓名" displayColumn="userName" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="电话" displayColumn="phone" orderable="false" cssClass="text-center" cssStyle="width:7%; word-wrap: break-word;"/>
                <e:gridColumn label="股票代码" renderColumn="companyCode" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="公司名称" renderColumn="companyName" orderable="false" cssClass="text-center" cssStyle="width:7%; word-wrap: break-word;"/>
                <e:gridColumn label="职务" renderColumn="post" orderable="false" cssClass="text-center" cssStyle="width:7%; word-wrap: break-word;"/>
                <e:gridColumn label="直播名称" displayColumn="liveName" orderable="false" cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
                <e:gridColumn label="ip地址" displayColumn="ip" orderable="false" cssClass="text-center" cssStyle="width:7%; word-wrap: break-word;"/>
                <e:gridColumn label="辖区" renderColumn="belongCommission" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="来源" renderColumn="renderColumnSource" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="签到时间" displayColumn="createTime" orderable="false" cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
