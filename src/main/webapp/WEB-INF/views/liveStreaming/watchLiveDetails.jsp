
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js />
    <e:base />
    <style type="text/css">
        table td {
            white-space:pre-wrap !important;
            word-wrap: break-word !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div class="ibox float-e-margins">
            <e:grid id="liveInfoTable" action="/watchLive/queryWatchLiveDetails?liveId=${liveViewInfoDto.liveId}&userId=${liveViewInfoDto.userId}" cssClass="table table-striped table-hover" >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="进入时间" displayColumn="startTime" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="退出时间" displayColumn="endTime" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="观看时间" renderColumn="timeColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="来源" renderColumn="sourceColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
