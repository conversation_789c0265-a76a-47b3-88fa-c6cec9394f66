
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        table td {
            white-space:pre-wrap !important;
            word-wrap: break-word !important;
        }
        .dataTables_scrollHead,
        .dataTables_scrollBody {
            overflow: visible !important;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="panel" style="min-height: 600px;">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">  观看记录</i>
    </div>
    <div class="panel-body">
        <form:form action=""  modelAttribute="liveViewInfoDto" id="liveViewInfoDto">
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">直播名称：</label>
                <div class="col-md-3">
                    <input id="liveId" type="text" class="t-select" json-data='${liveNameList}' placeholder="请选择直播名称" />
                    <input name="liveId" type="hidden" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center;margin-top: 5px">直播时间：</label>
                <div class="col-md-3 daterange">
                    <form:input path="liveTimeStr" placeholder="请输入直播时间" cssClass="form-control" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center;margin-top: 5px">用户名称：</label>
                <div class="col-md-3">
                    <form:input path="userName" autocomplete="off"  cssClass="form-control" placeholder="请输入用户名称"/>
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 no-padding control-label text-right" style="text-align:center;margin-top: 5px">股票代码：</label>
                <div class="col-md-3" >
                    <form:input path="companyCode" cssClass="form-control"  placeholder="请输入股票代码" maxlength="45"
                                autocomplete="off"/>
                </div>
                <label class="col-md-1 no-padding control-label text-right" style="text-align:center;margin-top: 5px" id="companyNameLabel">公司简称：</label>
                <div class="col-md-3">
                    <form:input path="companyName" cssClass="form-control"  placeholder="请输入公司名称" maxlength="45"
                                autocomplete="off"/>
                </div>
<%--                <label class="col-md-1 control-label" style="text-align:center">用户类型：</label>--%>
<%--                <div class="col-md-3">--%>
<%--                    <input id="personType" type="text" class="t-select" json-data='${personTypeList}' placeholder="请输入用户类型" />--%>
<%--                    <input name="personType" type="hidden"  />--%>
<%--                </div>--%>
            </div>


            <div class="row">
                <div class="col-md-offset-9 col-md-3" align="right">
                    <span id="resetBtn" class="btn btn-default">清空条件</span>
                    <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
                    <input type="button" id="exportBtn" class="btn btn-primary" value="导出">
<%--                    <sec:authorize access="hasAuthority('RES_SCH_VIEW_INFO_INIT_AUTHORITY_3')" >--%>

<%--                    </sec:authorize>--%>
                </div>
            </div>
        </form:form>

        <div class="example">
            <hr>
        </div>
        <div class="ibox float-e-margins">
            <e:grid id="liveInfoTable" action="/watchLive/queryWatchLiveList" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="直播名称" renderColumn="liveName" orderable="false" cssClass="text-center" cssStyle="width:12%; word-wrap: break-word;"/>
                <e:gridColumn label="用户名称" displayColumn="userName" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="公司简称" displayColumn="companyName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="观看时间" displayColumn="createTime" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="观看时长" renderColumn="timeColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
<%--                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false" cssClass="text-center" cssStyle="width:5%;"/>--%>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
