$(document).ready(function() {
    // 下拉初始化
    tSelectInit();
    $("#queryBtn").bind("click", function() {
        tableQuery();
    });

    $("#resetBtn").bind("click", function() {
        document.getElementById("liveShareSignParam").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery()
    });

    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/liveShareSign/shareLiveExport?" + $("#liveShareSignParam").serialize());
    });
});

function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

function renderColumnSource(data, type, row, meta){
    let source = ''
    if (row.source == '0') {
        source = 'web'
    } else if (row.source == '1') {
        source = 'H5'
    }
    return source;
}

function companyCode(data, type, row, meta){
    if (row.companyCode == '') {
        return '--'
    }
    return row.companyCode;
}

function companyName(data, type, row, meta){
    if (row.companyName == '') {
        return '--'
    }
    return row.companyName;
}

function belongCommission(data, type, row, meta){
    if (row.belongCommission == '') {
        return '--'
    }
    return row.belongCommission;
}

function post(data, type, row, meta){
    if (row.post == '') {
        return '--'
    }
    return row.post;
}


/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("shareSignTable", "/liveShareSign/queryShareTable", $("#liveShareSignParam").formSerialize());
}

function tSelectInit() {
    var liveNameSelectOptions = {
        id: 'liveId',
        name: 'typeName',
        value: 'liveId',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#liveId').tselectInit(null, liveNameSelectOptions);
}

function tSelectCustomCallBack(t) {
}

function tSelectSubmitCallBack(t) {
}
