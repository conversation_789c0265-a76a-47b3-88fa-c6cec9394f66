//@ sourceURL=liveInfoEdit.js
let REGEX_DATETIME = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
let _startDate = {maxDate: "2099-12-31 23:59:59"}, _endDate = {minDate: "1900-01-01 00:00:00"};
let _ImgUploadUrl = contextPath + '/filetempupload';
let _fileUploadUrl = contextPath + '/filetempupload';
let _teachRow = 0;
let _teachInfo = {};
let _teachOptionsHTML = '';

$(document).ready(function () {

  _teachRow = eval($('#teachRelaCount').val()) ? eval($('#teachRelaCount').val()) : 0;

  if (!window._babelPolyfill) {
    var oHead = document.getElementsByTagName('HEAD').item(0);
    var oScript = document.createElement("script");
    oScript.type = "text/javascript";
    oScript.src = contextPath + "/static/video/esdk-obs-browserjs-2.1.4.min.js";
    oHead.appendChild(oScript);
  }

  // teachTitle()
  // switch init
  switchInit();
  dateInit();
  // checkbox & radio init
  iCheckInit();
  // livePic init
  livePicInit();
  // 富文本初始化
  ueEditorInit();
  // 附件上传
  fileUploadInit("liveFile");
  // 讲师信息
  teachInfoInit();
  //下拉初始化
  tSelectInit();
  otherDivInit();

  $('#addTeachBtn').bind('click', function() {
    addTeachRow();
  });

  $('#generateLiveSummaryDocument').bind('click', function() {
    generateLiveSummaryDocument();
  })

  $('#saveButton').bind('click', function() {
    if( getValue($('#liveName').val())  == ""  ){
      popMsg("请填写直播名称");
      return;
    }
    if( getValue($('#liveTimeStr').val())  == ""  ){
      popMsg("请选择直播时间");
      return;
    }
    if( getValue($('#livePicFileId').val())  == ""  ){
      popMsg("请上传直播封面");
      return;
    }
    if (getValue($('[name="ifLearningHours"]:checked').val())  == "" ){
      popMsg("请选择是否有学分");
      return;
    }
    if (getValue($('[name="ifForm"]:checked').val())  == "" ){
      popMsg("请选择是否填写问卷");
      return;
    }
    if(getValue($('[name="ifLearningHours"]:checked').val())  == "1" ){
      if(getValue($('#learningMinute').val())=="" || getValue($('#learningMinute').val()).indexOf('.') !== -1 || (isNaN(getValue($('#learningMinute').val())))){
        popMsg("直播时长'分钟'请输入整数");
        return;
      }
      if(getValue($('#learningSecond').val())=="" ||getValue($('#learningSecond').val()) >=60 || getValue($('#learningSecond').val()) <0 ||  getValue($('#learningSecond').val()).indexOf('.') !== -1 || (isNaN(getValue($('#learningSecond').val())))){
        popMsg("直播时长'秒'请输入0-59之间的整数");
        return;
      }
      var regex = /^\d+(\.\d{1,2})?$/;
      if (getValue($('#credit').val())=="" || (isNaN(getValue($('#credit').val()))) || (!regex.test(getValue($('#credit').val())))){
        popMsg("学分请输入小数点后不超过两位的数字");
        return;
      }
    }else if(getValue($('[name="ifLearningHours"]:checked').val())  == "0" ){
      $('#learningMinute').val("");
      $('#learningSecond').val("");
      $('#credit').val(null);
    }

      popConfirm("保存?", function () {
        addLoading();
        saveLiveInfo();
      });
  });

  $('#publishButton').bind('click', function() {
    if ($("#editLiveInfoForm").valid()) {
      popConfirm("确认发布并保存?", function () {
        addLoading();
        saveLiveInfo("1");
      });
    } else {
      popMsg('发布失败！请检查参数');
    }
  });
  $('#unPublishButton').bind('click', function() {
    if ($("#editLiveInfoForm").valid()) {
      popConfirm("确认取消发布并保存?", function () {
        addLoading();
        saveLiveInfo("0");
      });
    } else {
      popMsg('取消发布失败！请检查参数');
    }
  });

  $('#cancelButton').bind('click', function() {
    popConfirm('确定放弃吗？', function () {
      closeWin();
    });
  });

  $.validator.addMethod("stringMaxLength",function(value,element,params){
    var length = value.length;
    for( var i = 0; i < value.length; i++ ) {
      if( value.charCodeAt(i) > 19967 ) {
        length++;
      }
    }
    return length>params[0]?false:true;
  },"最大长度不能超过50个字")


  $("#liveTimeStr").change(function(){
    $(this).valid();
  });

  $("#btnSelectCost").bind("click", function() {
    var relationId = $("#liveId").val()
    if(getValue(relationId) == ""){
      popMsg("请先保存数据")
      return ;
    }
    var param = {
      existPersonType:true,
      existIfFree:true,
      existIfDiscount:true,
      existDiscount:true,
      existPrice:true,
      relationId:relationId
    }
    popWin("选择用户类型", "/selectTrainTypeCost", param, "80%", "80%");
  });
  if ($("#examId").val() !=null && $("#examId").val() != ''){
    $('.selectExam-class').css('display', 'block');
  }else {
    $('.selectExam-class').css('display', 'none');
  }
});
function otherDivInit(){
  if (getValue($('[name="ifLearningHours"]:checked').val()) == '1'){
    otherDiv.style.display = "block";
  }
}

function decideShow(radio){
  var value = radio.value;
  var otherDiv = document.getElementById("otherDiv");
  if (value == "1") {
    otherDiv.style.display = "block";
  } else {
    otherDiv.style.display = "none";
  }
}

/**
 * 开关初始化
 */
function switchInit() {



  $('#ifQueSwitch').bootstrapSwitch('size', 'small');
  let ifQue = $('input[name="ifQue"]').val();
  if (ifQue == '0') {
    $('input[name="ifQue"]').val(false);
  } else {
    $('input[name="ifQue"]').val(true);
  }
  let ifQueOld = eval($('input[name="ifQue"]').val());
  let ifQueNow = $('#ifQueSwitch').prop("checked");
  if (ifQueOld !== ifQueNow) {
    $('#ifQueSwitch').bootstrapSwitch('toggleState');
  }

  $('#ifTalkSwitch').bootstrapSwitch('size', 'small');
  let ifTalk = $('input[name="ifTalk"]').val();
  if (ifTalk == '1') {
    $('input[name="ifTalk"]').val(true);
  } else {
    $('input[name="ifTalk"]').val(false);
  }
  let ifTalkOld = eval($('input[name="ifTalk"]').val());
  let ifTalkNow = $('#ifTalkSwitch').prop("checked");
  if (ifTalkOld !== ifTalkNow) {
    $('#ifTalkSwitch').bootstrapSwitch('toggleState');
  }

  $('#ifVerifySubSwitch').bootstrapSwitch('size', 'small');
  let ifVerifySub = $('input[name="ifVerifySub"]').val();
  if (ifVerifySub == '1') {
    $('input[name="ifVerifySub"]').val(true);
  } else {
    $('input[name="ifVerifySub"]').val(false);
  }
  let ifVerifySubOld = eval($('input[name="ifVerifySub"]').val());
  let ifVerifySubNow = $('#ifVerifySubSwitch').prop("checked");
  if (ifVerifySubOld !== ifVerifySubNow) {
    $('#ifVerifySubSwitch').bootstrapSwitch('toggleState');
  }

  $('#ifQueSwitch').on('switchChange.bootstrapSwitch',
      function (event, state) {
        if (state == true) {
          $('input[name="ifQue"]').val(true);
        } else {
          $('input[name="ifQue"]').val(false);
        }
      });
  $('#ifTalkSwitch').on('switchChange.bootstrapSwitch',
      function (event, state) {
        if (state == true) {
          $('input[name="ifTalk"]').val(true);
        } else {
          $('input[name="ifTalk"]').val(false);
        }
      });
  $('#ifVerifySubSwitch').on('switchChange.bootstrapSwitch',
      function (event, state) {
        if (state == true) {
          $('input[name="ifVerifySub"]').val(true);
        } else {
          $('input[name="ifVerifySub"]').val(false);
        }
      });
}


function dateInit() {
  var liveTimeStr = $('#liveTimeStr').val();
  if (liveTimeStr.trim() != '') {
    var liveTimes = liveTimeStr.split(' 至 ');
    dataRangePickerInit($('#liveTimeStr'), liveTimes[0], liveTimes[1], function () {
    }, function () {
    },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
  } else {
    dataRangePickerInit($('#liveTimeStr'), null, null, function () {
    }, function () {
    },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
  }

}

function LinkageEndDate(istg) {
  return {
    festival: true,
    isClear: false,
    trigger : istg || "click",
    format: 'YYYY-MM-DD hh:mm:ss',
    minDate: function (that) {
      //that 指向实例对象
      let nowMinDate = jeDate.valText('#liveBegTimeStr') == "" && jeDate.valText(that.valCell) == "";
      return nowMinDate ? jeDate.nowDate({DD:0}) : _endDate.minDate ;
    }, //设定最小日期为当前日期
    maxDate: '2099-12-31 23:59:59',
    donefun: function(obj){
      _startDate.maxDate = obj.val;
    }
  };
}

/**
 * 复选&单选初始化
 */
function iCheckInit() {
  $('input[type="checkbox"].minimal, input[type="radio"].minimal').iCheck({
    checkboxClass: 'icheckbox_minimal-blue',
    radioClass   : 'iradio_minimal-blue'
  });
  $('input[name="ifFee"]').on("ifChanged", function (ent) {
    let ifFreeStatus = $('[name="ifFee"]:checked').val();
    if (ifFreeStatus != '0') {
      $('#price').val('');
    }
  });
}

/**
 * 富文本初始化
 */
function ueEditorInit() {
  UE.delEditor('liveDetailEditor');
  um = UE.getEditor('liveDetailEditor', {
    initialFrameHeight : 150,
    textarea : "liveDetail",
    elementPathEnabled : false,
    autoHeightEnabled : false
  });
}

/**
 * 删除直播封面
 * @param obi
 * @param fieldId
 */
function clearLivePic() {
  popConfirm("确认删除该直播封面吗？", function() {
    $("#livePicFileId").val("");
    $("#livePicDiv").css("display", "none");
    $("#livePicFile").val("");
    $("#livePic").val("");
  });
}


/**
 * 直播封面初始化
 */
function livePicInit() {
  $('#livePicRemoveBtn').bind('click', function() {
    clearLivePic();
  });

  $('#livePicFile').fileupload({
    url : _ImgUploadUrl,
    dataType : 'json',
    autoUpload : true,
    add : function(e,data) {
      let fileName = data.files[0].name;
      let imgType = [ "gif", "jpeg", "jpg", "bmp", "png" ];
      if (!RegExp(
          "\.(" + imgType.join("|") + ")$",
          "i").test(fileName.toLowerCase())) {
        popMsg("选择文件错误,图片类型必须是"
            + imgType.join("，") + "中的一种");
        return;
      }
      data.submit();
    },
    submit : function(e, data) {
      index = layer.load(1, {
        shade : [ 0.1, '#fff' ]
        // 0.1透明度的白色背景
      });
    },
    done : function(e, data) {
      $.each(data.result, function(index, file) {
        $("#livePicFileId").val(file.filePath);
        $("#livePic").val(file.fileRelaId);
      });
      layer.close(index);
    },
    fail : function(e, data) {
      layer.close(index);
      popAlert("上传失败,请检查文件");
    }
  });

  $("#livePicFile").livePicUploadPreview({
    Img : "livePicImg",
    Width : 50,
    Height : 50
  });
}


$.fn.extend({
  livePicUploadPreview: function (opts) {
    let _self = this, _this = $(this);
    opts = jQuery.extend({
      Img: "livePicImg",
      Width: 100,
      Height: 100,
      ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
      Callback: function () {
      }
    }, opts || {});
    _self.getObjectURL = function (file) {
      var url = null;
      if (window.createObjectURL != undefined) {
        url = window.createObjectURL(file)
      } else if (window.URL != undefined) {
        url = window.URL.createObjectURL(file)
      } else if (window.webkitURL != undefined) {
        url = window.webkitURL.createObjectURL(file)
      }
      $("#livePic").val("");
      $("#livePicDiv").css("display", "block");
      return url
    };
    _this.change(function () {
      if (this.value) {
        if (!RegExp(
            "\.(" + opts.ImgType.join("|") + ")$",
            "i").test(this.value.toLowerCase())) {
          alert("选择文件错误,图片类型必须是"
              + opts.ImgType.join("，") + "中的一种");
          this.value = "";
          return false
        }
        $.browser = new Object();
        $.browser.msie = /msie/
            .test(navigator.userAgent.toLowerCase());
        if ($.browser.msie) {
          try {
            $("#" + opts.Img)
                .attr(
                    'src',
                    _self
                        .getObjectURL(this.files[0]))
          } catch (e) {
            var src = "";
            var obj = $("#" + opts.Img);
            var div = obj.parent("div")[0];
            _self.select();
            if (top != self) {
              window.parent.document.body.focus()
            } else {
              _self.blur()
            }
            src = document.selection.createRange().text;
            document.selection.empty();
            obj.hide();
            obj
                .parent("div")
                .css(
                    {
                      'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                      'width': opts.Width
                          + 'px',
                      'height': opts.Height
                          + 'px'
                    });
            div.filters
                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
          }
        } else {
          $("#" + opts.Img).attr('src',
              _self.getObjectURL(this.files[0]))
        }
        opts.Callback()
      }
    })
  }
});

function tSelectInit(){
  var teaSelectOptions = {
    id: 'id',
    name: 'teachName',
    value: 'id',
    grade: 1,
    resultType: 'all',
    inputSearch: true,
    style: {},
    customCallBack: tSelectCustomCallBack,
    submitCallBack: tSelectSubmitCallBack
  };
  $('#teacher').tselectInit(null, teaSelectOptions);
  var teaSelectBelong= {
    id: 'codeValue',
    name: 'codeName',
    value: 'codeValue',
    grade: 1,
    resultType: 'all',
    style: {},
    customCallBack: tSelectCustomCallBack,
    submitCallBack: tSelectSubmitCallBack
  };
  $('#liveType').tselectInit(null, teaSelectBelong);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

function orgListTSelectSubmitCallBack(t,d){
  $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
  $('input[name="' + t.attr('id') + '"]').val(d.value);
  var selectTeachList = [];
  var teachIds = d.value;
  for (var i=0;i<_teachInfoList.length;i++){
    if (teachIds.indexOf(_teachInfoList[i].id)!=-1){
      selectTeachList.push(_teachInfoList[i])
    }
  }
  addTeachRow(selectTeachList)
}
/**
 * 讲师信息初始化
 */
function teachInfoInit() {
  _teachOptionsHTML += '<option value="">请选择讲师</option>';
  for (let i = 0; i < _teachInfoList.length; i++) {
    let teachInfo = _teachInfoList[i];
    _teachInfo[teachInfo.id] = teachInfo;
    _teachOptionsHTML += '<option value="' + teachInfo.id + '">' + teachInfo.teachName + '</option>';
  }
}

/**
 * 增加讲师
 */
function addTeachRow(selectTeachList) {
  $("#teachTBody").empty();
  var content = '';
  for (var i=0;i<selectTeachList.length;i++){
    var html =
        '<tr>' +
        '<td>'+selectTeachList[i].teachName+'</td>' +
        '<td><img class="col-xs-12" style="height: 100px;width: auto;" src='+selectTeachList[i].teachPic+' /></td>' +
        '<td>'+selectTeachList[i].teachIntro+'</td>' +
        '</tr>'
    content+=html
  }
  $('#teachTBody').append(content);
}

/**
 * 讲师选择
 * @param item
 * @param teachRow
 */
function teachChange(item, teachRow) {
  let teachId = $('#teachSelect_' + teachRow).val();
  if (getValue(teachId) != '') {
    let teachInfo = _teachInfo[teachId];
    $('#teachIntro_' + teachRow).val(teachInfo.teachIntro);
    $('#teachPic_' + teachRow).attr('src', teachInfo.teachPic);
    $('#teachPic_' + teachRow).css('visibility', 'visible');
  } else {
    $('#teachPic_' + teachRow).css('visibility', 'hidden');
  }
}

/**
 * 删除讲师
 * @param teachRow
 */
function removeTeach(teachRow) {
  $('.teach-tr-' + teachRow).remove();
}

/**
 * 课件上传
 */
function fileUploadInit(fileUploadId) {
  $("#" + fileUploadId).fileupload({
    url: _fileUploadUrl,
    dataType: 'text',
    autoUpload: true,
    add: function(e, data) {
      // 上传文件时校验文件格式
      data.submit();
    },
    submit: function(e, data) {
      index = layer.load(1, {
        shade: [0.1, '#fff'] //0.1透明度的白色背景
      });
    },
    done: function(e, data) {
      layer.close(index);
      $.each($.parseJSON(data.result), function(index, file) {
        let divStr =
            ' <div class="col-xs-12 file-content">' +
            ' <span onclick="downloadFile(\'' + file.filePath + '\',\'' + file.fileName + '\', 0)" class="file-span" title="下载" data-file-name ="' + file.fileName + '"  data-file-size ="' + file.fileSize + '"   data-file-type="0" data-atta-id="" data-file-id="' + file.filePath + '">' + file.fileName + '</span>' +
            ' <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>' +
            ' </div>';
        $("#fileDiv").append(divStr);
      })
    },
    fail: function(e, data) {
      popMsg("上传附件失败！");
    }
  });
}

/**
 * 下载文件
 *
 * @param fileId 文件ID
 */
function downloadFile(fileId,fileName, rootType) {
  if (getValue(fileId) != '') {
    window.open(contextPath + "/ebSchoolLiveInfo/filedownload?fileId=" + fileId + "&fileName=" + fileName + "&rootType=" + rootType, "_blank");
  }
}

/**
 * 删除课件
 * @param item
 */
function removeFile(item) {
  // 移除页面元素
  $(item).parent().remove();
}

/**
 * 保存直播信息
 */
function saveLiveInfo(data) {
  let files = [];
  let url = '';
  //新增、更新判别
  if($("#liveId").val() == ''){
    url = '/ebSchoolLiveInfo/saveLiveInfo';
    $.each($('.file-span'), function (index, fileItem) {
      let fileId = $(fileItem).data('file-id');
      let fileType = $(fileItem).data('file-type');
      let fileName = $(fileItem).data('file-name');
      let fileSize = $(fileItem).data('file-size');
      if (getValue(fileId) != '') {
        files.push({
          dataType: fileType,
          tempId: fileId,
          fileName : fileName,
          size: fileSize
        });
      }
    });
  }else {
    url = '/ebSchoolLiveInfo/updateLiveInfo';
    $.each($('.file-span'), function(index, fileItem) {
      let fileType = $(fileItem).data('file-type');
      let attId = $(fileItem).data('atta-id');
      let tempId = $(fileItem).data('file-id');
      let fileName = $(fileItem).data('file-name');
      let fileSize = $(fileItem).data('file-size');
      if (getValue(tempId) != '' || getValue(attId) != '') {
        files.push({
          dataType: fileType,
          attachmentId: attId,
          tempId: tempId,
          fileName : fileName,
          size: fileSize
        });
      }
    });
  }
  debugger
  let param = {
    liveType: $('input[name="liveType"]').val(),
    liveId: $('#liveId').val(),
    liveName: $('#liveName').val(),
    courseType:$('input[name="courseType"]').val(),
    liveTimeStr: $('#liveTimeStr').val(),
    livePic: $('#livePicFileId').val(),
    ifVideo: $('[name="ifVideo"]:checked').val(),
    videoUrl: $('#videoUrl').val(),
    liveDetail: $('textarea[name="liveDetail"]').val(),
    releState: data!=null? data : $("#releStateInput").val(),
    fileList: files,
    teachList: $('input[name="teacher"]').val().split(","),
    examId :$('#examId').val(),
    ifLearningHours: $('[name="ifLearningHours"]:checked').val(),
    ifForm: $('[name="ifForm"]:checked').val(),
    learningMinute:$('#learningMinute').val(),
    learningSecond:$('#learningSecond').val(),
    credit:$('#credit').val()
  };
  $.ajax({
    url: contextPath + url,
    data: JSON.stringify(param),
    dataType: 'json',
    contentType: 'application/json',
    type: 'post',
    success: function (req) {
      if (req.success) {
        closeWinCallBack(req);
      } else {
        let errorMsg = ( req.error ? req.error : req.errorMsg );
        popMsg(errorMsg);
      }
    },
    error: function () {
      popMsg("保存失败");
    }
  });
}

// function teachTitle() {
//   if ($('#liveType').val()=='0'){
//     $('#teachTitle').text('培训导师')
//   }else if ($('#liveType').val()=='1'){
//     $('#teachTitle').text('活动导师')
//   }
// }

function uploadFileBefore(){
  console.log(document.getElementById('newUploadFile').files);
  var file = document.getElementById('newUploadFile').files[0];
  var checkFlag = checkFileSuffix(file.name);
  if (!checkFlag) {
    popMsg("上传失败，含有不可上传的文件格式");
    return;
  }
  hwCloudStartUpload(file,"live", callback);
}

function generateLiveSummaryDocument() {
  var liveId = $("#liveId").val()
  var videoUrl = $("#liveVideoUrl").val()
  if (!videoUrl) {
    popMsg("生成失败，回放地址不存在！");
  } else if (videoUrl.indexOf("cn-north-4") != -1) {
    popMsg("生成失败，回放地址不正确！");
  } else {
    ajaxData('/ebSchoolLiveInfo/generateLiveSummaryDocument?liveId=' + liveId,null,function (data) {
      popMsg("正在生成中，预计需要10~15分钟完成。可进行其他操作，所生成的文档将会上传到培训课件之中。");
    })
  }
}

function callback(videoUrl){
  $("#liveVideoUrl").val(videoUrl);
  $("#videoUrl").val(videoUrl);
}

function checkFileSuffix(fileName) {
  var checkFile = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length);
  var approveFiles = $("#approveFilesSuffix1").val();
  if (approveFiles == "") {
    return false;
  }
  var arrayFiles = approveFiles.split(";");
  for (var i = 0; i < arrayFiles.length; i++) {
    if (checkFile.toLowerCase() == arrayFiles[i].toLowerCase()) {
      return true;
    }
  }
  return false;
}
function selectExam(){
  var params = {
    //examType:'111111'
  }
  popWin("选择考试", "/specialConfig/selectExamPop", params, "90%", "90%", function (data) {
    $('.selectExam-class').css('display', 'block');
    $("#examId").val(data.examId);
    $("#examName").html(data.examName);
  })
}
function delExamName(obj) {
  $("#examId").val("");
  $(".selectExam-class").css('display', 'none')
}
