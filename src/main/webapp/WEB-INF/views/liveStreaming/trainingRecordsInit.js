//@ sourceURL=liveSubscribeList.js

let _startDate = {maxDate: "2099-12-31 23:59:59"}, _endDate = {minDate: "1900-01-01 00:00:00"};

$(document).ready(function() {

    $("#queryBtn").bind("click", function() {
        tableQuery();
    });

    $("#resetBtn").bind("click", function() {
        document.getElementById("liveSubscribeForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery();
    });

    $("#exportTableBtn").bind("click", function() {
        exportData();
    });

    $("#exportByLive").bind("click", function() {
        exportByLiveData();
    });

    //禁用Enter键
    document.onkeydown = function (e) {
        //捕捉回车事件
        var ev = (typeof event != 'undefined') ? window.event : e;
        if (ev.keyCode == 13 || event.which == 13) {
            return false;
        }
    }

    var liveNameSelectOptions = {
        id: 'liveId',
        name: 'typeName',
        value: 'liveId',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#liveId').tselectInit(null, liveNameSelectOptions);
});

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

/**
 * 列表查询
 */
function tableQuery() {
    var liveId = $("#liveId").val();

    var param = {
        liveId : liveId,
    }
    ajaxTableQuery("liveInfoTable", "/liveSubscribe/trainingRecordsList", $("#liveSubscribeForm").formSerialize(param));
}

function saveCallBack() {
    popMsg('保存成功！');
    tableReload(true);
}

function updateCallBack() {
    popMsg('保存成功！');
    tableReload(false);
}

function tableReload(resetPaging) {
    ajaxTableReload("liveInfoTable", resetPaging);
}

function exportData(){
    var liveId = $("#liveId").val();
    var examineState = $("#examineState").val();
    var param = {
        liveId : liveId,
        examineState :examineState
    }
    window.open(contextPath + "/liveSubscribe/trainingRecordsexport?" + $("#liveSubscribeForm").serialize(param));

}

function exportByLiveData(){
    var liveId = $("#liveId").val();
    var param = {
        liveId : liveId,
    }
    window.open(contextPath + "/liveSubscribe/exportByLive?" + $("#liveSubscribeForm").serialize(param));
}

function renderExamineStateName(data, type, row, meta) {
    if(data.examineStateName == '0'){
        return '待审核'
    }else if(data.examineStateName == '1'){
        return '审核通过'
    }else {
        return '审核未通过'
    }
}

/**
 * 操作列
 * @param data
 * @param type
 * @param row
 * @param meta
 * @returns {*}
 */
function renderColumnOperation(data, type, row, meta) {
    let content = '';
    var examineinfo = '';
    if (document.getElementById("examineinfo")) {
        if(data.examineState == '0'){
            content += '<a href="javascript:void(0)" onclick="examineSubscribe(\''+ data.liveSubscribeId +'\',\'' + data.phone + '\',\'' + data.liveName + '\',\'' + data.subNum + '\',\'' + data.examineStateName + '\',\'' + data.liveId + '\')" title="审核">审核</a>';
        }else{
            content += '<a href="javascript:void(0)" onclick="examineSubscribe(\''+ data.liveSubscribeId +'\',\'' + data.phone + '\',\'' + data.liveName + '\',\'' + data.subNum + '\',\'' + data.examineStateName + '\',\'' + data.liveId + '\',\'' + data.subscribeUserId + '\')" title="修改审核状态">修改审核状态</a>';
        }
    }
    if (!content) {
        content = '-';
    }
    return content;
}
function renderSubscribeUserName(data, type, row, meta) {
    let content = '<a href="javascript:void(0)" onclick="personDetail(\''+ data.subscribeUserId +'\')">' + data.subscribeUserName + '</a>';

    // content += ' | ';
    // content += '<a href="javascript:void(0)" onclick="deleteSubscribeInfo(\''+ data.liveSubscribeId +'\')" title="删除直播信息">删除</a>';
    return content;
}
function personDetail() {

}

function subSourceOperation(data, type, row, meta) {
    // 0: web 1:h5  2:小程序
    if(data.subSource == '1'){
        return 'H5';
    }else if(data.subSource == '0') {
        return 'Web';
    } else if(data.subSource == '2') {
        return '小程序'
    }
}
/**
 * 审核预约
 * @param liveSubscribeId
 */
function examineSubscribe(liveSubscribeId,phone,liveName,subNum,examineStateName,liveId,subscribeUserId) {
    var content = '<div style="width: 100%;padding-top: 10px;">' +
        '<div class="col-md-6">' +
        '<select id="examineStateCheck" class="form-control">' +
        '<option value="1" selected>通过</option>' +
        '<option value="2">不通过</option>' +
        '</select>' +
        '</div>' +
        '<div class="col-md-12">' +
        '<textarea rows="4" id="failReason" class="form-control" style="width: 100%"></textarea>'+
        '</div>' +
        '</div>';
    var layerIndex = layer.open({
        type:'1',
        title: '审核预约',
        content:content,
        btn: ['确认', '取消'],
        area: ['400px', '270px'],
        yes: function(index, layero){

            if($("#examineStateCheck").val() != examineStateName){
                var param = {
                    liveSubscribeId:liveSubscribeId,
                    examineState:$("#examineStateCheck").val(),
                    failReason:$("#failReason").val(),
                    phone:phone,
                    liveName:liveName,
                    subNum:subNum,
                    examineStated:examineStateName,
                    liveId:liveId,
                    subscribeUserId:subscribeUserId
                };
                ajaxData('/liveSubscribe/saveExamineInfo',param,function (data){
                    popMsg(data);
                    tableReload(true);
                    layer.close(layerIndex);
                })
            }else {
                popMsg('审核成功！');
                layer.close(layerIndex);
            }
        },
        btn2: function(index, layero){

        }
    });
}
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
/**
 * 删除直播信息
 * @param liveId
 */
function deleteSubscribeInfo(liveId) {
    popConfirm("确定删除该条预约信息吗？", function(){
        $.ajax({
            url: contextPath + '/ebSchoolLiveInfo/delete/' + liveId,
            type: 'GET',
            dataType: 'json',
            contentType: 'application/json',
            success:function (response) {
                if (response.success) {
                    popMsg("删除成功");
                    tableReload(false);
                } else {
                    let errorMsg = isStringEmpty(response.error) ?
                        response.errorMsg : response.error;
                    popMsg(errorMsg);
                }
            },
            error:function(error){
                popMsg(error.responseJSON.error);
            }
        });
    },null);
}

function callBackAddUser() {
    ajaxTableReload("liveInfoTable",false);
}

function personDetail(id) {
    var param ={
        id:id,
        watchType:"1"
    };
    parent.popWin('查看用户信息', '/trainUserManage/addUserInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);
}
