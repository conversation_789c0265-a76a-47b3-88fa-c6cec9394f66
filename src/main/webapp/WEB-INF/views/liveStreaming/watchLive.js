let REGEX_DATETIME = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
let _startDate = {maxDate: "2099-12-31 23:59:59"}, _endDate = {minDate: "1900-01-01 00:00:00"};


$(document).ready(function() {

    $("#queryBtn").bind("click", function() {
        tableQuery();
    });
    $("#exportBtn").bind("click", function() {
        exportWatchRecord();
    });


    $("#resetBtn").bind("click", function() {
        document.getElementById("liveViewInfoDto").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery()
    });

    var liveNameSelectOptions = {
        id: 'liveId',
        name: 'typeName',
        value: 'liveId',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#liveId').tselectInit(null, liveNameSelectOptions);
    dateInit();
    tSelectInit();
});
function dateInit() {
    var liveTimeStr = $('#liveTimeStr').val();
    if (liveTimeStr.trim() != '') {
        var liveTimes = liveTimeStr.split(' 至 ');
        dataRangePickerInit($('#liveTimeStr'), liveTimes[0], liveTimes[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    } else {
        dataRangePickerInit($('#liveTimeStr'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 1});
    }

}
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    // $('#userType').tselectInit(null, teaSelectOptions);
    $('#personType').tselectInit(null, teaSelectOptions);
}

function tSelectCustomCallBack(t) {
}

function tSelectSubmitCallBack(t) {
}

/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("liveInfoTable", "/watchLive/queryWatchLiveList", $("#liveViewInfoDto").formSerialize());
}

function exportWatchRecord(){
        let liveId=$('input[name="liveId"]').val();
        let liveTimeStr=$('#liveTimeStr').val();
        let userName=$('#userName').val();
        let companyCode=$('#companyCode').val();
        let companyName=$('#companyName').val();

    window.open(contextPath + "/watchLive/exportWatchRecord?liveId="+liveId+"&liveTimeStr="+liveTimeStr+"&userName="+userName+"&companyCode="+companyCode+"&companyName="+companyName, "_blank");
}

function saveCallBack() {
    popMsg('保存成功！');
    tableReload(true);
}

function updateCallBack() {
    popMsg('保存成功！');
    tableReload(false);
}

function tableReload(resetPaging) {
    ajaxTableReload("liveInfoTable", resetPaging);
}

/**
 * 序号列
 * @param data
 * @param type
 * @param row
 * @param meta
 * @returns {*}
 */
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function liveName(data,type,row,meta){
    if (row.liveStatus == '0'){
        return    '<span>'+ row.liveName + '<span style="color:red;">' + "(已删除)" + '</span>' + '</span>';
    }else{
        return row.liveName
    }
}


function sourceColumnIndex(data, type, row, meta) {
    // 0:h5  1: web  2:小程序
    const rules = {
        '0': 'H5',
        '1': 'Web',
        '2': '小程序'
    };
    if (!data.watchFrom) return '--';
    return data.watchFrom.split(',').map(char => rules[char] || char).join(',');
}

function renderColumnOperation(data, type, row, meta) {
    var str = '';
        str += '<a href="javascript:void(0)" onclick="details(\'' + data.liveId + '\',\'' + data.userId + '\')" title="查看详情">查看详情</a>';
    if (!str){
        str = '--';
    }

    return str;
}

function details(liveId,userId){
    var param = {
        liveId:liveId,
        userId:userId
    }
    popWin('直播记录详情', '/watchLive/watchLiveDetailsInit', param, '60%', '50%', callBackAddExam, '', callBackAddExam);
}

function callBackAddExam(){

}

function timeColumnIndex(data, type, row, meta) {
    const hours = Math.floor(data.watchTime / 3600);
    const minutes = Math.floor((data.watchTime % 3600) / 60);
    const remainingSeconds = data.watchTime % 60;

    let formattedTime = '';

    if (hours > 0) {
        formattedTime += hours + '小时';
    }

    if (minutes > 0) {
        formattedTime += minutes + '分';
    }

    formattedTime += remainingSeconds + '秒';

    return formattedTime;
}