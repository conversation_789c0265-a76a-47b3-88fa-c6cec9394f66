<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="col-md-12" style="margin: 10px 0">
<%--    <label class="col-xs-1 control-label">--%>
<%--        收费设置--%>
<%--    </label>--%>
    <div class="col-xs-12">
        <c:if test="${messageRecordList != null}">
            <table class="table table-bordered no-margin" style="text-align: center;float:left;" id="configCourseType">
                <thead>
                <tr>
                    <td width="8%" class="sch-td">发送时间</td>
                    <td width="6%" class="sch-td">发送人</td>
                    <td width="30%" class="sch-td">发送内容</td>
                    <td width="6%" class="sch-td">发送人数</td>
                    <td width="20%" class="sch-td">所选机构</td>
                    <td width="8%" class="sch-td">所选部门</td>
                    <td width="8%" class="sch-td">非选部门</td>
                    <td width="8%" class="sch-td">所选职务</td>
                    <td width="8%" class="sch-td">非选职务</td>
                </tr>
                </thead>
                <tbody id="freeTableId">
                <c:forEach items="${messageRecordList}" var="item" varStatus="status">
                    <tr>
                        <td>${item.createTime}</td>
                        <td>${item.createUser}</td>
                        <td>${item.remindContent}</td>
                        <td>${item.messageNum}</td>
                        <td>${item.personType}</td>
                        <td>${item.orgName}</td>
                        <td>${item.notOrgName}</td>
                        <td>${item.personPost}</td>
                        <td>${item.personPostNotIncluded}</td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </c:if>
    </div>
</div>
</body>
</html>
