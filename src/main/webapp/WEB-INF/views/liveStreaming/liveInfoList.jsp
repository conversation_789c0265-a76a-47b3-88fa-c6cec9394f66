
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <script>
        var personTypeList = '${personTypeList}';
        var personPostList = '${personPostList}';
    </script>
    <e:base/>
    <e:js/>
    <style type="text/css">
        table td {
            white-space:pre-wrap !important;
            word-wrap: break-word !important;
        }
        .dataTables_scrollHead,
        .dataTables_scrollBody {
            overflow: visible !important;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="panel" >
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  直播列表</i>
    </div>
    <div class="panel-body" style="min-height: 600px;">
        <form:form action=""  modelAttribute="liveInfoParam" id="liveInfoParamForm">
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center;margin-top: 5px">直播名称：</label>
                <div class="col-md-3">
                    <form:input path="liveName" autocomplete="off"  cssClass="form-control" placeholder="请输入直播名称"/>
                </div>
                <label class="col-md-1 control-label" style="text-align:center;margin-top: 5px">直播时间：</label>
                <div class="col-md-3 daterange">
                    <form:input path="liveTimeStr" placeholder="请输入直播时间" cssClass="form-control" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center;margin-top: 5px">发布状态：</label>
                <div class="col-md-3">
                    <form:select path="releState" cssClass="form-control">
                        <form:option value="">全部</form:option>
                        <form:options items="${liveReleaseOptions}" itemLabel="label" itemValue="value" />
                    </form:select>
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 control-label"  style="text-align:center;margin-top: 5px">选择讲师：</label>
                <div class="col-md-3">
                    <input id="teacher" type="text" class="t-select" placeholder="请选择讲师" json-data='${lecturerSelect}' />
                    <input name="teacher" id="teacherIds" type="hidden" placeholder="请输入讲师姓名" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-3" align="right" style="float: right">
<%--                    <sec:authorize access="hasAuthority('RES_EB_SCHOOL_LIVE_INFO_ADD_INIT_AUTHORITY_3')" >--%>
                        <span id="resetBtn" class="btn btn-default">清空条件</span>
                        <input type="button" id="addBtn" class="btn btn-primary" value="新增">
<%--                    </sec:authorize>--%>
<%--                    <sec:authorize access="hasAuthority('RES_EB_SCHOOL_LIVE_INFO_QUERY_LIST_AUTHORITY_3')" >--%>
                        <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
<%--                    </sec:authorize>--%>
                </div>
            </div>
        </form:form>
        <div class="example">
            <hr>
        </div>
        <div class="ibox float-e-margins">
            <e:grid id="liveInfoTable" action="/ebSchoolLiveInfo/queryPagingBySelective" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="直播名称" displayColumn="liveName" orderable="false" cssClass="text-center" cssStyle="width:13%; word-wrap: break-word;"/>
                <e:gridColumn label="在线观看人数" displayColumn="viewNum" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <%--                <e:gridColumn label="直播机构" displayColumn="liveOrg" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>--%>
                <e:gridColumn label="发布状态" displayColumn="releState" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="直播时间" renderColumn="renderColumnLiveDateTime" orderColumn="liveBegDateStr" cssClass="text-center" cssStyle="width:12%; word-wrap: break-word;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOpertion" orderable="false" cssClass="text-center" cssStyle="width:20%;"/>
            </e:grid>
        </div>
    </div>
</div>

<!-- 分享二维码 -->
<div id="qrCodeModal" class="modal fade bs-example-modal-sm" style="padding-left: 0;" tabindex="-1" role="dialog" aria-labelledby="gridSystemModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h3 class="modal-title" id="qrCodeModalLabel">分享二维码</h3>
            </div>
            <div class="modal-body" id="qrCodeBody" style="text-align: center;">
                <input type="hidden" id="qrCodeBaseUrl" value="${qrCodeBaseUrl}">
                <input type="hidden" id="capcoBaseUrl" value="${capcoBaseUrl}">
                <img src="" id="qrCode">
                <p id="shareUrl"></p>
                <%--                    <div style="width: 151px;height: 151px;margin: -151px auto 0 auto;" onclick="downloadShareQrCode()"></div>--%>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- /分享二维码 -->

<!-- 直播码（推流码） -->
<div id="liveCodeModal" class="modal fade" style="padding-left: 0;" tabindex="-1" role="dialog" aria-labelledby="gridSystemModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h3 class="modal-title" id="liveCodeModalLabel">直播码</h3>
            </div>
            <div class="modal-body" id="liveCodeBody" style="text-align: center;">
                <textarea id="liveCode" style="width: 100%;height: 80px;" readonly disabled="true"></textarea>
                <%--                    <input type="text" id="liveCode" value="" style="width: 400px;" readonly disabled="true"></input>--%>
                <%--                    <button type="button" id="copyBtn" class="copy-btn">复制</button>--%>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- /直播码（推流码）  -->
</body>
</html>
