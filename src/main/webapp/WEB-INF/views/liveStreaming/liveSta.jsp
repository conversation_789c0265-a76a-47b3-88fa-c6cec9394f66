<%@ page import="com.stock.capital.cloud.capcoTrain.dto.LecturerInfoDto" %>
<%@ page import="java.util.List" %>
<%@ page import="com.stock.core.util.JsonUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>新增直播信息</title>
    <e:base />
    <e:js />
    <style type="text/css">
        .config-title {
            margin-right: 75%;
            text-align: right;
        }
        .required-logo {
            color: red;
        }
        #saveButton {
            width: 100px;
        }
        .form-input {
            width: 100%;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        #livePicImg,
        #livePageImg {
            height: 200px;
            width:auto;
            border:0;
            margin: 10px 0;
        }
        #sharePicImg{
            height: 200px;
            width: 200px;
            border:0;
            margin: 10px 0;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
        #livePicUploadBtn,
        #shareImgUploadBtn,
        #livePageUploadBtn,
        #fileUploadBtn {
            width: 100px;
            height: 32px;
        }
        #livePicFile,
        #shareImgFile,
        #livePageFile,
        #liveFile {
            margin: -20px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .live-pic-btn-row,
        .live-page-btn-row,
        .live-file-btn-row {
            margin-top: 10px;
        }
        #fileDiv {
            margin: 5px 0 0 0;
        }
        .file-content {
            margin: 0 0 5px 0;
        }
        .file-span,
        .file-del-btn {
            cursor: pointer;
        }
        .teach-intro-textarea {
            height: 138px;
            width: 100%;
        }
        td.teach-intro-label,
        td.teach-name-label {
            width: 15%;
        }
        td.teach-name {
            width: 25%;
        }
        td.teach-intro {
            width: 40%;
        }
        td.teach-remove {
            width: 5%;
            text-align: center;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div class="col-xs-12">
            <span>点击量：</span>
            <span>${clickNum}</span>
        </div>
        <div class="col-xs-12">
            <span>微信人数：</span>
            <span>${wxPersonNum}</span>
        </div>
        <div class="col-xs-12">
            <span>浏览器人数：</span>
            <span>${notWxPersonNum}</span>
        </div>
    </div>
</div>
</body>
</html>
