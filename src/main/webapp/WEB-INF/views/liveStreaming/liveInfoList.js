//@ sourceURL=liveInfoList.js

let REGEX_DATETIME = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
let _startDate = {maxDate: "2099-12-31 23:59:59"}, _endDate = {minDate: "1900-01-01 00:00:00"};

$(document).ready(function() {

    $("#addBtn").bind("click", function() {
        parent.popWin('新增直播信息','/ebSchoolLiveInfo/addInit', '', '90%', '90%', saveCallBack);
    });
    $("#queryBtn").bind("click", function() {
        tableQuery();
    });
    $("#resetBtn").bind("click", function() {
        document.getElementById("liveInfoParamForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tSelectInit()
        dateInit();
        tableQuery();
    });
    $("#copyBtn").bind("click", function() {
        let copyText = $('#liveCode');
        copyText.select();
        document.execCommand("Copy");
        popMsg("复制成功！");
    });

    $('#refreshVideo').click(function () {
        getVideoDuration();
    });

    dateInit();

    tSelectInit()
});

function getVideoDuration(){
    ajaxData("/video/getVideoDuration", {videoType:'2'}, tableQuery);
}

function dateInit() {
    var liveTimeStr = $('#liveTimeStr').val();
    if (liveTimeStr.trim() != '') {
        var liveTimes = liveTimeStr.split(' 至 ');
        dataRangePickerInit($('#liveTimeStr'), liveTimes[0], liveTimes[1], function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    } else {
        dataRangePickerInit($('#liveTimeStr'), null, null, function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    }
}
//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'id',
        name: 'teachName',
        value: 'id',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);
}
function teachName(data, type, row, meta){
    var str = '--';
    if (row.teachName!=null && row.teachName!=''){
        str = row.teachName
    }
    return str;
}

function LinkageEndDate(istg) {
    return {
        festival: true,
        isClear: false,
        trigger : istg || "click",
        format: 'YYYY-MM-DD hh:mm:ss',
        minDate: function (that) {
            return _endDate.minDate ;
        },
        maxDate: '2099-12-31 23:59:59',
        donefun: function(obj){
            _startDate.maxDate = obj.val;
        }
    };
}

/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("liveInfoTable", "/ebSchoolLiveInfo/queryPagingBySelective", $("#liveInfoParamForm").formSerialize());
}

function saveCallBack() {
    popMsg('保存成功！');
    tableReload(true);
}

function updateCallBack() {
    popMsg('保存成功！');
    tableReload(false);
}

function tableReload(resetPaging) {
    ajaxTableReload("liveInfoTable", resetPaging);
}

/**
 * 直播时间列
 * @param data
 * @param type
 * @param row
 * @param meta
 * @returns {*}
 */
function renderColumnLiveDateTime(data, type, row, meta) {
    let content = '';
    if (data.liveBegDateStr) {
        content = data.liveBegDateStr + ' ' + data.liveBegTimeStr ;
        if (data.liveBegDateStr == data.liveEndDateStr) {
            content += ' - ' +data.liveEndTimeStr
        } else {
            content += "<br>"+ ' 至 '+data.liveEndDateStr + ' ' + data.liveEndTimeStr;
        }
    }

    return content;
}

/**
 * 操作列
 * @param data
 * @param type
 * @param row
 * @param meta
 * @returns {*}
 */
function renderColumnOpertion(data, type, row, meta) {
    let content = '';
        content += '<a href="javascript:void(0)" onclick="editLiveInfo(\'' + data.liveId + '\')" title="编辑直播信息">编辑</a>';
        content += ' | ' + '<a href="javascript:void(0)" onclick="getLiveCode(\'' + getValue(data.liveCode) + '\')" title="查看直播码">推流码</a>';
        content += ' | ' + '<a href="javascript:void(0)" onclick="getLivePullCode(\'' + getValuePullCode(data.pullCode) + '\')" title="后台观看地址">后台观看地址</a>';
        content += ' | ' + '<a href="javascript:void(0)" onclick="shareLiveInfo(\'' + data.liveId + '\')" title="查看分享二维码">分享</a>';
        content += ' | ' + '<a href="javascript:void(0)" onclick="sendMessage(\'' + data.liveId + '\')" title="发送短信提醒直播">发送提醒短信</a>';
        content += ' | ' + '<a href="javascript:void(0)" onclick="exportFeed(\'' + data.liveId + '\')" title="导出反馈">导出反馈</a>';
        if (content !== '') {
            content += ' | ';
            content += '<a href="javascript:void(0)" onclick="deleteLiveInfo(\'' + data.liveId + '\')" title="删除直播信息">删除</a>';
        }
    if (!content) {
        content = '-';
    }
    return content;
}

/**
 * 编辑直播信息
 * @param liveId
 */
function editLiveInfo(liveId) {
    let param = {
        liveInfoId:liveId
    }
    parent.popWin('编辑直播信息','/ebSchoolLiveInfo/addInit', param, '90%', '90%', updateCallBack);
}

/**
 * 删除直播信息
 * @param liveId
 */
function deleteLiveInfo(liveId) {
    popConfirm("确定删除该直播信息？", function(){
        $.ajax({
            url: contextPath + '/ebSchoolLiveInfo/delete/' + liveId,
            type: 'GET',
            dataType: 'json',
            contentType: 'application/json',
            success:function (response) {
                if (response.success) {
                    popMsg("删除成功");
                    tableReload(false);
                } else {
                    let errorMsg = isStringEmpty(response.error) ?
                        response.errorMsg : response.error;
                    popMsg(errorMsg);
                }
            },
            error:function(error){
                popMsg(error.responseJSON.error);
            }
        });
    },null);
}

/**
 * 查看分享二维码
 * @param liveId
 */
function shareLiveInfo(liveId) {
    let baseUrl = $('#qrCodeBaseUrl').val();
    let shareUrl = $('#capcoBaseUrl').val() + '/ui/lcab/live/liveShareH5?liveId=' + liveId + '&active=live';
    let qrCodeUrl = baseUrl + '?url=' + encodeURIComponent(shareUrl);
    $('#qrCode').attr('src', qrCodeUrl);
    $('#qrCodeBody p').text(shareUrl);
    $('#qrCodeModal').modal();
}

/**
 * 内测二维码
 * @param liveId
 */
function betaCode(liveId) {
    var key = $.base64.encode(liveId+'test');
    var baseUrl = $('#qrCodeBaseUrl').val();
    var shareUrl = $('#capcoBaseUrl').val() + '/trainingCenter/live/liveShareH5?liveId=' + liveId +'&key='+key;
    var qrCodeUrl = baseUrl + '?url=' + encodeURIComponent(shareUrl);
    $('#qrCode').attr('src', qrCodeUrl);
    $('#qrCodeModal').modal();
}

/**
 * 下载分享二维码
 */
function downloadShareQrCode() {
    var img = document.getElementById('qrCode');
    var url = img.src;
    var a = document.createElement('a');
    var event = new MouseEvent('click')
    a.download = '分享';
    a.href = url;
    a.dispatchEvent(event)
}

/**
 * 查看直播码
 * @param liveId
 */
function getLiveCode(liveCode) {
    $("#liveCodeModalLabel").text("推流码");
    $('#liveCode').val(liveCode);
    $('#liveCodeModal').modal();
}

function getValuePullCode(pullCode){
    var obj = JSON.parse(pullCode);
    var pullCodeUrl = obj.baseUrl + obj.authInfo.ud
    return pullCodeUrl

}

function getLivePullCode(pullCode) {
    $("#liveCodeModalLabel").text("后台观看地址");
    $('#liveCode').val(pullCode);
    $('#liveCodeModal').modal();
}



function getLiveSta(liveId) {
    parent.popWin('直播统计','/ebSchoolLiveInfo/liveSta',{liveId:liveId},'300','200')
}

var tSelectOptions = {
    id: 'itemNo',
    pid: 'parentItemNo',
    name: 'itemName',
    value: 'id',
    grade: 2,
    resultType: 'all',
    inputSearch: true,
    openDown: false,//是否初始展开tree
    style: {},
    customCallBack: tSelectCustomCallBack,
    submitCallBack: tSelectSubmitCallBack
};

var teaSelectOptions = {
    id: 'codeValue',
    name: 'codeName',
    value: 'codeValue',
    grade: 1,
    resultType: 'all',
    allCheck: true,//是否显示全选按钮
    style: {},
    customCallBack: tSelectCustomCallBack,
    submitCallBack: tSelectSubmitCallBack
};


function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function exportFeed(liveId){
    var param = {
        liveId:liveId
    }
    window.open(contextPath + "/export/feedbackExport?courseId=" + liveId);
}

function sendMessage(liveId){
    var content = "<div>"+
        " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
        "                    <label class=\"col-sm-2 control-label\" style='text-align: right'>短信内容：</label>" +
        "                    <div class=\"col-sm-9\">" +
        "                       <textarea id=\"remindContent\" class=\"teach-intro-textarea\" style='width: 450px;height: 100px'></textarea>"+
        "                    </div></div>"+
        "</div>";
    var layerIndex = layer.open({
        title:'短信提醒',
        content: content,
        btn: ['确定', '关闭'],
        area:["600px","240px"],
        yes: function(index){
            if ($("#remindContent").val() != null && $("#remindContent").val() != ''){
                var param = {
                    liveId:liveId,
                    remindContent: $("#remindContent").val(),
                }
                ajaxData("/ebSchoolLiveInfo/sendToNoAppointment", param, function (res) {
                    addLoading();
                    var myVar = window.setInterval(function() {
                        popMsg("短信发送成功!")
                        clearInterval(myVar)
                    },1000);

                    //getSendMessageInfo(liveId)
                })
            }else {
                popMsg("发送失败，短信提醒内容为必填项！")
            }
        },
        no:function (index,) {
            layer.close(layerIndex);
        }
    })


}
//查询短信是否发送成功
function getSendMessageInfo(liveId){
    var myVar = window.setInterval(function() {
        //查询短信是否发送成功
        addLoading();
        ajaxData("/ebSchoolLiveInfo/getSendMessageInfo",{liveId:liveId},function (data){
            addLoading();
            if (data=='true'){
                popMsg("短信发送成功!")
                clearInterval(myVar)
                removeLoading();
            }
            if (data=='false'){
                popMsg("短信发送失败，请稍后再试！")
                clearInterval(myVar)
                removeLoading();
            }
            if (data=='error'){
                popMsg("短信发送失败，请稍后再试！")
                clearInterval(myVar)
                removeLoading();
            }

        })
    },5000);
}

function messageRecord(liveId){
    parent.popWin('短信记录', '/ebSchoolLiveInfo/viewMessageRecord', {liveId:liveId}, '90%', '90%', callBackViewOrder, '', callBackViewOrder);
}
function callBackViewOrder(){
    ajaxTableReload("table", false);
}

function sendAppointmentNotWatch(liveId){
    var content =
        " <div class=\"col-sm-12\" style='margin-top: 5px'>" +
        " <label class=\"col-sm-3 control-label\" style='text-align: right'>短信内容：</label>" +
        " <div class=\"col-sm-8\">" +
        "    <textarea id=\"remindContent1\" class=\"teach-intro-textarea\" style='width: 450px;height: 300px'></textarea>"+
        " </div>" +
        "</div>"
    var layerIndex = layer.open({
        title: '短信内容',
        content: content,
        btn: ['确定', '关闭'],
        area: ["660px", "660px"],
        yes: function (index) {
            if ($("#remindContent1").val() != null && $("#remindContent1").val() != '') {
                var param = {
                    liveId: liveId,
                    remindContent: $("#remindContent1").val(),
                }
                ajaxData("/ebSchoolLiveInfo/sendAppointmentNotWatch", param, function (res) {
                    layer.close(layerIndex);
                    popMsg("短信发送成功!")
                })
            } else {
                popMsg("发送失败，短信提醒内容为必填项！")
            }
        },
        no: function (index,) {
            layer.close(layerIndex);
        }

    })
}

function liveCostInfo(liveId){
    ajaxData("/ebSchoolLiveInfo/liveCost",{liveId:liveId},function (data){
        var content =
            "<div class=\"col-sm-12\" style='margin-top: 5px'>" +
            " <label class=\"col-sm-3 control-label\" style='text-align: right'>观众人数：</label>" +
            " <div class=\"col-sm-8\">" +
            "<div>"+ data.maxUserCount + "人"+"</div>"+
            " </div>" +
            "</div>"+
            "<div class=\"col-sm-12\" style='margin-top: 5px'>" +
            " <label class=\"col-sm-3 control-label\" style='text-align: right'>直播时间：</label>" +
            " <div class=\"col-sm-8\">" +
            "<div>"+ data.timeCount + "分钟"+"</div>"+
            " </div>" +
            "</div>"+
            "<div class=\"col-sm-12\" style='margin-top: 5px'>" +
            " <label class=\"col-sm-3 control-label\" style='text-align: right'>直播费用：</label>" +
            " <div class=\"col-sm-8\">" +
            "<div>"+ data.moneyCount + "元"+"</div>"+
            " </div>" +
            "</div>"
        var layerIndex = layer.open({
            title: '直播费用',
            content: content,
            btn: ['确定', '关闭'],
            area: ["660px", "400px"],

        })
    })
}

function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
