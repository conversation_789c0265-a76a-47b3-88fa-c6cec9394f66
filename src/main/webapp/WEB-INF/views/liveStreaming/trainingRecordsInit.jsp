
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        table td {
            white-space:pre-wrap !important;
            word-wrap: break-word !important;
        }
        .dataTables_scrollHead,
        .dataTables_scrollBody {
            overflow: visible !important;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">参训记录</i>
    </div>
    <div class="panel-body">
        <form:form action=""  modelAttribute="liveSubscribeInfoDto" id="liveSubscribeForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">直播名称</label>
                    <div class="col-md-3">
                        <input id="liveId" type="text" class="t-select" json-data='${liveNameList}' placeholder="请选择直播名称" />
                        <input name="liveId" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="realName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">所在辖区</label>
                    <div class="col-md-3">
                        <form:select path="belongCommission" id="belongCommission" name="belongCommission"  cssClass="form-control">
                            <form:option value="">全部</form:option>
                            <c:forEach var="property" items="${belongCommissionList}" varStatus="index">
                                <form:option value="${property.codeValue}">${property.codeName}</form:option>
                            </c:forEach>
                        </form:select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">用户类型</label>
                    <div class="col-md-3">
                        <form:select path="personType" id="personType" name="personType" cssClass="form-control">
                            <form:option value="">全部</form:option>
                            <c:forEach var="property" items="${personTypeList}" varStatus="index">
                                <form:option value="${property.codeValue}">${property.codeName}</form:option>
                            </c:forEach>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">证券代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">公司名称</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-offset-9 col-md-3" align="right">
                    <sec:authorize access="hasAuthority('RES_SCH_LIVE_TRAININIG_RECORDS_INIT_AUTHORITY_3')" >
                        <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
                    </sec:authorize>
                    <input type="button" id="resetBtn" class="btn btn-primary" value="清空">
                    <sec:authorize access="hasAuthority('RES_SCH_LIVE_TRAININIG_RECORDS_EXPORT_AUTHORITY_3')" >
                        <input type="button" id="exportTableBtn" class="btn btn-primary" value="导出">
                    </sec:authorize>
                </div>
            </div>
        </form:form>
        <div class="ibox float-e-margins">
            <e:grid id="liveInfoTable" action="/liveSubscribe/trainingRecordsList" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="直播名称" displayColumn="liveName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="观看人" renderColumn="renderSubscribeUserName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="用户类型" displayColumn="subscribeUserType" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="公司代码" displayColumn="companyCode" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
