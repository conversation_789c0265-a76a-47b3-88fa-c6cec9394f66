<%@ page import="com.stock.capital.cloud.capcoTrain.dto.LecturerInfoDto" %>
<%@ page import="com.stock.capital.cloud.liveStreaming.dto.LiveInfoDto" %>
<%@ page import="com.stock.core.util.JsonUtil" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Optional" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>编辑直播信息</title>
    <e:base />
    <e:js />
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/video/js-vod-sdk-1.0.1.min.js"></script>
    <style type="text/css">
        .form-control1 {
            width: 68px;
            height: 30px;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            background-color: #fff;
            background-image: none;
            border: 1px solid #ccc;
            border-radius: 4px;
            -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
            -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
            -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
            transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        }
        .form-control1:focus {
            border-color:  #145CCD;
            outline: 0;
            -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
        }
        .config-title {
            margin-right: 75%;
            text-align: right;
        }
        .required-logo {
            color: red;
        }
        .form-input {
            width: 100%;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        #livePicImg,
        #livePageImg {
            height: 200px;
            width: auto;
            border:0;
            margin: 10px 0;
        }
        #sharePicImg{
            height: 200px;
            width: 200px;
            border:0;
            margin: 10px 0;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
        #livePicUploadBtn,
        #livePageUploadBtn,
        #shareImgUploadBtn,
        #fileUploadBtn {
            width: 100px;
            height: 32px;
        }
        #livePicFile,
        #livePageFile,
        #shareImgFile,
        #liveFile {
            margin: -20px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .live-pic-btn-row,
        .live-page-btn-row,
        .live-file-btn-row {
            margin-top: 10px;
        }
        #fileDiv {
            margin: 5px 0 0 0;
        }
        .file-content {
            margin: 0 0 5px 0;
        }
        .file-span,
        .file-del-btn {
            cursor: pointer;
        }
        .teach-intro-textarea {
            height: 138px;
            width: 100%;
        }
        td.teach-intro-label,
        td.teach-name-label {
            width: 15%;
        }
        td.teach-name {
            width: 25%;
        }
        td.teach-intro {
            width: 40%;
        }
        td.teach-remove {
            width: 5%;
            text-align: center;
            vertical-align: middle;
        }
        .isNumber{
            border: 1px solid #ccc;
            height: 30px;
            width: 50%;
            background: #F5F5F5;
        }

    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="editLiveInfoForm" action="/ebSchoolLiveInfo/updateLiveInfo" modelAttribute="liveInfoParam" cssClass="form-horizontal">
            <h2 class="col-xs-2 config-title">基本信息</h2>
            <form:hidden path="liveId"></form:hidden>
            <form:hidden path="releState" id="releStateInput" value="${liveInfoParam.releState}"></form:hidden>
            <form:hidden path="examId"/>

            <div class="control-group form-group">
                <label class="col-xs-2 control-label"><font class="required-logo">*</font>直播名称</label>
                <div class="col-xs-6 controls">
                    <form:input type="text" class="form-control" path="liveName" value="" autocomplete="off" placeholder="请输入直播名称"/>
                </div>
            </div>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label"><font class="required-logo">*</font>直播时间</label>
                <div class="col-xs-3 daterange" style="width: 370px">
                    <form:input path="liveTimeStr" placeholder="请选择直播时间" cssClass="form-control" />
                </div>
            </div>
            <div class="control-group form-group" style="height: 41px">
                <label class="col-xs-2 control-label"><font class="required-logo">*</font>是否有学分</label>
                <div class="col-xs-1 controls">
                    <c:if test="${liveInfoParam.ifLearningHours  == '1'}">
                        <input type="radio" name="ifLearningHours" value="1" checked onchange="decideShow(this)"/>是
                        <input type="radio" name="ifLearningHours" value="0" style="margin-left: 15px;" onchange="decideShow(this)"/>否
                    </c:if>
                    <c:if test="${liveInfoParam.ifLearningHours  != '1'}">
                        <input type="radio" name="ifLearningHours" value="1" onchange="decideShow(this)"/>是
                        <input type="radio" name="ifLearningHours" value="0" checked style="margin-left: 15px;" onchange="decideShow(this)"/>否
                    </c:if>
                </div>
                <div id="otherDiv" class="control-group form-group" style="display: none; height: 26px">
                    <label class="col-xs-1 control-label text-right "><font class="required-logo">*</font>直播时长</label>
                    <div class="col-xs-2 controls" style="display: inline-block">
                        <form:input type="text" class="form-control1" path="learningMinute" value="" autocomplete="off"  placeholder="分钟"/>分钟
                        <form:input type="text" class="form-control1" path="learningSecond" value="" autocomplete="off"   placeholder="秒"/>秒
                    </div>
                    <label class="col-xs-1 control-label text-right"><font class="required-logo">*</font>课程学分</label>
                    <div class="col-xs-2 controls">
                        <form:input type="text" class="form-control" path="credit" value="" autocomplete="off" placeholder="请输入课程学分"/>
                    </div>
                </div>
            </div>
            <div class="control-group form-group" style="height: 41px">
                <label class="col-xs-2 control-label"><font class="required-logo">*</font>是否填写问卷</label>
                <div class="col-xs-1 controls">
                    <c:if test="${liveInfoParam.ifForm  == '1'}">
                        <input type="radio" name="ifForm" value="1" checked />是
                        <input type="radio" name="ifForm" value="0" style="margin-left: 15px;" />否
                    </c:if>
                    <c:if test="${liveInfoParam.ifForm  != '1'}">
                        <input type="radio" name="ifForm" value="1" />是
                        <input type="radio" name="ifForm" value="0" checked style="margin-left: 15px;" />否
                    </c:if>
                </div>
            </div>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label">直播详情</label>
                <div class="col-xs-9 controls">
                    <script id="liveDetailEditor" type="text/plain" >${ liveInfoParam.liveDetail }</script>
                </div>
            </div>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label">直播类型</label>
                <div class="col-md-3">
                    <input id="liveType" type="text" class="t-select" placeholder="请选择直播类型" json-data='${liveTypeList}' selected-ids="${liveInfoParam.liveType}"/>
                    <input name="liveType" id="liveTypeIds" type="hidden" placeholder="请输入直播类型" />
                </div>
            </div>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label">选择讲师</label>
                <div class="col-md-3">
                    <input id="teacher" type="text" class="t-select" placeholder="请选择讲师" json-data='${lecturerSelect}' selected-ids="${liveInfoParam.teachIds}"/>
                    <input name="teacher" id="teacherIds" type="hidden" placeholder="请输入讲师姓名" />
                </div>
            </div>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label"><font class="required-logo">*</font>直播封面</label>
                <div class="col-xs-6">
                    <div id="livePicDiv" class="col-xs-12 no-padding"
                         <c:if test="${empty liveInfoParam.livePic}" >style="display: none;" </c:if>
                    >
                        <img id="livePicImg" src="${liveInfoParam.livePic}"/>
                        <input type="hidden" name="livePicFileId" id="livePicFileId" value="${liveInfoParam.livePic}" />
                        <form:hidden path="livePic" />
                    </div>
                    <div class="col-xs-12 no-padding controls live-pic-btn-row">
                        <a href="javascript:void(0);" id="livePicUploadBtn" class="file btn btn-warning btn-facebook btn-outline">
                            <i class="fa fa-upload"> </i> 上传图片
                            <input id="livePicFile" type="file" name="files" multiple />
                        </a>
                        <input type="button" id="livePicRemoveBtn" class="btn btn-danger" value="删除" />
                    </div>
                </div>
            </div>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label">培训课件</label>
                <div class="col-xs-6 controls">
                    <div class="col-xs-12 no-padding" id="fileDiv">
                        <c:forEach items="${liveInfoParam.fileList}" var="item" varStatus="status">
                            <div class="col-xs-12 file-content">
                                <span onclick="downloadFile('${item.attUrl}','${item.fileName}', 1)" class="file-span" title="下载"
                                      data-file-type="1" data-atta-id="${item.attId}"
                                      data-file-id="${item.attUrl}">${item.fileName}</span>
                                <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>
                            </div>
                        </c:forEach>
                    </div>
                    <div class="col-xs-12 no-padding live-file-btn-row" id="btnDiv">
                        <a href="javascript:void(0);" id="fileUploadBtn" class="file btn btn-warning btn-facebook btn-outline" id="fileUploadBtn">
                            <i class="fa fa-upload"> </i> 上传课件
                            <input id="liveFile" type="file" name="files" multiple />
                        </a>
                    </div>
                </div>
            </div>
            <c:if test="${liveInfoParam.liveId  != null}">
            <h2 class="col-xs-2 config-title">直播设置</h2>
            <div class="control-group form-group">
                <label class="col-xs-2 control-label">是否回放</label>
                <div class="col-xs-6 controls">
                    <c:if test="${liveInfoParam.ifVideo  == '1'}">
                        <input type="radio" name="ifVideo" value="1" checked/>是
                        <input type="radio" name="ifVideo" value="0" style="margin-left: 15px;"/>否
                    </c:if>
                    <c:if test="${liveInfoParam.ifVideo  != '1'}">
                        <input type="radio" name="ifVideo" value="1" />是
                        <input type="radio" name="ifVideo" value="0" checked style="margin-left: 15px;"/>否
                    </c:if>
                </div>
            </div>
                <div class="control-group form-group">
                    <label class="col-xs-2 control-label">回放地址</label>
                    <div class="col-xs-6 controls">
                        <form:input type="text" class="form-control" path="liveVideoUrl" readonly="true" autocomplete="off" />
                    </div>
                    <div class="col-xs-1 controls">
                        <label for="newUploadFile" class="btn btn-primary">点击上传</label>
                        <input id="newUploadFile" type="file" onchange ="uploadFileBefore()"  placeholder="请选择文件" style="display: none">
                    </div>
                    <c:if test="${liveInfoParam.liveVideoUrl  != ''}">
                        <div class="col-xs-1 controls">
                            <input type="button" id="generateLiveSummaryDocument" class="btn btn-primary" value="生成视频概要">
                        </div>
                    </c:if>
                </div>
            </c:if>
            <div class="control-group form-group button-group">
                <div class="col-xs-12 text-center">
                    <c:if test="${liveInfoParam.releState =='0'}">
                        <input type="button" id="publishButton" class="btn btn-primary" value="发布" />
                    </c:if>
                    <c:if test="${liveInfoParam.releState =='1'}">
                        <input type="button" id="unPublishButton" class="btn btn-primary" value="取消发布" />
                    </c:if>
                    <input type="button" id="saveButton" class="btn btn-primary" value="保存" />
                    <input type="button" id="cancelButton" class="btn btn-default" value="取消" />
                </div>
            </div>
        </form:form>
        <input type="hidden" id="approveFilesSuffix1" value="mp4;ts;mov;mxf;mpg;flv;wmv"/>
        <div style="border:1px solid #a0d7e9;background-color: #E6ECEE;border-radius: 350px;position: absolute;
                    left:50%;margin-left:-90px;margin-top:-300px;width:300px;height:20px; display: none" id="rateprogress">
            <span id="progress_num" style="font-size: 16px;color: #0086A7;  width: 33px; max-width: 33px;position: absolute;top: -3px;left: 302px;"></span>
            <div id="progress" class="progress-bar" style="background-color:#0086A7; border-radius: 350px;"></div>
        </div>
    </div>
</div>
<input type="hidden" id="uploadVideoType" value="2"/>
</body>
<script type="text/javascript">
    <%
        List<LecturerInfoDto> lecturerInfoDtoList = (List<LecturerInfoDto>) request.getAttribute("lecturerList");
        String lecturerInfoDtoListJson =  JsonUtil.toJson(lecturerInfoDtoList);
    %>
    let _teachInfoList = <%=lecturerInfoDtoListJson %>;
</script>

</html>
