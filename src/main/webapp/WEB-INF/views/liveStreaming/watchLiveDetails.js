$(document).ready(function() {

});

/**
 * 列表查询
 */

/**
 * 序号列
 * @param data
 * @param type
 * @param row
 * @param meta
 * @returns {*}
 */
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}


function sourceColumnIndex(data, type, row, meta) {
    // 0:h5  1: web  2:小程序
    if (data.watchFrom === '0') {
        return 'H5';
    } else if (data.watchFrom === '1') {
        return 'Web';
    } else if (data.watchFrom === '2') {
        return '小程序';
    } else {
        return data.watchFrom;
    }
}

function timeColumnIndex(data, type, row, meta) {
    const hours = Math.floor(data.watchTime / 3600);
    const minutes = Math.floor((data.watchTime % 3600) / 60);
    const remainingSeconds = data.watchTime % 60;

    let formattedTime = '';

    if (hours > 0) {
        formattedTime += hours + '小时';
    }

    if (minutes > 0) {
        formattedTime += minutes + '分';
    }

    formattedTime += remainingSeconds + '秒';

    return formattedTime;
}