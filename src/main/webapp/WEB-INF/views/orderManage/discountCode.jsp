<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
</head>
<body>
<div class="panel"  style="padding: 15px 15px 40px">
    <form:form modelAttribute="discountRulesInfo" id="discountCodeForm">
        <div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">生成优惠码折扣：</label>
                    <div class="col-md-3">
                        <form:input path="discount"  placeholder="请输入生成优惠码折扣"  cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">申请人：</label>
                    <div class="col-md-3">
                        <form:input path="applyUser" placeholder="请输入申请人" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div  class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">申请原因：</label>
                    <div class="col-md-11">
                        <form:textarea path="purpose" placeholder="请输入申请原因" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div  class="row">
                <div class="col-md-12">
                    <sec:authorize access="hasAuthority('RES_CREATE_DIS_CODE_AUTHORITY_3')" >
                        <span id="btnExport" class="btn btn-primary">生成优惠码</span>
                    </sec:authorize>
                </div>
            </div>
            <div  class="row">
                <div class="col-md-12">
                    <label class="col-md-2 control-label">已为您生成优惠码：</label>
                    <div class="col-md-3">
                        <span id="discountCode"></span>
                    </div>
                </div>
            </div>

           <%-- <div  class="row">
                <div class="col-md-12">
                    <label class="col-md-2 control-label">生成的优惠码：</label>
                    <div class="col-md-3">
                    </div>
                </div>
            </div>
--%>
        </div>
    </form:form>

</div>
</body>
</html>
