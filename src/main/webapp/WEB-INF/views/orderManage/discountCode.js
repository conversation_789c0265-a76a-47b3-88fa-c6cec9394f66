$(document).ready(function() {
    $("#discountCodeForm").validate({
        rules : {
            "discount" : {
                required : true,
                checkDiscount: true
            },
            "applyUser" : {
                required : true,
                maxlength : 512
            },
            "purpose" : {
                required : true,
                maxlength : 32
            }
        },
        tooltip_options : {
            "_all_" : {
                trigger : 'focus'
            }
        }
    });
    $.validator.addMethod("checkDiscount", function (value, element, params) {
        var checkDiscount = /(^([0-9]|10)(\.\d{1,2})?$)|(^10$)/;
        var blank = /^\S*$/;
        if ((value.indexOf(" ")!=-1)){
            return false
        }
        return this.optional(element) || (checkDiscount.test(value));
    }, "请输入正确的折扣！");
    //导出
    $('#btnExport').click(function () {
        if ($("#discountCodeForm").valid()) {
            ajaxData("/discountCode/getDiscountCode" , $("#discountCodeForm").serialize(),function (data) {
                $("#discountCode").html(data.discountCode)
            })
        }
    });
})
