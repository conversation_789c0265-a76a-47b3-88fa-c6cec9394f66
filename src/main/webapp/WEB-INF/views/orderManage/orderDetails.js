$(document).ready(function() {

    // 下拉初始化
    tSelectInit();

    //列表查询
    $('#btnQuery').click(function () {
        search();
    });

    //导出
    $('#btnExport').click(function () {
        window.open(contextPath + "/orderDetails/exportDetailsTable?" + $("#orderDetailDtoForm").serialize());
    });

    $('#btnClear').click(function () {
        $("#orderDetailDtoForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tSelectInit()
        search();
        dateInit();
    });

    dateInit();
})
//列表查询
function search() {
    ajaxTableQuery("table", "/orderDetails/getOrderDetailsList",$("#orderDetailDtoForm").formSerialize());
}

//清空条件
$('#btnClear').click(function () {
    document.getElementById("queryForm").reset();
    $(".t-select").each(function (n, obj) {
        $('input[name="' + obj.id + '"]').val("");
    });
    search();
});

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function seeDetail() {

}

//下拉初始化
function tSelectInit() {
    var tSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#orderTypeList').tselectInit(null, tSelectOptions);
    $('#wareTypeList').tselectInit(null, tSelectOptions);
    $('#personTypeList').tselectInit(null, tSelectOptions);
    $('#belongCommissionList').tselectInit(null, tSelectOptions);
    $('#ifFreeBuyList').tselectInit(null, tSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function dateInit() {
    var timeStr = $('#timeStr').val();
    if (timeStr.trim() != '') {
        var timeStr = timeStr.split(' 至 ');
        dataRangePickerInit($('#timeStr'), timeStr[0], timeStr[1], function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    } else {
        dataRangePickerInit($('#timeStr'), null, null, function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    }
}
