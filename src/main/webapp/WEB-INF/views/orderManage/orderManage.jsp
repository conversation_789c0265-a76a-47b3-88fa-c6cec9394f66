<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="panel"  style="padding: 15px 15px 40px">
        <form:form modelAttribute="orderDto" id="orderDtoForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">订单ID：</label>
                    <div class="col-md-3">
                        <form:input path="orderId" placeholder="请输入订单ID" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">账单ID：</label>
                    <div class="col-md-3">
                        <form:input path="billNo" placeholder="请输入账单ID" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">订单状态</label>
                    <div class="col-md-3">
                        <input id="orderStatusList" type="text" class="t-select" json-data='${orderStatusList}'  placeholder="请选择订单状态" />
                        <input name="orderStatusList" type="hidden" />
                    </div>


                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">订单类型</label>
                    <div class="col-md-3">
                        <input id="orderTypeList" type="text" class="t-select" json-data='${orderTypeList}' placeholder="请选择订单类型" />
                        <input name="orderTypeList" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">手机号:</label>
                    <div class="col-md-3">
                        <form:input path="createUserPhone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">公司代码:</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">订单发起人:</label>
                    <div class="col-md-3">
                        <form:input path="createUserName" placeholder="请输入订单发起人" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">购买时间</label>
                    <div class="col-md-3 daterange">
                        <form:input path="createTime" placeholder="购买时间范围" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">订单来源</label>
                    <div class="col-md-3">
                        <form:select path="dataSources" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="web">web</form:option>
                            <form:option value="MINI">MINI</form:option>
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row" style="margin-bottom: 8px;margin-top:20px;float: right;">
                <sec:authorize access="hasAuthority('RES_EXPORT_ORDER_AUTHORITY_3')" >
                    <span id="btnExport" class="btn btn-primary">导出</span>
                </sec:authorize>
                <sec:authorize access="hasAuthority('RES_ORDER_QUERY_AUTHORITY_3')" >
                    <span id="btnQuery" class="btn btn-primary">查询</span>
                </sec:authorize>
                <span id="btnClear" class="btn btn-default">清空条件</span>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_ORDER_INFO_VIEW_AUTHORITY_3')" >
            <%--查看详情权限--%>
            <input type="hidden" id="detailAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_ORDER_REFUND_AUTHORITY_3')" >
            <%--退款权限--%>
            <input type="hidden" id="refundAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_INVOICE_REVERSE_AUTHORITY_3')" >
            <%--红冲发票权限--%>
            <input type="hidden" id="reverseAuth" value="true">
        </sec:authorize>
        <div class="row">
            <e:grid id="table" action="/orderManage/orderQuery" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:2%"/>
                <e:gridColumn label="订单id/账单id" renderColumn="orderAndBillId" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="发票号码/发票代码" renderColumn="invoiceNoAndinvoiceCode" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="商品名称" displayColumn="wareName" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="订单状态" displayColumn="orderStatusStr" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="订单来源" displayColumn="dataSources" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="发票状态" displayColumn="invoiceStatusStr" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="订单类型" displayColumn="orderTypeStr" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="订单发起姓名" displayColumn="createUserName" orderable="false"
                              cssClass="text-center" cssStyle="width:5%"/>
                <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="公司代码" displayColumn="companyCode"  orderable="false"
                              cssClass="text-center" cssStyle="width:5%;"/>
                <e:gridColumn label="公司名称" displayColumn="companyName"  orderable="false"
                              cssClass="text-center" cssStyle="width:10%;"/>
                <e:gridColumn label="订单总金额" displayColumn="totalAmount" orderable="false"
                              cssClass="text-center" cssStyle="width:5%"/>
                <e:gridColumn label="创建时间" displayColumn="createTime"  orderable="false"
                              cssClass="text-center" cssStyle="width:6%;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                              cssClass="text-center" cssStyle="width:6%;"/>
            </e:grid>
        </div>
    <%--课程附件模版--%>
</div>
</body>
</html>
