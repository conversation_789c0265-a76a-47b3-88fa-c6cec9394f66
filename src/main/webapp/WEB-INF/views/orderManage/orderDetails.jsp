<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="panel"  style="padding: 15px 15px 22px">
        <form:form modelAttribute="orderDetailDto" id="orderDetailDtoForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">商品名称</label>
                    <div class="col-md-3">
                        <form:input path="wareName" placeholder="请输入商品名称" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">手机号</label>
                    <div class="col-md-3">
                        <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off" />
                    </div>

                    <label class="col-md-1 control-label">购买人</label>
                    <div class="col-md-3">
                        <form:input path="createUser" placeholder="请输入购买人" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">购买时间</label>
                    <div class="col-md-3 daterange">
                        <form:input path="timeStr" placeholder="请选择购买时间" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">订单类型</label>
                    <div class="col-md-3">
                        <input id="orderTypeList" type="text" class="t-select" json-data='${orderTypeList}' placeholder="请选择订单类型" />
                        <input name="orderTypeList" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">讲师</label>
                    <div class="col-md-3">
                        <form:input path="teacher" placeholder="请输入讲师" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">用户类别</label>
                    <div class="col-md-3">
                        <input id="personTypeList" type="text" class="t-select" json-data='${personTypeList}' placeholder="请选择用户类别" />
                        <input name="personTypeList" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">所属辖区</label>
                    <div class="col-md-3">
                        <input id="belongCommissionList" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择所属辖区" />
                        <input name="belongCommissionList" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">公司代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">0元购类型</label>
                    <div class="col-md-3">
                        <input id="ifFreeBuyList" type="text" class="t-select" json-data='${ifFreeBuyList}' placeholder="请选择" />
                        <input name="ifFreeBuyList" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">商品类型</label>
                    <div class="col-md-3">
                        <input id="wareTypeList" type="text" class="t-select" json-data='${wareTypeList}' placeholder="请选择商品类型" />
                        <input name="wareTypeList" type="hidden" />
                    </div>
                    <div class="col-md-4">
                        <div style="float: right;padding-top: 10px">
                            <sec:authorize access="hasAuthority('RES_ORDER_DETAILS_EXPORT_AUTHORITY_3')" >
                                <span id="btnExport" class="btn btn-primary">条件下导出</span>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_ORDER_DETAILS_QUERY_AUTHORITY_3')" >
                                <span id="btnQuery" class="btn btn-primary">查询</span>
                            </sec:authorize>
                            <span id="btnClear" class="btn btn-default">清空条件</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </form:form>
        <div>
            <e:grid id="table" action="/orderDetails/getOrderDetailsList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:2%"/>
                <e:gridColumn label="商品名称" displayColumn="wareName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="商品类型" displayColumn="wareType" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="订单类型" displayColumn="orderType" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="金额" displayColumn="price" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="0元购类型" displayColumn="zeroType" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="讲师" displayColumn="teacher" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="购买时间" displayColumn="timeStr"  orderable="false"
                              cssClass="text-center" cssStyle="width:10%;"/>
                <e:gridColumn label="购买人" displayColumn="createUser" orderable="false"
                              cssClass="text-center" cssStyle="width:5%"/>
                <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="用户类别" displayColumn="personType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="所属辖区" displayColumn="belongCommissionStr"  orderable="false"
                              cssClass="text-center" cssStyle="width:5%;"/>
                <e:gridColumn label="公司代码" displayColumn="companyCode"  orderable="false"
                              cssClass="text-center" cssStyle="width:5%;"/>
            </e:grid>
        </div>
</div>
</body>
</html>
