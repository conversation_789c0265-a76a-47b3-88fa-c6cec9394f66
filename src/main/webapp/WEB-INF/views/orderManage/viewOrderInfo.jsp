<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="col-md-12" style="margin: 10px 0">
    <div class="col-xs-12">
        <c:if test="${orderDetailList != null}">
            <table class="table table-bordered no-margin" style="text-align: center;float:left;" id="configCourseType">
                <thead>
                <tr>
                    <td width="16%" class="sch-td">商品名称</td>
                    <td width="8%" class="sch-td">商品类型</td>
                    <td width="8%" class="sch-td">商品价格</td>
                    <td width="10%" class="sch-td">订单创建时间</td>
                    <td width="8%" class="sch-td">订单发起人</td>
                    <td width="8%" class="sch-td">订单状态</td>
                    <td width="10%" class="sch-td">退款金额</td>
                    <td width="10%" class="sch-td">退款时间</td>
                </tr>
                </thead>
                <tbody id="freeTableId">
                <c:forEach items="${orderDetailList}" var="item" varStatus="status">
                    <tr>
                        <td>${item.wareName}</td>
                        <td>${item.wareType}</td>
                        <td>${item.warePrice}</td>
                        <td>${item.createTime}</td>
                        <td>${item.createUser}</td>
                        <td>${item.orderStatusStr}</td>
                        <td>
                            <c:if test="${item.orderStatus == 'REFUND'}">
                                ${item.warePrice}
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${item.orderStatus == 'REFUND'}">
                                ${item.refundTime}
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </c:if>
    </div>
</div>
</body>
</html>
