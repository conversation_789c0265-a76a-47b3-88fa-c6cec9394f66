$(document).ready(function() {

    // 下拉初始化
    tSelectInit();
    // 日期初始化
    dateInit();
    //列表查询
    $('#btnQuery').click(function () {
        search();
    });

    //导出
    $('#btnExport').click(function () {
        window.open(contextPath + "/orderManage/exportOrderTable?" + $("#orderDtoForm").serialize());
    });

    $('#btnClear').click(function () {
        $("#orderDtoForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tSelectInit()
        search();
    });

})
//列表查询
function search() {
    ajaxTableQuery("table", "/orderManage/orderQuery",$("#orderDtoForm").formSerialize());
}

//清空条件
$('#btnClear').click(function () {
    document.getElementById("queryForm").reset();
    $(".t-select").each(function (n, obj) {
        $('input[name="' + obj.id + '"]').val("");
    });
    search();
});

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function orderAndBillId(data, type, row, meta){
    var html =
        '<div>'+data.orderId+'</div>' +
        '<div>'+data.billNo+'</div>'
    return html
}

function invoiceNoAndinvoiceCode(data, type, row, meta){
    var html = ''
    if (data.invoiceNo) {
        html += '<div>'+data.invoiceNo+'</div>'
    }
    if (data.invoiceCode) {
        html += '<div>'+data.invoiceCode+'</div>'
    }
    return html
}

function createTime(data, type, row, meta) {
    if (data.createTime === '' || data.createTime === null) {
        return "- -";
    } else {
        return data.createTime;
    }
}
//日期初始化
function dateInit() {
    dataRangePickerInit($('#createTime'), null, null, function () {
    }, function () {
    });
}
// 操作
function renderColumnOperation(data, type, row, meta) {
    var str = '';

    if (document.getElementById("detailAuth")) {
        str += '<a href="#" onclick="seeDetail(\'' + data.orderId + '\')" style="margin-right:4px;">查看详情</a>';
    }
    if (data.orderStatus =='PAID' ){//已支付且未开票
        if (document.getElementById("refundAuth")) {
            str += '<a href="#" onclick="orderRefund(\'' + data.orderId + '\',\'' + data.dataSources + '\')" style="margin-right:4px;display: none">退款</a>';
        }

        if (document.getElementById("reverseAuth")) {
            str += '<a href="#" onclick="invoiceReverse(\'' + data.billNo + '\')" style="margin-right:4px;display: none">红冲发票</a>';
        }
    }

    if(!str){
        str += '-';
    }
    return str;
}

function seeDetail() {

}
function orderRefund(orderId,dataSources) {
    popConfirm("确认退款?", function () {
        var param = {
            orderId : orderId,
            dataSources : dataSources
        }
        ajaxData("/orderManage/orderRefund",param,function (data) {
            popMsg("退款已提交，请稍后刷新列表查看退款进度")
            search();
        })
    });
}
function invoiceReverse(billNo) {
    popConfirm("确认红冲发票?", function () {
        var param = {
            billNo : billNo,
        }
        ajaxData("/orderManage/invoiceReverse",param,function (data) {
            popMsg("红冲发票申请已提交，请稍后刷新列表查看进度")
            search();
        })
    });
}

//下拉初始化
function tSelectInit() {
    var tSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#orderStatusList').tselectInit(null, tSelectOptions);
    $('#orderTypeList').tselectInit(null, tSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

//详情
function seeDetail(id){
    var param = {
        orderId: id,
    };
    parent.popWin('订单详情', '/orderManage/viewOrderInfo', param, '98%', '98%', callBackViewOrder, '', callBackViewOrder);
}
function callBackViewOrder(){
    ajaxTableReload("table", false);
}
