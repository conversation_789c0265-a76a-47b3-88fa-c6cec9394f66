//@ sourceURL=classificationInit.js
$(document).ready(function () {
    $("#btnAdd").bind("click", function () {
        addTagRow();
    });

    $("#btnSave").bind("click", function () {
        ajaxSubmitForm("/trainingDynamics/saveClassificationName?noticeType=" + '2' + "&codeType=" + '3', "", function (data) {
            $('#courseDataBody').empty(tbody);
            for (var i = 0; i < data.length; i++) {
                var tbody = "";
                tbody = '<tr><td style="text-align: center;">' + (i + 1) + '</td>' +
                    '<td><input type="hidden" name="classificationList[' + i + '].id" value="' + data[i].id + '">' +
                    '<input type="hidden" name="classificationList[' + i + '].codeValue" value="' + data[i].codeValue + '">' +
                    '<input style="width: 100%;" name="classificationList[' + i + '].codeName" value="' + data[i].codeName + '"></td>' +
                    '</tr>'
                $('#courseDataBody').append(tbody)
            }
            popMsg("保存成功")
        })
    });
})

function addTagRow() {
    var len = $('#courseDataBody').find("tr").length
    var row = ""
    row += "<tr>\n" +
        "<td style=\"text-align: center;\">" + (len + 1) + "</td>\n" +
        "<td><input type=\"hidden\" name=\"classificationList[" + len + "].codeValue\" value=\"" + len + "\">" +
        "<input style=\"width: 100%;\" name=\"classificationList[" + len + "].codeName\"></td>\n" +
        "</tr>"
    $('#courseDataBody').append(row)
}

function tableSave() {

}