<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/9/26
  Time: 11:49
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            text-align: center;
            margin-top: 6px;
        }

        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">培训通知</i>
    </div>
    <div class="panel-body">
        <form:form modelAttribute="trainingDynamicsDto" id="superviseInfoForm">
            <div class="row">
                <label class=" col-md-1 control-label">通知分类:</label>
                <div class="col-md-3">
                    <form:select path="subType" id="subType" name="subType" class="form-control">
                        <form:option value="">全部</form:option>
                        <c:forEach var="property" items="${notificationTypeList}" varStatus="index">
                            <c:if test="${property.type =='3'}">
                                <form:option value="${property.typeValue}">${property.typeName}</form:option>
                            </c:if>
                        </c:forEach>
                    </form:select>
                </div>
                <label class=" col-md-1 control-label">标题:</label>
                <div class="col-md-3">
                    <form:input path="title" placeholder="请输入文本" cssClass="form-control" autocomplete="off" maxlength="128"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
                </div>
                <label class=" col-md-1 control-label">是否发布:</label>
                <div class="col-md-3">
                    <form:select path="releaseFlg" id="releaseFlg" name="releaseFlg" class="form-control">
                        <form:option value="">全部</form:option>
                        <c:forEach var="property" items="${whetherTypeList}" varStatus="index">
                            <form:option value="${property.codeValue}">${property.codeName}</form:option>
                        </c:forEach>
                    </form:select>
                </div>
            </div>
            <div class="row" style="margin-bottom: 8px;margin-top:20px;float: right;">
                <sec:authorize access="hasAuthority('RES_CAPCO_GET_TD_INIT_AUTHORITY_3')" >
                    <span id="btnAdd" class="btn btn-primary">新增</span>
                </sec:authorize>
                <sec:authorize access="hasAuthority('RES_CAPCO_GET_QT_LIST_AUTHORITY_3')" >
                    <span id="btnQuery" class="btn btn-primary">查询</span>
                </sec:authorize>
                <span id="btnClear" class="btn btn-default">清空条件</span>
                <sec:authorize access="hasAuthority('RES_SCH_ORG_INIT_AUTHORITY_3')" >
                    <span id="btnOrg" class="btn btn-primary">维护通知分类</span>
                </sec:authorize>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_CAPCO_GET_TD_INIT_AUTHORITY_3')" >
            <%--编辑权限--%>
            <input type="hidden" id="editAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_CAPCO_GET_TD_DEL_AUTHORITY_3')" >
            <%--删除权限--%>
            <input type="hidden" id="delAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_CAPCO_GET_TD_SAVE_AUTHORITY_3')" >
            <%--发布/置顶权限--%>
            <input type="hidden" id="saveAuth" value="true">
        </sec:authorize>
            <e:grid id="table1" action="/trainingDynamics/queryTrainingDynamicsInfoList?noticeType=3&type=3"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:3%"/>
                <e:gridColumn label="通知类型" displayColumn="typeName" renderColumn="typeName" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="标题" displayColumn="title" renderColumn="title" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="时间" displayColumn="trainDate" orderable="false" renderColumn="trainDate"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="来源" displayColumn="source" orderable="false" renderColumn="source"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="是否发布" displayColumn="releaseName" renderColumn="releaseName" orderable="false"
                              cssClass="text-center" cssStyle="width:4%;"/>
                <e:gridColumn label="创建人" displayColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%;"/>
                <e:gridColumn label="创建时间" displayColumn="createTime" orderable="false"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="发布时间" displayColumn="releaseTime" orderable="false"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                              cssClass="text-center" cssStyle="width:12%;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
