<%--
  Created by IntelliJ IDEA.
  User: Zou
  Date: 2020/3/16
  Time: 17:46
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            text-align: center;
            margin-top: 6px;
        }

        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div class="row" style="margin-bottom: 8px;float: right;">
            <span id="btnAdd" class="btn btn-primary">新增</span>
            <span id="btnSave" class="btn btn-primary">保存</span>
        </div>
        <div class="row">
            <form:form id="trainingDynamicsDtoForm" modelAttribute="trainingDynamicsDto">
                <table id="courseData" class="table table-bordered no-margin"
                       style="border-color: #D7D7D7;margin-top: 10px !important;">
                    <thead>
                    <th class="text-center table-th" width="10%">序号</th>
                    <th class="text-center table-th">分类名称</th>
                    </thead>
                    <tbody id="courseDataBody">
                    <c:forEach items="${classificationList}" var="item" varStatus="status">
                        <tr>
                            <td style="text-align: center;"> ${status.index+1}</td>
                            <td><input type="hidden" value="${item.id}" name="classificationList[${status.index}].id"
                                       id="classificationList[${status.index}].id">
                                <input type="hidden" value="${item.codeValue}"
                                       name="classificationList[${status.index}].codeValue"
                                       id="classificationList[${status.index}].codeValue">
                                <input style="width: 100%;" value="${item.codeName}"
                                       name="classificationList[${status.index}].codeName"
                                       id="classificationList[${status.index}].codeName"></td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </form:form>

        </div>

    </div>
</div>
</body>
</html>

