//@ sourceURL=insertTeacherManagementInit.js
let _fileUploadUrl = contextPath + '/filetempupload';
$(document).ready(function () {

    if($("#releaseFlg").val() == '1'){
        $("#not_save").show();
        $("#save").hide();
    }
    if($("#releaseFlg").val() == '0'){
        $("#not_save").hide();
        $("#save").show();
    }
    if($("#id").val() == ''|| $("#id").val() == null){//新增
        $("#not_save").hide();
        $("#save").show();
    }
    // 富文本初始化
    ueEditorInit();
    miniEditorInit();
    // 取消
    $('#btnCancel').bind('click', function () {
        closeWinCallBack();
    });
    $('#btnCancelSave').bind('click', function () {
        closeWinCallBack();
    });

    // 取消发布
    $('#releaseClick').bind('click', function () {
        releaseClick();
    });
    // 保存
    $("#btnSave").bind("click", function () {

        var releaseFlg = '';
        if ($('#releaseFlg').val() != null && $('#releaseFlg').val() != undefined && $('#releaseFlg').val() != '') {
            releaseFlg = $('#releaseFlg').val();
        } else {
            releaseFlg = '0'
        }
        let flag = true;
        if (!$('#subType').val()) {
            subType = '-'
            popMsg("通知类型不能为空")
            flag = false
        }
        if (!$('#title').val()) {
            popMsg("标题不能为空")
            flag = false
        }
        let files = [];
        let oldFiles = [];
        $.each($('.file-span'), function(index, fileItem) {
            let fileType = $(fileItem).data('file-type');
            let attId = $(fileItem).data('atta-id');
            let tempId = $(fileItem).data('file-id');
            if (getValue(attId) != '') {
                oldFiles.push({
                    id: attId
                });
            }else {
                files.push({
                    dataType: fileType,
                    attachmentId: attId,
                    tempId: tempId
                })
            }
        });
        if (flag) {
            var param = {
                id: $("#id").val(),
                title: $("#title").val(),
                trainDate: $("#trainDate").val(),
                content: $('textarea[name="content"]').val(),
                miniContent: $('textarea[name="miniContent"]').val(),
                subType: $("#subType").val(),
                releaseTime: $("#releaseTime").val(),
                source: $("#source").val(),
                noticeType: $("#noticeType").val(),
                releaseFlg: releaseFlg,
                attStr:JSON.stringify(files) ,//新上传的附件
                oldAttStr:JSON.stringify(oldFiles) ,//原来的附件
                isRelease : '1'//表示操作附件
            };
            popConfirm("确认保存？", function () {
                ajaxData("/trainingDynamics/saveTrainingDynamicsInfo", param, function (data) {
                    popMsg("保存成功");
                    closeWinCallBack(data);
                });
            })
        }
    })

    // 保存并发布
    $("#btnSaveRelease").bind("click", function () {
        let flag = true;
        if (!$('#title').val()) {
            popMsg("标题不能为空")
            flag = false
        }
        if (!$('#subType').val()) {
            popMsg("通知类型不能为空")
            flag = false
        }
        let files = [];
        let oldFiles = [];
        $.each($('.file-span'), function(index, fileItem) {
            let fileType = $(fileItem).data('file-type');
            let attId = $(fileItem).data('atta-id');
            let tempId = $(fileItem).data('file-id');
            if (getValue(attId) != '') {
                oldFiles.push({
                    id: attId
                });
            }else {
                files.push({
                    dataType: fileType,
                    attachmentId: attId,
                    tempId: tempId
                })
            }
        });
        if (flag) {
            var param = {
                id: $("#id").val(),
                title: $("#title").val(),
                trainDate: $("#trainDate").val(),
                content: $('textarea[name="content"]').val(),
                miniContent: $('textarea[name="miniContent"]').val(),
                subType: $("#subType").val(),
                source: $("#source").val(),
                releaseTime: $("#releaseTime").val(),
                noticeType: $("#noticeType").val(),
                releaseFlg: "1",
                attStr:JSON.stringify(files) ,//新上传的附件
                oldAttStr:JSON.stringify(oldFiles) ,//原来的附件
                isRelease : '1'//表示操作附件
            };
            popConfirm("确认保存并发布？", function () {
                ajaxData("/trainingDynamics/saveTrainingDynamicsInfo", param, function (data) {
                    popMsg("保存成功");
                    closeWinCallBack(data);
                });
            })
        }
    })
    //上传附件
    modelUpdate("fileupload");


})

/**
 * 富文本初始化
 */
function ueEditorInit() {
    UE.delEditor('content');
    um = UE.getEditor('content', {
        initialFrameHeight: 400,
        textarea: "content",
        elementPathEnabled: false,
        autoHeightEnabled: false
    });
}
function miniEditorInit() {
    UE.delEditor('miniContent');
    um = UE.getEditor('miniContent', {
        initialFrameHeight: 400,
        textarea: "miniContent",
        elementPathEnabled: false,
        autoHeightEnabled: false
    });
}

//发布、取消发布
function releaseClick() {
    var releaseFlg =  $("#releaseFlg").val();
    if (releaseFlg == 1) {
        popConfirm("确认取消发布？", function () {
            var param = {
                id:$("#id").val(),
                releaseFlg: 0
            };
            ajaxData("/trainingDynamics/saveTrainingDynamicsInfo", param, function (data) {
                if (data.length > 0) {
                    popMsg("取消发布成功");
                    $("#releaseFlg").val('0');
                    $("#not_save").hide();
                    $("#save").show();
                    // closeWinCallBack(data);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}
/**
 * 课件上传
 */
function modelUpdate(fileUploadId) {
    $("#" + fileUploadId).fileupload({
        url: _fileUploadUrl,
        dataType: 'text',
        autoUpload: true,
        add: function(e, data) {
            // 上传文件时校验文件格式
            data.submit();
        },
        submit: function(e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
            });
        },
        done: function(e, data) {
            var num = 1
            $.each($('.file-span'), function(index, fileItem) {
                num ++
            })
            num = num+'、'
            layer.close(index);
            $.each($.parseJSON(data.result), function(index, file) {
                let divStr =
                    ' <div class="col-xs-12 file-content">' +
                    '<span>'+num+'</span>' +
                    ' <span onclick="downFile()" class="file-span" title="下载" data-file-type="0" data-atta-id="" data-file-id="' + file.fileRelaId + '">' + file.fileName + '</span>' +
                    ' <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>' +
                    ' </div>';
                $("#fileDiv").append(divStr);
            })
        },
        fail: function(e, data) {
            popMsg("上传附件失败！");
        }
    });
}
/**
 * 删除课件
 * @param item
 */
function removeFile(item) {
    // 移除页面元素
    $(item).parent().remove();
}
$.fn
    .extend({
        uploadPreview : function(opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img : "ImgPr",
                Width : 100,
                Height : 100,
                ImgType : [ "gif", "jpeg", "jpg", "bmp", "png" ],
                Callback : function() {
                }
            }, opts || {});
            _self.getObjectURL = function(file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }
                if ($("#teachPic").val() != "") {
                    $(
                        '<input type="hidden" name="delFileId" value="'
                        + $("#teachPic").val() + '"/>').appendTo(
                        '#delFileIdList');
                    $("#teachPic").val("");
                }

                $("#imgId").css("display", "block");
                return url
            };

            _this.change(function() {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter' : 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width' : opts.Width
                                            + 'px',
                                        'height' : opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })
