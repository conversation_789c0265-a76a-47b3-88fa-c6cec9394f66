//1：互动交流，2：常见问题，3：培训通知 4:精彩回顾
let type = '4'
$(document).ready(function () {
    //查询
    $("#btnQuery").bind("click", function () {
        let param = {
            subType: $("#subType").val(),
            title: $("#title").val(),
            releaseFlg: $("#releaseFlg").val(),
            noticeType: type,
            type: type
        }

        ajaxTableQuery("table1", "/trainingDynamics/queryTrainInfoList", param);
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("superviseInfoForm").reset();
        let param = {
            noticeType: type,
            type: type
        }
        ajaxTableQuery("table1", "/trainingDynamics/queryTrainInfoList", param);
    });
    //新增
    $("#btnAdd").bind("click", function () {
        let param = {
            pageFlg: '0',
            noticeType: type,
            type: type
        }
        popWin("精彩回顾新增", "/trainingDynamics/trainingDynamicsInit", param, "90%", "90%", winCallback, "", winCallback);
    });
//维护精彩回顾分类
    $("#btnOrg").bind("click", function () {
        let param = {
            type: type
        }
        popWin("维护精彩回顾分类", "/ebSchoolLecturerInfo/orgInit", param, "900px", "600px", winCallback, "", orgCallback);
    });
});

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
//置顶
function topFlg(id,topFlg){
    let param = {
        id: id,
        topFlg: topFlg === '1'?'0':'1'
    };
    ajaxData("/trainingDynamics/saveTrainingDynamicsInfo", param, function (data) {
        if (data.length > 0) {
            popMsg("操作成功");
            ajaxTableReload("table1", false);
        } else {
            popMsg("操作失败，请稍后再试");
        }
    });
}

// 操作
function renderColumnOperation(data, type, row, meta) {
    var str1 = '';
    var str2 = '';
    var str3 = '';
    var str4 = '';

    if (document.getElementById("editAuth")) {
        str1 = '<a href="#" onClick="infoEdit(\'' + data.id + '\')" style="margin-right:4px;">编辑</a>';
    }

    if (document.getElementById("delAuth")) {
        str2 = '<a href="#" onClick="infoDel(\'' + data.id + '\')" style="margin-right:4px;">删除</a>';
    }

    if (document.getElementById("saveAuth")) {
        if (data.releaseFlg === "0") {
            str3 = '<a href="#" onClick="releaseInfo(\'' + data.id + '\',\'' + data.releaseFlg + '\')" style="margin-right:4px;">发布</a>';
        } else if (data.releaseFlg === "1") {
            str3 = '<a href="#" onClick="releaseInfo(\'' + data.id + '\',\'' + data.releaseFlg + '\')" style="margin-right:4px;">取消发布</a>';
        }
        str4 = '<a href="#" onClick="topFlg(\'' + data.id + '\',\'' + data.topFlg + '\')" style="margin-right:4px;">置顶</a>';
        if (data.topFlg === "1") {
            str4 = '<a href="#" onClick="topFlg(\'' + data.id + '\',\'' + data.topFlg + '\')" style="margin-right:4px;">取消置顶</a>';
        }
    }

    if (str1 + str2 + str3 + str4 === '') {
        return '-';
    }else {
        return str1 + " | " + str2 + " | " + str3 + " | " + str4;
    }
}

//发布
function releaseName(data) {
    let str = data.releaseName;
    if (!str) {
        return '-';
    }
    return str;
}

//标题
function title(data) {
    let str = data.title;
    if (!str) {
        return '-';
    }
    return str;
}

//通知类型
function typeName(data) {
    let str = data.typeName;
    if (!str) {
        return '-';
    }
    return str;
}

//来源
function source(data) {
    let str = data.source;
    if (!str) {
        return '-';
    }
    return str;
}

//时间
function trainDate(data) {
    let str = data.trainDate;
    if (!str) {
        return '-';
    }
    return str;
}

//创建人
function realName(data) {
    let str = data.realName;
    if (!str) {
        return '-';
    }
    return str;
}

function infoEdit(id) {
    let param = {
        id: id,
        type: type,
        noticeType: type,
        pageFlg: "0"
    }
    popWin("培训通知编辑", "/trainingDynamics/trainingDynamicsInit", param, "90%", "90%", winCallback, "", winCallback);
}

//删除
function infoDel(id) {
    popConfirm("确认删除该培训动态吗？", function () {
        let param = {
            id: id
        };
        ajaxData("/trainingDynamics/trainingDynamicsInfoDel", param, function (data) {
            if (data > 0) {
                ajaxTableReload("table1", false);
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}

//发布、取消发布
function releaseInfo(id, releaseFlg) {
    if (releaseFlg === '1') {
        popConfirm("确认取消发布吗？", function () {
            let param = {
                id: id,
                releaseFlg: 0
            };
            ajaxData("/trainingDynamics/saveTrainingDynamicsInfo", param, function (data) {
                if (data.length > 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1", false);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    } else {
        popConfirm("确认发布吗？", function () {
            let param = {
                id: id,
                releaseFlg: 1
            };
            ajaxData("/trainingDynamics/saveTrainingDynamicsInfo", param, function (data) {
                if (data.length > 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1", false);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}


function winCallback() {
    ajaxTableReload("table1", false);
}

//?
function orgCallback() {
    ajaxData("/trainingDynamics/saveClassificationList?type=" + '4', "", function (data) {
        $('#subType').empty()
        let option = '<option value="">全部</option>'
        for (let i = 0; i < data.length; i++) {
            option += '<option value="' + data[i].typeValue + '">' + data[i].typeName + '</option>'
        }
        $('#subType').append(option)
    });
}