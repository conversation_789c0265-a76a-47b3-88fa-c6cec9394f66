<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="winningRecordForm" modelAttribute="trainingDynamicsDto" cssClass="form-horizontal">
            <form:hidden path="id" value="${trainingDynamicsDto.id}"/>
            <form:hidden path="releaseFlg" value="${trainingDynamicsDto.releaseFlg}"/>
            <form:hidden path="noticeType" value="${trainingDynamicsDto.noticeType}"/>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">通知类型</label>
                <div class="col-md-10 no-padding">
                    <form:select path="subType" id="subType" name="subType" class="form-control">
                        <form:option value="">请选择分类</form:option>
                        <c:forEach var="property" items="${notificationTypeList}" varStatus="index">
                            <form:option value="${property.typeValue}"
                            >${property.typeName}</form:option>
                        </c:forEach>
                    </form:select>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">标题</label>
                <div class="col-md-10 no-padding" >
                    <form:input path="title" cssClass="form-control" placeholder="请输入标题" onkeyup="this.value=this.value.replace(/^ +| +$/g,'')"
                                maxlength="128"/>
                </div>
            </div>
            <c:if test="${trainingDynamicsDto.pageFlg == 0}">
                <div class="col-md-12" style="display: flex">
                    <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">时间</label>
                    <div class="col-md-10 no-padding" >
                        <form:input path="trainDate" cssClass="form-control" placeholder="请输入时间" maxlength="128"/>
                    </div>
                </div>
                <div class="col-md-12" style="display: flex">
                    <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">来源</label>
                    <div class="col-md-10 no-padding" >
                        <form:input path="source" cssClass="form-control" placeholder="请输入来源" maxlength="128"/>
                    </div>
                </div>
            </c:if>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">发布时间</label>
                <div class="col-md-10 no-padding" >
                    <e:date cssClass="form-control" path="releaseTime" format="yyyy-mm-dd"  disabled="disabled"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">web正文</label>
                <div class="col-md-10 no-padding" >
                    <script id="content" type="text/plain">${trainingDynamicsDto.content}</script>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">h5正文</label>
                <div class="col-md-10 no-padding" >
                    <script id="miniContent" type="text/plain">${trainingDynamicsDto.miniContent}</script>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">附件:</label>
                <div class="col-md-10 no-padding" style="margin-top: 5px">
                        <span class="btn btn-sm btn-success fileinput-button">
                            <span>上传</span>
                            <input id="fileupload" type="file" name="files" multiple>
                        </span>
                </div>
            </div>
            <div class="form-group" style="margin-left:91px; margin-top: 2px" id="fileDiv">
                <c:forEach items="${trainingDynamicsDto.attList}" var="item" varStatus="status">
                    <div class="col-md-12">
                        <span >${status.index + 1}、</span>
                        <span onclick="downloadFile('${item.id}')" style="cursor: pointer;"class="file-span"  data-file-type="1" data-atta-id="${item.id}"
                              data-file-id="${item.attUrl}">${item.attName}</span>
                        <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>
                    </div>
                </c:forEach>
            </div>
            <div class="row" style="margin-top: 20px;text-align: center;">
                <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存"/>
                 <div id="not_save" style="display: inline-block">
                        <input type="button"  style="margin-right: 15px;" class="btn btn-primary" id="releaseClick" value="取消发布" />
                        <input type="button" id="btnCancelSave" class="btn btn-default" value="取消"/>
                 </div>
                 <div id="save" style="display: inline-block">
                      <input type="button" id="btnSaveRelease" style="margin-right: 15px;" class="btn btn-primary"  value="保存并发布"/>
                      <input type="button" id="btnCancel" class="btn btn-default" value="取消"/>
                 </div>
            </div>

        </form:form>
    </div>
</div>
</body>
</html>
