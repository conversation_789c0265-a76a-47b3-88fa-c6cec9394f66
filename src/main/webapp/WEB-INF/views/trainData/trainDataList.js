$(document).ready(function () {
//查询
    $("#btnQuery").bind("click", function () {
        var param = {
            dataType: $("#dataType").val(),
            title: $("#title").val(),
            releaseFlag: $("#releaseFlag").val(),
        }

        ajaxTableQuery("table1", "/dataConfig/trainDataInfoList", param);
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("superviseInfoForm").reset();
        var param = {
            type: ''
        }
        ajaxTableQuery("table1", "/dataConfig/trainDataInfoList",param);
    });
    //新增
    $("#btnAdd").bind("click", function () {
        var param = {
            type: "4"
        }
        parent.popWin("资料新增", "/dataConfig/trainDataAdd", param, "80%", "80%", winCallback, "",winCallback);
    });
    //维护问题分类
    $("#btnOrg").bind("click", function () {
        var param = {
            type: '4'
        }
        parent.popWin("维护资料分类", "/ebSchoolLecturerInfo/orgInit", param, "900px", "600px", winCallback, "", orgCallback);
    });
});

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function winCallback(paraWin, paraCallBack) {
    ajaxTableReload("table1", false);
}

function orgCallback() {
    ajaxData("/trainingDynamics/saveClassificationList?type=" + '4', "", function (data) {
        $('#dataType').empty()
        var option = '<option value="">全部</option>'
        for (var i = 0; i < data.length; i++) {
            option += '<option value="' + data[i].typeValue + '">' + data[i].typeName + '</option>'
        }
        $('#dataType').append(option)
    });
}

// 操作
function renderColumnOperation(data, type, row, meta) {
    let content = '';

    if (document.getElementById("editAuth")) {
        content += '<a href="#" onClick="infoEdit(\'' + data.id + '\')" style="margin-right:4px;">编辑</a>';
    }

    if (document.getElementById("updateAuth")) {
        content += ' | ';
        content += '<a href="#" onClick="infoDel(\'' + data.id+'\')" style="margin-right:4px;">删除</a>';
        if (data.releaseFlagType == "0") {
            content += ' | ';
            content += '<a href="#" onClick="releaseInfo(\'' + data.id + '\',\'' + data.releaseFlagType + '\')" style="margin-right:4px;">取消发布</a>';
        }else {
            content += ' | ';
            content += '<a href="#" onClick="releaseInfo(\'' + data.id + '\',\'' + data.releaseFlagType + '\')" style="margin-right:4px;">发布</a>';
        }
    }

    if (!content) {
        content += '-';
    }
    return content;
}
function infoEdit(id) {
    var param = {
        id:id,
    }
    parent.popWin("编辑资料信息", "/dataConfig/trainDataAdd", param, "80%", "80%", winCallback, "",winCallback);

}
//发布、取消发布
function releaseInfo(id,releaseFlagType){
    if(releaseFlagType == '1'){
        popConfirm("确认取消发布吗？",function(){
            var param = {
                id:id,
                releaseFlag:0,
            };
            ajaxData("/dataConfig/getTrainDataStatus",param,function(data) {
                
                if(data.length > 0 ) {
                    popMsg("操作成功");
                    ajaxTableReload("table1",false);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }else{
        popConfirm("确认发布吗？",function(){
            var param = {
                id:id,
                releaseFlag:1,
            };
            ajaxData("/dataConfig/getTrainDataStatus",param,function(data) {
                if(data.length > 0) {
                    popMsg("操作成功");
                    ajaxTableReload("table1",false);
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}
//删除
function infoDel(id){
    popConfirm("确认删除该资料信息吗？",function(){
        var param = {
            id:id,
            status:'0'
        };
        ajaxData("/dataConfig/getTrainDataStatus",param,function(data) {
            
            if(data.length == 0) {
                popMsg("删除成功");
                ajaxTableReload("table1",false);
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}

