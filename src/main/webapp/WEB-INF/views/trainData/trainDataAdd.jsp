<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .required-logo {
            color: red;
        }
        .sch-margin{
            margin-top: 15px;
        }


    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="TrainDataDtoForm" modelAttribute="TrainDataDto" cssClass="form-horizontal">
            <form:hidden path="id" value="${TrainDataDto.id}"/>
            <form:hidden path="releaseFlagType" value="${TrainDataDto.releaseFlagType}"/>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font class="required-logo">*&nbsp;</font>资料类型</label>
                <div class="col-md-10 no-padding" style="width: 600px;">
                    <form:select path="dataType" id="dataType" name="dataType" class="form-control">
                        <form:option value="">请选择资料类型</form:option>
                        <c:forEach var="property" items="${notificationTypeList}" varStatus="index">
                            <form:option value="${property.typeValue}"
                            >${property.typeName}</form:option>
                        </c:forEach>
                    </form:select>
                </div>
            </div>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font class="required-logo">*&nbsp;</font>标题</label>
                <div class="col-md-4 no-padding" style="width: 600px">
                    <form:input path="title" cssClass="form-control" placeholder="请输入标题"
                                maxlength="128"/>
                </div>
            </div>
<%--            <div class="row sch-margin" >--%>
<%--                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;margin-left: 21px;"><font class="required-logo">*&nbsp;</font>关联类型</label>--%>
<%--                <div class="col-md-6 controls " onclick="relationType()">--%>
<%--                    <label>--%>
<%--                        <form:radiobutton class="minimal formal-score-type" path="type" value="0"--%>
<%--                                          checked="checked"/>--%>
<%--                        附件--%>
<%--                    </label>--%>
<%--                    <label>--%>
<%--                        <form:radiobutton class="minimal formal-score-type" path="type" value="1"/>--%>
<%--                        链接--%>
<%--                    </label>--%>
<%--                </div>--%>
<%--            </div>--%>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font class="required-logo">&nbsp;</font>链接</label>
                <div class="col-md-4 no-padding" style="width: 600px">
                    <form:input path="link" cssClass="form-control" placeholder="请输入链接地址"
                                maxlength="128"/>
                </div>
            </div>
            <div class="row sch-margin" id="relationFontDiv" >
                <label class="col-md-2 " style="margin-left: 11px;width: 100px;"><font class="required-logo"></font>
                    <font id="relationFont" style="margin-top: 5px;">选择附件</font>
                </label>
                <div class="col-md-6 " >
                        <div class="col-md-10" style="display: inline-block;position: relative; ">
                        <span class="btn btn-sm btn-success fileinput-button" id="chooseLive" style="width: 47px;">
                            <span>上传</span>
                            <input id="fileupload" type="file" name="files" multiple>
                        </span>
                        </div>
<%--                        <input style="top: 2px;" type="text" class="form-control relationItemHttps"  name="relationItemHttps" id="relationItemHttps"--%>
<%--                               value="${TrainDataDto.link}"--%>
<%--                               autocomplete="off" placeholder="请输入连接地址"/>--%>
                </div>
            </div>
            <div class="form-group" style="margin-left:91px; margin-top: 2px" id="fileDiv">
                <c:forEach items="${TrainDataDto.courseList}" var="item" varStatus="status" >
                    <div class="col-md-12" style="margin-bottom: 5px;">
                        <span >${status.index + 1}、</span>
                        <span onclick="downloadFile('${item.id}')" style="cursor: pointer;"class="file-span"  data-file-type="1" data-atta-id="${item.id}"
                              data-file-id="${item.attUrl}" >${item.attName}</span>
                         <input type="button" style="margin-left:15px;width: 50px;height: 26px;line-height: 5px;" onclick="removeFile(this)" class="btn btn-default" value="删除"  >
                         <input type="button" style="margin-left:15px;width: 50px;height: 26px;line-height: 5px;" onclick="downloadFile('${item.id}')"   class="btn btn-default" value="下载">
                         <input type="button" style="margin-left:15px;width: 50px;height: 26px;line-height: 5px;" onclick="editFile('${item.attName}',this)"  class="btn btn-default" value="编辑">
                    </div>
                </c:forEach>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font class="required-logo">*&nbsp;</font>适用用户类型</label>
                <div class="form-group" style="width: 400px;">
                    <div class="col-md-11 ">
                        <input id="TypeList" type="text" class="t-select" json-data='${TypeList}'
                               selected-ids="${TrainDataDto.personType}"/>
                        <input name="TypeList" type="hidden" placeholder="请选择适用用户类型" value='${TrainDataDto.personType}'/>
                    </div>
                </div>
            </div>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"></font>无话术权限</label>
                <div class="col-md-4 no-padding" style="width: 600px">
                    <form:input path="speechArt" cssClass="form-control" placeholder="请输入话术"
                                maxlength="256"/>
                </div>
            </div>
            <div class="col-md-12">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"></font>关联课程</label>
                <div class="col-md-4 no-padding" style="display: inline-block;position: relative;">
                <span class="btn btn-primary" onclick="chooseCourse()">选择课程</span>
                </div>
            </div>
            <div class="col-md-12" style="margin-top: 5px">
                <label class="col-md-1 no-padding control-label" style="margin-top: 10px;width: 100px;">已关联课程：</label>
                <div class="col-md-10 no-padding">
                    <span><pre id="courseName" style="width: 600px">${TrainDataDto.courseName}</pre></span>
                </div>
            </div>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font class="required-logo">&nbsp;</font>正文</label>
                <div class="col-md-10 no-padding" style="width: 100%;">
                    <script id="content" type="text/plain" style="width: 100% !important;">${TrainDataDto.content}</script>
                </div>
            </div>
            <div></div>
            <div class="row" style="margin-top: 20px;text-align: center;">
                <div id="not_save">
                    <input type="button"  style="margin-right: 15px;" class="btn btn-primary" value="取消发布" onclick="releaseInfo()"/>
                    <input type="button" id="btnCancel" class="btn btn-default" value="取消" />
                </div>
                <div id="save">
                    <input type="button"  style="margin-right: 15px;" class="btn btn-primary" value="保存" onclick="saveTrain(0)"/>
                    <input type="button" onclick="saveTrain(1)" style="margin-right: 15px;" class="btn btn-primary" value="保存并发布" />
                    <input type="button" id="btnCancelSave" class="btn btn-default" value="取消" />
                </div>
            </div>
        </form:form>

    </div>
</div>
</body>
</html>