//@ sourceURL=insertTeacherManagementInit.js
let _fileUploadUrl = contextPath + '/filetempupload';
$(document).ready(function () {
    if($("#releaseFlagType").val() == '1'){
        $("#not_save").show();
        $("#save").hide();
    }
    if($("#releaseFlagType").val() == '0'){
        $("#not_save").hide();
        $("#save").show();
    }
    if($("#id").val() == ''|| $("#id").val() == null){//新增
        $("#not_save").hide();
        $("#save").show();
    }

    tSelectInit()
    $('.relationItemHttps').css('display', 'none');//链接input
    $('.relationItemCourse').css('display', 'none');//课程名字和删除
    // 富文本初始化
    ueEditorInit();
    // 取消
    $('#btnCancel').bind('click', function () {
        closeWinCallBack();
    });
    $('#btnCancelSave').bind('click', function () {
        closeWinCallBack();
    });

    //附件上传
    modelUpdate("fileupload");
    /**
     * 课件上传
     */
    function modelUpdate(fileUploadId) {
        $("#" + fileUploadId).fileupload({
            url: _fileUploadUrl,
            dataType: 'text',
            autoUpload: true,
            add: function(e, data) {
                // 上传文件时校验文件格式
                data.submit();
            },
            submit: function(e, data) {
                index = layer.load(1, {
                    shade: [0.1, '#fff'] //0.1透明度的白色背景
                });
            },
            done: function(e, data) {
                layer.close(index);
                $.each($.parseJSON(data.result), function(index, file) {
                    let divStr =
                        ' <div class="col-xs-12 file-content" style="margin-bottom: 5px;">' +
                        '<span></span>'+
                        ' <span class="file-span" title="下载" data-file-type="0" data-atta-id="" data-file-id="' + file.fileRelaId + '" id="file' + file.fileRelaId + '">' + file.fileName + '</span>' +
                        ' <input type="button" style="margin-left:15px;width: 50px;height: 26px;line-height: 5px;" onclick="removeFile(this)" id="btnCancel" class="btn btn-default" value="删除"  >'+
                        ' <input type="button" style="margin-left:15px;width: 50px;height: 26px;line-height: 5px;" onclick="downFile()"  id="btnCancel" class="btn btn-default" value="下载">'+
                        ' <input type="button" style="margin-left:15px;width: 50px;height: 26px;line-height: 5px;" onclick="editFile(\'' + file.fileName + '\',this)" id="btnCancel" class="btn btn-default" value="编辑">'+
                        ' </div>';
                    $("#fileDiv").append(divStr);
                })
            },
            fail: function(e, data) {
                popMsg("上传附件失败！");
            }
        });
    }

    $("#TrainDataDtoForm").validate({
        rules: {
            "dataType": {
                required : true,
            },
            "title": {
                required : true,
            },
            // "link": {
            //     required : true,
            // },
        },
    });
})

function editFile(name,obj) {
    var fileName = name.substring(0,name.lastIndexOf('.'));
    var fileD = name.substring(name.lastIndexOf('.'));
    var content = "<div>"+
        "<input type=\"text\" id='fileName' placeholder=\"请输入修改的文件名\"    value='" + fileName + "' name='fileName' style='width: 300px;height: 72px;line-height: 30px;border: 1px solid #e6e6e6;' maxlength='256'></input>"+
        "</div>"
    var layerIndex = layer.open({
        title:'修改文件名',
        content: content,
        btn:['确定','取消']
        ,
        yes:function (index, layero) {
            
            var fname = $("#fileName").val()
            $(obj).parent().children("span:eq(1)").html(fname+fileD)
            layer.close(layerIndex);
            popMsg("修改成功")
        }
        ,btn2:function (index, layero) {
            layer.close(layerIndex);
        }
    })
}

function downFile() {
    popMsg("请先保存新上传的附件，再进行下载");
    return;
}
/**
 * 富文本初始化
 */
function ueEditorInit() {
    UE.delEditor('content');
    um = UE.getEditor('content', {
        initialFrameHeight: 200,
        textarea: "content",
        elementPathEnabled: false,
        autoHeightEnabled: false
    });
}

function relationType() {
    var relation = $('[name="type"]:checked').val();
    if (relation === '0') {//附件
        $('#relationFont').html('选择附件');
        $('#chooseLive').css('display', 'block');//课程按钮
        $('.relationItemHttps').css('display', 'none');//链接input
        $('#fileDiv').css('display', 'block');//课程按钮
    } else if (relation === '1') {//链接
        $('#relationFont').html('链接地址');
        $('#chooseLive').css('display', 'none');//课程按钮
        $('.relationItemHttps').css('display', 'block');//链接input
        $('#fileDiv').css('display', 'none');//课程按钮
    }
}

//删除课件
function removeRow(obj){
    var fileId = $(obj).prev().attr("value");
    if(fileId.indexOf('-') != -1){
        $(obj).parent().remove();
    }else{
        popConfirm('删除后无法恢复，确认删除吗？',function(){
            var parma = {attachmentId:fileId};
            ajaxData('/signManage/deleteAttachment',parma,function(){
                $(obj).parent().remove();
            });
        });
    }
}
/**
 * 删除课件
 * @param item
 */
function removeFile(item) {
    // 移除页面元素
    $(item).parent().remove();
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack,
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
    };
    $('#TypeList').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('TypeList') + '"]').val(d.value);
}
function saveTrain(type) {
    
    let files = [];
    let courseList = [];
    $.each($('.file-span'), function(index, fileItem) {
        let fileType = $(fileItem).data('file-type');
        let attName = fileItem.innerHTML;
        let attId = $(fileItem).data('atta-id');
        let tempId = $(fileItem).data('file-id');
        if (getValue(attId) != '') {
            courseList.push({
                id: attId,
                attName:attName
            });
        }else {
            files.push({
                dataType: fileType,
                attachmentId: attId,
                tempId: tempId,
                fileName:attName

            })
        }
    });
    if( getValue($('#dataType').val())  == ""  ){
        popMsg("请输入通知类型");
        return;
    }
    if( getValue($("#title").val())  == ""  ){
        popMsg("请输入标题");
        return;
    }
    // if( getValue($("#link").val())  == ""  ){
    //     popMsg("请输入链接地址");
    //     return;
    // }
    if((courseList.length == 0  && files.length == "0") && ($("#link").val()  == "" || $("#link").val()  == null )){
        popMsg("请上传附件或者输入链接地址");
        return;
    }
    // if( ($("#relationItemHttps").val()  == "" || $("#relationItemHttps").val()  == null )&& $('[name="type"]:checked').val() == '1'){
    //     popMsg("请输入连接地址");
    //     return;
    // }
    // if(courseList.length == 0  && (files.length == "0" && $('[name="type"]:checked').val() == '0')){
    //     popMsg("请上传附件");
    //     return;
    // }
    if( $("#TypeList").val()  == "" ||   $("#TypeList").val()  == null){
        popMsg("请输入适用用户类型");
        return;
    }
    if ($("#TrainDataDtoForm").valid()) {
        var param = {
            id: $("#id").val(),
            title: $("#title").val(),
            dataType: $("#dataType").val(),
            speechArt: $("#speechArt").val(),
            content: $('textarea[name="content"]').val(),
            // type: $('[name="type"]:checked').val(),
            link: $("#link").val(),
            releaseFlag: type,
            attList: files,//课件
            courseList: courseList,
            personType: $('input[name="TypeList"]').val(),
        };
        ajaxData("/dataConfig/saveTrainData", JSON.stringify(param), function (data) {
            popMsg("保存成功");
            closeWinCallBack(data);
        }, "application/json;charset=UTF-8");
    }
}
window.onload = function() {
    relationType();
}

//发布、取消发布
function releaseInfo(){
    var   releaseFlagType = $("#releaseFlagType").val();
    if(releaseFlagType == '1'){
        popConfirm("确认取消发布吗？",function(){
            var param = {
                id: $("#id").val(),
                releaseFlag:0,
            };
            ajaxData("/dataConfig/getTrainDataStatus",param,function(data) {
                if(data.length > 0 ) {
                    popMsg("发布成功");
                    $("#releaseFlagType").val('0');
                    $("#not_save").hide();
                    $("#save").show();
                } else {
                    popMsg("操作失败，请稍后再试");
                }
            });
        })
    }
}


//选择关联课程
function chooseCourse(obj) {
    var id = $("#id").val();
    var releaseFlagType = $("#releaseFlagType").val();
    // if (releaseFlagType == '0'){
    if (id != null && id != '') {
        if (releaseFlagType == '0') {
            var param = {
                id: $("#id").val(),
            }
            parent.popWin("选择课程", "/dataConfig/chooseCourseList", param, "100%", "100%", function (data) {
                // //查并且更新所选课程
                var param = {
                    id: $("#id").val(),
                    ids: data,
                }
                ajaxData("/dataConfig/addRelationCourse", param, function (dto) {
                    let courseNameList = [];
                    courseNameList = dto[0].courseName.split(' ');
                    courseNameList.pop();
                    var str = '';
                    for (var i = 0; i < courseNameList.length; i++) {
                        if (courseNameList[i] != "null") {
                            str += courseNameList[i] + "<br>"
                        } else {
                            str += ""
                        }
                    }
                    $("#courseName").html(str);
                })
            })
        } else {
            popMsg("请先取消发布")
        }
    } else {
        popMsg("保存资料后,才能选择关联课程")
    }
}
