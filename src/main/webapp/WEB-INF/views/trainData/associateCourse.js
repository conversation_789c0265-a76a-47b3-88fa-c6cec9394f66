var courseName = '';
var selIds = '';
$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });
    //提交按钮
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空按钮
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        search();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/dataConfig/getCourseNameList", $("#queryForm").formSerialize());
}

function rcIndex(data, type, row, meta) {
    var checkbox = false;
    if ($("#checkedId").val() != '') {
        var ids = $("#checkedId").val().split(',');
        for (let i = 0; i < ids.length; i++) {
            var checkId = ids[i].trim();
            if (checkId == data.id) {
                checkbox = true;
            }
        }
    }
    var str = '';
    str += '<div class="classCheckBox case-opt">';
    if (checkbox) {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id + '"  value="' + data.id + '" checked class="hidden">';
    } else {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id + '"  value="' + data.id + '"  class="hidden">';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="courseId"box value="' + data.id + '">'
    str += '</div>';
    return str;
}



function submitOn() {
    $("#tableAll").find("tbody").find("tr").each(function (n,obj){
        var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
        if (checkFlag==true){
            var id = $(obj).find('td').eq(0).find('input').attr('d-id');
            selIds = selIds + id +','
        }
    })
    closeWinCallBack(selIds)
}


