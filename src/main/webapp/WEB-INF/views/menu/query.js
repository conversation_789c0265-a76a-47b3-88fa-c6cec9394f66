//@ sourceURL=query.js

var setting = {
	view : {
		showLine : true,
		showIcon : false,
	},
	callback : {
		onClick : menuOnClick,

	},
	data : {
		simpleData : {
			enable : true,
			idKey : "id",
			pIdKey : "pMenuId",
			rootPId : 0
		},
		key : {
			name : "menuName"
		}
	},
};

// 当前选择节点
var selectNode;

// 树节点初始化
var zNodes = $.parseJSON($("#menuList").text());

// 表单校验规则
var validator;
var back;

var id;
var resourceDes;

$(document).ready(

		function() {
			if(back == null){
				back = '0';
			}
			// 初始化树状图
			$.fn.zTree.init($("#menuTree"), setting, zNodes);
			var treeObj = $.fn.zTree.getZTreeObj("menuTree");
			var node = treeObj.getNodesByParam("id", back, null)[0];
			treeObj.expandNode(node, treeObj);
			// 重新选中节点
			treeObj.selectNode(node, false);
			var nodes = treeObj.transformToArray(treeObj.getNodes());
			for (var i = 0; i < nodes.length; i++) {

				if (!nodes[i].isParent) {
					nodes[i].iconSkin = "level";
					treeObj.updateNode(nodes[i]);
				}
			}

			// 设定表单校验规则
			validator = $("#menuForm").validate({
				rules : {},
				messages : {
					"menuName" : {
						remote : ""
					},
				}
			});

			// 添加节点
			if(document.getElementById("update")){
				$("#update").bind(
						"click",
						function() {

							if ($("#ID").val() == "") {

								popAlert("请选择节点");

							} else {
								if ($("#menuForm").valid()) {
									
									if(null == id){
										//2017.10.9 by zhanghaoyu start
//										var param = "id=" + $("#ID").val()
//										+ "&menuFolderFlag="
//										+ $("#level").val() + "&pMenuId="
//										+ $("#pMenuId").val() + "&menuName="
//										+ $("#menuName").val() + "&menuDes="
//										+ $("#menuDes").val() + "&remark="
//										+ $("#remark").val() + "&sortNo="
//										+ $("#sortNo").val() + "&resourceId="
//										+ $("#resId").val();
										var param = {
												id : $("#ID").val() ,
												menuFolderFlag : $("#level").val() ,
												pMenuId : $("#pMenuId").val() ,
												menuName : $("#menuName").val() ,
												menuDes : $("#menuDes").val() ,
												remark : $("#remark").val() ,
												sortNo : $("#sortNo").val() ,
												resourceId : $("#resId").val()
										}
									}else {
//										var param = "id=" + $("#ID").val()
//										+ "&menuFolderFlag="
//										+ $("#level").val() + "&pMenuId="
//										+ $("#pMenuId").val() + "&menuName="
//										+ $("#menuName").val() + "&menuDes="
//										+ $("#menuDes").val() + "&remark="
//										+ $("#remark").val() + "&sortNo="
//										+ $("#sortNo").val() + "&resourceId="
//										+ id;
										var param = {
												id : $("#ID").val() ,
												menuFolderFlag : $("#level").val() ,
												pMenuId : $("#pMenuId").val() ,
												menuName : $("#menuName").val() ,
												menuDes : $("#menuDes").val() ,
												remark : $("#remark").val() ,
												sortNo : $("#sortNo").val() ,
												resourceId : id
										}
										//2017.10.9 by zhanghaoyu end
									}
									
									ajaxData("/menu/updateSelectNode", param,
											callBack);
								}
							}
						});
			}
			

			// 删除节点
			if(document.getElementById("delLevel")){
				$("#delLevel").bind("click", function() {

					if ($("#id").val() == "") {
						popAlert("请选择节点");
					} else if ($("#menuName").val() == "云端管理平台") {
						popAlert("此节点不可删除");
					} else if (selectNode.isParent) {
						popAlert("请先删除子节点");
					} else {
						var tempNode = selectNode.getParentNode();
						var param = "id=" + $("#ID").val();
						popConfirm("确认删除节点", function() {
							ajaxData("/menu/delSelectNode", param, callBack);
							selectNode = tempNode
						}, null);

					}
				});
			}
			
			if(document.getElementById("addPager")){
				$("#addPager").bind(
						"click",
						function() {

							if ($("#ID").val() == "") {
								popAlert("请选择节点");
								return;
							}
							//2017.10.16 by zhanghaoyu start
//							var param = "id=" + $("#ID").val();
							var param = {
									id : $("#ID").val()
							}
							//2017.10.16 by zhanghaoyu end
							parent.popWin("新增节点", "/menu/addpop", param, "600px", "400px",
									myWinCallback, callBack);
						});
			}
			
			if(document.getElementById("addItem")){
				$("#addItem").bind(
						"click",
						function() {

							if ($("#ID").val() == "") {
								popAlert("请选择节点");
								return;
							}
							//2017.10.16 by zhanghaoyu start
//							var param = "id=" + $("#ID").val();
							var param = {
									id : $("#ID").val()
							}
							//2017.10.16 by zhanghaoyu end
							parent.popWin("新增节点", "/menu/addpop", param, "600px", "400px",
									myWinCallback, callBack);
						});
			}
			

			if(document.getElementById("resourceIdBtn")){
				$("#resourceIdBtn").bind(
						"click",
						function() {

							parent.popWin("新增资源", "/menu/menuresourcepop", "", "65%",
									"70%", winCallback, mycallBack);

						});
			}
			

		});

function winCallback(paraWin, paraCallBack) {
	
	id = paraWin.result[0].id;
	resourceDes = paraWin.result[0].resourceDes;
	$("#resourceId").val(resourceDes);
}
function mycallBack(data) {

	var jsonObj = {
		result : [ {} ]
	};

	closeWinCallBack(jsonObj);

}

// Ajax回调函数
function callBack(data) {
	if (data) {

		$.fn.zTree.init($("#menuTree"), setting, $.parseJSON(data));
		var curLawName = $("#typeName").val();
		$("#lawName").text("");
		$("#menuName").val("");
		$("#pMenuId").val("");
		$("#menuDes").val("");
		$("#remark").val("");
		$("#sortNo").val("");
		$("#resId").val("");
		$("#resourceId").val("");
		$("#ID").val("");
		var treeObj = $.fn.zTree.getZTreeObj("menuTree");
		var nodes = treeObj.transformToArray(treeObj.getNodes());
		//var node = treeObj.getNodesByParam("id", selectNode.id, null)[0];
		var node = treeObj.getNodesByParam("id", 0, null)[0];
		treeObj.expandNode(node, treeObj);
		for (var i = 0; i < nodes.length; i++) {

			if (!nodes[i].isParent) {
				nodes[i].iconSkin = "level";
				treeObj.updateNode(nodes[i]);
			}

		}

		popMsg("处理成功", null);
		// popAlert("处理成功");

	} else {
		popMsg("处理失败", null);
		// popAlertShow("修改失败");
	}
}

// 树点击函数
function menuOnClick(event, treeId, zNodes) {

	// 重置check样式
	validator.resetForm();

	$("#ID").val(zNodes.id);
	$("#lawName").text(zNodes.menuName);
	$("#menuName").val(zNodes.menuName);
	$("#menuDes").val(zNodes.menuDes);
	$("#remark").val(zNodes.remark);
	$("#sortNo").val(zNodes.sortNo);
	$("#resId").val(zNodes.resourceId);
	$("#pMenuId").val(zNodes.pMenuId);
	$("#level").val(zNodes.level);

	if ($("#menuName").val() == "云端管理平台") {

		$("#menuName").prop("readonly", true);
		$("#menuDes").prop("readonly", true);
		$("#remark").prop("readonly", true);
		$("#sortNo").prop("readonly", true);
	} else {
		$("#menuName").prop("readonly", false);
		$("#menuDes").prop("readonly", false);
		$("#remark").prop("readonly", false);
		$("#sortNo").prop("readonly", false);
	}

	if (zNodes.level == "0") {
		$("#trdisplay").hide();
		if(document.getElementById("delLevel")){
		$("#delLevel").prop("disabled", true);
		}
		if(document.getElementById("update")){
			$("#update").prop("disabled", true);
		}
		if(document.getElementById("addItem")){
		$("#addItem").prop("disabled", true);
		}
		if(document.getElementById("addPager")){
		$("#addPager").prop("disabled", false);
		}
	} else if (zNodes.level == "1") {
		$("#trdisplay").hide();
		if(document.getElementById("delLevel")){
		$("#delLevel").prop("disabled", false);
		}
		if(document.getElementById("update")){
		$("#update").prop("disabled", false);
		}
		if(document.getElementById("addPager")){
		$("#addPager").prop("disabled", true);
		}
		if(document.getElementById("addItem")){
		$("#addItem").prop("disabled", false);
		}
		if(document.getElementById("delLevel")){
		$("#delLevel").prop("disabled", false);
		}
		if(document.getElementById("update")){
		$("#update").prop("disabled", false);
		}
	} else if (zNodes.level == "2") {

		var prm = "id=" + zNodes.resourceId;

		ajaxData("/menu/selectName", prm, nameCallBack);
		$("#trdisplay").show();
		$("#resourceId").prop("disabled", true);
		if(document.getElementById("addItem")){
		$("#addItem").prop("disabled", true);
		}
		if(document.getElementById("addPager")){
		$("#addPager").prop("disabled", true);
		}
		if(document.getElementById("delLevel")){
		$("#delLevel").prop("disabled", false);
		}
		if(document.getElementById("update")){
		$("#update").prop("disabled", false);
		}
	}

	var treeObj = $.fn.zTree.getZTreeObj("menuTree");
	var nodes = treeObj.getSelectedNodes();

	if (nodes.length > 0) {
		selectNode = nodes[0];
	}
}

function nameCallBack(data) {

	if (data != null) {
		$("#resourceId").val(data);
	}

}

// 弹出窗口的回调函数 paraWin:弹出窗口传来的值 paraCallBack:调用窗口时自己传的回调参数值
function myWinCallback(paraWin, paraCallBack) {
	back = paraWin.result[0].name;
	movePage('/menu/menudata');
}