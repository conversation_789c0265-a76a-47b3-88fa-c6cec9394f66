<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:js />
</head>

<body>

    <div class="col-md-4">
        <div class="panel">
            <div class="panel-heading">
                <i class="icon icon-list"> &nbsp;菜单</i>
            </div>
            <div class="panel-body">
                <ul id="menuTree" class="ztree"></ul>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="panel">
            <div class="panel-heading">
                <i class="icon icon-list-alt">&nbsp;菜单项信息<span id="lawTypeID"></span></i>
            </div>
            <div class="panel-body">
                <form:form id="menuForm" modelAttribute="menuDto">
                    <table>
                        <tr height="50px">
                            <td width="50%" align="center"><b>当前编码</b></td>
                            <td width="50%"><label id="lawName"></label></td>
                        </tr>
                        <tr height="50px">
                            <td width="50%" align="center"><b>名称</b></td>
                            <td width="50%"><form:input path="menuName" id="menuName" cssClass="form-control" /></td>
                        </tr>
                        <tr height="50px">
                            <td width="50%" align="center"><b>描述</b></td>
                            <td width="50%"><form:input path="menuDes" id="menuDes" cssClass="form-control" /></td>
                        </tr>
                        <tr height="50px">
                            <td width="50%" align="center"><b>备注</b></td>
                            <td width="50%"><form:input path="remark" id="remark" cssClass="form-control" /></td>
                        </tr>
                        <tr height="50px">
                            <td width="50%" align="center"><b>序号</b></td>
                            <td width="50%"><form:input path="sortNo" id="sortNo" cssClass="form-control" /></td>
                        </tr>
                        <tr height="50px" id="trdisplay" style="display: none">
                            <td width="50%" align="center"><b>资源ID</b></td>
                            <td width="50%"><div class="input-group">
                                    <form:input path="resourceId" id="resourceId" cssClass="form-control" />
                                    <span class="input-group-btn">
                                    <sec:authorize access="hasAuthority('RES_MENU_INIT')" >
                                        <sec:authorize access="hasAuthority('RES_MENU_INIT_AUTHORITY_3')" >
                                        <button class="btn btn-default" type="button" id="resourceIdBtn">
                                            <i class="icon-search"></i>
                                        </button>
                                        </sec:authorize>
                                        </sec:authorize>
                                    </span>
                                </div>
                        </tr>
                    </table>

                    <div class="row">&nbsp;</div>
                    <div class="row">
                        <div class="col-md-offset-1 col-md-8">
                        <sec:authorize access="hasAuthority('RES_MENU_INIT')" >
                                        <sec:authorize access="hasAuthority('RES_MENU_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="update" class="btn btn-primary">保存</form:button>
                            </sec:authorize>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_MENU_INIT')" >
                                        <sec:authorize access="hasAuthority('RES_MENU_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="delLevel" class="btn btn-primary">删除</form:button>
                            </sec:authorize>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_MENU_INIT')" >
                                        <sec:authorize access="hasAuthority('RES_MENU_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="addPager" class="btn btn-primary">新增菜单夹</form:button>
                            </sec:authorize>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_MENU_INIT')" >
                                        <sec:authorize access="hasAuthority('RES_MENU_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="addItem" class="btn btn-primary">新增菜单项</form:button>
                            </sec:authorize>
                            </sec:authorize>
                        </div>
                    </div>

                    <form:hidden path="id" id="ID" />
                    <form:hidden path="pMenuId" id="pMenuId" />
                    <form:hidden path="menuFolderFlag" id="level" />
                    <div id="menuList" style="display: none">${menuList}</div>
                    <div id="resId" style="display: none"></div>
                </form:form>
            </div>
        </div>
    </div>
    <div id="userName" style="display: none;">${userName}</div>
</body>
</html>