<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body>
    <div class="panel" id="myPanel">
        <div class="panel-body">
            <form:form action="/menu/menuresourcepop" modelAttribute="resourceDto">
                <div class="form-group">
                    <div class='col-md-3'>
                        <form:input type='text' path="resourceName" id='resourceName' value='' class='form-control'
                            placeholder='资源名称' />
                    </div>
                    <div class='col-md-3'>
                        <form:input type='text' path="resourceDes" id='resourceDes' value='' class='form-control'
                            placeholder='资源描述' />
                    </div>
                    <div class='col-md-4'>
                        <form:input type='text' path="resourceUrl" id='resourceUrl' value='' class='form-control'
                            placeholder='资源URL' />
                    </div>
                    <div class="col-md-offset-2 col-md-10 text-right">
                        <input type="button" id="btnQuery" class="btn btn-primary" value="查询" /> <input type="button"
                            id="btn" class="btn btn-primary" value="确认" />
                    </div>
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table action="/menu/menuresourcepop" col="5" cssClass="table table-hover table-striped">
                <tr>
                    <th width="5%" align="center">序号</th>
                    <th width="20%" align="left">资源名称</th>
                    <th width="20%" align="left">资源描述</th>
                    <th width="20%" align="left">URL</th>
                </tr>
                <c:forEach var="item" items="${resourceListDto}" varStatus="status">
                    <tr
                        onclick="seachResource('${item.id}','${item.resourceName}','${item.resourceDes}','${item.resourceUrl}')">
                        <td align="center"><c:out value="${status.count}" /></td>
                        <td align="left"><c:out value="${item.resourceName}" /></td>
                        <td align="left"><c:out value="${item.resourceDes}" /></td>
                        <td align="left"><c:out value="${item.resourceUrl}" /></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>
</body>
</html>