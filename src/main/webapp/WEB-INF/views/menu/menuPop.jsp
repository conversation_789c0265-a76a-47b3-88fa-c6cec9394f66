<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body style="padding-top: 0px;">
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 新增菜单
        </div>
        <div class="panel-body">
            <form:form action="" modelAttribute="menuDto" id="menuForm">
                <table>
                    <tr height="50px">
                        <td width="50%" align="center"><b>名称</b></td>
                        <td width="50%"><form:input path="menuName" id="menuName" cssClass="form-control" /></td>
                    </tr>
                    <tr height="50px">
                        <td width="50%" align="center"><b>描述</b></td>
                        <td width="50%"><form:input path="menuDes" id="menuDes" cssClass="form-control" /></td>
                    </tr>
                    <tr height="50px">
                        <td width="50%" align="center"><b>备注</b></td>
                        <td width="50%"><form:input path="remark" id="remark" cssClass="form-control" /></td>
                    </tr>
                    <tr height="50px">
                        <td width="50%" align="center"><b>序号</b></td>
                        <td width="50%"><form:input path="sortNo" id="sortNo" cssClass="form-control" /></td>
                    </tr>
                </table>
                <div class="form-group">
                    <div class="col-md-offset-10 col-md-2" align="right">
                        <input type="button" id="btnSave" class="btn btn-primary" value="保存" /> <input type="button"
                            id="btnClose" class="btn btn-default" value="关闭" />
                    </div>
                </div>
                <form:hidden path="id" id="id" />
                <form:hidden path="pMenuId" id="pMenuId" />
            </form:form>
        </div>
    </div>
</body>
</html>
