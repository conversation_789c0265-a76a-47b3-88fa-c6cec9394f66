//@ sourceURL=menuResourcePop.jsp

var id;
var resourceDes;
$(document).ready(function() {

	$("#btn").bind("click", function() {
		
		if($("#resourceName").val()==""&&$("#resourceDes").val()==""&&$("#resourceUrl").val()==""){
			popAlert("请选择一条资源！");
			return;
		}
		var jsonObj = {
			result : [ {
				id : id,
				resourceDes : resourceDes
			} ]
		};

		closeWinCallBack(jsonObj);
	});
	
	// 查询
	$("#btnQuery").bind("click", function() {
		
		submitForm("/menu/selectchdmenu");
	});

});

function seachResource(id, resourceName, resourceDes, resourceUrl) {
	
	this.id = id;
	this.resourceDes = resourceDes;
	$("#resourceName").val(resourceName);
	$("#resourceDes").val(resourceDes);
	$("#resourceUrl").val(resourceUrl);
}
function edititemPop(url, data) {
	parent.popWin("编辑子资源", url, data, "500px", "450px", myWinCallback, callBack);
}

function myWinCallback(paraWin, paraCallBack) {

}
function callBack(data) {
}
