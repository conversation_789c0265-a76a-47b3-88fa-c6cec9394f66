//@ sourceURL=menuPop.js
$(document).ready(
		function() {

			$("#menuName").val("");
			$("#menuDes").val("");
			$("#remark").val("");
			$("#sortNo").val("");

			// 设定表单校验规则
			validator = $("#menuForm").validate({
				rules : {

					"menuName" : {
						// 必须
						required : true,
						// 后台校验：唯一性校验
						remote : {
							url : contextPath + "/menu/checkName",
							type : "post",
							async : false,
							data : {
								name : function() {
									return $("#menuName").val();
								}
							}
						},
						// 长度
						maxlength : 32
					}
				},
				messages : {

					"menuName" : {
						remote : "名称重复！"
					},
				}
			});

			// 关闭
			$("#btnClose").bind("click", function() {
				closeWin();
			});

			// 添加节点
			$("#btnSave").bind(
					"click",
					function() {
						//2017.10.9 by z<PERSON><PERSON>yu start
//						var param = "id=" + $("#ID").val() + "&pMenuId="
//						+ $("#id").val() + "&menuName="
//						+ $("#menuName").val() + "&menuDes="
//						+ $("#menuDes").val() + "&remark="
//						+ $("#remark").val() + "&sortNo="
//						+ $("#sortNo").val();
						var param = {
								id : $("#ID").val() ,
								pMenuId : $("#id").val() ,
								menuName : $("#menuName").val() ,
								menuDes : $("#menuDes").val() ,
								remark : $("#remark").val() ,
								sortNo : $("#sortNo").val()
						}
						//2017.10.9 by zhanghaoyu end
						ajaxData("/menu/addSave", param, function(data) {
							callback(data)
						});
					});
		});
// Ajax回调函数
function callback(data) {

	var jsonObj = {
		result : [ {
			name : $("#id").val(),
		} ]
	};

	closeWinCallBack(jsonObj);
}
