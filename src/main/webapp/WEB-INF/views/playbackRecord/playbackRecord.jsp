<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/video/js-vod-sdk-1.0.1.min.js"></script>
</head>
<body>
<div class="panel" style="padding: 15px 15px 40px">
            <form:form modelAttribute="schUserInfoDto" id="videoForm" onkeydown="bindEnter(event)">
                <div class="row">
                    <div class="col-md-12">
                        <label class="col-md-1 control-label">用户类型</label>
                        <div class="col-md-3">
                            <input id="personTypeList" type="text" class="t-select" json-data='${personTypeList}' placeholder="请选择用户类型"/>
                            <input name="personTypeList" type="hidden" placeholder="请输入用户类型" />
                        </div>
                        <label class="col-md-1 control-label">证券代码</label>
                        <div class="col-md-3">
                            <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control" autocomplete="off" />
                        </div>
                        <label class="col-md-1 control-label">公司名称</label>
                        <div class="col-md-3">
                            <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label class="col-md-1 control-label">姓名</label>
                        <div class="col-md-3">
                            <form:input path="realName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"/>
                        </div>
                        <label class="col-md-1 control-label">手机号</label>
                        <div class="col-md-3">
                            <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                        </div>
                        <label class="col-md-1 control-label">课程</label>
                        <div class="col-md-3">
                            <input id="courseList" type="text" class="t-select" json-data='${courseList}'  placeholder="请选择课程"/>
                            <input name="courseList" type="hidden"  />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label class="col-md-1 control-label">业务专题</label>
                        <div class="col-md-3">
                            <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' placeholder="请选择业务专题" />
                            <input name="courseType" type="hidden" />
                        </div>
                        <label class="col-md-1 control-label">职务分类</label>
                        <div class="col-md-3">
                            <input id="subject" type="text" class="t-select" json-data='${courseTypeSelect005}' placeholder="请选择职务分类" />
                            <input name="subject" type="hidden" />
                        </div>
                        <label class="col-md-1 control-label">课程类型</label>
                        <div class="col-md-3">
                            <input id="applyMechanism" type="text" class="t-select" json-data='${courseTypeSelect004}'  placeholder="请选择课程类型"/>
                            <input name="applyMechanism" type="hidden" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label class="col-md-1 control-label">所在辖区</label>
                        <div class="col-md-3">
                            <input id="belongCommissionList" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
                            <input name="belongCommissionList" type="hidden" />
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-bottom: 8px;margin-right: 8px;float: right;">
                    <sec:authorize access="hasAuthority('RES_PLAYBACK_RECORD_QUERY_AUTHORITY_3')" >
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                            <span id="btnClear" class="btn btn-default">清空条件</span>
                           <%-- <span id="export" class="btn btn-primary">导出</span>--%>
                </div>
            </form:form>
            <sec:authorize access="hasAuthority('RES_PLAYBACK_RECORD_LOOK_AUTHORITY_3')" >
                <%--查看权限--%>
                <input type="hidden" id="lookAuth" value="true">
            </sec:authorize>
    <div class="row">
        <e:grid id="table1" action="/playbackRecord/playbackRecordQuery?phone=1"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:2%"/>
            <e:gridColumn label="姓名" renderColumn="renderRealName" orderable="false"
                          cssClass="text-center" cssStyle="width:8%" />
            <e:gridColumn label="用户类型" renderColumn="renderPersonTypeStr" orderable="false"
                          cssClass="text-center" cssStyle="width:7%;" />
            <e:gridColumn label="手机号" renderColumn="renderPhone" orderable="false"
                          cssClass="text-center" cssStyle="width:7%;" />
            <e:gridColumn label="证券代码" renderColumn="renderCompanyCode" orderable="false"
                          cssClass="text-center" cssStyle="width:8%" />
            <e:gridColumn label="公司名称" renderColumn="renderCompanyName" orderable="false"
                          cssClass="text-center" cssStyle="width:8%" />
            <e:gridColumn label="总学时" renderColumn="renderPeriodAllNum"  orderable="false"
                          cssClass="text-center" cssStyle="width:10%;"/>
            <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                          cssStyle="width:10%" />
        </e:grid>

    </div>
    </div>
    </body>
</html>
