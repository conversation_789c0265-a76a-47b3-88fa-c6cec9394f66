<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style></style>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/video/js-vod-sdk-1.0.1.min.js"></script>
</head>
<body>
<div class="panel" style="padding: 15px 15px 40px">
            <form:form modelAttribute="courseDto" id="courseForm" onkeydown="bindEnter(event)">
                <div class="row">
                    <div class="col-md-12">
                        <label class="col-md-1 control-label">课程名称</label>
                        <div class="col-md-3">
                            <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" autocomplete="off" />
                        </div>
                        <label class="col-md-1 control-label">用户类型</label>
                        <div class="col-md-3">
                            <input id="personTypeList" type="text" class="t-select" json-data='${personTypeList}' />
                            <input name="personTypeList" type="hidden" placeholder="请输入用户类型" />
                        </div>
                        <label class="col-md-1 control-label">所在辖区</label>
                        <div class="col-md-3">
                            <input id="belongCommissionList" type="text" class="t-select" json-data='${belongCommissionList}' />
                            <input name="belongCommissionList" type="hidden" placeholder="请选择辖区" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12" text-left>
<%--                        <div class="col-md-6">--%>
<%--                            <label class="col-md-2 control-label" style="margin-top: 6px;text-align: right">上传时间</label>--%>
<%--                            <div class="col-md-4 daterange">--%>
<%--                                <form:input path="courseCreateTime" placeholder="请选择上传时间" cssClass="form-control"/>--%>
<%--                            </div>--%>
<%--                        </div>--%>
                        <div class="col-md-12  text-right">
                            <sec:authorize access="hasAuthority('RES_COURSE_PLAYBACK_RECORD_QUERY_AUTHORITY_3')" >
                                <span id="btnQuery" class="btn btn-primary">查询</span>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_COURSE_PLAYBACK_RECORD_EXPORT_AUTHORITY_3')" >
                                <span id="export" class="btn btn-primary">导出</span>
                            </sec:authorize>
                            <span id="btnClear" class="btn btn-default">清空条件</span>
                        </div>
                    </div>
                </div>
            </form:form>
    <div class="row" style="margin-top: 10px">
        <e:grid id="courseTable" action="/coursePlaybackRecord/coursePlaybackRecordQuery"
                cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:2%"/>
            <e:gridColumn label="课程名称" displayColumn="courseName" orderable="false"
                          cssClass="text-center" cssStyle="width:8%" />
            <e:gridColumn label="观看人数" displayColumn="watchNum" orderable="false"
                          cssClass="text-center" cssStyle="width:7%;" />
            <e:gridColumn label="观看完成人数" displayColumn="completeNum" orderable="false"
                          cssClass="text-center" cssStyle="width:7%;" />
            <e:gridColumn label="学时" displayColumn="classTime" orderable="false"
                          cssClass="text-center" cssStyle="width:7%;" />
<%--            <e:gridColumn label="总学时" displayColumn="timeAll" orderable="false"--%>
<%--                          cssClass="text-center" cssStyle="width:8%" />--%>
            <e:gridColumn label="观看完成学时数" displayColumn="completeTimeAll" orderable="false"
                          cssClass="text-center" cssStyle="width:8%" />
        </e:grid>

    </div>
    </div>
    </body>
</html>
