<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
</head>
<style>
    .row{
        margin: 0;
    }
    .startingLabel{
        border-radius:2px;
        color:#14BCF5;
        background: #E8F8FE;
        border: 1px solid #D0F2FD;
        font-size: 12px;
        width: 45px;
        float: right;
        padding: 0 3px;
        display: inline-block;
        text-align: center;
    }
    .finishLabel{
        border: 1px solid #d1d1cc;
        border-radius:2px;
        background-color:#F5F5F5;
        color:#999999;
        font-size: 12px;
        width: 45px;
        padding: 0 2px;
        float: right;
        display: inline-block;
        text-align: center;
    }
</style>
<script type="text/javascript">
    var checkStatusList ='${checkStatusList}'
    var capcoBaseUrl = "${capcoBaseUrl}";
</script>
<body>
<div class="panel">
    <div class="panel-body">
    <form:form modelAttribute="schUserInfoDto" id="videoForm" onkeydown="bindEnter(event)">
        <form:hidden path="id" value="${schUserInfoDto.getId()}"/>
        <div class="col-md-6 no-padding" style="margin-top: 20px;position: fixed;left: 76%;z-index: 888">
                <label class=" col-md-1 control-label" style="top: 8px">状态:</label>
                <div class="col-xs-3">
                    <form:select path="status" cssClass="form-control">
                        <form:option value="">全部</form:option>
                        <form:option value="0">学习中</form:option>
                        <form:option value="1">已完成</form:option>
                    </form:select>
                </div>
        </div>

        <div class="col-md-6 no-padding" style="margin-top: 80px;position: fixed;left: 76%;z-index: 888">
            <label class="col-md-1 control-label" style="top: 8px">课程类型</label>
            <div class="col-xs-3">
                <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect}'  placeholder="请选择课程类型"/>
                <input name="courseType" type="hidden" />
            </div>
            <span id="btnQuery" class="btn btn-primary">查询</span>
        </div>
     </form:form>
            <div class="col-md-12 no-padding stuDiv" style="margin-left: 10px;">
                <label class="col-md-2 no-padding control-label text-right" style="width: 80px;">学习记录:</label>
                <div id="findData">
                <c:forEach var="item" items="${dataList}" varStatus="status" >
                    <c:forEach var="item1" items="${item.courseList}" varStatus="status1">
                        <div style="margin-left: 87px;padding: 10px 10px 10px 10px;line-height: 24px;border: 1px solid #f6f6f6;width: 60%;min-height: 182px;margin-bottom: 10px;">
                            <div  style="float: left;width: 300px;height: 157px;">
                                <img id="Img" src="${item1.pic}"  style="width: 279px;height: 157px;"/>
                            </div>
                            <div style="margin-left: 304px;font-size: 14px;color:#666666">
                                <span style="font-size: 16px;color:#333333">${item1.courseName}</span>
                                <div style="margin-bottom: 10px;margin-top:10px;font-size: 14px;color:#666666">业务专题：${item1.courseTypeText}</div>
                                <c:forEach var="item2" items="${item1.chapterList}" varStatus="status2">
                                    <div style="font-size: 14px;color:#333333">${item2.itemName}</div>
                                    <c:forEach var="item3" items="${item2.watchInfoCourseDtoList}" varStatus="status3">
                                        <div style="margin-bottom:15px">
                                            <div>${item3.chapterName}</div>
                                            <c:if test="${item3.status == '1'}">
                                                <div class="finishLabel">已完成</div>
                                            </c:if>
                                            <c:if test="${item3.status == '0'}">
                                                <div class="startingLabel">学习中</div>
                                            </c:if>
<%--                                            <c:if test="${item1.courseRelationId == '12782114556612567595'}">--%>
<%--                                                <span onclick="viewMonitor('${item1.courseNo}','${item1.accId}')" class="btn btn-primary">查看监控</span>--%>
<%--                                            </c:if>--%>
                                            <div style="font-size: 12px;color:#999999">
                                                <span>已学习：${item3.watchPer}%</span>
                                                <span>总学时：${item3.period}</span>
                                                <span>总时长：${item3.videoAllTime}</span>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </c:forEach>
                            </div>
                        </div>
                    </c:forEach>
                </c:forEach>
                </div>
                <div id="findQueryData">
                </div>
            </div>
            <div class="col-md-12 no-padding" style="margin-top: 50px">
                <div class="col-md-12 text-center" >
                    <span id="btnClose" class="btn btn-default">关闭</span>
                </div>
            </div>
    </div>
</div>
</body>
</html>
