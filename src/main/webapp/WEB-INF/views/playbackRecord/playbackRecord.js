
$(document).ready(function() {
    //查询
    tSelectInit()
    $("#btnQuery").bind("click",function() {

        ajaxTableQuery("table1", "/playbackRecord/playbackRecordQuery",$("#videoForm").formSerialize());
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("videoForm").reset();
        $('input[name="personTypeList"]').val("");
        $('input[name="courseList"]').val("");
        $('input[name="belongCommissionList"]').val("");
        $('input[name="courseType"]').val("");
        $('input[name="subject"]').val("");
        $('input[name="applyMechanism"]').val("");
        ajaxTableQuery("table1", "/playbackRecord/playbackRecordQuery",$("#videoForm").formSerialize());
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/playbackRecord/export?" + $("#videoForm").serialize());
    });
})

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

//学习进度
function watchPer(data, type, row, meta){
    return data.watchPer + "%";
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        inputSearch: true,
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseList').tselectInit(null, teaSelectOptions);
    $('#personTypeList').tselectInit(null, teaSelectOptions);
    $('#belongCommissionList').tselectInit(null, teaSelectOptions);

    var courseTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, courseTypeSelectOptions);

    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#applyMechanism').tselectInit(null, tSelectOptions);
    $('#subject').tselectInit(null, tSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function columnOperation(data, type, row, meta) {
    var look = '';
    if(document.getElementById("lookAuth")) {
        look='<i class="icon icon-eye-open iconColor" title="查看" onclick="watchUserInfo(\'' + data.id + '\')"></i>'
    } else {
        look = '-';
    }
    return look;
}


function renderRealName(data, type, row, meta) {
    if (data.realName == null || data.realName == undefined || data.realName == '') {
        var str = '-';
    } else {
        var str = data.realName;
    }
    return str;
}
function renderPersonTypeStr(data, type, row, meta) {
    if (data.personTypeStr == null || data.personTypeStr == undefined || data.personTypeStr == '') {
        var str = '-';
    } else {
        var str = data.personTypeStr;
    }
    return str;
}
function renderPhone(data, type, row, meta) {
    if (data.phone == null || data.phone == undefined || data.phone == '') {
        var str = '-';
    } else {
        var str = data.phone;
    }
    return str;
}//发布
function renderCompanyCode(data, type, row, meta) {
    if (data.companyCode == null || data.companyCode == undefined || data.companyCode == '') {
        var str = '-';
    } else {
        var str = data.companyCode;
    }
    return str;
}//发布
function renderCompanyName(data, type, row, meta) {
    if (data.companyName == null || data.companyName == undefined || data.companyName == '') {
        var str = '-';
    } else {
        var str = data.companyName;
    }
    return str;
}
function renderPeriodAllNum(data, type, row, meta) {
    if (data.periodAllNum == null || data.periodAllNum == undefined || data.periodAllNum == '') {
        var str = '-';
    } else {
        var str = data.periodAllNum;
    }
    return str;
}

function watchUserInfo(id) {
    var param = {
        id : id
    };
    parent.popWin('查看学习记录信息', '/playbackRecord/playbackRecordLook', param, '98%', '98%', '','','');
}

function callBackAddUser() {
    ajaxTableReload("table1",false);
}
