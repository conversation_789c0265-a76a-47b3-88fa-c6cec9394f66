$(document).ready(function() {
    tSelectInit()
    //查询
    $("#btnQuery").bind("click",function() {
        ajaxTableQuery("courseTable", "/coursePlaybackRecord/coursePlaybackRecordQuery",$("#courseForm").formSerialize());
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("courseForm").reset();
        $('input[name="personTypeList"]').val("");
        $('input[name="belongCommissionList"]').val("");
        ajaxTableQuery("courseTable", "/coursePlaybackRecord/coursePlaybackRecordQuery",$("#courseForm").formSerialize());
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/coursePlaybackRecord/courseExport?" + $("#courseForm").serialize());
    });
})
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personTypeList').tselectInit(null, teaSelectOptions);
    $('#belongCommissionList').tselectInit(null, teaSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
