$(document).ready(function () {

    // 下拉初始化
    tSelectInit();

    $("#btnClose").bind("click", function () {
        closeWin();
    });
    $("#btnQuery").bind("click", function () {
      queryData();
    });
});

function tSelectInit() {
    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, tSelectOptions);
}

function tSelectCustomCallBack(t) {
}

function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function queryData() {
    var param = {
        userId:$("#id").val(),
        status:$("#status").val(),
        courseType:$("#courseType").val(),
    };
    // $("#queryForm").formSerialize()
    ajaxData("/playbackRecord/playbackRecordLookList",$("#videoForm").formSerialize(),function(data) {
        if (data.length >= 0) {
            let divStr = ""
            for (let i = 0; i < data.length; i++) {
                for (let q = 0; q < data[i].courseList.length; q++) {
                    divStr += '            <div style="margin-left: 87px;padding: 10px 10px 10px 10px;line-height: 24px;border: 1px solid #f6f6f6;width: 60%;min-height: 182px;margin-bottom: 10px;">\n' +
                        '                        <div  style="float: left;width: 300px;height: 157px;">\n' +
                        '                                <img id="Img" src="' + data[i].courseList[q].pic + '"  style="width: 279px;height: 157px;"/>\n' +
                        '                        </div>\n' +
                        '                        <div style="margin-left: 304px;font-size: 14px;color:#666666">\n' +
                        '                           <span style="font-size: 16px;color:#333333"> ' + data[i].courseList[q].courseName + ' </span>\n' +
                        '                           <div style="margin-bottom: 10px;margin-top:10px;font-size: 14px;color:#666666">业务专题：' + data[i].courseList[q].courseTypeText + '</div>\n'
                    for (let e = 0; e < data[i].courseList[q].chapterList.length; e++) {
                        if(data[i].courseList[q].chapterList[e].itemName  != null){
                            divStr += '                         <div style="font-size: 14px;color:#333333">' + data[i].courseList[q].chapterList[e].itemName + '</div>\n'
                        }
                        for (let r = 0; r < data[i].courseList[q].chapterList[e].watchInfoCourseDtoList.length; r++) {
                            divStr += '      <div style="margin-bottom:15px">\n' +
                                '                  <div>' + data[i].courseList[q].chapterList[e].watchInfoCourseDtoList[r].chapterName + '</div>\n'
                            if (data[i].courseList[q].chapterList[e].watchInfoCourseDtoList[r].status == '1') {
                                divStr += '  <div class="finishLabel">已完成</div>\n'
                            }
                            if (data[i].courseList[q].courseRelationId == '12782114556612567595') {
                                divStr += "<span onclick='viewMonitor(" + data[i].courseList[q].courseNo + "," + data[i].courseList[q].accId + ")' class='btn btn-primary'>查看监控</span>"
                            }
                            if (data[i].courseList[q].chapterList[e].watchInfoCourseDtoList[r].status == '0') {
                                divStr += '  <div class="startingLabel">学习中</div>\n'
                            }
                            divStr += '       <div style="font-size: 12px;color:#999999">\n' +
                                '                   <span>已学习：' + data[i].courseList[q].chapterList[e].watchInfoCourseDtoList[r].watchPer + '</span>\n' +
                                '                   <span>总学时：' + data[i].courseList[q].chapterList[e].watchInfoCourseDtoList[r].period + '</span>\n' +
                                '                   <span>总时长：' + data[i].courseList[q].chapterList[e].watchInfoCourseDtoList[r].videoAllTime + '</span>\n' +
                                '             </div>\n' +
                                '             </div>\n'
                        }
                    }
                    divStr += '        </div>\n' +
                        '      </div>'
                }
            }
            var dt = document.getElementById('findQueryData');//通过id获取该div
            dt.innerHTML = divStr;
            $("#findData").hide();
            popMsg("操作成功");
        }else{
            popMsg("操作失败");
        }

    });

}

function viewMonitor(courseNo,userId) {
    window.open(capcoBaseUrl + '/trainingCenter/rtc/roomHistory?courseNo=' + courseNo + "&userId=" + userId);
}

