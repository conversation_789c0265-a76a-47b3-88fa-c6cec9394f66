<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>北京上市公司协会培训管理平台</title>
<e:base />
<link rel="shortcut icon" href="${pageContext.request.contextPath}/static/images/logo.png" type="image/x-icon" />
<script type="text/javascript">
var isChangePassFlag = "${isChangePassFlag}";
function processSidebar() {
	if ($("#sidebar").is(":visible")) {
    	$("#sidebar").hide();
    	$("#main").removeClass().addClass("col-sm-12 col-md-12 fullmain");
    	var datatables = $.fn.dataTable.tables({visible: true, api: true});
    	for (var i = 0; i < datatables.length; i++) {
    		$(datatables[i]).dataTable().api().columns.adjust();
        }
	} else {
		$("#main").removeClass().addClass("col-sm-9 col-md-10 main");
		$("#sidebar").show();
		var datatables = $.fn.dataTable.tables({visible: true, api: true});
		for (var i = 0; i < datatables.length; i++) {
    		$(datatables[i]).dataTable().api().columns.adjust();
        }
	}
}
function changePassword(){
	 parent.popWin("设置账号密码", "/userManager/updatePersonPassword", "",
	            "400px", "300px", function (){

	 }, null);
}
function closePopPassword(){
	$("#changePassWin").animate({bottom:'-220px'});
}
function popPassWin(){
	$("#changePassWin").show();
	$("#changePassWin").animate({bottom:'0px'});
}
$(document).ready(function(){
	localStorage.msg = '';
	if(isChangePassFlag != ""){
		popPassWin();
		setTimeout(closePopPassword, 10000);
	}
});
function colorChange(e){
	$(".realLi").children().css("color","");
	$(e).children().css("color","#1F78DB");
}

</script>
<style type="text/css">
.winpop {
	width:316px;
	height:auto;
	position:fixed;
	right:0; bottom:-200px;
	border:1px solid #999999;
	margin:0;
	overflow:hidden;
	display:none;
	background:#FFFFFF;
	border-radius:8px 8px 8px 8px;
	z-index: 10000;
	}
.winpop .title {
	width:100%;
	height:26px;
	background:#1b8cf2;
	text-align:center;
	font-size:12px;
	}
.winpop .con {
	width:100%;
	height:100%;
	font-size:14px;
	text-align:center;
	line-height: 35px;
    padding-top: 15px;
	}
.closeBtn {
	position:absolute;
	right:8px;
	color:#FFFFFF;
	cursor:pointer;
	top: 5px
	}
</style>
</head>

<body class="index-body" style="padding-top: 50px;">
    <nav class="navbar navbar-inverse navbar-fixed-top">
        <div class="container-fluid">
            <div class="navbar-header">
                <img alt="" src="${pageContext.request.contextPath}/static/images/beishangxielogo.png"
                     style="width: 378px;height: 68px">
                <div class="l" style="display: inline-block;height: 27px; margin-left: 10px;">
                    <div style="width: 2px; height: 42px; background-color: rgb(204, 204, 204);"></div>
                </div>
                <div class="l" style="display: inline-block; color: rgb(51, 51, 51); font-size: 22px; margin-left: 12px; font-family: Songti SC;"><span>培训平台管理端</span></div>
            </div>
            <ul class="tooltip-demo topnav">
                <li><a href="javascript:processSidebar();"><i class="icon icon-2x icon-bars"></i></a></li>
<%--                <li class="first"><a href="javascript:movePage('/userManager/editUserCenter')" data-toggle="tooltip" data-placement="bottom" title="" data-original-title="">--%>
<%--                    <i class="icon icon-2x icon-user"></i></a></li>--%>
                <li><a href="#" title="" data-toggle="modal" data-target="#mySmModal1"><i class="icon icon-2x icon-signout"></i></a></li>
            </ul>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <div id="sidebar" class="col-sm-3 col-md-2 sidebar">
                <ul id="accordion" class="accordion">
                    <c:forEach var="menu" items="${menus}">
                        <c:if test="${not empty menu.subMenus && menu.subMenus.size() > 0}">
                        <li>
                            <div class="link">
                                <i class="icon icon-th-list"></i>${menu.menuName}<i class="icon icon-angle-down"></i>
                            </div>
                            <ul class="submenu">
                                <c:forEach var="submenu" items="${menu.subMenus}">
                                    <li class="realLi" onclick="colorChange(this)"><a href="javascript:movePage('${submenu.menuUrl}')">${submenu.menuName}</a></li>
                                </c:forEach>
                            </ul>
                        </li>
                        </c:if>
                    </c:forEach>
                </ul>
            </div>
            <div id="main" class="col-sm-9 col-md-10 main"></div>
        </div>
    </div>

    <!--logout popup-->
    <div class="modal fade" id="mySmModal1">
        <div class="modal-dialog  modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span aria-hidden="true">×</span><span class="sr-only">关闭</span>
                    </button>
                    <h4 class="modal-title">操作确认</h4>
                </div>
                <div class="modal-body">
                    <p>确定要退出吗?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onClick="window.open(contextPath+'/logout', '_self');">确定</button>
                </div>
            </div>
        </div>
    </div>
    <div id="changePassWin" class="winpop">
    	<div class="title"><div class="closeBtn"><i onclick="closePopPassword()" class="fa fa-times"></i></div></div>
			<div class="con">
			<c:if test="${isChangePassFlag=='1'}">
			<p style="color: #f0181b">
			首次登录成功，请注意保护自己的密码
			</p>
			</c:if>
			<c:if test="${isChangePassFlag=='2'}">
			<p style="color: #f0181b">
			已经超过【90天】未修改过密码，请注意保护自己的密码
			</p>
			</c:if>
			<div style="background-color: #1b8cf2;line-height: normal;width: 90px;height: 29px;display: inline-block;vertical-align: middle;margin-top: 6px;margin-bottom:20px;cursor: pointer;" onclick="changePassword()">
			<div style="margin-top: 6px;color:white;" >修改密码</div>
			</div>
			<div style="background-color: #e2e2e2;line-height: normal;width: 90px;height: 29px;display: inline-block;vertical-align: middle;margin-top: 6px;margin-bottom:20px;cursor: pointer;" onclick="closePopPassword()">
			<div style="margin-top: 6px;color:#2486ab;" >忽略</div>
			</div>
		</div>
    </div>

</body>
</html>
