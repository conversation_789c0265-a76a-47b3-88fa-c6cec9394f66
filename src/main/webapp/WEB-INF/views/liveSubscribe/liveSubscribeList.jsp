
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        table td {
            white-space:pre-wrap !important;
            word-wrap: break-word !important;
        }
        .dataTables_scrollHead,
        .dataTables_scrollBody {
            overflow: visible !important;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="panel" style="min-height: 650px">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">  预约信息</i>
    </div>
    <div class="panel-body">
        <form:form action=""  modelAttribute="liveSubscribeInfoDto" id="liveSubscribeForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">直播名称：</label>
                    <div class="col-md-3">
                        <input id="liveId" type="text" class="t-select" json-data='${liveNameList}' placeholder="请选择直播名称" />
                        <input name="liveId" type="hidden" />
                    </div>
<%--                    <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">审核状态：</label>--%>
<%--                    <div class="col-md-3">--%>
<%--                        <form:select path="examineState" cssClass="form-control">--%>
<%--                            <form:option value="">全部</form:option>--%>
<%--                            <form:options items="${liveSubscribeExamineState}" itemLabel="label" itemValue="value" />--%>
<%--                        </form:select>--%>
<%--                    </div>--%>
                    <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">股票代码：</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入股票代码" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">公司名称：</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">

                    <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">预约人名称：</label>
                    <div class="col-md-3">
                        <form:input path="realName" placeholder="请输入预约人名称" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label" style="text-align:center; margin-top: 5px">手机号：</label>
                    <div class="col-md-3">
                        <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-md-offset-9 col-md-3" align="right">
<%--                    <sec:authorize access="hasAuthority('RES_SCH_LIVE_SUBSCRIBE_LIST_AUTHORITY_3')" >--%>
                         <span id="resetBtn" class="btn btn-default">清空条件</span>
                        <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
<%--                    </sec:authorize>--%>
                </div>
            </div>
        </form:form>
<%--        <sec:authorize access="hasAuthority('RES_LIVE_SAVE_EXAMINEINFO_AUTHORITY_3')" >--%>
            <%--审核/修改审核状态权限--%>
            <input type="hidden" id="examineinfo" value="true">
<%--        </sec:authorize>--%>
        <div class="example">
            <hr>
        </div>
        <div class="ibox float-e-margins">
            <e:grid id="liveInfoTable" action="/liveSubscribe/queryLiveSubscribeList" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="直播名称" renderColumn="liveName" orderable="false" cssClass="text-center" cssStyle="width:12%; word-wrap: break-word;"/>
                <e:gridColumn label="预约人" renderColumn="renderSubscribeUserName" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false" cssClass="text-center" cssStyle="width:16%; word-wrap: break-word;"/>
<%--&lt;%&ndash;                <e:gridColumn label="用户类型" displayColumn="subscribeUserType" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>&ndash;%&gt;--%>
<%--                <e:gridColumn label="审核状态" renderColumn="renderExamineStateName" orderable="false" cssClass="text-center" cssStyle="width:12%; word-wrap: break-word;"/>--%>
<%--                <e:gridColumn label="审核人员" displayColumn="examineUserName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>--%>
                <e:gridColumn label="预约时间" displayColumn="examineTime" orderable="false" cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
                <e:gridColumn label="观看来源" renderColumn="subSourceOperation" orderable="false" cssClass="text-center" cssStyle="width:6%; word-wrap: break-word;"/>
                <e:gridColumn label="是否观看" displayColumn="watchState" orderable="false" cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
<%--                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false" cssClass="text-center" cssStyle="width:8%;"/>--%>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
