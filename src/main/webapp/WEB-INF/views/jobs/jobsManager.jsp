<%--
/***************************************************************
* 程序名 : jobsManager.jsp
* 日期  :  2015-8-12
* 作者  :  王志昌
* 模块  :  机构管理
* 描述  :  机构岗位维护
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>易董 云端管理平台</title>
<e:js/>
</head>

<body>
    <div class="col-md-4">
        <div class="panel">
            <div class="panel-heading" >
                <i class="icon icon-list"> &nbsp;公司机构</i>
            </div>
            <div class="panel-body">
                <ul id="orgTree" class="ztree"></ul>
            </div>
        </div>
    </div>
     <div class="col-md-8">
     <form:form id="jobsForm"  modelAttribute="orgDto" action="">
        <div class="panel">
            <div class="panel-heading">
              <i class="icon" onclick="divShow('orgDiv')">+</i>
              <i class="icon" onclick="divHidden('orgDiv')">-</i>
              <i class="icon icon-list-alt"><span id="orgTitleID"></span></i>
            </div>
            <div id="orgDiv" class="panel-body">
                	<table>
                        <tr height="50px">
                            <td width="50%" align="center"><b>当前机构</b></td>
                            <td width="50%"><label id="curOrgNameID"></label></td>
                        </tr>
                        <tr height="50px">
                            <td width="50%" align="center"><b>机构名称</b></td>
                            <td width="50%">
                               <form:input path="orgName"  id="orgNameID" cssClass="form-control"/>
                            </td>
                        </tr>
                    	</table>
                    <div class="row">&nbsp;</div>
                    <div class="row">
                        <div class="col-md-offset-1 col-md-8">
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="addSameLevel" class="btn btn-primary">添加同级机构</form:button>
                            </sec:authorize>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="addLowLevel" class="btn btn-primary">添加下级机构</form:button>
                            </sec:authorize>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="modifyLevel" class="btn btn-primary">修改选中机构</form:button>
                            </sec:authorize>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="delLevel" class="btn btn-primary">删除选中机构</form:button>
                            </sec:authorize>
                            </sec:authorize>
                        </div> 
                    </div>
                    <form:hidden path="pOrgNo" id="parentID"/>
                    <form:hidden path="id" id="ID"/>
                    <form:hidden path="orgNo" id="orgNoID"/>
                    <form:hidden path="orgType" id="orgTypeID"/>
                    <div id="orgListID" style="display:none">${orgQueryList}</div>
             </div>

		</div>
		<div class="panel">
				<div class="panel-heading">
              <i class="icon" onclick="divShow('jobsDiv')">+</i>
              <i class="icon" onclick="divHidden('jobsDiv')">-</i>
				岗位信息
				</div>
				<div id="jobsDiv" class="panel-body" >
					<table id="jobsList" class="table table-hover table-striped">
						<thead>
							<tr>
								<th width="10%" align="center">序号</th>
								<th  align="center">名称</th>
								<th  align="center">别名</th>
							</tr>
						</thead>
<%-- 			 			<c:forEach var="s" items="${orgDto.jobsList}" varStatus="status">
							<tr>
								<td>
									<form:hidden path="jobsList[${status.index}].orgId" />
									<form:checkbox path="jobsList[${status.index}].id"  value="${s.id}" cssClass="checkbox-inline"/>
									<span style="margin:6px">${status.index+1}</span>
								</td>
								<td><form:input path="jobsList[${status.index}].jobsName" cssClass="form-control" /></td>
								<td><form:input path="jobsList[${status.index}].nickName" cssClass="form-control" /></td>
							</tr>
						</c:forEach> --%>
					</table>
					<div class="form-group">
						<div class="col-md-offset-1 col-md-8" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
							<form:button type="button" id="addRow" class="btn btn-primary">添加行</form:button>
                            </sec:authorize>
                            </sec:authorize>
                             <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
							<form:button type="button" id="addJobs" class="btn btn-primary">保存岗位</form:button>
                            </sec:authorize>
                            </sec:authorize>
                             <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT')" >
                        <sec:authorize access="hasAuthority('RES_JOBSMANAGER_INIT_AUTHORITY_3')" >
                            <form:button type="button" id="delJobs" class="btn btn-primary">删除岗位</form:button>
                            </sec:authorize>
                            </sec:authorize>
						</div>
					</div>
				</div>
			</div>
 		</form:form> 
		<form:form action="" modelAttribute="emptyOrgDto">
			<table id="template" style="display: none;">
				<tr>
					<td><form:hidden path="jobsList[0].id" />
						<form:hidden path="jobsList[0].orgId"/>
						<form:checkbox path="jobsList[0].id" value="" cssClass="checkbox-inline"/>
						<span id="orderNum" style="margin:6px">0</span>
					</td>
					<td><form:input path="jobsList[0].jobsName" cssClass="form-control" /></td>
					<td><form:input path="jobsList[0].nickName" cssClass="form-control" /></td>
				</tr>
			</table>
		</form:form>
		
		
		
    </div>
</body>
</html>