//@ sourceURL=jobsManage.js
var setting = {
	view : {
		showLine : false,
	},
	callback : {
		onClick : orgTreeOnClick
	},
	data : {
		keep : {
			parent : true
		},
		simpleData : {
			enable : true,
			idKey : "orgNo",
			pIdKey : "pOrgNo",
			rootPId : 0
		},
		key : {
			name : "orgName"
		}
	}
};

//当前选择节点
var selectNode;

//按钮提交方式(1:添加同级或修改节点；2：添加下级节点；3：删除节点)
var addMethod;

//树节点
var zNodes = $.parseJSON($("#orgListID").text());

//岗位列表行数
var rowSize=0;
//岗位列表内容
var defalt;

var validator;

$(document).ready(function() {
	
	defalt = $("#jobsList").html();
	
	// 设定表单校验规则
	validator = $("#jobsForm").validate(
			{
			rules: {
				orgName:  {remote: contextPath + "/jobsManager/checkOrgName"}
			},
			messages: {
				orgName: {remote: "机构名称已存在"}
			}
	}
			);
    
	$.fn.zTree.init($("#orgTree"), setting, zNodes);
	var treeObj = $.fn.zTree.getZTreeObj("orgTree");
	var nodes =  treeObj.transformToArray(treeObj.getNodes());
	for (var i = 0; i < nodes.length; i++) {
		
		if(!nodes[i].isParent){
			nodes[i].iconSkin = "level";
			treeObj.updateNode(nodes[i]);
		}
	}
	
	//数据初始化
	if (nodes.length > 0) {
		selectNode = nodes[0];
	}
	//设定选中的节点
	treeObj.selectNode(selectNode);
	setFormValue(selectNode);
	// 取得岗位数据
	getInfoData();
	
	//添加同级节点
	if(document.getElementById("addSameLevel")){
		$("#addSameLevel").bind("click",function() {
			//去除校验
			removeValidateFromRules();
			//添加组织机构名称校验
			addValidateFromRules();
			if ($("#jobsForm").valid()) {
				addMethod = "1";
				//2017.10.2 by zhanghaoyu start
//				var param = "orgNo=" + $("#orgNoID").val() + "&pOrgNo="
//				+ $("#parentID").val() + "&orgName="
//				+ $("#orgNameID").val() + "&orgType="
//				+ $("#orgTypeID").val();
				var param = {
						orgNo : $("#orgNoID").val() , 
						pOrgNo : $("#parentID").val() ,
						orgName : $("#orgNameID").val() ,
						orgType : $("#orgTypeID").val()
				}
				//2017.10.2 by zhanghaoyu end
				ajaxData("/jobsManager/addSameNode", param, function(data) {callBack(data)});
			}
		});
	}
	

	//添加下级节点
	if(document.getElementById("addLowLevel")){
		$("#addLowLevel").bind("click",function() {
			//去除校验
			removeValidateFromRules();
			//添加组织机构名称校验
			addValidateFromRules();
			if ($("#jobsForm").valid()) {
				addMethod = "2";
				//2017.10.2 by zhanghaoyu start
//				var param = "orgNo=" + $("#orgNoID").val() + "&orgName="
//				+ $("#orgNameID").val() + "&orgType="
//				+ $("#orgTypeID").val();
				var param = {
						orgNo : $("#orgNoID").val() ,
						orgName : $("#orgNameID").val() ,
						orgType : $("#orgTypeID").val()
				}
				//2017.10.2 by zhanghaoyu end
				ajaxData("/jobsManager/addLowNode", param, function(data) {callBack(data)});
			}
		});
	}
	

	//修改节点
	if(document.getElementById("modifyLevel")){
		$("#modifyLevel").bind("click",function() {
			//去除校验
			removeValidateFromRules();
			//添加组织机构名称校验
			addValidateFromRules();
			if ($("#jobsForm").valid()) {
				addMethod = "1";
				//2017.10.2 by zhanghaoyu start
//				var param = "id=" + $("#ID").val() + 
//							"&orgNo=" + $("#orgNoID").val() + 
//							"&pOrgNo=" +$("#parentID").val() + 
//							"&orgName="+ $("#orgNameID").val() + 
//							"&orgType="+ $("#orgTypeID").val();
				var param = {
						id : $("#ID").val() ,
						orgNo : $("#orgNoID").val() ,
						pOrgNo : $("#parentID").val() ,
						orgName : $("#orgNameID").val() ,
						orgType : $("#orgTypeID").val()
				}
				//2017.10.2 by zhanghaoyu end
				ajaxData("/jobsManager/modifyNode", param, function(data) {callBack(data)});
			}
		});
	}
	

	//删除节点
	if(document.getElementById("modifyLevel")){
		$("#delLevel").bind("click", function() {
			//去除校验
			removeValidateFromRules();
			if(selectNode.isParent){
				popAlert("请先删除下级机构");
			}else if (rowSize>0){
				popAlert("请先删除岗位");
			}
			else {
				addMethod = "3";
				var tempNode = selectNode.getParentNode();
				//2017.10.2 by zhanghaoyu start
//				var param = "id=" + $("#ID").val() ;
				var param = {
						id : $("#ID").val()
				}
				//2017.10.2 by zhanghaoyu end
				popConfirm("确认删除机构",function(){ajaxData("/jobsManager/delSelectNode", param, function(data) {callBack(data)});selectNode = tempNode},null);
			}
		});
	}
	
	
	//添加一行
	if(document.getElementById("addRow")){
		$("#addRow").bind("click", function() {
			//去除校验
			removeValidateFromRules();
			
			if ($("#ID").val() != "") {
				//alert('---添加一行rowSize----'+rowSize);
				// 取得行的模板
				var template = $.trim($("#template").html());
				var $newRow = $(template.replace(new RegExp("\\[0\\]", "g"),"[" + rowSize + "]").replace(new RegExp("jobsList0", "g"),"jobsList[" + rowSize + "]"));
				$newRow.data('index', rowSize);
				// 往table里动态添加一行
				$newRow.appendTo("#jobsList");
				// 动态为新增的行添加校验规则
				validateRowRules($newRow, rowSize);
				//序号
				$newRow.find("span[id='orderNum']").text(rowSize + 1);
				//组织机构id
				$("input[name='jobsList[" + rowSize + "].orgId']").val($("#ID").val());
				$("input[name='jobsList[" + rowSize + "].id']").val("");
				$("input[name='jobsList[" + rowSize + "].jobsName']").attr("readonly",false);
				$("input[name='jobsList[" + rowSize + "].nickName']").attr("readonly",false);
				
//				$("input[name='jobsList[" + rowSize + "].jobsName']").attr("onblur","addJobs();");
//				$("input[name='jobsList[" + rowSize + "].nickName']").attr("onblur","addJobs();");
				rowSize = refreshTableIndex("jobsList");
				
			} else {
				popAlert("请选择公司机构");
			}
		});
	}

	
	//删除岗位
	if(document.getElementById("delJobs")){
		$("#delJobs").bind("click", function() {
			var ids = [];
			$(("tr"), $("#jobsList")).each(
			function() {
				if($(this).find("input[type='checkbox']").is(':checked'))
				{
					var jobId= $(this).find("input[type='checkbox']").val();
					ids.push(jobId);
				}
			});
			if(ids==''){
				popAlert("请选择岗位");
			}else {
				//删除
				//2017.10.2 by zhanghaoyu start
//				var param = "id=" + $("#ID").val() +"&jobsIds="+ids;
				var param = {
						id : $("#ID").val() ,
						jobsIds : ids.join(',')
				}
				//2017.10.2 by zhanghaoyu end
				ajaxData("/jobsManager/delJobs", param, function(data) {putData(data)});
			}
			
		});
	}
	
	
	//增加岗位
	if(document.getElementById("addJobs")){
		$("#addJobs").bind("click", function() {
			//去除校验
			removeValidateFromRules();
			if ($("#jobsForm").valid()) {
				if(rowSize>0){
					var ulr = "/jobsManager/addJobs";
					ajaxSubmitForm(ulr, "", putData, 0);
					popMsg("处理成功");
				}
			}
		});
	}
	

	
});

//Ajax回调函数
function callBack(data) {
	if (data) {
		$.fn.zTree.init($("#orgTree"), setting, data);
		var curOrgName = $("#orgNameID").val();
		$("#orgTitleID").text("");
		$("#curOrgNameID").text("");
		$("#orgNameID").val("");
		$("#parentID").val("");
		$("#orgNoID").val("");
		$("#orgTypeID").val("");
		$("#ID").val("");
		var treeObj = $.fn.zTree.getZTreeObj("orgTree");
		var nodes =  treeObj.transformToArray(treeObj.getNodes());
		for (var i = 0; i < nodes.length; i++) {
			
			if(!nodes[i].isParent){
				nodes[i].iconSkin = "level";
				treeObj.updateNode(nodes[i]);
			}
			
		}

		//删除一级目录时
		if(selectNode == null){
			selectNode = nodes[0];
		}else {
			if(addMethod!=3){
				selectNode = treeObj.getNodeByParam("orgName", curOrgName, null);
			}else{
				selectNode = treeObj.getNodeByParam("id", selectNode.id, null);
			}
		}
		treeObj.expandNode(selectNode, true, false, true);
		treeObj.selectNode(selectNode);
		setFormValue(selectNode);
		
		popMsg("处理成功");
	} else {
		popAlert("修改失败");
	}
	
	// 处理完组织机构节点 取得岗位数据 
	getInfoData();
}

//树点击函数
function orgTreeOnClick(event, treeId, zNodes) {
	var treeObj = $.fn.zTree.getZTreeObj("orgTree");
	var nodes = treeObj.getSelectedNodes();

	if (nodes.length > 0) {
		selectNode = nodes[0];
	}
	
	setFormValue(zNodes);
	// 取得岗位数据
	getInfoData();
}

//组织机构赋值
function setFormValue(zNodes){
	$("#orgTitleID").text(" " + zNodes.orgName);
	$("#curOrgNameID").text(zNodes.orgName);
	$("#parentID").val(zNodes.pOrgNo);
	$("#ID").val(zNodes.id);
	$("#orgNoID").val(zNodes.orgNo);
	$("#orgTypeID").val(zNodes.orgType);
}

// 取得岗位数据
function getInfoData() {
	if ($("#ID").val() != "") {
		//2017.10.2 by zhanghaoyu start
//		var param = "id=" + $("#ID").val();
		var param = {
				id : $("#ID").val()
		}
		//2017.10.2 by zhanghaoyu end
		ajaxData("/jobsManager/selectJobs", param, function(data) {putData(data)});
	} else {
		popAlert("请选择公司机构");
	}
}


//将取得的数据放到岗位列表中
function putData(data) {
	//alert("---putData前==rowSize----"+rowSize);
	var jobsList = data.jobsList;
	//清空table
	$("#jobsList").html(defalt);
	for (var i = 0; i < jobsList.length; i++) {
		// 取得行的模板
		var template = $.trim($("#template").html());
		var $newRow = $(template.replace(new RegExp("\\[0\\]", "g"),"[" + i + "]").replace(new RegExp("jobsList0", "g"),"jobsList[" + i + "]"));

		$newRow.data('index', i);
		// 往table里动态添加一行
		$newRow.appendTo("#jobsList");
		$("input[name='jobsList[" + i + "].id']").val(jobsList[i].id);
		$("input[name='jobsList[" + i + "].orgId']").val(jobsList[i].orgId);
		//序号
		$newRow.find("span[id='orderNum']").text(i+1);
		$("input[name='jobsList[" + i + "].jobsName']").val(jobsList[i].jobsName);
		$("input[name='jobsList[" + i + "].nickName']").val(jobsList[i].nickName);
		$("input[name='jobsList[" + i + "].jobsName']").attr("readonly",true);
		$("input[name='jobsList[" + i + "].nickName']").attr("readonly",true);
	}
	
	rowSize = jobsList.length;
	//alert("---putData后==rowSize----"+rowSize);
}

//为table中每行添加校验规则
function validateRowRules($trObj, index) {
	$("input[name*='jobsName']", $trObj).rules("add", {
		required : true,
		maxlength : 30
	});
	$("input[name*='nickName']", $trObj).rules("add", {
		required : true,
		maxlength : 30
	});
}

//为组织机构名称添加校验规则
function addValidateFromRules(){
	$("#orgNameID").rules("add", {
		required : true,
		maxlength : 30
	});
}

//去除校验规则
function removeValidateFromRules(){
	validator.resetForm();
}

function divShow(obj){
	$("#"+obj).show();
}

function divHidden(obj){
	$("#"+obj).hide();
}