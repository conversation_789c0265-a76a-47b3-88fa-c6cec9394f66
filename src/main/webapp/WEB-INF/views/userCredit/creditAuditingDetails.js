$(document).ready(function () {

    remarkInit()
    $('#Button1').click(function () {
        $('#myModal').modal('show');
    });
    $('#btnCheck').click(function () {
        if (getValue($('#remark').val()).length>200){
            popMsg("审核备注不可以超过200字");
            return;
        }
        popConfirm("审核后不可重复修改,确认审核?", function () {
            creditAudit();
        });
    });
    $("#btnClose").bind("click", function () {
        closeWinCallBack();
    });
});

function remarkInit(){
    if ($("#auditStatus").val() == '2'){
        document.getElementById("remarkDiv").style.visibility = "visible";
    }else {
        remarkStr =  $("#remark").val()
    }
}
function creditAudit(){
    let param = {
        id:$("#id").val(),
        userId:$("#userId").val(),
        businessId:$("#businessId").val(),
        auditStatus:$("#auditStatus").val(),
        failureContent:$("#remark").val(),
        businessType:$("#businessType").val()
    };
    ajaxData("/creditAuditing/creditAudit", param, function (res) {
        debugger
        if (res.num === 1) {
            closeWinCallBack()
        }else {
            parent.popAlert("审核失败！")
        }
    });
}

var remarkStr = ''
function selState(value) {
    if (value == '1') {
        $("#remarkDiv").css("visibility", "hidden");
        remarkStr =  $("#remark").val()
        $("#remark").val("")
    } else if (value == '2'){
        $("#remark").val(remarkStr)
        $("#remarkDiv").css("visibility", "visible");
    }else{
        $("#remarkDiv").css("visibility", "hidden");
        remarkStr =  $("#remark").val()
        $("#remark").val("")
    }
}
function imgeClick(index) {
    const viewer = new Viewer(document.querySelectorAll(".ImgPr")[index], {
        inline: false
    });
}

