$(document).ready(function() {
    dateInit();
    tSelectInit();
    $("#queryBtn").bind("click", function() {
        tableQuery();
    });
    $("#btnClear").bind("click", function() {
        document.getElementById("queryForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery();
    });
    //统计导出
    $("#exportQuery").bind("click", function() {
        exportStatisticsTableData();
    });

    $('#exportGgDel').bind("click",function () {
        // 清空表单数据
        $('#exportUserType').val('');
        $('#exportFieIdName').val('');
        $('input[name="exportCourseIds"]').val('');
        $('#exportCourseIds').val('');
        // 显示模态对话框
        $('#exportGgDelModal').modal('show');
        // 重新初始化课程选择下拉框
        initCourseSelect();
    });

    // 确认导出按钮点击事件
    $('#btnExportGgDel').bind("click",function () {
        var userType = $('#exportUserType').val();
        var fieIdName = $('#exportFieIdName').val();
        var courseIds = $('input[name="exportCourseIds"]').val();

        // 执行导出
        window.open(contextPath + "/learningRecord/exportGgDel?" + "userType=" + userType + "&fieIdName=" + fieIdName + "&courseIds=" + courseIds);

        // 关闭模态对话框
        $('#exportGgDelModal').modal('hide');
    });

});

function exportStatisticsTableData(){
    let userName = $('#userName').val();
    let companyCode = $('#companyCode').val();
    let companyName = $('#companyName').val();
    let obtainingCreditTimeStr = $('#obtainingCreditTimeStr').val();
    let courseCreditTypes=$('input[name="courseCreditTypes"]').val();
    let postTypes=$('input[name="postTypes"]').val();
    let phone =  $('#phone').val();
    window.open(contextPath + "/userCredit/exportUserCredit?userName="+userName+"&companyCode="+companyCode+"&companyName="+companyName+"&obtainingCreditTimeStr="+obtainingCreditTimeStr+"&courseCreditTypes="+courseCreditTypes+"&postTypes="+postTypes+"&phone="+phone);
}



/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("tableAll", "/userCredit/getUserCreditList", $("#queryForm").formSerialize());
}
function columnOperation(data, type, row, meta) {
    let obtainingCreditTimeStr = $('#obtainingCreditTimeStr').val();
    let courseCreditTypes=$('input[name="courseCreditTypes"]').val();
    let content = '';
    content += '<a href="javascript:void(0)" onclick="openCreditDetails(\'' + data.userId + '\',\'' + "1" + '\',\'' + obtainingCreditTimeStr + '\',\'' + courseCreditTypes + '\')" title="查看详情">查看</a>';

    return content;
}
function openCreditDetails(userId,ifCredit,obtainingCreditTimeStr,courseCreditTypes){
    let param={
        userId:userId,
        ifCredit:ifCredit,
        obtainingCreditTimeStr:obtainingCreditTimeStr,
        courseCreditTypes:courseCreditTypes
    }
    parent.popWin('学分详情','/userCredit/openUserCreditDetails', param, '58%', '90%');
}


function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseCreditTypes').tselectInit(null, teaSelectOptions);
    $('#postTypes').tselectInit(null, teaSelectOptions);
}
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function dateInit() {
    var obtainingCreditTimeStr = $('#obtainingCreditTimeStr').val();
    if (obtainingCreditTimeStr.trim() != '') {
        var creditTimes = obtainingCreditTimeStr.split(' 至 ');
        dataRangePickerInit($('#obtainingCreditTimeStr'), creditTimes[0], creditTimes[1], function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    } else {
        dataRangePickerInit($('#obtainingCreditTimeStr'), null, null, function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    }
}
function LinkageEndDate(istg) {
    return {
        festival: true,
        isClear: false,
        trigger : istg || "click",
        format: 'YYYY-MM-DD hh:mm:ss',
        minDate: function (that) {
            return _endDate.minDate ;
        },
        maxDate: '2099-12-31 23:59:59',
        donefun: function(obj){
            _startDate.maxDate = obj.val;
        }
    };
}

// 初始化课程选择下拉框
function initCourseSelect() {
    // 导出模态框中的课程下拉框初始化
    var exportCourseSelectOptions = {
        id: 'businessId',
        name: 'businessName',
        value: 'businessId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#exportCourseIds').tselectInit(null, exportCourseSelectOptions);
}
