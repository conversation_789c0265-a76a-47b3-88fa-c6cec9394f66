$(document).ready(function() {
    dateInit();
    tSelectInit();
    $("#queryBtn").bind("click", function() {
        addLoading();
        queryCreditdetail();
    });
    $("#btnClear").bind("click", function() {
        debugger
        document.getElementById("queryForm").reset();
        $('input[name="courseCreditTypes"]').val("");
        $('[name="ifCredit"]:checked').val("");
        var html =""
        html+="<div id=\"creditSelect\" class=\"col-md-6\" style=\"margin-left: -10px\">";
        html+="<input type=\"radio\" name=\"ifCredit\" value=\"1\"  />是";
        html+="<input type=\"radio\" name=\"ifCredit\" value=\"0\" style=\"margin-left: 15px;\" />否";
        html+="</div>"
        $("#creditSelect").html(html);
        addLoading();
        queryCreditdetail();
    });
    //统计导出
    $("#exportQuery").bind("click", function() {
        exportStatisticsTableData();
    });
});

function exportStatisticsTableData(){
    let userId = $('#userId').val();
    let obtainingCreditTimeStr =  $('#obtainingCreditTimeStr').val();
    let courseCreditTypes = $('input[name="courseCreditTypes"]').val();
    let ifCredit = $('[name="ifCredit"]:checked').val();
    window.open(contextPath + "/userCredit/exportUserCreditDetails?userId="+userId+"&obtainingCreditTimeStr="+obtainingCreditTimeStr+"&courseCreditTypes="+courseCreditTypes+"&ifCredit="+ifCredit);
}
function queryCreditdetail(){
    let url = '/userCredit/getUserCreditDetails';
    let param= {
        userId : $('#userId').val(),
        obtainingCreditTimeStr: $('#obtainingCreditTimeStr').val(),
        courseCreditTypes: $('input[name="courseCreditTypes"]').val(),
        ifCredit: $('[name="ifCredit"]:checked').val()
    }
    $.ajax({
        url: contextPath + url,
        data: JSON.stringify(param),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (res) {
            debugger
            $("#userId").val(res.result.userId);
            $("#creditSum").html(res.result.creditSum);
            var html = "";
            if (res.result.courseList.length>0){
                for (var i = 0; i < res.result.courseList.length; i++) {
                    html += "<div class='picdiv'>";
                    html += "<div class='col-md-5'>";
                    html += "<img id='coursePicImg' src='" + res.result.courseList[i].coursePicUrl + "' height='170px' width='320px'/>";
                    html += "</div>";
                    html += "<div class='col-md-6'>";
                    html += "<div style='margin-bottom: 40px'>课程名称：" + res.result.courseList[i].courseName + "</div>";
                    html += "<div style='margin-top: 40px'>学分：" + res.result.courseList[i].courseCreditStr + "</div>";
                    html += "</div>";
                    html += "</div>";
                }
            }else{
                html += "<div style=' width: 90%; margin: 0 auto;  display: flex; align-items: center;'>暂无数据</div>";
            }
            $("#courseList").html(html);
            removeLoading();
        }
    });
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseCreditTypes').tselectInit(null, teaSelectOptions);
}
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function dateInit() {
    var obtainingCreditTimeStr = $('#obtainingCreditTimeStr').val();
    if (obtainingCreditTimeStr.trim() != '') {
        var creditTimes = obtainingCreditTimeStr.split(' 至 ');
        dataRangePickerInit($('#obtainingCreditTimeStr'), creditTimes[0], creditTimes[1], function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    } else {
        dataRangePickerInit($('#obtainingCreditTimeStr'), null, null, function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    }
}
function LinkageEndDate(istg) {
    return {
        festival: true,
        isClear: false,
        trigger : istg || "click",
        format: 'YYYY-MM-DD hh:mm:ss',
        minDate: function (that) {
            return _endDate.minDate ;
        },
        maxDate: '2099-12-31 23:59:59',
        donefun: function(obj){
            _startDate.maxDate = obj.val;
        }
    };
}
