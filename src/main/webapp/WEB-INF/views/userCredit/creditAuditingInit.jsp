<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>学分审核</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<script>
</script>
<body>
<div class="panel" style="min-height: 700px">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  学分审核</i>
    </div>
    <div class="panel-body" >
        <form:form modelAttribute="ExchangeReviewDto" id="queryForm" >
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">姓名：</label>
                <div class="col-md-3">
                    <form:input path="userName" placeholder="请输入姓名"  cssClass="form-control" autocomplete="off"/>
                </div>
                <label class="col-md-1 control-label" style="text-align:center">股票代码：</label>
                <div class="col-md-3">
                    <form:input path="companyCode" placeholder="请输入股票代码" cssClass="form-control" autocomplete="off" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center">公司名称：</label>
                <div class="col-md-3">
                    <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">审核状态：</label>
                <div class="col-md-3">
                    <input id="auditStatus" type="text" class="t-select" placeholder="请选择审核状态" json-data='${creditAuditingList}' selected-ids="${ExchangeReviewDto.auditStatus}"/>
                    <input name="auditStatus" id="auditStatu" type="hidden" placeholder="请选择审核状态" />
                </div>
                <label class="col-md-1 control-label"  style="text-align:center">申请课程：</label>
                <div class="col-md-3">
                    <input id="courseIds" type="text" class="t-select" placeholder="请选择申请课程" json-data='${courseSelectList}' />
                    <input name="courseIds" id="courseId" type="hidden" placeholder="请选择申请课程" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="queryBtn" class="btn btn-primary">查询</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/creditAuditing/getExchangeReviewDtoList?auditStatus=${ExchangeReviewDto.auditStatus}" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="姓名" displayColumn="userName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="课程名称" displayColumn="businessName" orderable="false"
                              cssClass="text-center" cssStyle="width:35%" />
                <e:gridColumn label="课程类型" displayColumn="businessTypeStr" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="审核状态" displayColumn="auditStatus" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>

</div>
</body>
</html>
