<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>学分管理</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<script>
</script>
<body>
<div class="panel" style="min-height: 700px">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  学分管理</i>
    </div>
    <div class="panel-body" >
        <form:form modelAttribute="creditRecordDto" id="queryForm" >
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">姓名：</label>
                <div class="col-md-3">
                    <form:input path="userName" placeholder="请输入姓名"  cssClass="form-control" autocomplete="off"/>
                </div>
                <label class="col-md-1 control-label" style="text-align:center">股票代码：</label>
                <div class="col-md-3">
                    <form:input path="companyCode" placeholder="请输入股票代码" cssClass="form-control" autocomplete="off" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center">公司名称：</label>
                <div class="col-md-3">
                    <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">时间范围：</label>
                <div class="col-md-3 daterange">
                    <form:input path="obtainingCreditTimeStr" placeholder="请选择时间范围" cssClass="form-control" />
                </div>
                <label class="col-md-1 control-label"  style="text-align:center">课程类型：</label>
                <div class="col-md-3">
                    <input id="courseCreditTypes" type="text" class="t-select" placeholder="请选择课程类型" json-data='${courseCreditTypeList}' />
                    <input name="courseCreditTypes" id="courseCreditType" type="hidden" placeholder="请选择课程类型" />
                </div>
                <label class="col-md-1 control-label"  style="text-align:center">选择职务：</label>
                <div class="col-md-3">
                    <input id="postTypes" type="text" class="t-select" placeholder="请选择职务" json-data='${postTypeList}' />
                    <input name="postTypes" id="postType" type="hidden" placeholder="请选择课程类型" />
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">手机号：</label>
                <div class="col-md-3">
                    <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <span id="exportGgDel" class="btn btn-default">批量导出高管学分明细</span>
                    <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="queryBtn" class="btn btn-primary">查询</span>
                    <span id="exportQuery" class="btn btn-primary">导出</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/userCredit/getUserCreditList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="姓名" displayColumn="userName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:30%" />
                <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                              cssClass="text-center" cssStyle="width:15%" />
                <e:gridColumn label="职务" displayColumn="post" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="获得总学分" displayColumn="creditSum" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
    <!-- 高管学分明细导出模态对话框 -->
    <div class="modal fade" id="exportGgDelModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title">批量导出高管学分明细</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <label class="col-md-3 control-label">导出课程：</label>
                            <div class="col-md-9">
                                <input id="exportCourseIds" class="t-select" placeholder="请选择需要导出的课程" json-data='${courseSelectList}' />
                                <input name="exportCourseIds" id="exportCourseId" type="hidden" placeholder="请选择需要导出的课程" />
                                <small class="text-muted">未选择课程时默认导出全部课程</small>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 15px; display: none;">
                        <div class="col-md-12">
                            <label class="col-md-3 control-label">字段名称：</label>
                            <div class="col-md-9">
                                <input id="exportFieIdName" name="exportFieIdName" type="text" class="form-control" placeholder="请输入逗号拼接的字段名称"/>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 15px; display: none;">
                        <div class="col-md-12">
                            <label class="col-md-3 control-label">用户类型：</label>
                            <div class="col-md-9">
                                <input id="exportUserType" name="exportUserType" type="text" class="form-control" placeholder="请输入用户类型"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnExportGgDel" class="btn btn-primary">确认导出</button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
