//@ sourceURL=userManage.js

var SchUserInfoList = [];
$(document).ready(function () {
    $('#btnCheck').click(function () {
        //修改审核状态
        userAdminAudit();
        //刷新表格数据
        search();
    });

    // 下拉初始化
    tSelectInit();
    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
        $('input[name="post"]').val("");
        $('input[name="adminAuditStr"]').val("");
        $('#post').val("");
        $('#adminAuditStr').val("");
        $('#phone').val("");
        $('#realName').val("");
        $('#companyCode').val("");
        $('#companyName').val("");
        search();
    });
});

//修改审核状态
function userAdminAudit() {
    debugger
    let param = {
        id:$("#userId").val(),
        selectAdminAudit:$('#selectAdminAudit').val()
    };
    ajaxData("/userCredit/userAdminAudit", param, function (res) {
        if (res) {
            $('#myModal').modal('hide');
        }else {
            parent.popAlert("审核失败！")
        }
    });
}

function search() {
    ajaxTableQuery("tableAll", "/userCredit/getAdminAuditList", $("#queryForm").formSerialize());
}

function userName(data, type, row, meta) {
    let content = '<a href="javascript:void(0)" onclick="personDetail(\''+ data.id +'\')">' + data.realName + '</a>';
    return content;
}
function personDetail(id) {
    var param ={
        id:id,
        auditBtn:'1'
    };
    parent.popWin('查看用户信息', '/trainUserManage/addUserInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);
}
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

function callBackAddUser() {
    ajaxTableReload("tableAll",false);
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#post').tselectInit(null, teaSelectOptions);
    $('#adminAuditStr').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function columnOperation(data, type, row, meta) {
    let str = '';
    str += '<a href="#"  style="margin-right:4px;" onclick="showAuditDiv(\'' + data.id + '\',\'' + data.selectAdminAudit + '\')">审核</a>';
    if(!str) {
        str += "--"
    }
    return str;
}

function showAuditDiv(id,selectAdminAudit){
    $('#userId').val(id);
    $('#selectAdminAudit').val(selectAdminAudit);
    $('#myModal').modal('show');
}


