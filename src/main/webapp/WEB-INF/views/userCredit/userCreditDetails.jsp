<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>学分详情</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
        .picdiv{
            border: 1px solid black;
            height: 190px;
            width: 90%;
            margin: 0 auto;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
    </style>
</head>
<script>
</script>
<body>
<div class="panel" style="min-height: 750px">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  学分详情</i>
    </div>
    <div class="panel-body" >
        <form:form modelAttribute="creditRecordDto" id="queryForm" >
            <form:hidden path="userId" id="userId"/>
            <form:hidden path="ifCredit" />
            <div class="row">
                <div class="col-md-6 ">
                    <label class="col-md-2 control-label" style="text-align:center">时间范围：</label>
                    <div class="col-md-8 daterange">
                        <form:input path="obtainingCreditTimeStr" placeholder="请选择时间范围" cssClass="form-control" />
                    </div>
                </div>
                <div class="col-md-6 ">
                    <label class="col-md-2 control-label"  style="text-align:center">课程类型：</label>
                    <div class="col-md-8">
                        <input id="courseCreditTypes" type="text" class="t-select" placeholder="请选择课程类型" json-data='${courseCreditTypeList}' selected-ids='${creditRecordDto.courseCreditTypes}'/>
                        <input name="courseCreditTypes" id="courseCreditType" type="hidden" placeholder="请选择课程类型" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label class="col-md-3 control-label" style="margin-top: 5px">是否获得学分：</label>
                    <div id="creditSelect" class="col-md-6" style="margin-top: 10px">
                        <input type="radio" name="ifCredit" value="1"  checked/>是
                        <input type="radio" name="ifCredit" value="0" style="margin-left: 15px;" />否
                    </div>
                </div>
                <div class="col-md-6 ">
                    <label class="col-md-12 control-label" style="margin-top: 5px">当前总学分：  <span id="creditSum">${creditRecordDto.creditSum}</span></label>

                </div>
            </div>
            <div class="row">
                <div class="col-md-11 text-right" >
                    <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="queryBtn" class="btn btn-primary">查询</span>
                    <span id="exportQuery" class="btn btn-primary">导出</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body" id="courseList">
        <c:if test="${empty creditRecordDto.courseList}">
            <div style='width: 90%; margin: 0 auto; display: flex; align-items: center;'>暂无数据</div>
        </c:if>
        <c:forEach var="item" items="${creditRecordDto.courseList}" varStatus="status">
            <div class="picdiv">
                <div class="col-md-5" >
                    <img id="coursePicImg" src="${item.coursePicUrl}" height="170px" width="320px"/>
                </div>
                <div class="col-md-6">
                    <div style="margin-bottom: 40px">课程名称：${item.courseName}</div>
                    <div style="margin-top: 40px">学分：${item.courseCreditStr}</div>
                </div>
            </div>
        </c:forEach>
    </div>
</div>
</body>
</html>
