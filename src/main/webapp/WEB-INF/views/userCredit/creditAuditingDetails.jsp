<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>学分审核详情</title>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/viewer.js"></script>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/viewer.css">
    <e:base/>
    <e:js/>
    <style>

        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }

    </style>
</head>
<script>
</script>
<body>

<div class="panel" style="min-height: 750px">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  学分详情</i>
    </div>
    <div class="panel-body" >
        <form:form modelAttribute="exchangeReviewDto" id="queryForm" >
            <form:hidden path="id" id="id"/>
            <form:hidden path="userId" id="userId"/>
            <form:hidden path="businessId" id="businessId"/>
            <form:hidden path="businessType" id="businessType"/>


            <div class="row" >
                <div class="col-xs-2 ">
                    <div class="col-xs-3"  ></div>
                    <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">用户姓名：</label>
                </div>
                <div class="col-xs-10 ">
                    <span style="font-size: 15px;text-align:left;">${exchangeReviewDto.userName}</span>
                </div>
            </div>
            <div class="row" >
                <div class="col-xs-2 ">
                    <div class="col-xs-3"  ></div>
                    <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">手机号：</label>
                </div>
                <div class="col-xs-10 ">
                    <span style="font-size: 15px;text-align:left;">${exchangeReviewDto.phone}</span>
                </div>
            </div>
            <div class="row" >
                <div class="col-xs-2 ">
                    <div class="col-xs-3"  ></div>
                    <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">股票代码：</label>
                </div>
                <div class="col-xs-10 ">
                    <span style="font-size: 15px;text-align:left;">${exchangeReviewDto.companyCode}</span>
                </div>
            </div>
            <div class="row" >
                <div class="col-xs-2 ">
                    <div class="col-xs-3"  ></div>
                    <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">公司名称：</label>
                </div>
                <div class="col-xs-10 ">
                    <span style="font-size: 15px;text-align:left;">${exchangeReviewDto.companyName}</span>
                </div>
            </div>
            <div class="row" >
                <div class="col-xs-2 ">
                    <div class="col-xs-3"  ></div>
                    <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">申请课程：</label>
                </div>
                <div class="col-xs-10 ">
                    <span style="font-size: 15px;text-align:left;">${exchangeReviewDto.businessName}</span>
                </div>
            </div>
            <div class="row" >
                <div class="col-xs-2 ">
                    <div class="col-xs-3"  ></div>
                    <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">证明来源：</label>
                </div>
                <div class="col-xs-10 ">
                    <span style="font-size: 15px;text-align:left;">${exchangeReviewDto.sourceContent}</span>
                </div>
            </div>
    </div>

    <div class="panel-body" id="picImgDiv">
        <c:if test="${exchangeReviewDto.picImgList!=null && exchangeReviewDto.picImgList.size()>0}">
        <div class="row" style="display: flex;align-items: center">
            <div class="col-xs-2 ">
                <div class="col-xs-3"  ></div>
                <label class="col-xs-9 control-label" style="text-align:left;font-size: 15px">证明图片：</label>
            </div>
            <div class="col-xs-10 ">
                <span style="font-size: 12px;color: #990000">(注:双击可放大查看图片)</span>
            </div>
        </div>
        </c:if>
        <div class="row" >
            <div class="col-xs-2 "></div>
            <div class="col-xs-10" >
                <c:forEach var="item" items="${exchangeReviewDto.picImgList}" varStatus="status">
                    <img  class="ImgPr" src="${item.attUrl}" style="height:300px;width:300px;padding:10px;position:relative;cursor:pointer"  onclick="imgeClick(${status.index})"/>
                </c:forEach>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-center"  style="margin-top: 25px">
                <c:if test="${exchangeReviewDto.auditStatus eq '1'}">
                    <input id="Button1" type="button" value="审核" class="btn btn-primary"/>
                </c:if>
                <span id="btnClose" class="btn btn-default" style="margin-left: 30px">关闭</span>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="myModal"  >
    <div class="modal-dialog">
        <div class="modal-content">
            <%--用户管理保存用户信息权限--%>
            <div class="modal-header">
                <button type="button" class="close"  >
                </button>
                <h4 class="modal-title" id="myModalLabel">
                    审核
                </h4>
            </div>
            <div class="modal-body">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">审核结果：</label>
                <div id="selectCheck" class="col-md-10 no-padding" style="margin-bottom: 10px;">
                    <form:select path="auditStatus" onchange="selState(this.value)" cssClass="form-control">
                        <c:forEach var="map" items="${creditAuditingList}" varStatus="index">
                            <form:option  value="${map.codeValue}">${map.codeName}</form:option>
                        </c:forEach>
                    </form:select>
                </div>
                <div id="remarkDiv" style="visibility: hidden">
                    <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">备注：</label>
                    <div class="col-md-11">
                        <textarea id="remark" type="text" class="form-control" value="${exchangeReviewDto.failureContent}">${exchangeReviewDto.failureContent}</textarea>
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">关闭
                </button>
                <button type="button" id="btnCheck" class="btn btn-primary">
                    确认
                </button>
            </div>
        </div>
    </div>
</div>
</form:form>
</body>
</html>
