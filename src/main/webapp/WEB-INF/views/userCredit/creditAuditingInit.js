$(document).ready(function() {
    tSelectInit();
    $("#queryBtn").bind("click", function() {
        tableQuery();
    });
    $("#btnClear").bind("click", function() {
        document.getElementById("queryForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery();
    });

});
/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("tableAll", "/creditAuditing/getExchangeReviewDtoList", $("#queryForm").formSerialize());
}

function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
function columnOperation(data, type, row, meta) {
    let content = '';
    content += '<a href="javascript:void(0)" onclick="openCreditAuditingDetails(\'' + data.id + '\')" title="审核">审核</a>';
    // content += ' | ' + '<a href="javascript:void(0)" onclick="deleteCreditAuditing(\'' + data.id + '\')" title="删除">删除</a>';
    return content;
}

function openCreditAuditingDetails(id){
    let param={
        id:id
    }
    parent.popWin('审核详情','/creditAuditing/openCreditAuditingDetails', param, '50%', '90%',callBackAddUser,'',callBackAddUser);
}
function callBackAddUser() {
    ajaxTableReload("tableAll",false);
}

function deleteCreditAuditing(id){
    popConfirm("确认删除该申请吗？",function(){
        var param = {
            id:id,
            status:"0"
        };
        ajaxData("/creditAuditing/delCreditAuditing",param,function(data) {
            if(data > 0) {
                ajaxTableReload("tableAll",false);
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}
//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#auditStatus').tselectInit(null, teaSelectOptions);

    var courseSelectOptions = {
        id: 'businessId',
        name: 'businessName',
        value: 'businessId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        courseIds: tSelectSubmitCallBack
    };
    $('#courseIds').tselectInit(null, courseSelectOptions);

}
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
