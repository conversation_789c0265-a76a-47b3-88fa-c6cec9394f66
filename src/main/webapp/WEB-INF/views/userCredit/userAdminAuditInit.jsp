<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<script>
</script>
<body>
<div class="panel" style="min-height: 650px">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">  管理员审核</i>
    </div>
    <div class="panel-heading">
        <form:form modelAttribute="schUserInfoDto" id="queryForm" >
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">姓名：</label>
                    <div class="col-md-3">
                        <form:input path="realName" placeholder="请输入姓名"  cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">股票代码：</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入股票代码" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">公司名称：</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">职务：</label>
                    <div class="col-md-3">
                        <input id="post" type="text" class="t-select" json-data='${postList}'  placeholder="请选择职务"/>
                        <input name="post" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">手机号：</label>
                    <div class="col-md-3">
                        <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">审核状态：</label>
                    <div class="col-md-3">
                        <input id="adminAuditStr" type="text" class="t-select" json-data='${adminAuditStatusList}'  placeholder="请选择审核状态"/>
                        <input name="adminAuditStr" type="hidden" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="btnQuery" class="btn btn-primary">查询</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/userCredit/getAdminAuditList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="姓名" renderColumn="userName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="职务" displayColumn="post" orderable="false"
                              cssClass="text-center" cssStyle="width:7%" />
                <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="审核状态" displayColumn="adminAuditStr" orderable="false"
                              cssClass="text-center" cssStyle="width:7%" />
                <e:gridColumn label="注册时间" displayColumn="createDate" orderable="true" orderColumn="createDate"
                              cssClass="text-center" cssStyle="width:8%;" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</div>
<div class="modal fade" id="myModal"  >
    <form:form modelAttribute="auditSchUserInfoDto" id="auditQueryForm">
        <form:hidden path="id" id="userId"></form:hidden>
    <div class="modal-dialog">
        <div class="modal-content">
            <%--用户管理保存用户信息权限--%>
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">
                    审核
                </h4>
            </div>
                <div class="modal-body" style="height: 140px">
                    <label class="col-md-2 no-padding control-label" style=" width: 100px;">审核结果：</label>
                    <div id="selectCheck" class="col-md-10 no-padding" style="margin-bottom: 10px;">
                        <form:select path="selectAdminAudit"  cssClass="form-control">
                            <c:forEach var="map" items="${adminAuditStatusMap}" varStatus="index">
                                <form:option  value="${map.codeValue}">${map.codeName}</form:option>
                            </c:forEach>
                        </form:select>
                    </div>
                </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">关闭
                </button>
                <button type="button" id="btnCheck" class="btn btn-primary">
                    确认
                </button>
            </div>
        </div>
    </div>
    </form:form>
</div>
</body>
</html>
