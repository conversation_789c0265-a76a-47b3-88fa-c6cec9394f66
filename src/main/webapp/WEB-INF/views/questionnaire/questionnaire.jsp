<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="referrer" content="no-referrer" charset="UTF-8">
    <script>
        var serviceGuiBaseUrl = '${serviceGuiBaseUrl}';
    </script>
    <e:js />
</head>
<body>
<div style="top: 0px;left: 0px;position: absolute;width: 100%;border:none;">
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_ADD_AUTHORITY_3')" >
        <%--新增问卷权限--%>
        <input type="hidden" id="addAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_RELEASE_AUTHORITY_3')" >
        <%--发布权限--%>
        <input type="hidden" id="releaseAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_UPDATE_AUTHORITY_3')" >
        <%--修改权限--%>
        <input type="hidden" id="updateAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_EDIT_AUTHORITY_3')" >
        <%--编辑权限--%>
        <input type="hidden" id="editAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_REVIEW_AUTHORITY_3')" >
        <%--预览权限--%>
        <input type="hidden" id="reviewAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_DELETE_AUTHORITY_3')" >
        <%--删除权限--%>
        <input type="hidden" id="deleteAuth" value="true">
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_QUESTIONNAIRE_VIEW_RESULT_AUTHORITY_3')" >
        <%--查看结果权限--%>
        <input type="hidden" id="viewResultAuth" value="true">
    </sec:authorize>
    <iframe id="questionnaireIframe" width="100%" style="height: calc(100vh - 94px);"  frameborder="0" src='${serviceGuiBaseUrl}/trainingCenter/questionnaire'></iframe>
</div>
</body>
</html>
