var reHeightTimer;
var reHeightFrame;
var reHeightTag;

$(document).ready(function () {
    updateIframeSrc();
    //动态调整高度
    reHeightFrame = '#questionnaireIframe';
    reHeightTimer = false;
    //reHeightInteval();
});

//调整iframe高度定时器
function reHeightInteval() {
    if (!reHeightTimer) {
        reHeightTag = true;
        reHeightTimer = setInterval(function() {
            if (reHeightTag) {
                adjust_R_e_g_u_l_atoryHeight(reHeightFrame);
            } else {
                closeReHeightInteval();
            }
        }, 500);
    }
}
function adjust_R_e_g_u_l_atoryHeight(frameLabel) {
    if (Object.prototype.toString.call(frameLabel) === "[object String]") {
        frameLabel = '#' + frameLabel.replace(/\#/g, "");
        var frame = $(frameLabel);
        if (frame.length > 0) {
            if (frame[0].contentWindow.$) {
                var frameHeight = frame[0].contentWindow.$("body").height() + 30;
                if (frameHeight < 500) {
                    frame.height(500);
                } else {
                    frame.height(frameHeight);
                }
            }
        }
    }
}
//关闭调整iframe高度定时器
function closeReHeightInteval() {
    clearInterval(reHeightTimer);
    reHeightTimer = false;
}

var serviceGuiBaseUrl = '${serviceGuiBaseUrl}';
function updateIframeSrc() {
    let addAuth = document.getElementById("addAuth") ? 'true' : 'false';
    let releaseAuth = document.getElementById("releaseAuth") ? 'true' : 'false';
    let updateAuth = document.getElementById("updateAuth") ? 'true' : 'false';
    let editAuth = document.getElementById("editAuth") ? 'true' : 'false';
    let reviewAuth = document.getElementById("reviewAuth") ? 'true' : 'false';
    let deleteAuth = document.getElementById("deleteAuth") ? 'true' : 'false';
    let viewResultAuth = document.getElementById("viewResultAuth") ? 'true' : 'false';

    var iframe = document.getElementById("questionnaireIframe");
    var src = serviceGuiBaseUrl + "/trainingCenter/questionnaire";
    var params = [];

    params.push(addAuth == 'true' ? "addAuth=true" : "addAuth=false");
    params.push(releaseAuth == 'true' ? "releaseAuth=true" : "releaseAuth=false");
    params.push(updateAuth == 'true' ? "updateAuth=true" : "updateAuth=false");
    params.push(editAuth == 'true' ? "editAuth=true" : "editAuth=false");
    params.push(reviewAuth == 'true' ? "reviewAuth=true" : "reviewAuth=false");
    params.push(deleteAuth == 'true' ? "deleteAuth=true" : "deleteAuth=false");
    params.push(viewResultAuth == 'true' ? "viewResultAuth=true" : "viewResultAuth=false");

    if (params.length > 0) {
        src += "?" + params.join("&");
    }

    console.log("src: " + src)
    iframe.src = src;
}
