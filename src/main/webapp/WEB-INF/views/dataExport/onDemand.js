$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // 日期初始化
    dateInit();
    $('#btnQuery').click(function () {
        search();
    });
    //清空
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        search();
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/onDemand/onDemandListExport?" + $("#queryForm").formSerialize());
    });
})
function search() {
    ajaxTableQuery("tableAll", "/onDemand/getOnDemand",
        $("#queryForm").formSerialize()+"");
}
function refreshTables() {
    location.reload();
}
function tSelectInit() {
    var teaSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        useFooter: true,
        hiddenInput: true,
        grade: 1,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, teaSelectOptions);


    var tSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#superviseType').tselectInit(null, tSelectOptions);

}


//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

function itemName(data, type, row, meta){
    var str = "无"
    if (data.itemName === null || data.itemName === "") {
        return str
    }else {
        return data.itemName
    }
}

function createTime(data, type, row, meta) {
    if (data.createTime === '' || data.createTime === null) {
        return "- -";
    } else {
        return data.createTime;
    }
}
//日期初始化
function dateInit() {
    dataRangePickerInit($('#createTime'), null, null, function () {
    }, function () {
    });
}
