<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script>

  </script>
  <title>易董 云端管理平台</title>
  <e:base />
  <e:js/>
  <style>
    label{
      padding-top: 7px;
    }
  </style>
</head>
<body>
<div class="panel">
  <div class="panel-heading">
    <form:form modelAttribute="courseSpecialDto" id="queryForm" >
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">专题类型</label>
          <div class="col-md-3">
            <form:select path="specialId" cssClass="form-control">
              <form:option value="">全部</form:option>
              <form:options items="${specialNameList}" itemLabel="specialName" itemValue="specialId" />
            </form:select>
          </div>
          <label class="col-md-1 control-label">级别类型</label>
          <div class="col-md-3">
            <form:select path="gradeName" cssClass="form-control" disabled="true">
              <form:option value="">全部</form:option>
            </form:select>
          </div>
          <label class="col-md-1 control-label">所在辖区</label>
          <div class="col-md-3">
            <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
            <input name="belongCommission" type="hidden" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">证券代码</label>
          <div class="col-md-3">
            <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label">公司名称</label>
          <div class="col-md-3">
            <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label">时间范围</label>
          <div class="col-md-3 daterange">
            <form:input path="createTime" placeholder="时间范围" cssClass="form-control" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-right" >
<%--          <sec:authorize access="hasAuthority('RES_EXPORT_SUBJECT_EXLIST_AUTHORITY_3')">--%>
<%--            <span id="export" class="btn btn-primary">导出</span>--%>
<%--          </sec:authorize>--%>
          <sec:authorize access="hasAuthority('RES_EXPORT_SUBJECT_LIST_AUTHORITY_3')">
            <span id="btnQuery" class="btn btn-primary">查询</span>
          </sec:authorize>
          <span id="btnClear" class="btn btn-default">清空</span>
        </div>
      </div>
    </form:form>
  </div>
  <div class="panel-body">
    <div class="row" style="padding-left: 10px;padding-right: 10px">
      <e:grid id="tableAll" action="/courseSpecial/getCourseSpecial" cssClass="table table-striped table-hover">
        <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                      cssStyle="width:5%" />
        <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                      cssClass="text-center" cssStyle="width:7%" />
        <e:gridColumn label="公司名称/学校" displayColumn="companyName" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="公司代码" renderColumn="companyCodeData" orderable="false"
                      cssClass="text-center" cssStyle="width:5%" />
        <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                      cssClass="text-center" cssStyle="width:7%" />
        <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                      cssClass="text-center" cssStyle="width:8%;" />
        <e:gridColumn label="专题名称" displayColumn="specialName" orderable="false"
                      cssClass="text-center" cssStyle="width:8%;"/>
        <e:gridColumn label="级别类型" renderColumn="typeLevelData" orderable="false"
                      cssClass="text-center" cssStyle="width:7%;" />
        <e:gridColumn label="课程名称" displayColumn="courseName" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="培训时间" displayColumn="createTime" orderable="false"
                      cssClass="text-center" cssStyle="width:10%;"/>
        <e:gridColumn label="辖区" displayColumn="belongCommission" orderable="false"
                      cssClass="text-center" cssStyle="width:5%;"/>
      </e:grid>
    </div>
  </div>
</div>
</body>
</html>
