
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <e:base />
    <style type="text/css">
        .sch-big-div {
            padding-left: 20px;
        }

        .sch-big-font {
            font-size: 22px;
            font-weight: 600;
        }

        .sch-td {
            background-color: #f1f1f1;
            FONT-WEIGHT: 600;
        }

        .sch-btn {
            color: #0a67fb;
            cursor: pointer;
        }

        .sch-sdiv {
            text-align: right;
        }

        .sch-row {
            margin-top: 5px;
        }
        .rectangles{
            width: 6px;
            height: 18px;
            float: left;
            margin-top: 2px;
            background: #1d89cf;
        }
        .bannerClass{
            word-wrap:break-word;word-break:break-all
        }
        .icon-class{
            cursor: pointer;
            margin: 0 5px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="sch-banner sch-big-div">
        <div class="row" style="padding: 10px;">
            <div class="col-md-12 text-right">
                <sec:authorize access="hasAuthority('RES_EXPORT_REGISTER_NUMS_AUTHORITY_3')">
                    <span id="export" class="btn btn-primary">导出</span>
                </sec:authorize>
            </div>
        </div>
        <div class="panel panel-default" id="schBanner">
            <table class="table table-bordered no-margin" id="schBannerTable" style="text-align: center;">
                <thead>
                <tr>
                    <td width="20%" class="sch-td">会员公司(家数)</td>
                    <td width="10%" class="sch-td">数量</td>
                    <td width="20%" class="sch-td">非会员公司(家数)</td>
                    <td width="10%" class="sch-td">数量</td>
                    <td width="20%" class="sch-td">其他</td>
                    <td width="10%" class="sch-td">数量</td>
                </tr>
                </thead>
                <tbody id="schBannerBody">
                <c:forEach items="${datas}" var="item">
                    <tr>
                        <td>上市公司-会员单位</td>
                        <td>${item.comListedVIP}</td>
                        <td>上市公司-非会员单位</td>
                        <td>${item.comListedUNVIP}</td>
                        <td>证监会及派出机构</td>
                        <td>${item.comCSRC}</td>
                    </tr>
                    <tr>
                        <td>其他-会员单位</td>
                        <td>${item.comOtherVIP}</td>
                        <td>其他-非会员单位</td>
                        <td>${item.comOtherUNVIP}</td>
                        <td>学校</td>
                        <td>${item.comStudent}</td>
                    </tr>
                    <tr>
                        <td>非上市公司-会员单位</td>
                        <td>${item.comUnlistedVIP}</td>
                        <td>非上市公司-非会员单位</td>
                        <td>${item.comUnlistedUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>拟上市公司-会员单位</td>
                        <td>${item.comPrelistedVIP}</td>
                        <td>拟上市公司-非会员单位</td>
                        <td>${item.comPrelistedUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>新三板挂牌公司-会员单位</td>
                        <td>${item.comNewlistedVIP}</td>
                        <td>新三板挂牌公司-非会员单位</td>
                        <td>${item.comNewlistedUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>地方上市公司协会-会员单位</td>
                        <td>${item.comLOCVIP}</td>
                        <td>地方上市公司协会-非会员单位</td>
                        <td>${item.comLOCUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
        <br>
        <div class="panel panel-default" id="schBanner2">
            <table class="table table-bordered no-margin" id="schBannerTable2" style="text-align: center;">
                <thead>
                <tr>
                    <td width="20%" class="sch-td">会员人数</td>
                    <td width="10%" class="sch-td">数量</td>
                    <td width="20%" class="sch-td">非会员人数</td>
                    <td width="10%" class="sch-td">数量</td>
                    <td width="20%" class="sch-td">其他</td>
                    <td width="10%" class="sch-td">数量</td>
                </tr>
                </thead>
                <tbody id="schBannerBody2">
                <c:forEach items="${datas}" var="item">
                    <tr>
                        <td>上市公司-会员单位</td>
                        <td>${item.memListedVIP}</td>
                        <td>上市公司-非会员单位</td>
                        <td>${item.memListedUNVIP}</td>
                        <td>证监会及派出机构</td>
                        <td>${item.memCSRC}</td>
                    </tr>
                    <tr>
                        <td>其他-会员单位</td>
                        <td>${item.memOtherVIP}</td>
                        <td>其他-非会员单位</td>
                        <td>${item.memOtherUNVIP}</td>
                        <td>学生</td>
                        <td>${item.memStudent}</td>
                    </tr>
                    <tr>
                        <td>非上市公司-会员单位</td>
                        <td>${item.memUnlistedVIP}</td>
                        <td>非上市公司-非会员单位</td>
                        <td>${item.memUnlistedUNVIP}</td>
                        <td>协会工作人员及委员</td>
                        <td>${item.memWorker}</td>
                    </tr>
                    <tr>
                        <td>拟上市公司-会员单位</td>
                        <td>${item.memPrelistedVIP}</td>
                        <td>拟上市公司-非会员单位</td>
                        <td>${item.memPrelistedUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>新三板挂牌公司-会员单位</td>
                        <td>${item.memNewlistedVIP}</td>
                        <td>新三板挂牌公司-非会员单位</td>
                        <td>${item.memNewlistedUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>地方上市公司协会-会员单位</td>
                        <td>${item.memLOCVIP}</td>
                        <td>地方上市公司协会-非会员单位</td>
                        <td>${item.memLOCUNVIP}</td>
                        <td></td>
                        <td></td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="schUserDto" id="queryForm" >
            <div class="row">
                <div class="col-md-12" >
                    <label class="col-md-1 control-label">所在辖区</label>
                    <div class="col-md-3">
                        <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
                        <input name="belongCommission" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">公司名称</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入用户名称" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">公司代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12" >
                    <label class="col-md-1 control-label">是否为A股上市公司</label>
                    <div class="col-md-3">
                        <form:select path="belongsState" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="1">是</form:option>
                            <form:option value="0">否</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">用户类型</label>
                    <div class="col-md-3">
                        <input id="personType" type="text" class="t-select" json-data='${personTypeList}' placeholder="请选择注册员工类型" />
                        <input name="personType" type="hidden" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_EXPORT_REGISTER_LIST_AUTHORITY_3')">
                        <span id="exportList" class="btn btn-primary">导出</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_EXPORT_SELECT_LIST_AUTHORITY_3')">
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <span id="btnClear" class="btn btn-default">清空</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="grid panel panel-default">
            <e:grid id="tableAll" action="/registerMember/getUserInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center" cssStyle="width:5%" />
                <e:gridColumn label="公司代码" renderColumn="companyCodeData" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false" cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="公司简称" renderColumn="companyShortName" orderable="false" cssClass="text-center" cssStyle="width:15%" />
                <e:gridColumn label="注册总人数" displayColumn="memNumber" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否为A股上市公司" renderColumn="belongsPlate" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="用户类型" displayColumn="personType" orderable="false" cssClass="text-center" cssStyle="width:15%" />
                <e:gridColumn label="所属区域" renderColumn="belongCommission" orderable="false" cssClass="text-center" cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
