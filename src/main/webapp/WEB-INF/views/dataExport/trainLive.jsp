<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script>

  </script>
  <title>易董 云端管理平台</title>
  <e:base />
  <e:js/>
  <style>
    label{
      padding-top: 7px;
    }
  </style>
</head>
<body>
<div class="panel">
  <div class="panel-heading">
    <form:form modelAttribute="trainLiveDto" id="queryForm" >
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">直播名称</label>
          <div class="col-md-3">
            <input id="liveId" type="text" class="t-select" json-data='${liveInfoList}'
                   selected-ids="${coursePackageDto.packageId}" placeholder="请选择直播课程"/>
            <input name="liveId" type="hidden" placeholder="请选择直播" value='${trainLiveDto.liveId}'/>
          </div>
          <label class="col-md-1 control-label">时间范围</label>
          <div class="col-md-3 daterange">
            <form:input path="createTime" placeholder="时间范围" cssClass="form-control" />
          </div>
          <label class="col-md-1 control-label">课程来源</label>
          <div class="col-md-3">
            <form:select path="orgId" cssClass="form-control">
              <form:option value="">请选择</form:option>
              <form:option  selected="true" value="0001">中上协培训平台</form:option>
              <form:option value="9001">独董平台</form:option>
            </form:select>
          </div>
            <label class="col-md-1 control-label">监管课程</label>
            <div class="col-md-3">
                <input id="superviseType" type="text" json-data='${superviseTypeList}' class="t-select isRequired" placeholder="请选择监管课程"/>
                <input name="superviseType" type="hidden" />
            </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-right" >
          <sec:authorize access="hasAuthority('RES_EXPORT_TRAIN_LIVE_EXLIST_AUTHORITY_3')">
            <span id="export" class="btn btn-primary">导出</span>
          </sec:authorize>
          <sec:authorize access="hasAuthority('RES_EXPORT_TRAIN_LIVE_LIST_AUTHORITY_3')">
            <span id="btnQuery" class="btn btn-primary">查询</span>
          </sec:authorize>
          <span id="btnClear" class="btn btn-default">清空</span>
        </div>
      </div>
    </form:form>
  </div>
  <div class="panel-body">
    <div class="row" style="padding-left: 10px;padding-right: 10px">
      <e:grid id="tableAll" action="/trainLive/getTrainLive" cssClass="table table-striped table-hover">
        <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                      cssStyle="width:5%" />
        <e:gridColumn label="直播名称" displayColumn="liveName" orderable="false"
                      cssClass="text-center" cssStyle="width:35%" />
        <e:gridColumn label="课程来源" renderColumn="orgId" orderable="false"
                      cssClass="text-center" cssStyle="width:35%" />
        <e:gridColumn label="开始时间" displayColumn="createTime" orderable="false"
                      cssClass="text-center" cssStyle="width:20%" />
        <e:gridColumn label="观看人数" displayColumn="renShu" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
        <e:gridColumn label="观看人次" displayColumn="renCi" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
        <e:gridColumn label="预约人数" displayColumn="yuYue" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
      </e:grid>
    </div>
  </div>
</div>
</body>
</html>
