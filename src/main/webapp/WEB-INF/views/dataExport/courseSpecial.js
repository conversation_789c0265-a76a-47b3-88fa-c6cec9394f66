$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // 日期初始化
    dateInit();
    $('#btnQuery').click(function () {
        search();
    });
    //清空
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        let str = '<option value="">全部</option>';
        $("#gradeName").attr("disabled", true);
        $('#gradeName').append(str)

        search();
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/courseSpecial/specialListExport?" + $("#queryForm").formSerialize());
    });
    $('#specialId').change(function () {
        getSpecialNameList();
    });
})
function search() {
    ajaxTableQuery("tableAll", "/courseSpecial/getCourseSpecial",
        $("#queryForm").formSerialize()+"");
}
function refreshTables() {
    location.reload();
}
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#belongCommission').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
function companyCodeData(data, type, row, meta){
    var str = "无"
    if (data.companyCode === null || data.companyCode === "") {
        return str
    }else {
        return data.companyCode
    }
}
function typeLevelData(data, type, row, meta){
    var str = "无"
    if (data.typeLevel === null || data.typeLevel === "") {
        return str
    }else {
        return data.typeLevel
    }
}

function createTime(data, type, row, meta) {
    if (data.createTime === '' || data.createTime === null) {
        return "- -";
    } else {
        return data.createTime;
    }
}
//日期初始化
function dateInit() {
    dataRangePickerInit($('#createTime'), null, null, function () {
    }, function () {
    });
}
function getSpecialNameList() {
    let params = {
        specialId: $('#specialId').val(),
    }
    ajaxData("/coursePlayback/getExportGradeTypeList", params, function (res) {
        $('#gradeName').empty();
        let str = '<option value="">全部</option>';
        if ($('#specialId').val() === "" || $('#specialId').val() === null) {
            $("#gradeName").attr("disabled", true);
            $('#gradeName').append(str)
        } else {
            res.forEach(item => {
                str += '<option value="' + item.gradeId + '">' + item.gradeName + '</option>'
            })
            $("#gradeName").attr("disabled", false);
            $('#gradeName').append(str)
        }
    })
}
