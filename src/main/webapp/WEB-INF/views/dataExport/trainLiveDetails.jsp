<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script>

  </script>
  <title>易董 云端管理平台</title>
  <e:base />
  <e:js/>
  <style>
    label{
      padding-top: 7px;
    }
  </style>
</head>
<body>
<div class="panel">
  <div class="panel-heading">
    <form:form modelAttribute="trainLiveDto" id="queryForm" >
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">直播名称</label>
          <div class="col-md-3">
            <input id="liveId" type="text" class="t-select" json-data='${liveInfoList}'
                   selected-ids="${coursePackageDto.packageId}" placeholder="请选择直播课程"/>
            <input name="liveId" type="hidden" placeholder="请选择直播" value='${trainLiveDto.liveId}'/>
          </div>
          <label class="col-md-1 control-label">观看日期</label>
          <div class="col-md-3 daterange">
            <form:input path="createTime" placeholder="观看日期" cssClass="form-control" />
          </div>
          <label class="col-md-1 control-label">所在辖区</label>
          <div class="col-md-3">
            <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
            <input name="belongCommission" type="hidden" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">用户名称</label>
          <div class="col-md-3">
            <form:input path="realName" placeholder="请输入用户名称" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label">用户类型</label>
          <div class="col-md-3">
            <input id="personType" type="text" class="t-select" json-data='${personTypeList}' placeholder="请选择用户类型" />
            <input name="personType" type="hidden" />
          </div>
          <label class="col-md-1 control-label">职务</label>
          <div class="col-md-3">
            <input id="post" type="text" class="t-select" json-data='${postList}' placeholder="请选择职务" />
            <input name="post" type="hidden" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">公司名称</label>
          <div class="col-md-3">
            <form:input path="companyName" placeholder="请输入用户名称" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label">公司代码</label>
          <div class="col-md-3">
            <form:input path="companyCode" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label">岗位</label>
          <div class="col-md-3">
            <form:input path="jobName" placeholder="请输入岗位" cssClass="form-control" autocomplete="off" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12" >
          <label class="col-md-1 control-label">监管课程</label>
          <div class="col-md-3">
            <input id="superviseType" type="text" json-data='${superviseTypeList}' class="t-select isRequired" placeholder="请选择监管课程"/>
            <input name="superviseType" type="hidden" />
          </div>
          <label class="col-md-1 control-label">是否查看独董</label>
          <div class="col-md-3">
            <form:select path="showDuDong" cssClass="form-control">
              <form:option value="">请选择是否查看独董</form:option>
              <form:option value="1">是</form:option>
              <form:option value="0" selected="selected">否</form:option>
            </form:select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-right" >
<%--          <sec:authorize access="hasAuthority('RES_EXPORT_TRAIN_LIVEDE_EXLIST_AUTHORITY_3')">--%>
<%--            <span id="export" class="btn btn-primary">导出</span>--%>
<%--          </sec:authorize>--%>
          <sec:authorize access="hasAuthority('RES_EXPORT_TRAIN_LIVEDE_LIST_AUTHORITY_3')">
            <span id="btnQuery" class="btn btn-primary">查询</span>
          </sec:authorize>
          <span id="btnClear" class="btn btn-default">清空</span>
        </div>
      </div>
    </form:form>
  </div>
  <div class="panel-body">
    <div class="row" style="padding-left: 10px;padding-right: 10px">
      <e:grid id="tableAll" action="/trainLiveDetails/getTrainLiveDetails" cssClass="table table-striped table-hover">
        <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                      cssStyle="width:5%" />
        <e:gridColumn label="用户姓名" displayColumn="realName" orderable="false"
                      cssClass="text-center" cssStyle="width:8%" />
        <e:gridColumn label="公司名称" displayColumn="companyName" orderable="false"
                      cssClass="text-center" cssStyle="width:13%" />
        <e:gridColumn label="公司代码" renderColumn="companyCode" orderable="false"
                      cssClass="text-center" cssStyle="width:8%" />
        <e:gridColumn label="直播名称" displayColumn="liveName" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="观看时长" renderColumn="watchTime" orderable="false"
                      cssClass="text-center" cssStyle="width:9%" />
        <e:gridColumn label="观看日期" displayColumn="createTime" orderable="false"
                      cssClass="text-center" cssStyle="width:9%" />
        <e:gridColumn label="手机号码" displayColumn="phone" orderable="false"
                      cssClass="text-center" cssStyle="width:8%" />
        <e:gridColumn label="辖区" displayColumn="belongCommission" orderable="false"
                      cssClass="text-center" cssStyle="width:6%" />
        <e:gridColumn label="职务" renderColumn="post" orderable="false"
                      cssClass="text-center" cssStyle="width:8%" />
        <e:gridColumn label="岗位" displayColumn="jobName" orderable="false"
                      cssClass="text-center" cssStyle="width:8%" />
        <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
      </e:grid>
    </div>
  </div>
</div>
</body>
</html>
