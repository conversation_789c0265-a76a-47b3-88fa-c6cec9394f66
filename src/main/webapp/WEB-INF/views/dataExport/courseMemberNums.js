$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // 日期初始化
    dateInit();
    $('#btnQuery').click(function () {
        search();
    });
    $('#btnQueryPass').click(function () {
        searchPass();
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/courseMemberNums/courseRenCiExport?" + $("#queryForm").formSerialize());
    });
    $("#exportPass").bind("click",function () {
        window.open(contextPath + "/courseMemberNums/coursePassExport?" + $("#queryFormPass").formSerialize());
    });
    //清空
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        search();
    });
    //清空
    $('#btnClearPass').click(function () {
        document.getElementById("queryFormPass").reset();
        $("#queryFormPass")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        searchPass();
    });
})
function search() {
    ajaxTableQuery("tableAll", "/courseMemberNums/getCourseMemberNums",
        $("#queryForm").formSerialize()+"");
}
function searchPass() {
    ajaxTableQuery("tableAllPass", "/courseMemberNums/getCoursePassNums",
        $("#queryFormPass").formSerialize()+"");
}
function createTime(data, type, row, meta) {
    if (data.createTime === '' || data.createTime === null) {
        return "- -";
    } else {
        return data.createTime;
    }
}
function createTimePass(data, type, row, meta) {
    if (data.createTime === '' || data.createTime === null) {
        return "- -";
    } else {
        return data.createTime;
    }
}
//日期初始化
function dateInit() {
    dataRangePickerInit($('#createTime'), null, null, function () {
    }, function () {
    });
    dataRangePickerInit($('#createTimePass'), null, null, function () {
    }, function () {
    });
}
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#specialName').tselectInit(null, teaSelectOptions);
    $('#belongCommissionPass').tselectInit(null, teaSelectOptions);
    $('#specialNamePass').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
//级别名称
function typeLevelData(data, type, row, meta){
    var str = "无"
    if (data.typeLevel === null || data.typeLevel === "") {
        return str
    }else {
        return data.typeLevel
    }
}

