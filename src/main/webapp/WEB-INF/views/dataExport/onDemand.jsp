<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>

    </script>
    <title>易董 云端管理平台</title>
    <e:base />
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="onDemand" id="queryForm" >
            <div class="row">
                <div class="col-md-12" >
                    <label class="col-md-1 control-label">点播名称</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入点播名称" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">业务类别</label>
                    <div class="col-md-3">
                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeList}' placeholder="请选择业务类别" />
                        <input name="courseType" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">课程发布时间范围</label>
                    <div class="col-md-3 daterange">
                        <form:input path="createTime" placeholder="课程发布时间范围" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12" >
                    <label class="col-md-1 control-label" >监管课程</label>
                    <div class="col-md-3">
                        <input id="superviseType" type="text" json-data='${superviseTypeList}' class="t-select isRequired" placeholder="请选择监管课程"/>
                        <input name="superviseType" type="hidden" />
                    </div>
                    <div class="col-md-8 text-right" >
                        <sec:authorize access="hasAuthority('RES_EXPORT_TRAIN_ONDEMAND_EXLIST_AUTHORITY_3')">
                            <span id="export" class="btn btn-primary">导出</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_EXPORT_TRAIN_ONDEMAND_LIST_AUTHORITY_3')">
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空</span>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row" style="padding-left: 10px;padding-right: 10px">
            <e:grid id="tableAll" action="/onDemand/getOnDemand" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="点播名称" displayColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:35%" />
                <e:gridColumn label="业务类别" renderColumn="itemName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="课程发布时间" displayColumn="releaseTime" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="观看人数" displayColumn="renShu" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="观看人次" displayColumn="renCi" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
