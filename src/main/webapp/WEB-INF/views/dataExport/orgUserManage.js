$(document).ready(function () {
    tSelectInit();
    $('#btnQuery').click(function () {
        search();
    });
    //清空
    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        $('input[name="belongCommission"]').val("");
        $('input[name="belongsPlate"]').val("");
        $('input[name="personType"]').val("");
        $('#belongCommission').val("")
        $('#belongsPlate').val("")
        $('#personType').val("")

        search();
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/registerMember/registerNumsExport");
    });
    //列表导出
    $("#exportList").bind("click",function () {
        window.open(contextPath + "/registerMember/registerListExport?" + $("#queryForm").formSerialize());
    });
})

function refreshTables() {
    location.reload();
}
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
function companyCodeData(data, type, row, meta){
    var str = "无"
    if (data.companyCode === null || data.companyCode === "") {
        return str
    }else {
        return data.companyCode
    }
}
// //上市状态
// function personType(data, type, row, meta){
//     if (data.personTypeList.length < 2) {
//         return data.personType
//     }else {
//         return "上市公司"
//     }
// }
//公司类型
function belongsPlate(data, type, row, meta){
    if (data.belongsPlate === "00" || data.belongsPlate === "01" || data.belongsPlate === "02") {
        return "是"
    }else {
        return "否"
    }
}
//所属区域
function belongCommission(data, type, row, meta){
    var str = "无"
    if (data.belongCommission === null || data.belongCommission === "") {
        return str
    }else {
        return data.belongCommission
    }
}
//公司简称
function companyShortName(data, type, row, meta){
    var str = "无"
    if (data.companyShortName === null || data.companyShortName === "") {
        return str
    }else {
        return data.companyShortName
    }
}
//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#personType').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function search() {
    ajaxTableQuery("tableAll", "/registerMember/getUserInfoList",
        $("#queryForm").formSerialize()+'');
}

