<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base />
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="courseMemberNumsDto" id="queryForm" >
            <div class="row">
                <div class="col-md-12" >
                    <label class="col-md-1 control-label">专题类型</label>
                    <div class="col-md-3">
                        <input id="specialName" type="text" class="t-select" json-data='${specialNameList}' placeholder="请选择专题类型" />
                        <input name="specialName" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">所在辖区</label>
                    <div class="col-md-3">
                        <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区" />
                        <input name="belongCommission" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">时间范围</label>
                    <div class="col-md-3 daterange">
                        <form:input id="createTime" path="createTime" placeholder="时间范围" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_EXPORT_NUMBER_EXLIST_AUTHORITY_3')">
                        <span id="export" class="btn btn-primary">导出</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_EXPORT_NUMBER_LIST_AUTHORITY_3')">
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <span id="btnClear" class="btn btn-default">清空</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row" style="padding-left: 10px;padding-right: 10px">
            <e:grid id="tableAll" action="/courseMemberNums/getCourseMemberNums" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="专题类型" displayColumn="specialName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="培训人次" displayColumn="renCi" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="培训人数" displayColumn="renShu" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</div>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="courseMemberNumsDto" id="queryFormPass" >
            <div class="row">
                <div class="col-md-12" >
                    <label class="col-md-1 control-label">专题类型</label>
                    <div class="col-md-3">
                        <input id="specialNamePass" type="text" class="t-select" json-data='${specialNameListPass}' placeholder="请选择专题类型" />
                        <input name="specialNamePass" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">所在辖区</label>
                    <div class="col-md-3">
                        <input id="belongCommissionPass" type="text" class="t-select" json-data='${belongCommissionListPass}' placeholder="请选择辖区" />
                        <input name="belongCommissionPass" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">时间范围</label>
                    <div class="col-md-3 daterange">
                        <form:input id="createTimePass" path="createTime" placeholder="时间范围" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_EXPORT_PNUMBER_EXLIST_AUTHORITY_3')">
                        <span id="exportPass" class="btn btn-primary">导出</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_EXPORT_PNUMBER_LIST_AUTHORITY_3')">
                        <span id="btnQueryPass" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <span id="btnClearPass" class="btn btn-default">清空</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row" style="padding-left: 10px;padding-right: 10px">
            <e:grid id="tableAllPass" action="/courseMemberNums/getCoursePassNums" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="专题类型" displayColumn="specialName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="通过人数" displayColumn="pass" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="未通过人数" displayColumn="unPass" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
