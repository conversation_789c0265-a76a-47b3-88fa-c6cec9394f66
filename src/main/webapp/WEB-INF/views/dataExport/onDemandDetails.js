$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // 日期初始化
    dateInit();
    $('#btnQuery').click(function () {
        search();
    });
    //清空
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        search();
    });
    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/onDemandDetails/onDemandDetailsListExport?" + $("#queryForm").formSerialize());
    });
})
function search() {
    ajaxTableQuery("tableAll", "/onDemandDetails/getOnDemandDetails",
        $("#queryForm").formSerialize());
}
function refreshTables() {
    location.reload();
}
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#post').tselectInit(null, teaSelectOptions);
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#superviseType').tselectInit(null, teaSelectOptions);

}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
function companyCode(data, type, row, meta){
    var str = "无"
    if (data.companyCode === null || data.companyCode === "") {
        return str
    }else {
        return data.companyCode
    }
}
function studyTime(data, type, row, meta){
    let time = data.studyTime
    time=time/60
    return time.toFixed(1)+"分钟"
}
function post(data, type, row, meta){
    var str = "无"
    if (data.post === null || data.post === "") {
        return str
    }else {
        return data.post
    }
}
function personType(data, type, row, meta){
    var str = "无"
    if (data.personType === null || data.personType === "") {
        return str
    }else {
        return data.personType
    }
}

function createTime(data, type, row, meta) {
    if (data.createTime === '' || data.createTime === null) {
        return "- -";
    } else {
        return data.createTime;
    }
}
function updateTime(data, type, row, meta) {
    if (data.updateTime === '' || data.updateTime === null) {
        return "- -";
    } else {
        return data.updateTime;
    }
}
//日期初始化
function dateInit() {
    dataRangePickerInit($('#createTime'), null, null, function () {
    }, function () {
    });
    dataRangePickerInit($('#updateTime'), null, null, function () {
    }, function () {
    });
}
