<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="panel">
        <div class="panel-heading" style="height: 45px;">
            <i class="icon icon-list">  课程设置</i>
        </div>
    <div class="panel-body">
        <form:form modelAttribute="courseDto" id="queryForm" onkeydown="bindEnter(event)">

            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="text-align:center">课程名称：</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label" style="text-align:center">发布时间：</label>
                    <div class="col-md-3 daterange">
                        <form:input path="releaseTime" placeholder="请输入发布时间" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label" style="text-align:center">是否发布：</label>
                    <div class="col-md-3">
                        <form:select path="releaseFlag" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未发布</form:option>
                            <form:option value="1">已发布</form:option>
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="text-align:center">讲师：</label>
                    <div class="col-md-3">
                        <input id="teacher" type="text" class="t-select" json-data='${teacherSelect}' placeholder="请输入讲师" />
                        <input name="teacher" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label" style="text-align:center">课程类型：</label>
                    <div class="col-md-3">
                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}'  placeholder="请选择课程类型"/>
                        <input name="courseType" type="hidden" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-12 text-right"  style="padding-top: 5px">
                        <span id="btnClear" class="btn btn-default">清空条件</span>
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                        <span id="addCourse" class="btn btn-primary">新增课程</span>
                    </div>
                </div>
            </div>
        </form:form>
        <%--编辑权限--%>
        <input type="hidden" id="editCourseAuth" value="true">
        <%--删除权限--%>
        <input type="hidden" id="delCourseAuth" value="true">
    </div>

    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/onDemandCourse/queryRepCaseInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="学分" renderColumn="credit" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程类型" renderColumn="courseType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程讲师" renderColumn="teacher" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否发布" renderColumn="columnRelease"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="发布时间" renderColumn="releaseTime" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
