<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>观看记录</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<script>
</script>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  观看记录</i>
    </div>
    <div class="panel-body" >
        <form:form modelAttribute="courseWatchInfoDto" id="queryForm" >
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">点播名称：</label>
                <div class="col-md-3">
                    <input id="courseIds" type="text" class="t-select" placeholder="请选择点播课程" json-data='${courseCodeList}' />
                    <input name="courseIds" id="courseId" type="hidden" placeholder="请选择点播课程" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center">课程类型：</label>
                <div class="col-md-3">
                    <input id="courseTypes" type="text" class="t-select" json-data='${courseTypeSelect001}'  placeholder="请选择课程类型"/>
                    <input name="courseTypes" type="hidden" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center">观看时间：</label>
                <div class="col-md-3 daterange">
                    <form:input path="watchTime" placeholder="请输入观看时间" cssClass="form-control" />
                </div>
            </div>
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">姓名：</label>
                <div class="col-md-3">
                    <form:input path="userName" placeholder="请输入姓名"  cssClass="form-control" autocomplete="off"/>
                </div>
                <label class="col-md-1 control-label" style="text-align:center">股票代码：</label>
                <div class="col-md-3">
                    <form:input path="companyCode" placeholder="请输入股票代码" cssClass="form-control" autocomplete="off" />
                </div>
                <label class="col-md-1 control-label" style="text-align:center">公司名称：</label>
                <div class="col-md-3">
                    <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="queryBtn" class="btn btn-primary">查询</span>
                    <span id="exportQuery" class="btn btn-primary">导出</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/courseViewingRecords/queryCourseWatchList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="姓名" displayColumn="userName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="公司名称" renderColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="观看时间" displayColumn="watchTime" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="来源" displayColumn="sourceStr" orderable="false"
                              cssClass="text-center" cssStyle="width:7%" />
                <e:gridColumn label="观看时长" renderColumn="studyTime" orderable="false"
                              cssClass="text-center" cssStyle="width:9%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:9%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
