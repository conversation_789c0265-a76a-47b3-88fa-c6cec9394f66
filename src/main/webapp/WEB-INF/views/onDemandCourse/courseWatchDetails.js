function studyTime(data, type, row, meta) {
    const hours = Math.floor(data.studyTime / 3600);
    const minutes = Math.floor((data.studyTime % 3600) / 60);
    const remainingSeconds = Math.floor(data.studyTime % 60);

    let formattedTime = '';

    if (hours > 0) {
        formattedTime += hours + '小时';
    }

    if (minutes > 0) {
        formattedTime += minutes + '分';
    }

    formattedTime += remainingSeconds + '秒';

    return formattedTime;
}
function courseName(data, type, row, meta){
    if (row.courseStatus == '0'){
        return    '<span>'+ row.courseName + '<span style="color:red;">' + "(已删除)" + '</span>' + '</span>';
    }else{
        return row.courseName
    }
}
// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
