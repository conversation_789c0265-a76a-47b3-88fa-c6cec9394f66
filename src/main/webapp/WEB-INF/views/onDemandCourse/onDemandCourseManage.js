//@ sourceURL=courseManage.js
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    dateInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        tSelectInit();
        dateInit();
        search();
    });


    $('#addCourse').click(function () {
        addCourse("");
    });

});

function search() {
    ajaxTableQuery("tableAll", "/onDemandCourse/queryRepCaseInfoList",
        $("#queryForm").formSerialize());
}

function addCourse(id) {
    var param = {
        id: id
    };
        parent.popWin('新增课程信息', '/onDemandCourse/addCourseManageInit', param, '98%', '98%',callBackAddCourse);

}

function editCourse(id) {
    var param = {
        id: id
    };
    parent.popWin('编辑课程信息', '/onDemandCourse/addCourseManageInit', param, '98%', '98%',callBackAddCourse);
}

function callBackAddCourse() {
    popMsg('保存成功！');
    ajaxTableReload("tableAll", false);
}

function dateInit() {
    dataRangePickerInit($('#releaseTime'), null, null, function () {

    }, function () {

    });
    dataRangePickerInit($('#amountTime'), null, null, function () {

    }, function () {

    });

}

//下拉初始化
function tSelectInit() {
    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, tSelectOptions);

    var teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);

}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

function releaseTime(data, type, row, meta){
    if (row.releaseTime!=null && row.releaseTime!=''){
        return row.releaseTime;
    }else{
        return '--';
    }
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function columnRelease(data, type, row, meta) {
    if (data.releaseFlag=='0'){
        return '否'
    }else if (data.releaseFlag=='1'){
        return '是'
    }else {
        return ''
    }
}

function columnOperation(data, type, row, meta) {
    var str = '';
    let editCourseAuth = '';
    let delCourseAuth = '';

    if (document.getElementById("editCourseAuth")) {
        editCourseAuth = true
    }

    if (document.getElementById("delCourseAuth")) {
        delCourseAuth = true
    }
    if (editCourseAuth) {
        str = '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editCourse(\'' + data.id + '\')"></i>';
    }
    if (data.releaseFlag=='0') {
        if (delCourseAuth) {
            str += '<i class="fa fa-trash-o" style="cursor: pointer;margin: 0 5px;" title="删除" onclick="delCourse(\'' + data.id + '\')"></i>';
        }
    }
    // else {
    //     str += '<i class="fa fa-copy" style="cursor: pointer;margin: 0 5px;" title="复制视频播放链接" onclick="copyCourse(\'' + data.id + '\')"></i>';
    // }

    if(!str) {
        str = '-';
    }
    return str;
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function delCourse(id) {
    parent.popConfirm("确认删除?", function () {
        var param = {
            id: id,
            status: '0'
        };
        ajaxData("/basicInformation/deleteCourse", param, callBackAddCourse);
    });
}

function courseName(data, type, row, meta) {
    var str = data.courseName;
    // if (str!=null && str !=''){
    // 	if (str.length>12){
    // 		str = str.substring(0,12) + "...";
    // 	}
    // }
    return str;
}


//学时
function credit(data, type, row, meta) {
    return data.credit;
}

function courseType(data, type, row, meta) {
    var str = data.courseType;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return '<span title="' + data.courseType + '">' + str + '</span>';
}

function applyPlate(data, type, row, meta) {
    var str = data.applyPlate;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPlate + '">' + str + '</span>';
}

function applyPerson(data, type, row, meta) {
    var str = data.applyPerson;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPerson + '">' + str + '</span>';
}



function teacher(data, type, row, meta) {
    var str = data.teacher;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.teacher + '">' + str + '</span>';
}

function copyCourse(id) {
    var input = document.getElementById(id);
    input.select(); // 选中文本
    document.execCommand("copy"); // 执行浏览器复制命令
    alert("复制成功");
}
