<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>点播记录详情</title>
  <e:base/>
  <e:js/>
  <style>
    label{
      padding-top: 7px;
    }
    .col-md-mine {
      position: relative;
      min-height: 1px;
      padding-right: 5px;
      padding-left: 5px;
      float: left;
      width: 12%;
      font-size: 14px;
    }
    .iconColor{
      color: #145ccd;
      margin: 0 3px;
    }
    .icon{
      cursor: pointer;
    }
  </style>
</head>
<script>
</script>
<body>
<div class="panel">
  <div class="panel-heading" style="height: 45px;">
    <i class="icon icon-list">  点播记录详情</i>
  </div>
  <div class="panel-body" >
    <form:form modelAttribute="courseWatchInfoDto" id="queryForm" >
      <form:hidden path="userId" id="userId"/>
      <form:hidden path="courseId" id="courseId"/>
    </form:form>
  </div>
  <div class="panel-body">
    <div class="row">
      <e:grid id="tableAll" action="/courseViewingRecords/queryCourseWatchDetails?userId=${courseWatchInfoDto.userId}&courseId=${courseWatchInfoDto.courseId}&source=${courseWatchInfoDto.source}" cssClass="table table-striped table-hover">
        <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                      cssStyle="width:5%" />
        <e:gridColumn label="姓名" displayColumn="userName" orderable="false"
                      cssClass="text-center" cssStyle="width:8%" />
        <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false"
                      cssClass="text-center" cssStyle="width:30%" />
        <e:gridColumn label="观看渠道" displayColumn="source" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
        <e:gridColumn label="进入时间" displayColumn="watchStartTime" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="退出时间" displayColumn="watchEndTime" orderable="false"
                      cssClass="text-center" cssStyle="width:15%" />
        <e:gridColumn label="观看时长" renderColumn="studyTime" orderable="false"
                      cssClass="text-center" cssStyle="width:10%" />
      </e:grid>
    </div>
  </div>
</div>
</body>
</html>
