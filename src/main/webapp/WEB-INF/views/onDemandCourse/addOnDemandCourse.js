//@ sourceURL=addCourse.js
var _ImgUploadUrl = contextPath + '/filetempupload';
let _fileUploadUrl = contextPath + '/filetempupload';
var log, className = "dark", curDragNodes, autoExpandNode;

var videoSelectFlag = true;

var maxSort;
var showFlag = "1";
$(document).ready(function () {
    $("#edit").hide();
    //保存基本信息
    $("#btnSaveCopy").click(function () {
        saveCourse()
    })

    otherDivInit();
    switchInit();
    compulsorySwitchInit();
    // selection plugin init
    tSelectInit();
    //date selection plugin init
    // dateInit();
    //pic upload init
    livePicInit();

    fileUploadInit("courseFile");



    $.validator.addMethod("stringMaxLength", function (value, element, params) {
        var length = 0;
        for (var i = 0; i < value.length; i++) {
            if (value.charCodeAt(i) > 19967) {
                length++;
            }
        }
        return length > params[0] ? false : true;
    }, "最大长度不能超过{0}个字");

    $.validator.addMethod("numberVaild", function (value, element) {
        if (value.replace(/,/g, '').trim() != "") {
            var re = /^(-?[0-9]\d{0,9})(,\d{3})*(\.\d{0,2})?$/;
            var isValid = this.optional(element) || re.test(value.replace(/,/g, '').trim());
            //formatter(element);
            return isValid;
        } else {
            return true;
        }
    }, "请输入正确的数值(整数或小数,保留小数点后2位)");

    $("#queryForm").validate({
        ignore:"",
        rules: {
            "courseName": {
                required: true,
                stringMaxLength: [60]
            },
            "commodityPrice": {
                numberVaild: true,
                required: true,
            },
            "markingPrice": {
                numberVaild: true
            },
            "mainTextTag": {
                required: true,
                stringMaxLength: [200]
            },
            "introduce": {
                required: true,
                stringMaxLength: [1000]
            },
        },
        messages: {
            "courseName": {
                required: "请填写课程名称"
            },
        }
    });


});
function otherDivInit(){
    if (getValue($('[name="ifLearningHours"]:checked').val()) == '1'){
        document.getElementById("otherDiv").style.display = "block";
    }
}


function decideShow(radio){
    var value = radio.value;
    var otherDiv = document.getElementById("otherDiv");
    if (value == "1") {
        otherDiv.style.display = "block";
    } else {
        $('#credit').val(null);
        otherDiv.style.display = "none";
    }
}

function switchInit() {
    $('#ifOpenSwitch').bootstrapSwitch('size', 'small');
    let ifOpen = $('input[name="ifOpen"]').val();
    if (ifOpen == '1') {
        $('input[name="ifOpen"]').val(true);
    } else {
        $('input[name="ifOpen"]').val(false);
    }
    let ifOpenOld = eval($('input[name="ifOpen"]').val());
    let ifOpenNow = $('#ifOpenSwitch').prop("checked");
    if (ifOpenOld !== ifOpenNow) {
        $('#ifOpenSwitch').bootstrapSwitch('toggleState');
    }
    $('#ifOpenSwitch').on('switchChange.bootstrapSwitch',
        function (event, state) {
            if (state == true) {
                $('input[name="ifOpen"]').val(true);
            } else {
                $('input[name="ifOpen"]').val(false);
            }
        });
}

function compulsorySwitchInit() {
    $('#compulsorySwitch').bootstrapSwitch('size', 'small');
    let compulsory = $('input[name="compulsory"]').val();
    if (compulsory == '1') {
        $('input[name="compulsory"]').val(true);
    } else {
        $('input[name="compulsory"]').val(false);
    }
    let compulsoryOld = eval($('input[name="compulsory"]').val());
    let compulsoryNow = $('#compulsorySwitch').prop("checked");
    if (compulsoryOld !== compulsoryNow) {
        $('#compulsorySwitch').bootstrapSwitch('toggleState');
    }
    $('#compulsorySwitch').on('switchChange.bootstrapSwitch',
        function (event, state) {
            if (state == true) {
                $('input[name="compulsory"]').val(true);
            } else {
                $('input[name="compulsory"]').val(false);
            }
        });
}

function saveCourse(){
    debugger
    if (getValue($("#videoId").val()) == ""){
        popMsg("请选择课程视频");
        return;
    }
    if( getValue($('#courseName').val())  == ""  ){
        popMsg("请填写点播名称");
        return;
    }
    if (!getValue($('[name="releaseFlag"]:checked').val())){
        popMsg("请选择发布状态");
        return;
    }
    if( getValue($('#introduce').val())  == ""  ){
        popMsg("请填写课程简介");
        return;
    }
    if(getValue($("#livePicImg").val()) == "") {
        popMsg("请上传课程封面图片");
        return;
    }
    if (getValue($('[name="ifLearningHours"]:checked').val())  == "" ){
        popMsg("请选择是否有学分");
        return;
    }
    if (getValue($('[name="ifForm"]:checked').val())  == "" ){
        popMsg("请选择是否填写问卷");
        return;
    }
    if(getValue($('[name="ifLearningHours"]:checked').val())  == "1" ){
        var regex = /^\d+(\.\d{1,2})?$/;
        if (getValue($('#credit').val())=="" || (isNaN(getValue($('#credit').val()))) || (!regex.test(getValue($('#credit').val())))){
            popMsg("学分请输入小数点后不超过两位的数字");
            return;
        }
    }

    if ($("#queryForm").valid()) {
        popConfirm("保存?", function () {
            btnSave();
        });
    }

}
function btnSave() {
    let files = [];
    let url = '/onDemandCourse/courseSave';
//新增、更新判别
    if($("#id").val() == ''){
        $.each($('.file-span'), function (index, fileItem) {
            let fileUrl = $(fileItem).data('file-id');
            let fileType = $(fileItem).data('file-type');
            let fileName = $(fileItem).data('file-name');
            let fileSize = $(fileItem).data('file-size');
            if (getValue(fileUrl) != '') {
                files.push({
                    fileType: fileType,
                    attUrl: fileUrl,
                    attName : fileName,
                    size: fileSize
                });
            }
        });
    }else {
        $.each($('.file-span'), function(index, fileItem) {
            let fileType = $(fileItem).data('file-type');
            let attId = $(fileItem).data('atta-id');
            let fileUrl = $(fileItem).data('file-id');
            let fileName = $(fileItem).data('file-name');
            let fileSize = $(fileItem).data('file-size');
            if (getValue(fileUrl) != '' || getValue(attId) != '') {
                files.push({
                    fileType: fileType,
                    id: attId,
                    attUrl: fileUrl,
                    attName : fileName,
                    size: fileSize
                });
            }
        });
    }
    let param = {
        releaseFlag: $('[name="releaseFlag"]:checked').val(),
        id: $('#id').val(),
        courseName: $('#courseName').val(),
        courseType:$('input[name="courseType"]').val(),
        introduce:$('#introduce').val(),
        livePicImg:$("#livePicImg").val(),
        fileList: files,
        teacher:$('input[name="teacher"]').val(),
        teacherList: $('input[name="teacher"]').val().split(","),
        ifLearningHours: $('[name="ifLearningHours"]:checked').val(),
        ifForm: $('[name="ifForm"]:checked').val(),
        credit:$('#credit').val(),
        videoId:$('#videoId').val()
    };
    $.ajax({
        url: contextPath + url,
        data: JSON.stringify(param),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (res) {
            closeWinCallBack(res);
        },
        error: function () {
            popMsg("保存失败");
        }
    });
}

function dateInit() {
    var limitTime = $('#limitTime').val();
    if (limitTime.trim() != '') {
        var limitTimes = limitTime.split(' 至 ');
        dataRangePickerInit($('#limitTime'), limitTimes[0], limitTimes[1], function () {
        }, function () {
        });
    } else {
        dataRangePickerInit($('#limitTime'), null, null, function () {
        }, function () {
        });
    }
}

//下拉初始化
function tSelectInit() {

    var tSelectOptionType = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        useFooter: true,
        hiddenInput: true,
        grade: 1,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };

    $('#courseType').tselectInit(null, tSelectOptionType);

    var teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}


function livePicInit() {
    $('#livePicRemoveBtn').bind('click', function () {
        clearLivePic();
    });
    $('#livePicFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#livePicFileId").val(file.fileRelaId);
                $("#livePicImg").val(file.filePath);
                $("#picAttId").val("");
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });
    $("#livePicFile").livePicUploadPreview({
        Img: "livePicImgSrc",
        Width: 50,
        Height: 50
    });
}
/**
 * 课件上传
 */
function fileUploadInit(fileUploadId) {
    $("#" + fileUploadId).fileupload({
        url: _fileUploadUrl,
        dataType: 'text',
        autoUpload: true,
        add: function(e, data) {
            // 上传文件时校验文件格式
            data.submit();
        },
        submit: function(e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
            });
        },
        done: function(e, data) {
            layer.close(index);
            $.each($.parseJSON(data.result), function(index, file) {
                let divStr =
                    ' <div class="col-xs-12 file-content">' +
                    ' <span onclick="downloadFile(\'' + file.filePath + '\',\'' + file.fileName + '\', 0)" class="file-span" title="下载" data-file-name ="' + file.fileName + '"  data-file-size ="' + file.fileSize + '"   data-file-type="0" data-atta-id="" data-file-id="' + file.filePath + '">' + file.fileName + '</span>' +
                    ' <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>' +
                    ' </div>';
                $("#fileDiv").append(divStr);
            })
        },
        fail: function(e, data) {
            popMsg("上传附件失败！");
        }
    });
}
/**
 * 下载文件
 *
 * @param fileId 文件ID
 */
function downloadFile(fileId,fileName, rootType) {
    if (getValue(fileId) != '') {
        window.open(contextPath + "/ebSchoolLiveInfo/filedownload?fileId=" + fileId + "&fileName=" + fileName + "&rootType=" + rootType, "_blank");
    }
}

/**
 * 删除课件
 * @param item
 */
function removeFile(item) {
    // 移除页面元素
    $(item).parent().remove();
}

function clearLivePic() {
    popConfirm("确认删除图片吗？", function () {
        $("#livePicFileId").val("");
        $("#livePicDiv").css("display", "none");
        $("#livePicFile").val("");
        $("#livePicImg").val("");
        $("#picAttId").val("");
    });
}

$.fn.extend({
    livePicUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "livePicImgSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#livePicImg").val("");
            $("#livePicDiv").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});


function onRemove(e, treeId, treeNode) {
    //onRemove暂时用不到, beforeRemove=false, 永远也不触发onRemove
}

function onDrag(event, treeId, treeNodes) {
}


//开始drop的钩子函数
//如果为true后, 方可执行onDrop
function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
    console.log(treeId, treeNodes, targetNode, moveType, isCopy);
    //true:node, false:leaf
    var treeFlag = treeNodes[0].level == '0' ? true : false;
    var targetFlag = targetNode.level == '0' ? true : false;
    //leaf->node添加可以, 反之拒绝操作
    if (moveType == "inner") {
        if (treeFlag == false && targetFlag == true) {
            return true;
        }
        return false;
    } else if (moveType == "prev" || moveType == "next") {
        //相同类型的可以移动
        if (treeFlag == targetFlag) {
            return true;
        } else {
            return false;
        }
    }
    return true;
}

//前端放行, 可以执行放的动作, 请求后台
function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
    treeOperateShow();
    videoSelectFlag = false;
}

function refresh() {
    var param = {
        id: $("#id").val()
    };
    ajaxData("/basicInformation/refresh", param, refreshCallback);
}

function myWinCallback(data) {
    if(data.period == "null"){
        if (data) {
            var param = {
                id: $("#nodeId").val(),
                videoId: data.videoId,
                iconSkin: 'VIDEO',
                videoName: data.videoName,
                bizId: $("#id").val()
            };
            ajaxData("/basicInformation/nodeUpdate", param, selectVideoCallback);
        }
    }else{
        if (data) {
            var param = {
                id: $("#nodeId").val(),
                videoId: data.videoId,
                iconSkin: 'VIDEO',
                videoName: data.videoName,
                period: data.period,
                bizId: $("#id").val()
            };
            ajaxData("/basicInformation/nodeUpdate", param, selectVideoCallback);
        }
    }

}

function btnCancel() {
    var param = {
        id: $("#id").val(),
        releaseFlag: '0'
    };
    ajaxData("/basicInformation/courseRelease", param, function (data) {
        popMsg("已取消");
        $("#releaseFlag").val('0');
        $("#btnCancel").hide();
        $("#btnRelease").show();
        $("#dataUploadBtn").show();
        $(".deleteFile").each(function (n,obj) {
            $(obj).show();
        })
    });
}

function btnClose() {
    closeWinCallBack();
}

//金融显示千位符,保存至后台是带","的字符串
function formatter(o) {
    var arr = o.value.split('.');
    var tmp = arr[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
    var ss = o.selectionStart + tmp.length - arr[0].length;// 控制光标的位置-保持不变
    if (arr[1] != undefined) {
        o.value = tmp + '.' + arr[1];
    } else {
        o.value = tmp
    }
    o.selectionStart = o.selectionEnd = ss
}


function checkbox(fieldId,obj) {
    var x = $(obj).is(':checked');
    var openFlag;
    if (x == true) {
        openFlag = "1";
    }else {
        openFlag = "0";
    }
    var param = {
        attachmentId: fieldId,
        openFlag: openFlag
    };
    ajaxData("/schoolCourse/openFile", param, function (data) {
        popMsg("操作成功")
    })
}
function relationLive(){
    let param = {
        id: $("#id").val(),
    };
    popWin("关联直播", "/basicInformation/relationLiveInit",param,"40%", "70%",relationCallBack);
}

function relationCallBack(){
    popMsg("关联成功")
}
