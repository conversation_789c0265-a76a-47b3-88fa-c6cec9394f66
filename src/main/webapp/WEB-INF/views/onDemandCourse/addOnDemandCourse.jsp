<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script>
    var id = '${id}';
    var modifyType = '${modifyType}';
    var editStatus = '${editStatus}';
    var completeCou = '${completeCou}';
  </script>
  <title>北京上市公司协会培训管理平台</title>
  <e:base/>
  <e:js/>
  <style>
    label {
      padding-top: 7px;
    }

    .col-md-mine {
      position: relative;
      min-height: 1px;
      padding-right: 5px;
      padding-left: 5px;
      float: left;
      width: 12%;
      font-size: 14px;
    }
    #courseFile,
    #livePicFile {
      margin: -16px;
      opacity: 0;
      -ms-filter: 'alpha(opacity=0)';
      direction: ltr;
      cursor: pointer;
      width: 100px;
      height: 32px;
    }
    .live-pic-btn-row,
    .live-file-btn-row {
      margin-top: 10px;
    }
    .file-span,
    .file-del-btn {
      cursor: pointer;
    }
    #dataFile {
      margin: -16px;
      opacity: 0;
      -ms-filter: 'alpha(opacity=0)';
      direction: ltr;
      cursor: pointer;
      height: 32px;
    }

    #livePicImg {
      height: 200px;
      border: 0;
      margin: 10px 0;
    }


    .ztree li span.button.VIDEO_ico_open {
      margin-right: 2px;
      background: url('../../../static/images/video.png') no-repeat scroll 0 0 transparent;
      vertical-align: top;
    }

    .ztree li span.button.VIDEO_ico_close {
      margin-right: 2px;
      background: url('../../../static/images/video.png') no-repeat scroll 0 0 transparent;
      vertical-align: top;
    }

    .ztree li span.button.VIDEO_ico_docu {
      margin-right: 2px;
      background: url('../../../static/images/video.png') no-repeat scroll 0 0 transparent;
      vertical-align: top;
    }

    .ztree li span.button.TITLE_ico_open {
      margin-right: 2px;
      background: url('../../../static/images/title.png') no-repeat scroll 0 0 transparent;
      vertical-align: top;
    }

    .ztree li span.button.TITLE_ico_close {
      margin-right: 2px;
      background: url('../../../static/images/title.png') no-repeat scroll 0 0 transparent;
      vertical-align: top;
    }

    .ztree li span.button.TITLE_ico_docu {
      margin-right: 2px;
      background: url('../../../static/images/title.png') no-repeat scroll 0 0 transparent;
      vertical-align: top;
    }

    .ztree * {
      font-size: 14px !important;
    }

    .tree-div {
      /*display: none;*/
    }

    .table-th {
      background-color: #37C8F4 !important;
      color: white;
    }

    .table-del {
      font-style: inherit !important;
    }
    .isNumber{
      border: 1px solid #ccc;
      height: 30px;
      width: 50%;
      background: #F5F5F5;
    }

  </style>
</head>
<body>
<div class="panel">
  <div class="panel-heading">
    <div class="row" style="padding-top: 5px;padding-bottom: 10px">
      <div class="col-md-12">
        <div class="col-md-6 text-left">
          <div style="font-size: 20px;font-weight: bold">课程基本信息</div>
        </div>
      </div>
    </div>
    <form:form modelAttribute="courseDto" id="queryForm" autocomplete="off">
      <div class="row">
        <div class="col-md-12">
          <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>点播名称</label>
          <div class="col-md-7">
            <form:input path="courseName" placeholder="请输入点播名称" cssClass="form-control"/>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
        <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>是否发布</label>
          <div class="col-md-7 controls">
            <c:if test="${courseDto.releaseFlag  == '1'}">
              <input type="radio" name="releaseFlag" value="1" checked/>发布
              <input type="radio" name="releaseFlag" value="0" style="margin-left: 15px;"/>未发布
            </c:if>
            <c:if test="${courseDto.releaseFlag  != '1'}">
              <input type="radio" name="releaseFlag" value="1" />发布
              <input type="radio" name="releaseFlag" value="0" checked style="margin-left: 15px;"/>未发布
            </c:if>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <label class="col-xs-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>课程简介</label>
          <div class="col-xs-10">
            <div class="col-xs-12 no-padding">
              <form:textarea cssClass="form-control" path="introduce" id="introduce" rows="3"/>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <label class="col-md-1 control-label">课程类型</label>
          <div class="col-md-3">
            <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}'
                   selected-ids="${courseDto.courseType}" placeholder="请选择课程类型"/>
            <input name="courseType" type="hidden"
                   value='${courseDto.courseType}'/>
          </div>
          <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>是否有学分</label>
          <div class="col-md-1 controls">
            <c:if test="${courseDto.ifLearningHours  == '1'}">
              <input type="radio" name="ifLearningHours" value="1" checked onchange="decideShow(this)"/>是
              <input type="radio" name="ifLearningHours" value="0" style="margin-left: 15px;" onchange="decideShow(this)"/>否
            </c:if>
            <c:if test="${courseDto.ifLearningHours  != '1'}">
              <input type="radio" name="ifLearningHours" value="1" onchange="decideShow(this)"/>是
              <input type="radio" name="ifLearningHours" value="0" checked style="margin-left: 15px;" onchange="decideShow(this)"/>否
            </c:if>
          </div>
          <div id="otherDiv" class="control-group form-group" style="display: none; height: 26px">
            <label class="col-md-1 control-label text-right"><span style="color: #FF0000;font-weight: bold;">*</span>课程学分</label>
            <div class="col-md-2 controls">
              <form:input type="text" class="form-control" path="credit" value="" autocomplete="off" placeholder="请输入课程学分"/>
            </div>
          </div>
        </div>
      </div>

      <div class="row" >
        <div class="col-md-12">
          <label class="col-md-1 control-label">选择讲师</label>
          <div class="col-md-3">
            <input id="teacher" type="text" class="t-select" json-data='${teacherSelect}'
                   selected-ids="${courseDto.teacher}" placeholder="请选择讲师"/>
            <input name="teacher" type="hidden"  value='${courseDto.teacher}'/>
          </div>
          <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>是否填写问卷</label>
          <div class="col-md-1 controls">
            <c:if test="${courseDto.ifForm  == '1'}">
              <input type="radio" name="ifForm" value="1" checked />是
              <input type="radio" name="ifForm" value="0" style="margin-left: 15px;" />否
            </c:if>
            <c:if test="${courseDto.ifForm  != '1'}">
              <input type="radio" name="ifForm" value="1" />是
              <input type="radio" name="ifForm" value="0" checked style="margin-left: 15px;" />否
            </c:if>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>视频选择</label>
          <div class="col-md-3">
            <form:select path="videoId" cssClass="form-control">
              <form:option value="">请选择</form:option>
              <c:forEach var="video" items="${videoSelect}" varStatus="index">
                <form:option  value="${video.id}">${video.videoName}</form:option>
              </c:forEach>
            </form:select>
          </div>
        </div>
      </div>

        <form:hidden path="id" id="id"/>
        <form:hidden path="releaseFlag"/>

<%--      <div class="panel-heading tree-div">--%>
<%--        <div class="row" style="padding-top: 5px;padding-bottom: 10px">--%>
<%--          <div class="col-md-12">--%>
<%--            <div class="col-md-6 text-left">--%>
<%--              <div style="font-size: 20px;font-weight: bold">关联设置</div>--%>
<%--            </div>--%>
<%--            <div class="col-md-6 text-right">--%>
<%--            </div>--%>
<%--          </div>--%>
<%--        </div>--%>
<%--        <div class="row tree-div">--%>

<%--          <div class="col-md-4">--%>
<%--            <div class="panel">--%>
<%--              <div class="panel-heading">--%>
<%--                <i class="icon icon-list">&nbsp;章节维护</i>--%>
<%--              </div>--%>
<%--              <div class="row" style="padding: 10px 0 0 0;">--%>
<%--                <div class="col-md-12">--%>
<%--                  <div class="col-md-3">--%>
<%--                    <input type="button" id="nodeCreate" class="btn btn-success" value="创建"/>--%>
<%--                      &lt;%&ndash;<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>&ndash;%&gt;--%>
<%--                  </div>--%>
<%--                  <div class="col-md-3">--%>
<%--                    <input type="button" id="refresh" class="btn" value="撤销修改"/>--%>
<%--                      &lt;%&ndash;<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>&ndash;%&gt;--%>
<%--                  </div>--%>
<%--                  <div class="col-md-3" style="display: none;">--%>
<%--                    <input type="button" id="getConsole" class="btn" value="Get"/>--%>
<%--                      &lt;%&ndash;<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>&ndash;%&gt;--%>
<%--                  </div>--%>
<%--                  <div class="col-md-3">--%>
<%--                    <input type="button" id="nodeSave" class="btn btn-primary" value="保存修改"/>--%>
<%--                      &lt;%&ndash;<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>&ndash;%&gt;--%>
<%--                  </div>--%>
<%--                </div>--%>
<%--              </div>--%>
<%--              <div class="panel-body expandArea violateType">--%>
<%--                <ul id="tree" class="ztree"></ul>--%>
<%--              </div>--%>
<%--            </div>--%>
<%--          </div>--%>
<%--          <div class="col-md-8">--%>
<%--            <div class="panel">--%>
<%--              <div class="panel-heading">--%>
<%--                <i class="icon icon-list-alt"><label>&nbsp;视频关联</label></i>--%>
<%--              </div>--%>
<%--              <div class="panel-body">--%>
<%--                <div class="row form-group">--%>
<%--                  <div class="col-md-mine"><label><strong>章节类型</strong></label></div>--%>
<%--                  <div class="col-md-4"><label id="iconSkin"></label></div>--%>
<%--                </div>--%>
<%--                <div class="row form-group">--%>
<%--                  <div class="col-md-mine"><label><strong>章节名称</strong></label></div>--%>
<%--                  <div class="col-md-4"><label id="itemName"></label></div>--%>
<%--                </div>--%>
<%--                <div class="row form-group" id="show">--%>
<%--                  <div class="col-md-mine"><label><strong>视频名称</strong></label></div>--%>
<%--                  <div class="col-md-4"><label id="videoName"></label></div>--%>
<%--                </div>--%>
<%--                <div class="row form-group" id="showT">--%>
<%--                  <div class="col-md-mine"><label><strong>视频时长</strong></label></div>--%>
<%--                  <div class="col-md-4"><label id="videoDuration"></label></div>--%>
<%--                </div>--%>
<%--                <div class="row form-group" id="showP">--%>
<%--                  <div class="col-md-mine"><label><strong>学时</strong></label></div>--%>
<%--                  <div class="col-md-4"><label id="period"></label></div>--%>
<%--                  <i id="edit" class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="修改" onclick="editCoursePeriod(this)"></i>--%>
<%--                </div>--%>
<%--                <div class="row">--%>
<%--                  <div class="col-md-6 col-md-offset-3">--%>
<%--                    <input type="button" class="btn btn-primary" id="selectBtn"--%>
<%--                           style="margin-right: 20px" value="关联视频"/>--%>
<%--                    <input type="button" class="btn" id="cancelBtn" style="margin-right: 20px"--%>
<%--                           value="取消关联"/>--%>
<%--                  </div>--%>
<%--                </div>--%>
<%--                <div id="treeValue" style="display:none">${treeValue}</div>--%>
<%--                <input id="nodeId" type="hidden" value="">--%>
<%--              </div>--%>
<%--            </div>--%>
<%--          </div>--%>
<%--        </div>--%>
<%--      </div>--%>
      <div class="row" >
        <div class="col-md-12">
          <label class="col-xs-1 control-label">
            <span style="color: #FF0000;font-weight: bold;">*</span>课程封面
            <div style="font-size: 12px;color: red;">（请上传16:9的图片）</div>
          </label>
          <div class="col-xs-6" style="margin: 10px 0">
            <c:choose>
              <c:when test="${courseDto.livePicImg == '' || courseDto.livePicImg==null}">
                <div id="livePicDiv" class="col-xs-12 no-padding" style="display: none;">
                  <img id="livePicImgSrc" src="${courseDto.livePicImg}" style="width: 320px;height: 180px;"/>
                  <input type="hidden" name="livePicFileId" id="livePicFileId"/>
                  <form:hidden path="livePicImg"/>
                </div>
              </c:when>
              <c:otherwise>
                <div id="livePicDiv" class="col-xs-12 no-padding" style="display: block;">
                  <img id="livePicImgSrc" src="${courseDto.livePicImg}" style="width: 320px;height: 180px;"/>
                  <input type="hidden" name="livePicFileId" id="livePicFileId"/>
                  <form:hidden path="livePicImg"/>
                </div>
              </c:otherwise>
            </c:choose>
            <div class="col-xs-12 no-padding controls live-pic-btn-row">
              <a href="javascript:void(0);"  class="file btn btn-warning btn-facebook btn-outline" id="livePicUploadBtn" >
                <i class="fa fa-upload"> </i> 上传图片
                <input id="livePicFile" type="file" name="files"/>
              </a>
              <input type="button" id="livePicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
            </div>
          </div>
        </div>
      </div>
    </form:form>

    <div class="panel-heading tree-div">
      <div class="row" style="padding-top: 5px;padding-bottom: 10px">
        <div class="col-md-12">
          <div class="col-md-6 text-left">
            <div style="font-size: 20px;font-weight: bold">课程附件</div>
          </div>
          <div class="col-md-6 text-right">
          </div>
        </div>
      </div>
      <%--课程资料--%>
      <div class="control-group form-group">

        <div class="col-xs-6 controls">
          <div class="col-xs-12 no-padding" id="fileDiv">
            <c:forEach items="${courseDto.fileList}" var="item" varStatus="status">
              <div class="col-xs-12 file-content">
                                <span onclick="downloadFile('${item.attUrl}','${item.attName}', 1)" class="file-span" title="下载"
                                      data-file-type="1" data-atta-id="${item.id}"
                                      data-file-id="${item.attUrl}">${item.attName}</span>
                <i class="fa fa-times file-del-btn" style="margin-left:15px" onclick="removeFile(this)" title="删除课件"></i>
              </div>
            </c:forEach>
          </div>
          <div class="col-xs-12 no-padding live-file-btn-row" id="btnDiv">
            <a href="javascript:void(0);"  class="file btn btn-warning btn-facebook btn-outline" id="fileUploadBtn">
              <i class="fa fa-upload"> </i> 上传课件
              <input id="courseFile" type="file" name="files" multiple />
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="row" style="padding-top: 5px;padding-bottom: 10px">
      <div class="col-md-12">
        <div class="col-md-6 text-right">
          <span id="btnSaveCopy" class="identity-limit btn btn-primary">保存基本信息</span>
        </div>
      </div>
    </div>
  </div>

</div>
</body>
</html>
