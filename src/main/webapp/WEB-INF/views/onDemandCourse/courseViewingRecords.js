$(document).ready(function() {
    dateInit();
    tSelectInit();
    $("#queryBtn").bind("click", function() {
        tableQuery();
    });
    $("#btnClear").bind("click", function() {
        document.getElementById("queryForm").reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tableQuery()
    });
    //统计导出
    $("#exportQuery").bind("click", function() {
        exportStatisticsTableData();
    });
});

function exportStatisticsTableData(){
    let courseIds=$('input[name="courseIds"]').val();
    let courseTypes=$('input[name="courseTypes"]').val();
    let watchTime = $('#watchTime').val();
    let userName = $('#userName').val();
    let companyCode = $('#companyCode').val();
    let companyName = $('#companyName').val();
    window.open(contextPath + "/courseViewingRecords/exportCourseViewingRecords?courseIds="+courseIds+"&courseTypes="+courseTypes+"&watchTime="+watchTime+"&userName="+userName+"&companyCode="+companyCode+"&companyName="+companyName);
}

/**
 * 列表查询
 */
function tableQuery() {
    ajaxTableQuery("tableAll", "/courseViewingRecords/queryCourseWatchList", $("#queryForm").formSerialize());
}

function studyTime(data, type, row, meta) {
    const hours = Math.floor(data.studyTime / 3600);
    const minutes = Math.floor((data.studyTime % 3600) / 60);
    const remainingSeconds = Math.floor(data.studyTime % 60);

    let formattedTime = '';

    if (hours > 0) {
        formattedTime += hours + '小时';
    }

    if (minutes > 0) {
        formattedTime += minutes + '分';
    }

    formattedTime += remainingSeconds + '秒';

    return formattedTime;
}

function companyName(data, type, row, meta){
    var str = row.companyName;
    if (str != null && str != '') {
        if (str.length > 15) {
            str = str.substring(0, 15) + "...";
        }
    }
    return '<span title="' + row.companyName + '">' + str + '</span>';
}
function courseName(data, type, row, meta){
    var courseName = row.courseName;
    if (courseName != null && courseName != '') {
        if (courseName.length > 12) {
            courseName = courseName.substring(0, 12) + "...";
        }
    }


    if (row.courseStatus == '0'){
        return    '<span title="'+row.courseName+'">'+ courseName + '<span style="color:red;">' + "(已删除)" + '</span>' + '</span>';
    }else{
        return '<span title="'+row.courseName+'">'+ courseName + '</span>';
    }
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
function columnOperation(data, type, row, meta) {
    let content = '';
    content += '<a href="javascript:void(0)" onclick="openCourseWatchDetails(\'' + data.userId + '\',\'' + data.courseId + '\',\'' + data.source + '\')" title="查看详情">查看详情</a>';
    return content;
}
function openCourseWatchDetails(userId,courseId,source){
    let param={
        userId:userId,
        courseId:courseId,
        source:source
    }
    parent.popWin('点播记录详情','/courseViewingRecords/openCourseWatchDetails', param,'60%', '72%', callBackAddExam, '', callBackAddExam);
}
function callBackAddExam(){

}
//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseTypes').tselectInit(null, teaSelectOptions);

    var courseSelectOptions = {
        id: 'id',
        name: 'courseName',
        value: 'id',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseIds').tselectInit(null, courseSelectOptions);
}
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}


function dateInit() {
    var watchTime = $('#watchTime').val();
    if (watchTime.trim() != '') {
        var creditTimes = watchTime.split(' 至 ');
        dataRangePickerInit($('#watchTime'), creditTimes[0], creditTimes[1], function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    } else {
        dataRangePickerInit($('#watchTime'), null, null, function () {
        }, function () {
        }, {
            timePicker: true,
            timePicker12Hour: false,
            timePicker24Hour: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            timePickerIncrement: 5
        });
    }
}
function LinkageEndDate(istg) {
    return {
        festival: true,
        isClear: false,
        trigger : istg || "click",
        format: 'YYYY-MM-DD hh:mm:ss',
        minDate: function (that) {
            return _endDate.minDate ;
        },
        maxDate: '2099-12-31 23:59:59',
        donefun: function(obj){
            _startDate.maxDate = obj.val;
        }
    };
}
