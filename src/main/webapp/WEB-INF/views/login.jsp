<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>北京上市公司协会培训管理平台</title>
	<link rel="shortcut icon" href="${pageContext.request.contextPath}/static/images/logo.png" type="image/x-icon" />
	<e:base cssModules="all" jsModules=""/>
	<link href="${pageContext.request.contextPath}/static/css/login.css?20210118" rel="stylesheet" type="text/css">
	<!--[if lt IE 9]>
      <script src="${pageContext.request.contextPath}/static/lib/ieonly/html5shiv.js"></script>
      <script src="${pageContext.request.contextPath}/static/lib/ieonly/respond.js"></script>
      <script src="${pageContext.request.contextPath}/static/lib/ieonly/excanvas.js"></script>
    <![endif]-->
</head>
<body>
<c:if test="${ loginWay == null}">
	<c:set var="loginWay" scope="session" value="smsLogin"/>
</c:if>
<div style="position: absolute;width: 100%;height: 110px;line-height: 110px;padding-left: 25px;background: #ffffff;">
	<img alt="" src="${pageContext.request.contextPath}/static/images/beishangxielogo.png"
		 style="width: 378px;height: 68px">
	<div class="l" style="display: inline-block;height: 27px; margin-left: 10px;">
		<div style="width: 2px; height: 42px; background-color: rgb(204, 204, 204);"></div>
	</div>
	<div class="l" style="display: inline-block; color: rgb(51, 51, 51); font-size: 22px; margin-left: 12px; font-family: Songti SC;"><span>培训平台管理端</span></div>

</div>
<div style="margin: 0 auto;width:400px;padding-top: calc((100vh - 230px)/2);">
	<div id="loginframe"  class="box-right1">
		<div>
			<p align="center" style="color: #333333;margin-top: 20px;font-weight: bold;font-size: 24px;">账号登录</p>
		</div>
		<div style="margin-top: 25px;">
			<div class="errorMsg" id="errorMsg">${SPRING_SECURITY_LAST_EXCEPTION.message}</div>
			<form action="${pageContext.request.contextPath}/loginVerify" method="POST" id="login-form" class="login-form">
				<fieldset>
					<div style="margin-left: 44px" class="adminPng">
						<img alt="" src="${pageContext.request.contextPath}/static/images/<EMAIL>" class="input-icon">
						<input placeholder="请输入账号" type="text" id="username" name="username" value="" class="input-box">
					</div>
					<br>
					<div style="margin-left: 44px" class="pwdPng">
						<img alt="" src="${pageContext.request.contextPath}/static/images/<EMAIL>" class="input-icon">
						<input placeholder="请输入密码" type="password" id="password" name="password" class="input-box">
					</div>
					<input type="hidden" value="normalLogin" name="loginWay">
					<div style="text-align: center;margin-top: 35px">
						<button id="normalLoginBtn" class="loginframePBtn">
							<font color="white">登 录</font>
					</button>
					</div>
				</fieldset>
			</form>
			<div  style="font-weight:bold;text-align: center;margin-top: 20px">
				<a href="#" onclick="changeLoginType()"  style="text-decoration: none;">手机号登录</a>
			</div>
		</div>
	</div>

	<div id ="loginSmsFrame" class="box-right1" style="display: none;">
		<div>
			<p align="center" style="color: #333333;margin-top: 20px;font-weight: bold;font-size: 24px;">手机号登录</p>
		</div>
		<div style="margin-top: 25px;">
			<div class="errorMsg" id="smdErrorMsg">${SPRING_SECURITY_LAST_EXCEPTION.message}</div>
			<form action="${pageContext.request.contextPath}/loginVerify" method="POST" id="login-form2" class="login-form">
				<fieldset>
					<div style="margin-left: 44px" class="phonePng">
						<img alt="" src="${pageContext.request.contextPath}/static/images/<EMAIL>" class="input-icon">
						<input placeholder="请输入手机号" type="text" id="username" name="username" value="" class="input-box">
					</div>
					<br>
					<div style="margin-left: 44px" class="codePng">
						<img alt="" src="${pageContext.request.contextPath}/static/images/<EMAIL>" class="input-icon">
						<input placeholder="验证码" type="password" id="password2" name="password" class="input-box">
					</div>
					<input type="hidden" value="smsLogin" name="loginWay" id="loginWay">
					<div style="text-align: center;margin-top: 35px">
						<button id="smsLoginBtn" class="loginframePBtn">
							<font color="white">登 录</font></button>
					</div>
				</fieldset>
				<input type="hidden" id="personId" name="personId"/>
			</form>
			<div style="position: relative;z-index: 99;right: -285px;bottom: 104px;">
				<a href="#" type="button"  onclick="doGetSmsCode()" id="codeBtn" style="text-decoration: none;color: #E74B3B">
					获取验证码</a>
			</div>
			<div style="font-weight:bold;text-align: center"><a href="#" onclick="changeLoginType()" style="text-decoration: none;">账号登录</a></div>
		</div>
	</div>

</div>
</div>
<e:base cssModules="" jsModules="all"/>
<script type="text/javascript">
	$(function() {
		// 手机号码验证
		jQuery.validator.addMethod("isMobile", function(value, element) {
			var length = value.length;
			var mobile = /^(1[0-9]{10})$/;
			return this.optional(element) || (length == 11 && mobile.test(value));
		}, "请正确填写您的手机号码");
		// Validation
		$("#login-form").validate({
			// Rules for form validation
			rules : {
				username : {
					required : true,
				},
				password : {
					required : true,
					minlength : 6,
					maxlength : 40
				}
			},
			messages : {
				username : {
					required : '请输入您的用户名'
				},
				password : {
					required : '请输入你的密码'
				}
			},
			tooltip_options: {
				username: {trigger:'focus'},
				password: {trigger:'focus'}
			}
		});
		$("#login-form2").validate({
			rules : {
				username : {
					required : true,
					isMobile :true
				},
				password : {
					required : true,
					minlength : 6,
					maxlength : 40
				}
			},
			messages : {
				username : {
					required : '请输入您的手机号'
				},
				password : {
					required : '请输入你的验证码'
				}
			},
			tooltip_options: {
				username: {trigger:'focus'},
				password: {trigger:'focus'}
			}
		});

		phoneLogin();

		var contextPath = "${pageContext.request.contextPath}";
		$("#normalLoginBtn").click(function(){
			localStorage.msg="normalLoginBtn";
			$(".errorMsg").html("");
			if ($("#login-form").valid()) {
				$("#password").val($.md5($("#password").val()));
				document.forms[0].submit();
			}
		});
		$("#smsLoginBtn").click(function(){
			localStorage.msg="smsLoginBtn";
			$(".errorMsg").html("");
			$("#loginWay").val("smsLogin");
			if ($("#login-form2").valid()) {
				$("#password2").val($.md5($("#password2").val()));
				document.forms[1].submit();
			}
		});

		if(localStorage.msg=="normalLoginBtn"){
			$("#loginSmsFrame").hide();
			$("#loginframe").show();
			excuteing = false;
		}
		if(localStorage.msg=="smsLoginBtn"){
			$("#loginframe").hide();
			$("#loginSmsFrame").show();
			excuteing = false;
		}
		setContextPath(contextPath);
	});
	function doGetSmsCode(){
		$(".errorMsg").html("");
		var userName = $(".box-right1:visible").find("input[name=username]").val();
		if(userName == null || userName == ""){
			popMsg("请输入手机号");
			return;
		}
		ajaxData("/getSmsCode", "userName="+userName, function(data){
			if(data.success == true){
				popMsg("验证码已经发送");
			}else{
				var error = data.resMsg;
				$(".errorMsg").html(error);
			}
		});
	}
	var s = 60, t;
	function times(){
		$("#codeBtn").attr("disabled","true");
		s--;
		$("#codeBtn").val("重新发送("+s+")");
		t = setTimeout(times, 1000);
		if ( s <= 0 ){
			s = 60;
			clearTimeout(t);
			$("#codeBtn").removeAttr("disabled");
			$("#codeBtn").val("获取验证码");
		}
	}
	var excuteing = false;
	function changeLoginType(){
		$(".errorMsg").html("");
		if(excuteing == true){
			return;
		}
		excuteing = true;
		if($("#loginframe").is(":hidden")){
			$("#loginSmsFrame").hide();
			$("#loginframe").show();
			excuteing = false;
		}else{
			$("#loginframe").hide();
			$("#loginSmsFrame").show();
			excuteing = false;
		}
	}

	var wxsubmitted = false;
	var loginBoolean = "";
	var cancelLoginBoolean = "";
	var sureBoolean = false;
	function superAdminLogin(){
		$("#loginSmsFrame").hide();
		$("#loginframe").show();
		excuteing = false;
	}

	function phoneLogin(){
		$("#loginSmsFrame").show();
		excuteing = false;
		$("#loginframe").hide();
	}
</script>
</body>
</html>
