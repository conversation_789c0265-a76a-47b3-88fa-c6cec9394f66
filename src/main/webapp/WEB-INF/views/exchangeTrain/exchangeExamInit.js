$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });

    $("#btnClear").bind("click", function() {
        document.getElementById("examMonitoringDto").reset();
        let str = '<option value="">全部</option>';
        $("#examGradeType").attr("disabled",true);
        $('#examGradeType').append(str)
        search();
    });

    $('#examType').change(function () {
        getSpecialGradeList();
    });
})
function search(){
    ajaxTableQuery("tableAll", "/exchangeExam/queryExchangeExamMonitoringList", $("#examMonitoringDto").formSerialize()+'&examType=588624');
}

function getSpecialGradeList(){
    let params ={
        examId:$('#examType').val(),
    }
    ajaxData("/examMonitoring/getExamGradeTypeList",params, function (res) {
        $('#examGradeType').empty();
        let str = '<option value="">全部</option>';
        if($('#examType').val() === "" || $('#examType').val() === null){
            $("#examGradeType").attr("disabled",true);
            $('#examGradeType').append(str)
        }else{
            res.forEach(item=>{
                str += '<option value="' + item.gradeId +'">' + item.gradeName +'</option>'
            })
            $("#examGradeType").attr("disabled",false);
            $('#examGradeType').append(str)
        }
    })
}


// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    
    return meta.row + 1;
}

function companyCode(data, type, row, meta) {
    if(data.companyCode === '' || data.companyCode === null){
        return "- -";
    }else{
        return data.companyCode;
    }
}

function ifCertificate(data, type, row, meta) {
    if(data.ifCertificate === '1'){
        return "是";
    }else{
        return "否";
    }
}

function examStartTime(data, type, row, meta) {
    if(data.examStartTime === '' || data.examStartTime === null){
        return "- -";
    }else{
        return data.examStartTime;
    }
}

function submitTime(data, type, row, meta) {
    if(data.submitTime === '' || data.submitTime === null){
        return "- -";
    }else{
        return data.submitTime;
    }
}


function timeSlot(data, type, row, meta) {
    if(data.timeSlot === 0 || data.timeSlot === null){
        return "- -";
    }else{

        return timeCalculation(data.timeSlot);
    }
}

//时间计算
function timeCalculation(v) {
    // 将时间转为年月日时分秒
    let time = '';
    if (v / 60 >= 1) {
        time = Math.floor(v / 60) + '小时'
        v = v % 60
    }
    if (v / 1 >= 1) {
        time += Math.floor(v / 1) + '分'
        v = v % 1
    }
    return time
}

function score(data, type, row, meta) {
    if(data.score === '' || data.score === null){
        return "- -";
    }else{
        return data.score;
    }
}

function columnOperation(data, type, row, meta) {
    let str = '';
    if (data.examStatusValue !== '0') {
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="selectMonitor(\'' + data.recordId + '\',\'' + data.answerId + '\',\'' + data.examRank + '\')">查看监控</span>';
    }
    if (data.examStatusValue === '4' || data.examStatusValue === '5') {
        str += '<span style="color: #00a0e9">|</span>';
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="viewExam(\'' + data.answerId + '\',\'' + data.paperId + '\',\'' + data.createUser + '\')">查看试卷</span>';
        str += '<span style="color: #00a0e9">|</span>';
        str += '<span  style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="deleteExam(\'' + data.recordId + '\')">删除</span>';
    }
    if (data.examStatusValue === '0') {
        str += '<span style="color: #00a0e9">- -</span>';
    }
    return str;
}

function viewExam(answerId,paperId,createUser){
    let params = {
        createUser : createUser
    }
    ajaxData("/examMonitoring/getUserType",params, function (res){
        var userType = res;
        var url = examBaseUrl + 'ui/exam/finishLook?answerId='+answerId+'&paperLook=1&comeType=1&status=1&indexType=0&userType='+userType+'&paperId='+paperId;
        window.open(encodeURI(url));
    })

}

function deleteExam(recordId){
    popConfirm("当前操作不可恢复，确认删除?", function () {
        let param = {
            recordId:recordId,
            status:"0"
        }
        ajaxData("/examMonitoring/updateExamMonitoringStatus",param, function (res) {
            if(res){
                popMsg("删除成功");
            }else{
                popMsg("删除失败");
            }
            callBackAddExam();
        })
    });
}

function callBackAddExam(){
    ajaxTableReload("tableAll", false);
}

function selectMonitor(recordId,answerId,examRank){
    let param = {
        recordId:recordId,
        answerId:answerId,
        examRank:examRank
    }
    popWin('考试监控', '/examMonitoring/selectMonitorInit', param, '98%', '98%', callBackAddExam, '', callBackAddExam);
}