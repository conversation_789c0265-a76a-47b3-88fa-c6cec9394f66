$(document).ready(function() {

    //列表查询
    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        $("#exchangeTrainDtoForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        tSelectInit()
        search();
        dateInit();
    });

    //新增
    $("#addBtn").bind("click", function() {
        popWin("新增培训", "/exchangeTrain/exchangeTrainAddInit", {}, "98%", "98%", winCallback);
    });
})
//列表查询
function search() {
    ajaxTableQuery("table", "/exchangeTrain/getExchangeTrainList",$("#exchangeTrainDtoForm").formSerialize());
}

//清空条件
$('#btnClear').click(function () {
    document.getElementById("queryForm").reset();
    $(".t-select").each(function (n, obj) {
        $('input[name="' + obj.id + '"]').val("");
    });
    search();
});

//序号
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function columnOperation(data, type, row, meta) {
    let content = '';

    if (document.getElementById("editAuth")) {
        content += '<a href="javascript:void(0)" onclick="editTrainInfo(\''+ data.id +'\')" title="编辑培训信息">编辑</a>';
    }

    if (document.getElementById("updateAuth")) {
        content += ' | ';
        content += '<a href="javascript:void(0)" onclick="deleteTrainInfo(\''+ data.id +'\')" title="删除培训信息">删除</a>';
        if (data.publish === '已发布') {
            content += ' | ';
            content += '<a href="javascript:void(0)" onclick="cancelRelease(\''+ data.id +'\')">取消发布</a>';
        }else {
            content += ' | ';
            content += '<a href="javascript:void(0)" onclick="release(\''+ data.id +'\')">发布</a>';
        }
    }

    if (!content) {
        content += '-';
    }
    return content;
}

function editTrainInfo(id) {
    popWin("编辑培训", "/exchangeTrain/exchangeTrainAddInit", {id:id}, "98%", "98%", winCallback);
}

function deleteTrainInfo(id) {
    parent.popConfirm("确认删除?", function () {
        var param = {
            id: id,
            field:'status',
            publish: '0'
        };
        ajaxData("/exchangeTrain/updateField", param, callBacktrain);
    });
}

function release(id) {
    parent.popConfirm("确认发布?", function () {
        var param = {
            id: id,
            field:'publish',
            publish: '1'
        };
        ajaxData("/exchangeTrain/updateField", param, callBacktrain);
    });
}

function cancelRelease(id) {
    parent.popConfirm("确认取消发布?", function () {
        var param = {
            id: id,
            field:'publish',
            publish: '0'
        };
        ajaxData("/exchangeTrain/updateField", param, callBacktrain);
    });
}

function callBacktrain() {
    ajaxTableReload("table", false);
}

//活动时间
function renderColumnTrain(data, type, row, meta) {
    if(data.trainStartTime.substring(0,10) === data.trainEndTime.substring(0,10)){
        return data.trainStartTime + '-' + data.trainEndTime.slice(11);
    }else {
        return data.trainStartTime + '-' + data.trainEndTime;
    }
}

//报名时间
function renderColumnSignUp(data, type, row, meta) {
    if(data.signUpStartTime.substring(0,10) === data.signUpEndTime.substring(0,10)){
        return data.signUpStartTime + '-' + data.signUpEndTime.slice(11);
    }else {
        return data.signUpStartTime + '-' + data.signUpEndTime;
    }
}

//测试时间
function renderColumnTest (data, type, row, meta) {
    if(data.testStartTime.substring(0,10) === data.testEndTime.substring(0,10)){
        return data.testStartTime + '-' + data.testEndTime.slice(11);
    }else {
        return data.testStartTime + '-' + data.testEndTime;
    }
}

function winCallback(paraWin, paraCallBack) {
    ajaxTableReload("table", false);
}
