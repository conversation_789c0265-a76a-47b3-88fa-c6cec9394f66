var selCourse = [];

$(document).ready(function () {

    $(window).keydown( function(e) {
        var key = window.event?e.keyCode:e.which;
        if(key.toString() == "13"){
            return false;
        }
    });
    var courseMapDtos = $("#courseMapDtos").val();

    if(courseMapDtos != ''){
        selCourse = courseMapDtos.split(",")
    }

    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        tSelectInit();
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        selCourse = [];
        search();
    });
});

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function search() {
    ajaxTableQuery("tableAll", "/homepageConfig/queryChooseCourse", $("#queryForm").formSerialize());
}
function rcIndex(data, type, row, meta) {
    var flag = false;

    selCourse.forEach((item)=>{
        if(item == data.relationId){
            flag = true
        }
    })

    var str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;" onclick="selCompany(this)">';
    if(flag){
        str += '<input  type="checkbox" checked name="checkRow" class="selDeclare"  d-id="' + data.relationId
            + '" class="hidden">';
    }else {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.relationId
            + '" class="hidden">';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="relationId" value="' + data.relationId + '">'
    str += '</div>';
    return str;
}

function courseName(data, type, row, meta) {
    var str = '<div style="cursor:pointer;width: 100%" onclick="selCourseName(this)">'+data.courseName+'</div>'
    return str;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        selCourse.push(id)
    } else {
        selCourse.some((item,i)=>{
            if(item === id){
                selCourse.splice(i,1)
            }
        })
    }
}
function submitOn() {
    if (selCourse.length >0){
        closeWinCallBack(selCourse.toString())
    }else {
        popMsg("请选择课程")
    }

}