<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="panel"  style="padding: 15px 15px 22px">
        <form:form modelAttribute="exchangeTrainDto" id="exchangeTrainDtoForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">培训名称</label>
                    <div class="col-md-3">
                        <form:input path="title" placeholder="请输入培训名称" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">发布状态</label>
                    <div class="col-md-3">
                        <form:select path="publish" cssClass="form-control">
                            <form:option value="">全部</form:option>
                            <form:options items="${publishList}" itemLabel="label" itemValue="value" />
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-8">
                    </div>
                    <div class="col-md-4">
                        <div style="float: right;padding-top: 10px">
                            <sec:authorize access="hasAuthority('RES_EXCHANGE_ADD_INIT_AUTHORITY_3')" >
                                <span id="addBtn" class="btn btn-primary">新增</span>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_EXCHANGE_TRAIN_QUERY_AUTHORITY_3')" >
                                <span id="btnQuery" class="btn btn-primary">查询</span>
                            </sec:authorize>
                            <span id="btnClear" class="btn btn-default">清空条件</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_EXCHANGE_ADD_INIT_AUTHORITY_3')" >
            <%--编辑权限--%>
            <input type="hidden" id="editAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_EXCHANGE_TRAIN_UPDTE_AUTHORITY_3')" >
            <%--删除/发布权限--%>
            <input type="hidden" id="updateAuth" value="true">
        </sec:authorize>
        <div>
            <e:grid id="table" action="/exchangeTrain/getExchangeTrainList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:2%"/>
                <e:gridColumn label="培训名称" displayColumn="title" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="所属单位" displayColumn="exchangeName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="活动时间" renderColumn="renderColumnTrain" orderable="false"
                              cssClass="text-center" cssStyle="width:12%"/>
                <e:gridColumn label="报名时间" renderColumn="renderColumnSignUp" orderable="false"
                              cssClass="text-center" cssStyle="width:12%"/>
                <%--<e:gridColumn label="课程观看时间" displayColumn="price" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>--%>
                <e:gridColumn label="考试时间" renderColumn="renderColumnTest" orderable="false"
                              cssClass="text-center" cssStyle="width:12%"/>
                <e:gridColumn label="发布状态" displayColumn="publish" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="操作" renderColumn="columnOperation"  orderable="false"
                              cssClass="text-center" cssStyle="width:10%;"/>
            </e:grid>
        </div>
</div>
</body>
</html>
