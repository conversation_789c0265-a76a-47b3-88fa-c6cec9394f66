<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <script type="text/javascript">
        <%--var userId = "${userId}";--%>
        <%--var personName = "${personName}";--%>
        <%--var orgId = "${orgId}";--%>
        var examBaseUrl = "${examBaseUrl}";
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="examMonitoringDto" id="examMonitoringDto" >
            <div class="row" style="margin-top: 15px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="personName" placeholder="请输入姓名" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">公司名称/代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCodeAndName" placeholder="请输入公司" cssClass="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_EXCHSNGE_QUERY_EXAM_LIST_AUTHORITY_3')" >
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <span id="btnClear" class="btn btn-default">清空全部</span>
                </div>
            </div>
        </form:form>
    </div>
</div>
<div class="panel-body">
    <div class="row">
        <e:grid id="tableAll" action="/exchangeExam/queryExchangeExamMonitoringList?examType=588624" cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:5%" />
            <e:gridColumn label="姓名" displayColumn="personName" orderable="false"
                          cssClass="text-center" cssStyle="width:6%" />
            <e:gridColumn label="考试名称" displayColumn="examName" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
            <e:gridColumn label="证券代码" renderColumn="companyCode" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
            <e:gridColumn label="公司简称" displayColumn="companyName"  orderable="false" cssClass="text-center"
                          cssStyle="width:8%" />
            <e:gridColumn label="考试状态" displayColumn="examStatus" orderable="false" cssClass="text-center"
                          cssStyle="width:10%" />
            <e:gridColumn label="开始时间" renderColumn="examStartTime" orderable="false" cssClass="text-center"
                          cssStyle="width:10%" />
            <e:gridColumn label="交卷时间" renderColumn="submitTime" orderable="false" cssClass="text-center"
                          cssStyle="width:10%" />
            <e:gridColumn label="作答时间" renderColumn="timeSlot" orderable="false" cssClass="text-center"
                          cssStyle="width:6%" />
            <e:gridColumn label="分数" renderColumn="score" orderable="false" cssClass="text-center"
                          cssStyle="width:6%" />
<%--            <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"--%>
<%--                          cssStyle="width:12%" />--%>
        </e:grid>
    </div>
</div>
</div>

</body>
</html>
