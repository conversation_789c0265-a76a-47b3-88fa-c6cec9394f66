let _ImgUploadUrl = contextPath + '/filetempupload';
$(document).ready(function () {

    livePicInit();

    ueEditorInit();

    dateInit();

    $('#saveBtn').bind('click', function() {
        if(getValue($("#title").val()) == ""){
            popMsg("请填写培训名称");
            return;
        }
        if(getValue($("#trainId").val()) == "") {
            popMsg("请选择所属单位");
            return;
        }
        if(getValue($("#testId").val()) == "") {
            popMsg("请关联考试");
            return;
        }
        if(getValue($("#trainTimeStr").val()) == "") {
            popMsg("请选择活动时间");
            return;
        }
        if(getValue($("#signUpTimeStr").val()) == "") {
            popMsg("请选择报名时间");
            return;
        }
        if(getValue($("#testTimeStr").val()) == "") {
            popMsg("请选择考试时间");
            return;
        }
        if(getValue($('input[name="ifVerify"]:checked').val()) == "") {
            popMsg("请选择是否需要审核预约");
            return;
        }
        if(getValue($("#backImg").val()) == "") {
            popMsg("请上传背景图片");
            return;
        }
        popConfirm("确认保存?", function () {
            savePackInfo();
        });

    });

    $('#closeBtn').bind('click', function() {
        popConfirm('确定放弃吗？', function () {
            closeWin();
        });
    });

  //  calculatePrice()
})


function livePicInit() {
    $('#livePicRemoveBtn').bind('click', function () {
        clearLivePic();
    });
    $('#livePicFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#livePicFileId").val(file.fileRelaId);
                $("#backImg").val(file.fileRelaId);
                $("#picAttId").val("");
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });

    $("#livePicFile").imageUrlUploadPreview({
        Img: "livePicImgSrc",
        Width: 50,
        Height: 50
    });
}

function clearLivePic() {
    popConfirm("确认删除图片吗？", function () {
        $("#livePicFileId").val("");
        $("#livePicDiv").css("display", "none");
        $("#livePicFile").val("");
        $("#backImg").val("");
    });
}

//选择课程
function chooseCourse(id) {
    var courseMapDtos = ''
    var type = '1'
    $("#"+id).find("tr").each(function (n,obj){
        if(courseMapDtos === ''){
            courseMapDtos = $(obj).find("input").val()
        }else {
            courseMapDtos = courseMapDtos +','+ $(obj).find("input").val()
        }
    })
    if(id === 'liveTBody'){
        type = '0'
    }
    let params = {
        courseMapDtos: courseMapDtos,
        type: type,
        trainId: $("#trainId").val()
    }
    parent.popWin("选择课程", "/exchangeTrain/exchangeTrainCourse", params, "70%", "80%", function (data) {
        let param = {
            courseMapDtos:data,
            type: type
        }
        ajaxData("/exchangeTrain/getCourseInfo",param,function (data1) {
            if(data1){
                $("#"+id).empty()
                data1.some((item,index)=>{
                    var sort = index+1
                    var html = '<tr><input type="hidden" value="'+item.relationId+'"/>'
                        + '<td class="serialNumber">'+sort+'</td>'
                        + '<td>'+item.courseName+'</td>'
                        + '<td><i class="icon-arrow-up" title="上移" onclick="moveUp(this)"></i>\n' +
                        ' <i class="icon-arrow-down" title="下移" onclick="moveDown(this)"></i>\n' +
                        ' <i class="icon icon-trash" title="删除" onclick="deleteCourse(this)"></i></td></tr>'
                    $("#"+id).append(html)
                })
            }
        })
    })
}


//删除关联课程
function deleteCourse(obj){
    popConfirm("确认删除?",function() {
        $(obj).parent().parent().remove();//移除行
        sortMyTable()
        });
}

function sortMyTable(){
    $.each($("#courseTBody tr"),function(index,item){
        $(this).find(".serialNumber").html(index+1);     //列表重排序号
    })

    $.each($("#liveTBody tr"),function(index,item){
        $(this).find(".serialNumber").html(index+1);     //列表重排序号
    })
}

//上移
function moveUp(obj){
     if($(obj).parents("tr").index() != 0){
         let obj1 = $(obj).parent().parent()
         let obj2 = $(obj1).prev()
         changeDoms(obj1,obj2)
     }else {
         popMsg("无法上移")
     }
}

//下移
function moveDown(obj){
    var tr = $(obj).parents("tr");
    var trLength = $('#courseTBody').children('tr').length;
    if (tr.index() != trLength - 1) {
        let obj1 = $(obj).parent().parent()
        let obj2 = $(obj1).next()
        changeDoms(obj1,obj2)
    }else {
        popMsg("无法下移")
    }
}

function selectExam(){
    var params = {
        //暂时写死
        examType : '588624'
    }
    popWin("选择考试", "/specialConfig/selectExamPop", params, "90%", "90%", function (data) {
        $('.selectExam-class').css('display', 'block');
        $("#testId").val(data.examId);
        $("#testName").html(data.examName);
    })
}

function delExamName(obj) {
    $("#testId").val("");
    $(".selectExam-class").css('display', 'none')
}

function moveLiveDown(obj) {
    var tr = $(obj).parents("tr");
    var trLength = $('#liveTBody').children('tr').length;
    if (tr.index() != trLength - 1) {
        let obj1 = $(obj).parent().parent()
        let obj2 = $(obj1).next()
        changeDoms(obj1,obj2)
    }else {
        popMsg("无法下移")
    }
}

//交换两个节点
function changeDoms(d1,d2){
    var d11 = $('<hr/>')
    var d22 = $('<hr/>')
    $(d1).before(d11)
    $(d2).before(d22)

    $(d22).after(d1)
    $(d11).after(d2)

    $(d11).remove()
    $(d22).remove()

    sortMyTable()
}

function savePackInfo() {
    //关联课程
    var courseList = []
    $("#courseTBody").find("tr").each(function (n,obj){
        var relationId = $(obj).find("input").val()
        var sort = $(obj).find(".serialNumber").text()
        var courseMap = {
            relationId:relationId,
            sort:sort
        }
        courseList.push(courseMap)
    })

    var liveList = []
    $("#liveTBody").find("tr").each(function (n,obj){
        var relationId = $(obj).find("input").val()
        var sort = $(obj).find(".serialNumber").text()
        var liveMap = {
            relationId:relationId,
            sort:sort
        }
        liveList.push(liveMap)
    })

    let param = {
        id:$("#id").val(),
        testId:$("#testId").val(),
        title:$("#title").val(),
        trainId:$("#trainId").val(),
        trainTimeStr:$("#trainTimeStr").val(),
        signUpTimeStr:$("#signUpTimeStr").val(),
        testTimeStr:$("#testTimeStr").val(),
        ifVerify:$('input[name="ifVerify"]:checked').val(),
        content:$('textarea[name="content"]').val(),
        testId:$("#testId").val(),
        backImg:$("#backImg").val(),
        courseList:courseList,
        liveList:liveList
    }
    $.ajax({
        url: contextPath + '/exchangeTrain/saveExchangeTrainInfo',
        data: JSON.stringify(param),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (req) {
            if (req.success) {
                closeWinCallBack(req);
            }
        },
        error: function () {
            popMsg("保存失败");
        }
    });
}

$.fn.extend({
    imageUrlUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "livePicImgSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#backImg").val("");
            $("#livePicDiv").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});

/**
 * 富文本初始化
 */
function ueEditorInit() {
    UE.delEditor('content');
    um = UE.getEditor('content', {
        initialFrameHeight : 150,
        textarea : "content",
        elementPathEnabled : false,
        autoHeightEnabled : false
    });
}

function dateInit() {
    var trainTimeStr = $('#trainTimeStr').val();
    if (trainTimeStr.trim() != '') {
        var trainTimes = trainTimeStr.split(' 至 ');
        dataRangePickerInit($('#trainTimeStr'), trainTimes[0], trainTimes[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 5});
    } else {
        dataRangePickerInit($('#trainTimeStr'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 5});
    }

    var signUpTimeStr = $('#signUpTimeStr').val();
    if (signUpTimeStr.trim() != '') {
        var signUpTimes = signUpTimeStr.split(' 至 ');
        dataRangePickerInit($('#signUpTimeStr'), signUpTimes[0], signUpTimes[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 5});
    } else {
        dataRangePickerInit($('#signUpTimeStr'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 5});
    }

    var testTimeStr = $('#testTimeStr').val();
    if (testTimeStr.trim() != '') {
        var testTimes = testTimeStr.split(' 至 ');
        dataRangePickerInit($('#testTimeStr'), testTimes[0], testTimes[1], function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 5});
    } else {
        dataRangePickerInit($('#testTimeStr'), null, null, function () {
        }, function () {
        },{timePicker:true,timePicker12Hour:false, timePicker24Hour:true, format: 'YYYY-MM-DD HH:mm:ss', timePickerIncrement: 5});
    }
}
