<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        #livePicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .isNumber{
            border: 1px solid #ccc;
            height: 30px;
            width: 65%;
            background: #F5F5F5;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
    <form:form modelAttribute="exchangeTrainDto" id="queryForm" autocomplete="off">
        <form:hidden path="id"/>
        <form:hidden path="testId"/>
        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>培训名称：</label>
            <div class="col-md-7">
                <form:input path="title" placeholder="培训名称" cssClass="form-control"/>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>所属单位：</label>
            <div class="col-xs-3 daterange" style="width: 370px">
                <form:select path="trainId" cssClass="form-control">
                    <form:options items="${trainList}" itemLabel="label" itemValue="value" />
                </form:select>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 5px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>关联考试：</label>
            <span class="col-md-1 btn btn-primary" style="width: 100px; display: block;margin-left: 10px;" onclick="selectExam()">选择考试</span>
            <span class="col-md-2 selectExam-class" style="color: #00b7ee;font-size: 16px;">
                        <span id="testName">${exchangeTrainDto.testName}</span>
                        <span style="cursor: pointer;margin-left: 50px" onclick="delExamName(this)">删除</span>
                    </span>
        </div>
        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>活动时间：</label>
            <div class="col-xs-3 daterange" style="width: 370px">
                <form:input path="trainTimeStr" placeholder="请选择活动时间" cssClass="form-control" />
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>报名时间：</label>
            <div class="col-xs-3 daterange" style="width: 370px">
                <form:input path="signUpTimeStr" placeholder="请选择报名时间" cssClass="form-control" />
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>考试时间：</label>
            <div class="col-xs-3 daterange" style="width: 370px">
                <form:input path="testTimeStr" placeholder="请选择考试时间" cssClass="form-control" />
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 20px" >
            <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>是否需要审核预约</label>
            <div class="col-md-2 no-padding">
                <form:radiobutton path="ifVerify" value="0" id="ifVerify1"/>
                <label for="ifVerify1">否</label>
                <form:radiobutton path="ifVerify" value="1" id="ifVerify2" cssStyle="margin-left:6px"/>
                <label for="ifVerify2">是</label>
            </div>
        </div>
        <div class="col-md-12">
            <label class="col-xs-1 control-label">
                <span style="color: #FF0000;font-weight: bold;">*</span>背景图片：
                <div style="font-size: 12px;color: red;">（请上传16:9的图片）</div>
            </label>
            <div class="col-xs-6" style="margin: 10px 0">
                <div id="livePicDiv" class="col-xs-12 no-padding"
                     <c:if test="${empty exchangeTrainDto.backImg}" >style="display: none;" </c:if>
                >
                    <img id="livePicImgSrc" src="${exchangeTrainDto.backImg}" style="width: 320px;height: 180px;"/>
                    <input type="hidden" name="livePicFileId" id="livePicFileId" value="${exchangeTrainDto.backImg}"/>
                    <form:hidden path="backImg"/>
                </div>
                <div class="col-xs-12 no-padding controls live-pic-btn-row">
                    <a href="javascript:void(0);" id="livePicUploadBtn" class="file btn btn-warning btn-facebook btn-outline">
                        <i class="fa fa-upload"> </i> 上传图片
                        <input id="livePicFile" type="file" name="files"/>
                    </a>
                    <input type="button" id="livePicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                </div>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 20px">
            <label class="col-md-1 control-label">培训简介：</label>
            <div class="col-md-9 controls">
                <script id="content" type="text/plain" >${ exchangeTrainDto.content }</script>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 15px">
            <label class="col-xs-1 control-label">
                选择课程
            </label>
            <div class="col-xs-6">
                <input type="button" value="选择课程" onclick="chooseCourse('courseTBody')"/>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 5px">
            <label class="col-xs-1 control-label">
            </label>
            <div class="col-xs-6">
                <table id="courseTable" class="table table-bordered no-margin" style="text-align: center;width: 80%;float:left;" >
                    <thead>
                    <tr>
                        <th style="text-align: center" width="10%">序号</th>
                        <th style="text-align: center" width="50%">课程名称</th>
                        <th style="text-align: center" width="10%">操作</th>
                    </tr>
                    </thead>
                    <tbody id="courseTBody">
                    <c:forEach items="${exchangeTrainDto.courseList}" var="item" varStatus="status">
                        <tr>
                            <input type="hidden" value="${item.relationId}"/>
                            <td class="serialNumber">${status.index+1}</td>
                            <td>${item.courseName}</td>
                            <td>
                                <i class="icon-arrow-up" title="上移" onclick="moveUp(this)"></i>
                                <i class="icon-arrow-down" title="下移" onclick="moveDown(this)"></i>
                                <i class="icon icon-trash" title="删除" onclick="deleteCourse(this)"></i>
                            </td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 15px">
            <label class="col-xs-1 control-label">
                选择直播
            </label>
            <div class="col-xs-6">
                <input type="button" value="选择直播" onclick="chooseCourse('liveTBody')"/>
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 5px">
            <label class="col-xs-1 control-label">
            </label>
            <div class="col-xs-6">
                <table class="table table-bordered no-margin" style="text-align: center;width: 80%;float:left;" >
                    <thead>
                    <tr>
                        <th style="text-align: center" width="10%">序号</th>
                        <th style="text-align: center" width="50%">直播名称</th>
                        <th style="text-align: center" width="10%">操作</th>
                    </tr>
                    </thead>
                    <tbody id="liveTBody">
                    <c:forEach items="${exchangeTrainDto.liveList}" var="item" varStatus="status">
                        <tr>
                            <input type="hidden" value="${item.relationId}"/>
                            <td class="serialNumber">${status.index+1}</td>
                            <td>${item.courseName}</td>
                            <td>
                                <i class="icon-arrow-up" title="上移" onclick="moveUp(this)"></i>
                                <i class="icon-arrow-down" title="下移" onclick="moveLiveDown(this)"></i>
                                <i class="icon icon-trash" title="删除" onclick="deleteCourse(this)"></i>
                            </td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-12" style="text-align: center;padding-top: 30px">
            <input type="button" id="closeBtn" class="btn btn-default" value="关闭">
            <input type="button" id="saveBtn" class="btn btn-primary" value="保存">
        </div>
    </form:form>
    </div>
</div>
</body>
</html>
