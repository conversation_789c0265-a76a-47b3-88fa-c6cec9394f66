
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js />
    <style type="text/css">
        table td {
            white-space:pre-wrap !important;
            word-wrap: break-word !important;
        }
        .dataTables_scrollHead,
        .dataTables_scrollBody {
            overflow: visible !important;
        }
        .jedate .jedate-content .daystable td.action,
        .jedate .jedate-content .daystable td.action:hover,
        .jedate .jedate-content .daystable td.action .lunar,
        .jedate .jedate-footbtn .timecon,
        .jedate .jedate-footbtn .btnscon span,
        .jedate .jedate-time .hmslist ul li.action,
        .jedate-time .hmslist ul li.action:hover {
            background: #337ab7!important;
        }
        .dataBox {
            position: relative;
            padding: 0;
            margin: 0;
            float: left;
            cursor: pointer;
        }
        .add-on {
            position: absolute;
            top: 7px;
            right: 10px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 40px;">
        <i class="icon icon-list">预约信息</i>
    </div>
    <div class="panel-body">
        <form:form action=""  modelAttribute="exchangeSubscribeDto" id="exchangeSubscribeDtoForm">
            <div class="row">
                <label class="col-md-1 control-label" style="text-align:center">培训名称</label>
                <div class="col-md-3">
                    <form:select path="infoId" class="form-control">
                        <form:option value="">全部</form:option>
                        <form:options items="${trainNameList}" itemLabel="label" itemValue="value" />
                    </form:select>
                </div>
                <label class="col-md-1 control-label" style="text-align:center">审核状态</label>
                <div class="col-md-3">
                    <form:select path="examineState" cssClass="form-control">
                        <form:option value="">全部</form:option>
                        <form:options items="${subscribeExamineState}" itemLabel="label" itemValue="value" />
                    </form:select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-offset-9 col-md-3" align="right">
                    <sec:authorize access="hasAuthority('RES_SUBSCRIBE_TRAIN_QUERY_AUTHORITY_3')" >
                        <input type="button" id="queryBtn" class="btn btn-primary" value="查询">
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_SUBSCRIBE_TRAIN_EXPORT_AUTHORITY_3')" >
                        <input type="button" id="exportByLive" class="btn btn-primary" value="选择培训后导出">
                    </sec:authorize>
                    <input type="button" id="resetBtn" class="btn btn-primary" value="清空">
                </div>
            </div>
        </form:form>

        <sec:authorize access="hasAuthority('RES_SUBSCRIBE_TRAIN_EXAMINE_AUTHORITY_3')" >
            <%--审核权限--%>
            <input type="hidden" id="checkAuth" value="true">
        </sec:authorize>
        <div class="example">
            <hr>
        </div>
        <div class="ibox float-e-margins">
            <e:grid id="liveInfoTable" action="/trainSubscribe/getTrainSubscribeList" cssClass="table table-striped table-hover"  >
                <e:gridColumn label="序号" displayColumn="accId" orderable="false" cssClass="text-center" cssStyle="width:1%;"/>
                <e:gridColumn label="培训名称" displayColumn="trainName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="预约人" renderColumn="renderSubscribeUserName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="用户类型" displayColumn="subscribeUserType" orderable="false" cssClass="text-center" cssStyle="width:5%; word-wrap: break-word;"/>
                <e:gridColumn label="审核状态" renderColumn="renderExamineStateName" orderable="false" cssClass="text-center" cssStyle="width:12%; word-wrap: break-word;"/>
                <e:gridColumn label="审核人员" displayColumn="examineUserName" orderable="false" cssClass="text-center" cssStyle="width:8%; word-wrap: break-word;"/>
                <e:gridColumn label="审核时间" displayColumn="examineTime" orderable="false" cssClass="text-center" cssStyle="width:10%; word-wrap: break-word;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false" cssClass="text-center" cssStyle="width:8%;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
