
let _startDate = {maxDate: "2099-12-31 23:59:59"}, _endDate = {minDate: "1900-01-01 00:00:00"};

$(document).ready(function() {

    $("#queryBtn").bind("click", function() {
        tableQuery();
    });

    $("#resetBtn").bind("click", function() {
        document.getElementById("exchangeSubscribeDtoForm").reset();
        tableQuery();
    });

    $("#exportByLive").bind("click", function() {
        exportByLiveData();
    });

    //禁用Enter键
    document.onkeydown = function (e) {
        //捕捉回车事件
        var ev = (typeof event != 'undefined') ? window.event : e;
        if (ev.keyCode == 13 || event.which == 13) {
            return false;
        }
    }
});

/**
 * 列表查询
 */
function tableQuery() {
    var infoId = $("#infoId").val();

    var param = {
        infoId : infoId,
    }
    ajaxTableQuery("liveInfoTable", "/trainSubscribe/getTrainSubscribeList", $("#exchangeSubscribeDtoForm").formSerialize(param));
}

function saveCallBack() {
    popMsg('保存成功！');
    tableReload(true);
}

function updateCallBack() {
    popMsg('保存成功！');
    tableReload(false);
}

function tableReload(resetPaging) {
    ajaxTableReload("liveInfoTable", resetPaging);
}

function exportByLiveData(){
    var infoId = $("#infoId").val();
    var param = {
        infoId : infoId,
    }
    window.open(contextPath + "/trainSubscribe/exportSubscribeInfo?" + $("#exchangeSubscribeDtoForm").serialize(param));
}

function renderExamineStateName(data, type, row, meta) {
    if(data.examineState == '0'){
        return '待审核'
    }else if(data.examineState == '1'){
        return '审核通过'
    }else {
        return '审核未通过'
    }
}

/**
 * 操作列
 * @param data
 * @param type
 * @param row
 * @param meta
 * @returns {*}
 */
function renderColumnOperation(data, type, row, meta) {
    let content = '';

    if (document.getElementById("editAuth")) {
        if(data.examineState == '0'){
            content += '<a href="javascript:void(0)" onclick="examineSubscribe(\''+ data.id +'\',\'' + data.phone + '\',\'' + data.trainName + '\',\'' + data.examineState + '\')" title="审核">审核</a>';
        }else{
            content += '<a href="javascript:void(0)" onclick="examineSubscribe(\''+ data.id +'\',\'' + data.phone + '\',\'' + data.trainName + '\',\'' + data.examineState + '\')" title="修改审核状态">修改审核状态</a>';
        }
    } else {
        content += '-'
    }

    return content;
}
function renderSubscribeUserName(data, type, row, meta) {
    let content = '<a href="javascript:void(0)" onclick="personDetail(\''+ data.subscribeUserId +'\')">' + data.subscribeUserName + '</a>';

    return content;
}

/**
 * 审核预约
 *
 */
function examineSubscribe(id,phone,trainName,examineState1) {
    var content = '<div style="width: 100%;padding-top: 10px;">' +
        '<div class="col-md-6">' +
        '<select id="examineStateCheck" class="form-control">' +
        '<option value="1" selected>通过</option>' +
        '<option value="2">不通过</option>' +
        '</select>' +
        '</div>' +
        '<div class="col-md-12">' +
        '<textarea rows="4" id="failReason" class="form-control" style="width: 100%"></textarea>'+
        '</div>' +
        '</div>';
    var layerIndex = layer.open({
        type:'1',
        title: '审核预约',
        content:content,
        btn: ['确认', '取消'],
        area: ['400px', '270px'],
        yes: function(index, layero){
            if($("#examineStateCheck").val() != examineState1){
                var param = {
                    id:id,
                    examineState:$("#examineStateCheck").val(),
                    failReason:$("#failReason").val(),
                    phone:phone,
                    trainName:trainName
                };
                ajaxData('/trainSubscribe/saveExamineInfo',param,function (data){
                    popMsg(data);
                    tableReload(true);
                    layer.close(layerIndex);
                })
            }else {
                popMsg('审核成功！');
                layer.close(layerIndex);
            }
        },
        btn2: function(index, layero){

        }
    });
}

function callBackAddUser() {
    ajaxTableReload("liveInfoTable",false);
}

function personDetail(id) {
    var param ={
        id:id,
    };
    parent.popWin('查看用户信息', '/trainUserManage/addUserInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);
}