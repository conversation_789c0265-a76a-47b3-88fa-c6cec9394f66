$(document).ready(function () {

})

//刷新WEBbanner
function refreshBanner(useType){
    ajaxData("/homepageConfig/getBanner", "", function (data) {
        //刷新table
        $("#schBannerBody").empty()
        var html = ""
        for(var i=0;i<data.length;i++){
            var itemName = ""
            if (data[i].relationItemName != null){
                if (data[i].relationItemName.length<=40){
                    itemName = data[i].relationItemName + "<input type=\"hidden\" value="+data[i].relationItem+">"
                } else {
                    itemName = "<span class=\"ellipsis\" style=\"max-width: 150px; overflow: hidden; text-overflow: ellipsis;\">"+data[i].relationItemName.substring(0, 40)+"......</span>\n" +
                        "<span id=\"btn_copy\" class=\"btn btn-primary btn-xs\" style=\"float: right; margin-left: 5px; vertical-align: middle;\" onclick=\"copyText('"+data[i].relationItemName+"')\">复制</span>"
                }
            }
            var appendHtml = "<tr data-id="+"'"+data[i].id+"'"+" data-index="+data[i].length+">"+
                "<td class=\"serialNumber bannerClass\">"+Number(i+1)+"</td>" +
                "<td class=\"serialNumber bannerClass\">"+data[i].title+"</td>" +
                "<td class=\"relationItemName bannerClass\">"+itemName+"</td>" +
                "<td class=\"imageName bannerClass\">"+data[i].imageName+"</td>" +
                "<td class=\"aliveName bannerClass\">"+data[i].relationTypeName+"</td>" +
                "<td class=\"bannerClass\">" +
                "<i class=\"fa fa-edit icon-class\" title=\"编辑\" onclick=\"editBanner('"+ useType +"','" + data[i].id + "',this)\"></i>" +
                "<i class=\"fa fa-trash icon-class\" title=\"删除\" onclick=\"deleteBanener('" + useType + "',"+"'"+data[i].id+"'"+",this)\"></i>" +
                "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"moveDetail('up','" + useType + "',"+"'"+data[i].id+"'"+",this)\"></i>" +
                "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"moveDetail('down','" + useType + "',"+"'"+data[i].id+"'"+",this)\"></i>"
            "</td>"
            html = html+appendHtml
        }
        $("#schBannerBody").append(html)
        // 重新加上省略号样式
        $(".ellipsis").addClass("ellipsis");
    })
}

//刷新小程序Banner
function refreshBannerAPP(useType){
    ajaxData("/homepageConfig/getBannerAPP", "", function (data) {
        //刷新table
        $("#schBannerBodyAPP").empty()
        var html = ""
        for(var i=0;i<data.length;i++){
            var itemName = ""
            if (data[i].relationItemName != null){
                if (data[i].relationItemName.length<=40){
                    itemName = data[i].relationItemName + "<input type=\"hidden\" value="+data[i].relationItem+">"
                } else {
                    itemName = "<span class=\"ellipsis\" style=\"max-width: 150px; overflow: hidden; text-overflow: ellipsis;\">"+data[i].relationItemName.substring(0, 40)+"......</span>\n" +
                        "<span id=\"btn_copy\" class=\"btn btn-primary btn-xs\" style=\"float: right; margin-left: 5px; vertical-align: middle;\" onclick=\"copyText('"+data[i].relationItemName+"')\">复制</span>"
                }
            }
            var appendHtml = "<tr data-id="+"'"+data[i].id+"'"+" data-index="+data[i].length+">"+
                "<td class=\"serialNumber bannerClass\">"+Number(i+1)+"</td>" +
                "<td class=\"serialNumber bannerClass\">"+data[i].title+"</td>" +
                "<td class=\"relationItemName bannerClass\">"+itemName+"</td>" +
                "<td class=\"imageName bannerClass\">"+data[i].imageName+"</td>" +
                "<td class=\"aliveName bannerClass\">"+data[i].relationTypeName+"</td>" +
                "<td class=\"bannerClass\">" +
                "<i class=\"fa fa-edit icon-class\" title=\"编辑\" onclick=\"editBanner('"+ useType +"','" + data[i].id + "',this)\"></i>" +
                "<i class=\"fa fa-trash icon-class\" title=\"删除\" onclick=\"deleteBanener('" + useType + "',"+"'"+data[i].id+"'"+",this)\"></i>" +
                "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"moveDetail('up','" + useType + "',"+"'"+data[i].id+"'"+",this)\"></i>" +
                "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"moveDetail('down','" + useType + "',"+"'"+data[i].id+"'"+",this)\"></i>"
            "</td>"
            html = html+appendHtml
        }
        $("#schBannerBodyAPP").append(html)
    })
}

function refreshHome(){
    popConfirm("确认刷新？", function () {
        ajaxData("/homepageConfig/refreshHome","",function (){
            popMsg("刷新成功！")
        })
    })
}

function copyText(text){
    const input = document.createElement('textarea');
    input.style.position = 'fixed';
    input.style.opacity = '0';
    input.value = text;
    console.log(text)
    document.body.appendChild(input);
    input.focus();
    input.select();

    // 复制文本
    let copyResult = false;
    try {
        copyResult = document.execCommand('copy');
    } catch (error) {
        console.error('复制失败:', error);
    }

    // 移除文本输入框
    document.body.removeChild(input);

    if (copyResult) {
        popMsg("复制成功");
    } else {
        popMsg("复制失败，您的浏览器可能不支持复制功能。请尝试手动复制。");
    }
}

//添加轮播信息
function addBanner(useType) {
    var tit
    var param = {
        useType : useType
    }
    if(useType=="1"){
        tit = "新增WEB轮播图"
    }
    if(useType=="2"){
        tit = "新增小程序轮播"
    }
    parent.popWin(tit, "/homepageConfig/bannerAddInit", param, "100%", "100%", function () {
        if(useType=="1"){
            refreshBanner(useType);
        }
        if(useType=="2"){
            refreshBannerAPP(useType);
        }
        popMsg('操作成功');
    });

}

//编辑轮播信息
function editBanner(useType, id, obj) {
    var param = {
        id: id,
        useType: useType,
    };
    if(useType=="1"){
        parent.popWin("编辑WEB轮播", "/homepageConfig/bannerAddInit", param, "100%", "100%", function (id) {
            //刷新table
            refreshBanner(useType);
            popMsg('操作成功');
        });
    }
    if(useType=="2"){
        parent.popWin("编辑小程序轮播", "/homepageConfig/bannerAddInit", param, "100%", "100%", function (id) {
            //刷新table
            refreshBannerAPP(useType);
            popMsg('操作成功');
        });
    }
}

//删除轮播信息 ok
function deleteBanener(useType, id, obj) {
    popConfirm("确定删除？",function () {
        var param = {
            id: id,
            useType: useType,
        };
        ajaxData("/homepageConfig/deleteBanner", param, function () {
            if(useType=="1"){
                refreshBanner(useType);
            }
            if(useType=="2"){
                refreshBannerAPP(useType);
            }
            popMsg("删除成功！")
        });
    })
}
//上下移动轮播信息 ok
function moveDetail(moveType, useType, id, obj) {
    var seq = $(obj).parent().parent().find(".serialNumber").html();
    if(useType=="1"){
        var tableLength1 = $("#schBannerTable tbody tr").length;
        if (moveType == "up" && parseInt(seq) == "1") {
            popMsg("第一个，不能上移了");
        } else if (moveType == "down" && parseInt(seq) == tableLength1) {
            popMsg("最后一个，不能下移了");
        } else {
            ajaxData('/homepageConfig/moveDetail', {moveType: moveType, id: id}, function () {
                    refreshBanner(useType);
                popMsg('操作成功');
            })
        }
    }
    if(useType=="2"){
        var tableLength2 = $("#schBannerTableAPP tbody tr").length;
        if (moveType == "up" && parseInt(seq) == "1") {
            popMsg("第一个，不能上移了");
        } else if (moveType == "down" && parseInt(seq) == tableLength2) {
            popMsg("最后一个，不能下移了");
        } else {
            ajaxData('/homepageConfig/moveDetail', {moveType: moveType, id: id}, function () {
                    refreshBannerAPP(useType);
                popMsg('操作成功');
            })
        }
    }
}
//指定顺序调整
function quickMoveInfoTo(id, sortColumn, sort, length) {
    if (length == 1) {
        parent.popMsg("无法调整");
        return
    }
    var param = {
        id: id,
        sortColumn: sortColumn,
        sort: sort
    }
    parent.popWin("顺序调整", "/homepageConfig/editSortPageHome", param, "600px", "200px", function (data) {
        editCourseTeacherCallBack(data);
        popMsg('操作成功');
    });
}

function addNotice(){
    let param = {
        configType:'3'
    }
    parent.popWin("选择培训通知", "/homepageConfig/editNoticeInfo", param, "80%", "100%", editNoticeInfoCallBack,"")
}
function addReview(){
    let param = {
        configType:'4'
    }
    parent.popWin("选择精彩回顾", "/homepageConfig/editReviewInfo", param, "80%", "100%", editReviewInfoCallBack,"")
}

//编辑热门课程回调
function editNoticeInfoCallBack(data) {
    $("#noticeInfoTable").empty()
    let html = ""
    for (let i = 0; i < data.length; i++) {
        let noticeName = data[i].noticeName
        let appendHtml = "<tr>" +
            "<td>" + Number(i + 1) + "</td>" +
            "<td>" + noticeName + "</td>" +
            "<td>" +
            "<i class=\"fa fa-edit icon-class\" title=\"选择培训通知\" onclick=\"editNoticeInfo('" + data[i].id + "','" + data[i].configId + "','" + data[i].sort + "','" + data[i].noticeName + "','" + data[i].configType + "')\"></i>" +
            "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"editCourseTeacherUp('" + data[i].id + "','" + data[i].configId + "','" + data[i].sort + "','" + data[i].configType + "')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"editCourseTeacherDown('" + data[i].id + "','" + data[i].configId + "','" + data[i].sort + "','" + data[i].configType + "')\"></i>" +
            "<i class=\"fa fa-trash icon-class\" title=\"删除\" onclick=\"deleteCourseInfo('" + data[i].id + "','" + data[i].configType + "')\"></i>" +
            "</td>" +
            "</tr>"
        html = html + appendHtml
    }
    $("#noticeInfoTable").append(html)
}
//编辑精彩回顾回调
function editReviewInfoCallBack(data) {
    $("#reviewInfoTable").empty()
    let html = ""
    for (let i = 0; i < data.length; i++) {
        let noticeName = data[i].noticeName
        let appendHtml = "<tr>" +
            "<td>" + Number(i + 1) + "</td>" +
            "<td>" + noticeName + "</td>" +
            "<td>" +
            "<i class=\"fa fa-edit icon-class\" title=\"选择精彩回顾\" onclick=\"editReviewInfo('" + data[i].id + "','" + data[i].configId + "','" + data[i].sort + "','" + data[i].noticeName + "','" + data[i].configType + "')\"></i>" +
            "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"editCourseTeacherUp('" + data[i].id + "','" + data[i].configId + "','" + data[i].sort + "','" + data[i].configType + "')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"editCourseTeacherDown('" + data[i].id + "','" + data[i].configId + "','" + data[i].sort + "','" + data[i].configType + "')\"></i>" +
            "<i class=\"fa fa-trash icon-class\" title=\"删除\" onclick=\"deleteCourseInfo('" + data[i].id + "','" + data[i].configType + "')\"></i>" +
            "</td>" +
            "</tr>"
        html = html + appendHtml
    }
    $("#reviewInfoTable").append(html)
}

//培训通知
function editNoticeInfo(id, configId, sort, noticeName, configType) {
    let param = {
        id: id,
        configId: configId,
        sort: sort,
        noticeName: noticeName,
        configType: configType,
        courseType: '3'//培训通知
    }
    parent.popWin("选择培训通知", "/homepageConfig/editNoticeInfo", param, "80%", "100%", editNoticeInfoCallBack, "")
}
//精彩回顾
function editReviewInfo(id, configId, sort, noticeName, configType) {
    let param = {
        id: id,
        configId: configId,
        sort: sort,
        noticeName: noticeName,
        configType: configType,
        courseType: '3'
    }
    parent.popWin("选择精彩回顾", "/homepageConfig/editReviewInfo", param, "80%", "100%", editReviewInfoCallBack, "")
}

function addCourse(){
    var param = {
        configType:'1'
    }
    parent.popWin("选择课程", "/homepageConfig/editCourseInfo", param, "80%", "100%", editCourseInfoCallBack,"")
}

//热门远程课程
function editCourseInfo(id,configId,sort,courseName,configType) {
    var param = {
        id :id,
        configId : configId,
        sort : sort,
        courseName:courseName,
        configType:configType,
        courseType:'0'//远程课程
    }
    parent.popWin("选择课程", "/homepageConfig/editCourseInfo", param, "80%", "100%", editCourseInfoCallBack,"")
}
//热门直播课程
function editLiveCourseInfo(id,configId,sort,courseName,configType) {
    var param = {
        id :id,
        configId : configId,
        sort : sort,
        courseName:courseName,
        configType:configType,
        courseType:'1'//直播课程
    }
    parent.popWin("选择课程", "/homepageConfig/editCourseInfo", param, "80%", "100%", editCourseInfoCallBack,"")
}
//编辑热门课程回调
function editCourseInfoCallBack(data) {
    $("#courseInfoTable").empty()
    var html = ""
    for(var i=0;i<data.length;i++){
        var type = ""
        var courseName = data[i].courseName
        if (data[i].courseType == '0'){
            type = "远程课程"
        }else {
            type = "直播课程"
        }
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+courseName+"</td>"+
            "<td>"+type+"</td>"+
            "<td>" +
            "<i class=\"fa fa-edit icon-class\" title=\"选择远程课程\" onclick=\"editCourseInfo('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].courseName+"','"+data[i].configType+"')\"></i>" +
            "<i class=\"fa fa-edit icon-class\" title=\"选择直播课程\" onclick=\"editLiveCourseInfo('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].courseName+"','"+data[i].configType+"')\"></i>" +
            "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"editCourseTeacherUp('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"editCourseTeacherDown('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>"+
            // " <i class=\"fa fa-trash icon-class\" title=\"删除\" onclick=\"deleteCourseInfo('"+data[i].id+"','"+data[i].configType+"')\"></i>" +
            "</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseInfoTable").append(html)
}

function addCourseType(){
    var sort = $("#courseTableId").find("tr").length+1
    var param = {
        sort:sort,
        configType:'0'
    }
    parent.popWin("新建分类", "/homepageConfig/editCourseType", param, "800px", "500px", editCourseTypeCallBack,"")
}

//热门分类
function editCourseType(id,configType) {
    var param = {
        id : id,
        configType : configType
    }
    parent.popWin("编辑分类", "/homepageConfig/editCourseType", param, "800px", "500px", editCourseTypeCallBack,"")
}

function editCourseTypeCallBack(data){
    $("#courseTableId").empty();
    var html = ""
    for(var i=0;i<data.length;i++){
        var type = "无"
        var courseTypeName = "-"
        if (data[i].configId != null && data[i].configId != ''){
            courseTypeName = data[i].courseTypeName
            type = "固定"
        }
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+courseTypeName+"</td>"+
            "<td>"+type+"</td>"+
            "<td>" +
            "<i class=\"fa fa-edit icon-class\" title=\"编辑\" onclick=\"editCourseType('"+data[i].id+"','"+data[i].configType+"')\"></i>" +
            "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"editCourseTeacherUp('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"editCourseTeacherDown('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>" +
            "<i class=\"fa fa-trash icon-class\" title=\"取消固定\" onclick=\"deleteCourseType('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>"+
            "</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseTableId").append(html)
}


//选择讲师
function addCourseTeacher() {
    var sort = $("#courseTeacherTableId").find("tr").length+1;
    var param = {
        sort : sort
    }
    parent.popWin("选择讲师", "/homepageConfig/editCourseTeacher", param, "80%", "70%", editCourseTeacherCallBack,"")
}
//编辑讲师
function editCourseTeacher(id,teacherId,sort,courseTypeName) {
    var param = {
        id : teacherId,
        type : "0"
    }
    parent.popWin("讲师明细", "/homepageConfig/teacherInit", param, "900px", "600px",editCourseTeacherCallBack,"");
}
//编辑讲师回调
function editCourseTeacherCallBack(data) {
    $("#courseTeacherTableId").empty();
    var html = ""
    for(var i=0;i<data.length;i++){
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+data[i].teacherName+"</td>"+
            "<td>"+data[i].teachOrg+"</td>"+
            "<td>" +
            "<i class=\"fa fa-edit icon-class\" title=\"编辑\" onclick=\"editCourseTeacher('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].teacherName+"')\"></i>\n" +
            "<i class=\"fa fa-trash icon-class\" title=\"删除\" onclick=\"deleteCourseTeacher('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"','"+data[i].teacherName+"')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-up icon-class\" title=\"上移\" onclick=\"editCourseTeacherUp('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-down icon-class\" title=\"下移\" onclick=\"editCourseTeacherDown('"+data[i].id+"','"+data[i].configId+"','"+data[i].sort+"','"+data[i].configType+"')\"></i>\n" +
            "<i class=\"fa fa-exchange iconStyle\" title=\"顺序调整\" onclick=\"quickMoveInfoTo('"+data[i].id+"' , '"+(i+1)+"' , '"+data[i].sort+"','"+data.length+"')\"></i>" +
            "</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseTeacherTableId").append(html)
    $("#sort").val(data.length)
    applyStripe();//重新加载斑马纹
}
//讲师上移
function editCourseTeacherUp(id,configId,sort,configType) {
    if (sort=='1'){
        popMsg("当前排序已是最前")
    }else {
        var param = {
            id: id,
            configId: configId,
            sort: sort,
            configType: configType,
            move : '1'
        }
        ajaxData("/homepageConfig/editCourseTeacherSort",param,function (data) {
            popMsg("上移成功")
            if (configType=='2'){
                editCourseTeacherCallBack(data)
            }else if (configType=='0'){
                editCourseTypeCallBack(data)
            }else if (configType=='1'){
                editCourseInfoCallBack(data)
            }else if (configType=='3'){
                editNoticeInfoCallBack(data)
            }else if (configType=='4'){
                editReviewInfoCallBack(data)
            }
        })
    }
}
//讲师下移
function editCourseTeacherDown(id,configId,sort,configType) {
    if (configType=='2' && sort==$("#sort").val()){
        popMsg("当前排序已是最后")
    }else if (configType=='0' && sort==$("#courseTableId").find("tr").length){
        popMsg("当前排序已是最后")
    }else if (configType=='1' && sort==$("#courseInfoTable").find("tr").length){
        popMsg("当前排序已是最后")
    }else {
        var param = {
            id: id,
            configId: configId,
            sort: sort,
            configType: configType,
            move : '2'
        }
        ajaxData("/homepageConfig/editCourseTeacherSort",param,function (data) {
            popMsg("下移成功")
            if (configType=='2'){
                editCourseTeacherCallBack(data)
            }else if (configType=='0'){
                editCourseTypeCallBack(data)
            }else if (configType=='1'){
                editCourseInfoCallBack(data)
            }else if (configType=='3'){
                editNoticeInfoCallBack(data)
            }else if (configType=='4'){
                editReviewInfoCallBack(data)
            }
        })
    }
}
// 列表斑马纹
function applyStripe() {
    var rows = document.querySelectorAll('tbody tr');
    for (i = 0; i < rows.length; i++) {
        if (i % 2 === 0) {
            rows[i].classList.remove('odd');
            rows[i].classList.add('even');
        } else {
            rows[i].classList.remove('even');
            rows[i].classList.add('odd');
        }
    }
}
function deleteCourseTeacher(id,configId,sort,configType,teacherName) {
    popConfirm("确定删除？",function () {
        var param = {
            id: id,
            configId: configId,
            sort: sort,
            configType:configType,
            teacherName: teacherName
        }
        ajaxData("/homepageConfig/deleteHomePageTeach", param, function (data) {
            popMsg("删除成功")
            editCourseTeacherCallBack(data)
        })
    })
}

function deleteCourseInfo(id,configType){
    popConfirm("确定删除？",function () {
        var param = {
            id: id,
            configType: configType,
        }
        ajaxData("/homepageConfig/deleteHomePageTeach", param, function (data) {
            popMsg("删除成功")
            if (configType=='2'){
                editCourseTeacherCallBack(data)
            }else if (configType=='0'){
                editCourseTypeCallBack(data)
            }else if (configType=='1'){
                editCourseInfoCallBack(data)
            }else if (configType=='3'){
                editNoticeInfoCallBack(data)
            }else if (configType=='4'){
                editReviewInfoCallBack(data)
            }
        })
    })
}

function deleteCourseType(id,configId,sort,configType){
    popConfirm("取消固定？",function () {
        var param = {
            id: id,
            configType: configType,
        }
        ajaxData("/homepageConfig/deleteCourseType", param, function (data) {
            popMsg("取消固定成功")
            editCourseTypeCallBack(data)
        })
    })
}
