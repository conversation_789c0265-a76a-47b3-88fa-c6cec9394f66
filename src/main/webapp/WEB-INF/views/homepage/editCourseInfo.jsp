<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .courseInfo{
            display: inline-block;
            padding: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div style="background-color: #FFFFFF;padding-left: 20px">
    <div class="courseInfo" style="float: left">已选：</div>
    <div class="courseInfo" style="width: 84%" id="courseInfoSelect">${schHomepageResourceConfigDto.courseName}</div>
    <input type="hidden" id ="id" value="${schHomepageResourceConfigDto.id}">
    <input type="hidden" id ="sort" value="${schHomepageResourceConfigDto.sort}">
    <input type="hidden" id ="configId" value="${schHomepageResourceConfigDto.configId}">
    <input type="hidden" id = "configType" value="${schHomepageResourceConfigDto.configType}">
    <input type="hidden" id = "courseCategory" value="${schHomepageResourceConfigDto.courseType}">
    <input type="hidden" id ="configCourseList" value='${configCourseList}'>
    <input type="hidden" id="releaseFlag" value="">
    <input type="hidden" id="courseId" value="${schHomepageResourceConfigDto.configId}">
    <div id="saveCourseInfo" class="btn btn-primary" style="float: right;margin: 10px">确定选中</div>
</div>
<div class="panel">
    <div class="panel-heading" style="padding-bottom: 0;">
        <form:form modelAttribute="courseInfoDto" id="queryForm" onkeydown="bindEnter(event)">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">课程名称</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" />
                    </div>
                    <c:if test="${schHomepageResourceConfigDto.courseType=='0'}">
                        <label class="col-md-1 control-label">课程类型</label>
                        <div class="col-md-3">
                            <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' />
                            <input name="courseType" type="hidden" placeholder="请选择课程类型" />
                        </div>
                    </c:if>
                    <div class="col-md-4">
                        <span id="btnClear" class="btn btn-default" style="float: right;margin-left: 10px">清空</span>
                        <span id="btnQuery" class="btn btn-primary" style="float: right">查询</span>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body" style="padding-top: 0;">
        <div class="row">
            <c:if test="${schHomepageResourceConfigDto.courseType=='0'}">
                <e:grid id="tableAll" action="/basicInformation/queryRepCaseInfoList" cssClass="table table-striped table-hover">
                    <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                                  cssStyle="width:20%" />
                    <e:gridColumn label="课程名称" displayColumn="courseName" orderable="false"
                                  cssClass="text-center" cssStyle="width:20%" />
                    <e:gridColumn label="课程类型" renderColumn="courseType" orderable="false"
                                  cssClass="text-center" cssStyle="width:20%" />
                    <e:gridColumn label="是否发布" renderColumn="columnRelease"  orderable="false" cssClass="text-center"
                                  cssStyle="width:20%" />
                    <e:gridColumn label="" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                                  cssStyle="width:0%" />
                </e:grid>
            </c:if>
            <c:if test="${schHomepageResourceConfigDto.courseType=='1'}">
                <e:grid id="tableAll" action="/homepageConfig/queryChooseCourse" cssClass="table table-striped table-hover">
                    <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                                  cssStyle="width:20%" />
                    <e:gridColumn label="课程名称" displayColumn="courseName" orderable="false"
                                  cssClass="text-center" cssStyle="width:20%" />
                    <e:gridColumn label="直播机构" renderColumn="courseType" orderable="false"
                                  cssClass="text-center" cssStyle="width:20%" />
                    <e:gridColumn label="是否发布" renderColumn="columnRelease"  orderable="false" cssClass="text-center"
                                  cssStyle="width:20%" />
                    <e:gridColumn label="" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                                  cssStyle="width:0%" />
                </e:grid>
            </c:if>
        </div>
    </div>
</div>
</body>
</html>
