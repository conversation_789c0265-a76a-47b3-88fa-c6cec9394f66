//@ sourceURL=insertTeacherManagementInit.js
$(document).ready(function () {
    $(window).keydown( function(e) {
        var key = window.event?e.keyCode:e.which;
        if(key.toString() == "13"){
            return false;
        }
    });
    relationType();
    $("#schBannerInfoDto").validate({
        rules: {
            "title": {
                required: true,
                maxlength: 45
            }
        }
    });

    // 取消
    $('#cancelButton').bind('click', function () {
        closeWin();
    });
    $('#saveButton').bind('click', function () {
        saveBanner();
    });

    var url = contextPath + '/filetempupload';
    $('#bannerFile').fileupload({
        url: url,
        dataType: 'json',
        autoUpload: true,
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#fileId").val(file.fileRelaId);
                $("#imageUrl").val(file.fileRelaId);
                $("#imageName").val(file.fileName);
                $("#useType").val("1");
            });
            $("#bannerBox").css("display", "block");
            layer.close(index);
        }
    });

    $("#bannerFile").uploadPreview({
        Img: "ImgPr",
        Width: 50,
        Height: 50
    });
    //底部图片
    $('#bannerBgFile').fileupload({
        url: url,
        dataType: 'json',
        autoUpload: true,
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#fileBgId").val(file.fileRelaId);
                $("#imageBgUrl").val(file.fileRelaId);
                $("#imageBgName").val(file.fileName);
            });
            $("#bannerBgBox").css("display", "block");
            layer.close(index);
        }
    });

    $("#bannerBgFile").uploadBgPreview({
        Img: "ImgBgPr",
        Width: 50,
        Height: 50
    });


})

/**
 * 删除图片
 * @param obi
 * @param fieldId
 */
function clearImg() {
    popConfirm("确认删除图片", function () {
        $("#fileId").val("");
        $("#bannerBox").css("display", "none");
        $("#bannerFile").val("");
        $("#imageName").val("");
        if ($("#imageUrl").val() != "") {
            $("#delFileId").val($("#imageUrl").val());
            $("#imageUrl").val("");
        }
    });
}
function clearBgImg() {
    popConfirm("确认删除图片", function () {
        $("#fileBgId").val("");
        $("#bannerBgBox").css("display", "none");
        $("#bannerBgFile").val("");
        $("#imageBgName").val("");
        if ($("#imageBgUrl").val() != "") {
            $("#delBgFileId").val($("#imageBgUrl").val());
            $("#imageBgUrl").val("");
        }
    });
}

$.fn
    .extend({
        uploadPreview: function (opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img: "ImgPr",
                Width: 100,
                Height: 100,
                ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
                Callback: function () {
                }
            }, opts || {});
            _self.getObjectURL = function (file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }
                if ($("#imageBgUrl").val() != "") {
                    $(
                        '<input type="hidden" name="delFileId" value="'
                        + $("#imageUrl").val() + '"/>').appendTo(
                        '#delFileIdList');
                    $("#imageUrl").val("");
                }

                $("#imgId").css("display", "block");
                return url
            };

            _this.change(function () {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width': opts.Width
                                            + 'px',
                                        'height': opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })
$.fn
    .extend({
        uploadBgPreview: function (opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img: "ImgPr",
                Width: 100,
                Height: 100,
                ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
                Callback: function () {
                }
            }, opts || {});
            _self.getObjectURL = function (file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }
                if ($("#imageBgUrl").val() != "") {
                    $(
                        '<input type="hidden" name="delFileId" value="'
                        + $("#imageBgUrl").val() + '"/>').appendTo(
                        '#delFileIdList');
                    $("#imageBgUrl").val("");
                }

                $("#imgBgId").css("display", "block");
                return url
            };

            _this.change(function () {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width': opts.Width
                                            + 'px',
                                        'height': opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })
/**
 * 关联类型选择
 */
function relationType() {
    var relation = $('[name="relationType"]:checked').val();
    var chooseType = $("#chooseType").val();
    var relationItemCourse = $("#relationItemCourse").val();
    var relationItemLiveCourse = $("#relationItemLiveCourse").val();
    var relationItemLive = $("#relationItemLive").val();
    if (relation === '0') {
        $('#relationFont').html('选择课程');//
        $('#relationFontDiv').css('display', 'block');
        $('#chooseCourse').css('display', 'block');//远程课程按钮
        $('#liveCourse').css('display', 'none');//直播课程按钮
        $('.relationItemLiveCourse').css('display', 'none');//直播课程名字和删除
        $('#courseType').css('display', 'none');//专题
        $('.relationItemHttps').css('display', 'none');//链接input
        if (relationItemCourse) {
            $('.relationItemCourse').css('display', 'block');//直播课程名字和删除
        } else {
            $('.relationItemCourse').css('display', 'none');//直播课程名字和删除
        }
        $('.relationItemLive').css('display', 'none');//直播名字和删除
    } else if (relation === '1') {
        $('#relationFont').html('选择专题');//
        $('#relationFontDiv').css('display', 'block');
        $('#chooseCourse').css('display', 'none');//远程课程按钮
        $('.relationItemCourse').css('display', 'none');//远程课程名字和删除
        $('#liveCourse').css('display', 'none');//直播课程按钮
        $('.relationItemLiveCourse').css('display', 'none');//直播课程名字和删除
        $('#courseType').css('display', 'block');//专题
        $('.relationItemHttps').css('display', 'none');//链接input
        if (relationItemLive) {
            $('.relationItemLive').css('display', 'block');//直播名字和删除
        } else {
            $('.relationItemLive').css('display', 'none');//直播名字和删除
        }
    } else if (relation === '2') {
        $('#relationFont').html('链接地址');//
        $('#relationFontDiv').css('display', 'block');
        $('#chooseCourse').css('display', 'none');//远程课程按钮
        $('.relationItemCourse').css('display', 'none');//远程课程名字和删除
        $('#liveCourse').css('display', 'none');//直播课程按钮
        $('.relationItemLiveCourse').css('display', 'none');//课程名字和删除
        $('#courseType').css('display', 'none');//专题
        $('.relationItemHttps').css('display', 'block');//链接input
        $('.relationItemLive').css('display', 'none');//直播名字和删除
    } else if (relation === '3'){
        $('#relationFont').html('选择课程');//
        $('#relationFontDiv').css('display', 'block');
        $('#liveCourse').css('display', 'block');///直播直播课程按钮
        $('#chooseCourse').css('display', 'none');//远程课程按钮
        $('.relationItemCourse').css('display', 'none');//远程课程名字和删除
        $('#courseType').css('display', 'none');//专题
        $('.relationItemHttps').css('display', 'none');//链接input
        if (relationItemLiveCourse) {
            $('.relationItemLiveCourse').css('display', 'block');//直播课程名字和删除
        } else {
            $('.relationItemLiveCourse').css('display', 'none');//直播课程名字和删除
        }
        $('.relationItemLive').css('display', 'none');//直播名字和删除
    }else if (relation === '99') {
        $('#relationFontDiv').css('display', 'none');//
        $('#chooseCourse').css('display', 'none');//远程课程按钮
        $('.relationItemCourse').css('display', 'none');//远程课程名字和删除
        $('#liveCourse').css('display', 'none');//直播课程按钮
        $('.relationItemLiveCourse').css('display', 'none');//直播课程名字和删除
        $('#courseType').css('display', 'none');//专题
        $('.relationItemHttps').css('display', 'none');//链接input
        $('.relationItemLive').css('display', 'none');//直播名字和删除
    }


}

function saveBanner() {
    if ($("#schBannerInfoDto").valid()) {
        if(!$("#fileId").val()){
            popMsg("请添加轮播图片");
            return false;
        }
        if(!$("#fileBgId").val()){
            popMsg("请添加轮播背景图片");
            return false;
        }
        var relation = $('[name="relationType"]:checked').val();
        if(relation === '0'){
            if(!$("#relationItemCourse").val()){
                popMsg("请关联远程课程");
                return false;
            }
        }else if(relation === '1'){
            if(!$("#relationItemCourseType").val()){
                popMsg("请关联专题");
                return false;
            }
        }else if (relation === '2'){
            if(!$("#relationItemHttps").val()){
                popMsg("请输入链接");
                return false;
            }
        }else if (relation === '3'){
            if(!$("#relationItemLiveCourse").val()){
                popMsg("请关联直播课程");
                return false;
            }
        }
        var param = {
            id: $("#id").val(),
            title: $("#title").val(),
            description: $("#description").val(),
            relationItem: $("#relationItem").val(),
            relationType: $("#relationType").val(),
            imageUrl: $("#imageUrl").val(),
            imageName: $("#imageName").val(),
            alive: $("#alive").val(),
            useType: "1",
        }
        ajaxData("/homepageConfig/addBanner", $("#schBannerInfoDto").serialize(), function (data) {
            closeWinCallBack(data)
        })
    }

}
//远程课程
function chooseCourse() {
    var params = {
        courseTagType : '1'
    }
    parent.popWin("选择课程", "/homepageConfig/selectBannerCourse", params, "90%", "90%", function (data) {
        $('.relationItemCourse').css('display', 'block');
        $("#relationItemCourse").val(data.courseId);
        $(".relationItemNameCourseSpan").html(data.courseName);
    })
}
//直播课程
function liveCourse() {
    var params = {
        courseTagType : '2'
    }
    parent.popWin("选择课程", "/homepageConfig/selectBannerCourse", params, "90%", "90%", function (data) {
        $('.relationItemLiveCourse').css('display', 'block');
        $("#relationItemLiveCourse").val(data.courseId);
        $(".relationItemNameLiveCourseSpan").html(data.courseName);
    })
}

function chooseLive() {
    var param = {
        id: $("#id").val(),
    }
    parent.popWin("选择直播", "/schoolIndexConfig/chooseBannerLive", param, "70%", "70%", function (liveId) {
        var param = {
            liveId: liveId
        }
        ajaxData("/schoolIndexConfig/selectLive", param, function (dto) {
            $('.relationItemLive').css('display', 'block');
            $(".relationItemNameLiveSpan").html(dto.liveName);
            $("#relationItemLive").val(dto.liveId);
        })
    })
}

function delCourse(obj) {
    $("#relationItemCourse").val("");
    $(".relationItemCourse").css('display', 'none')
}

function delLiveCourse(obj) {
    $("#relationItemLiveCourse").val("");
    $(".relationItemLiveCourse").css('display', 'none')
}

function delLive(obj) {
    $("#relationItemLive").val("");
    $(".relationItemLive").css('display', 'none')
}
