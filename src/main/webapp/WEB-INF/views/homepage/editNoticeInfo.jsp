<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .noticeInfo{
            display: inline-block;
            padding: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div style="background-color: #FFFFFF;padding-left: 20px;height: 45px!important;">
    <div class="noticeInfo" style="float: left">已选：</div>
    <div class="noticeInfo" style="width: 84%" id="courseInfoSelect">${schHomepageResourceConfigDto.noticeName}</div>
    <input type="hidden" id="id" value="${schHomepageResourceConfigDto.id}">
    <input type="hidden" id="sort" value="${schHomepageResourceConfigDto.sort}">
    <input type="hidden" id="configId" value="${schHomepageResourceConfigDto.configId}">
    <input type="hidden" id="configType" value="${schHomepageResourceConfigDto.configType}">
    <input type="hidden" id="courseCategory" value="${schHomepageResourceConfigDto.courseType}">
    <input type="hidden" id="releaseFlag" value="">
    <input type="hidden" id="configCourseList" value='${configCourseList}'>
    <div id="saveNoticeInfo" class="btn btn-primary" style="float: right;margin: 10px">确定选中</div>
</div>
<div class="panel">
    <div class="panel-heading" style="padding-bottom: 0;">
        <form:form modelAttribute="trainingDynamicsDto" id="superviseInfoForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">通知分类:</label>
                    <div class="col-md-3">
                        <form:select path="subType" id="subType" name="subType" class="form-control">
                            <form:option value="">全部</form:option>
                            <c:forEach var="property" items="${notificationTypeList}" varStatus="index">
                                <c:if test="${property.type =='3'}">
                                    <form:option value="${property.typeValue}">${property.typeName}</form:option>
                                </c:if>
                            </c:forEach>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">标题:</label>
                    <div class="col-md-3">
                        <form:input path="title" placeholder="请输入文本" cssClass="form-control" autocomplete="off" maxlength="128"
                                    onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
                    </div>
                    <div class="col-md-4">
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                        <span id="btnClear" class="btn btn-default">清空条件</span>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/trainingDynamics/queryTrainingDynamicsInfoList?noticeType=3&type=3&releaseFlag=1"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:3%"/>
                <e:gridColumn label="通知类型" displayColumn="typeName" renderColumn="typeName" orderable="false"
                              cssClass="text-center" cssStyle="width:6%"/>
                <e:gridColumn label="标题" displayColumn="title" renderColumn="title" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="时间" displayColumn="trainDate" orderable="false" renderColumn="trainDate"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="来源" displayColumn="source" orderable="false" renderColumn="source"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="是否发布" displayColumn="releaseName" renderColumn="releaseName" orderable="false"
                              cssClass="text-center" cssStyle="width:4%;"/>
                <e:gridColumn label="创建人" displayColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%;"/>
                <e:gridColumn label="创建时间" displayColumn="createTime" orderable="false"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="修改时间" displayColumn="updateTime" orderable="false"
                              cssClass="text-center" cssStyle="width:7%;"/>
                <e:gridColumn label="" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:0%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>