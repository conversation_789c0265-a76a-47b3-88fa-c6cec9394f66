<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%--<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>--%>
<%--<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>--%>
<%@ taglib uri="/edm-tags" prefix="e" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>新增WEB轮播</title>
    <e:base/>
    <e:js/>
    <style>
        .required-logo {
            color: red;
        }

        #bannerFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }

        #bannerBgFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .sch-margin{
            margin-top: 15px;
        }


    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form action="" id="schBannerInfoDto" modelAttribute="schBannerInfoDto" cssClass="form-horizontal">
            <form:hidden path="id"/>
            <form:hidden path="urlCopy" value="${schBannerInfoDto.imageUrl}"/>
            <form:hidden path="urlBgCopy" value="${schBannerInfoDto.imageBgUrl}"/>
            <form:hidden path="useType" value="${schBannerInfoDto.useType}"/>
            <input type="hidden" id="chooseType" value="${schBannerInfoDto.relationType}"/>
            <div class="row sch-margin" >
                <label class="col-md-2 text-right " ><font
                        class="required-logo">*&nbsp;</font>轮播标题</label>
                <div class="col-md-6 controls  ">
                    <form:input path="title" cssClass="form-control" placeholder="请输入轮播标题" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="row sch-margin">
                <label class="col-md-2 text-right " ><font class="required-logo">*&nbsp;</font>关联类型</label>
                <div class="col-md-6 controls " onclick="relationType()">
                    <label>
                        <form:radiobutton class="minimal formal-score-type" path="relationType" value="0"
                                          checked="checked"/>
                        远程课程
                    </label>
                    <label>
                        <form:radiobutton class="minimal formal-score-type" path="relationType" value="3"/>
                        直播课程
                    </label>
                    <label>
                        <form:radiobutton class="minimal formal-score-type" path="relationType" value="1"/>
                        专题
                    </label>
                    <label>
                        <form:radiobutton class="minimal formal-score-type" path="relationType" value="2"/>
                        链接
                    </label>
                    <label>
                        <form:radiobutton class="minimal formal-score-type" path="relationType" value="99"/>
                        无
                    </label>
                </div>
            </div>
            <div class="row sch-margin" id="relationFontDiv">
                <label class="col-md-2 text-right " ><font class="required-logo">*&nbsp;</font>
                    <font id="relationFont">选择课程</font>
                </label>
                <div class="col-md-6 ">
                    <span id="chooseCourse" class="btn btn-primary" style="width: 100px" onclick="chooseCourse()">选择远程课程</span>
                    <span id="liveCourse" class="btn btn-primary" style="width: 100px" onclick="liveCourse()">选择直播课程</span>
<%--                    <span id="chooseLive" class="btn btn-primary" style="width: 60px" onclick="chooseLive()">选择</span>--%>
                    <div id="courseType" class="col-md-10 no-padding" style="width: 400px;">
                        <form:select path="relationItemCourseType" class="form-control">
                            <form:option value="">请选择</form:option>
                            <c:forEach var="property" items="${courseTypeList}" varStatus="index">
                                <form:option value="${property.id}">${property.itemName}</form:option>
                            </c:forEach>
                        </form:select>
                    </div>
                    <input type="text" class="form-control relationItemHttps"  name="relationItemHttps" id="relationItemHttps" value="${schBannerInfoDto.relationItemHttps}" autocomplete="off" placeholder="请输入连接地址"/>
                </div>`
            </div>
            <div class="row sch-margin relationItemCourse" >
                <div class="col-md-2 text-right " > &nbsp; </div>
                <input type="hidden" name="relationItemCourse" id="relationItemCourse" value="${schBannerInfoDto.relationItemCourse}" />
                    <div class="col-md-10 relationItemNameCourse">
                    <span  style=" color: #00b7ee" class="relationItemNameCourseSpan">
                            ${schBannerInfoDto.relationItemNameCourse}
                    </span>
                        <span style="color: #00b7ee;cursor: pointer;margin-left: 50px" onclick="delCourse(this)">删除</span>
                    </div>
            </div>
            <div class="row sch-margin relationItemLiveCourse" >
                <div class="col-md-2 text-right " > &nbsp; </div>
                <input type="hidden" name="relationItemLiveCourse" id="relationItemLiveCourse" value="${schBannerInfoDto.relationItemLiveCourse}" />
                <div class="col-md-10 relationItemNameLiveCourse">
                    <span  style=" color: #00b7ee" class="relationItemNameLiveCourseSpan">
                            ${schBannerInfoDto.relationItemNameLiveCourse}
                    </span>
                    <span style="color: #00b7ee;cursor: pointer;margin-left: 50px" onclick="delLiveCourse(this)">删除</span>
                </div>
            </div>
            <div class="row sch-margin relationItemLive"  >
                <div class="col-md-2 text-right " > &nbsp; </div>
                <input type="hidden" name="relationItemLive" id="relationItemLive" value="${schBannerInfoDto.relationItemLive}" />
                <div class="col-md-10 relationItemNameLive">
                    <span  style=" color: #00b7ee" class="relationItemNameLiveSpan">
                            ${schBannerInfoDto.relationItemNameLive}
                    </span>
                    <span style="color: #00b7ee;cursor: pointer;margin-left: 50px" onclick="delLive(this)">删除</span>
                </div>
            </div>
            <div class="row sch-margin">
                <div class="col-md-6">
                    <div class="col-md-12">
                        <label class="col-md-4 text-right "><font class="required-logo">*&nbsp;</font>轮播图片</label>
                        <div class="col-md-8 ">
                            <div id="bannerBox" >
                                <img id="ImgPr" src="${schBannerInfoDto.imageUrl}"/>
                                <input type="hidden" name="fileId" id="fileId" value="${schBannerInfoDto.imageUrl}"/>
                                <input type="hidden" name="imageName" id="imageName" value="${schBannerInfoDto.imageName}"/>
                                <input type="hidden" name="delFileId" id="delFileId" value="${schBannerInfoDto.imageUrl}"/>
                                <form:hidden path="imageUrl"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4">&nbsp; </div>
                        <div class="col-md-8 text-left" >
                            <a href="javascript:void(0);" id="bannerUploadBtn"
                               class="file btn btn-warning btn-facebook btn-outline">
                                <i class="fa fa-upload"></i> 上传图片
                                <input id="bannerFile" type="file" name="files" multiple/>
                            </a>
                            <input type="button" id="bannerRemoveBtn" class="btn btn-danger" onclick="clearImg()"
                                   value="删除"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="col-md-12">
                        <label class="col-md-2 text-right "><font class="required-logo">*&nbsp;</font>底图</label>
                        <div class="col-md-8 ">
                            <div id="bannerBgBox" >
                                <img id="ImgBgPr" src="${schBannerInfoDto.imageBgUrl}"/>
                                <input type="hidden" name="fileBgId" id="fileBgId" value="${schBannerInfoDto.imageBgUrl}"/>
                                <input type="hidden" name="imagBgName" id="imageBgName" value="${schBannerInfoDto.imageBgName}"/>
                                <input type="hidden" name="delBgFileId" id="delBgFileId" value="${schBannerInfoDto.imageBgUrl}"/>
                                <form:hidden path="imageBgUrl"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-2">&nbsp; </div>
                        <div class="col-md-10 text-left" >
                            <a href="javascript:void(0);" id="bannerBgUploadBtn"
                               class="file btn btn-warning btn-facebook btn-outline">
                                <i class="fa fa-upload"></i> 上传图片
                                <input id="bannerBgFile" type="file" name="files" multiple/>
                            </a>
                            <input type="button" id="bannerBgRemoveBtn" class="btn btn-danger" onclick="clearBgImg()"
                                   value="删除"/>
                        </div>
                    </div>
                </div>
            </div>
<%--            <div class="row sch-margin">--%>
<%--                <label class="col-md-2 text-right " >&nbsp;&nbsp;是否启用</label>--%>
<%--                <div class="col-md-7 controls ">--%>
<%--                    <label>--%>
<%--                        <form:radiobutton class="minimal formal-score-type" path="alive" value="1" checked="checked"/>--%>
<%--                        是--%>
<%--                    </label>--%>
<%--                    <label>--%>
<%--                        <form:radiobutton class="minimal formal-score-type" path="alive" value="0"/>--%>
<%--                        否--%>
<%--                    </label>--%>
<%--                </div>--%>
<%--            </div>--%>
            <div class="row sch-margin">
                <label class="col-md-2 text-right " >&nbsp;&nbsp;描述</label>
                <div class="col-md-7 controls ">
                    <form:textarea cols="60" rows="5" path="description" placeholder="请输入轮播描述" maxlength="2000"></form:textarea>
                </div>
            </div>
            <div class="button-group">
                <div class="col-md-12 text-center ">
                    <input type="button" id="saveButton" class="btn btn-primary" value="保存"/>
                    <input type="button" id="cancelButton" class="btn btn-default" value="取消"/>
                </div>
            </div>
        </form:form>
    </div>
</div>

</body>
</html>
