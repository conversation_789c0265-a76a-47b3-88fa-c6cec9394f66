$(document).ready(function () {
    $("#btnQuery").bind("click", function () {
        let param = {
            subType: $("#subType").val(),
            title: $("#title").val(),
            releaseFlg: "1",
            noticeType: "4",
        }
        ajaxTableQuery("tableAll", "/trainingDynamics/queryTrainInfoList", param);
    });

    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("superviseInfoForm").reset();
        let param = {
            noticeType: "4",
        }
        ajaxTableQuery("tableAll", "/trainingDynamics/queryTrainInfoList", param);
    });

    $("#saveReviewInfo").click(function () {
        saveReviewInfo();
    })
});

function dataTableLoadAfter() {
    clickNotice()
}

function saveReviewInfo() {
    let releaseFlag = $("#releaseFlag").val()
    if (releaseFlag === "0") {
        popMsg("当前选中精彩回顾未发布，请先去精彩回顾列表中更改精彩回顾")
    } else {
        let num = 0;
        let courseIdList = JSON.parse($("#configCourseList").val());
        for (let i = 0; i < courseIdList.length; i++) {
            if ($("#configId").val() === courseIdList[i].configId) {
                num++;
            }
        }
        if (num > 0) {
            popMsg("当前精彩回顾已被使用")
        } else {
            let param = {
                configType: $("#configType").val(),
                id: $("#id").val(),
                configId: $("#configId").val(),
                sort: $("#sort").val(),
                courseType: $("#courseCategory").val()
            }
            ajaxData("/homepageConfig/saveConfigCourseInfo", param, function (data) {
                closeWinCallBack(data);
            })
        }
    }
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function columnOperation(data, type, row, meta) {
    return '<input type="hidden" value="' + data.id + '">';
}

//通知类型
function typeName(data, type, row, meta) {
    if (data.typeName == null || data.typeName === '') {
        return '-';
    } else {
        return data.typeName;
    }
}

//标题
function title(data, type, row, meta) {
    if (data.title == null || data.title === '') {
        return '-';
    } else {
        return data.title;
    }
}

//时间
function trainDate(data, type, row, meta) {
    if (data.trainDate == null || data.trainDate === '') {
        return '-';
    } else {
        return data.trainDate;
    }
}

//来源
function source(data, type, row, meta) {
    if (data.source == null || data.source === '') {
        return '-';
    } else {
        return data.source;
    }
}

//发布
function releaseName(data, type, row, meta) {
    if (data.releaseName == null || data.releaseName === '') {
        return '-';
    } else {
        return data.releaseName;
    }
}

//创建人
function realName(data, type, row, meta) {
    if (data.realName == null || data.realName === '') {
        return '-';
    } else {
        return data.realName;
    }
}

function clickNotice() {
    $("#tableAll").find("tbody").find("tr").each(function (n,obj) {
        $(obj).css("cursor","pointer");
        $(obj).attr("onclick","courseSelect(this)");
    })
}

function courseSelect(obj) {
    let noticeName = $(obj).find("td").eq(2).html()
    let configId = $(obj).find("td").eq(9).find("input").val()
    let releaseFlag = $(obj).find("td").eq(3).html()
    $("#configId").val(configId)
    $("#courseInfoSelect").html(noticeName)
    $("#releaseFlag").val(releaseFlag)
}
