<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .courseInfo{
            display: inline-block;
            padding: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>

<div class="panel">
    <div class="panel-heading" style="background-color: #FFFFFF;">
        <div class="courseInfo">已选：</div>
        <div class="courseInfo" id="courseInfoSelect"></div>
        <input type="hidden" id ="teacherId" value="">
        <input type="hidden" id="sort" value="${sort}">
        <input type="hidden" id="configCourseTeacherList" value='${configCourseTeacherList}'>
        <span id="saveCourseTeacher" class="btn btn-primary" style="float: right;margin: 10px">确定选中</span>
    </div>
    <div class="panel-body">
        <form:form modelAttribute="ebSchoolLecturerInfoDto" id="teachInfoForm">
            <div class="row">
                <label class=" col-md-1 control-label">讲师姓名</label>
                <div class="col-md-3">
                    <form:input path="teachName" placeholder="请输入文本" cssClass="form-control" autocomplete="off"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
                </div>
                <label class=" col-md-1 control-label">所属机构</label>
                <div class="col-md-3">
                    <form:select path="teachOrg" id="teachOrg" name="teachOrg" class="form-control">
                        <form:option value="">全部</form:option>
                        <c:forEach var="property" items="${orgList}" varStatus="index">
                            <form:option value="${property.typeValue}">${property.typeName}</form:option>
                        </c:forEach>
                    </form:select>
                </div>
            </div>
            <div class="row" style="margin-bottom: 8px;float: right;margin-top: -40px;">
                <span id="btnQuery" class="btn btn-primary">查询</span>
                <span id="btnClear" class="btn btn-default">清空</span>
            </div>
        </form:form>
        <div class="row">
            <e:grid id="table1" action="/ebSchoolLecturerInfo/queryLecturerInfoList?type=0"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:30%"/>
                <e:gridColumn label="讲师名称" displayColumn="teachName" orderable="false"
                              cssClass="text-center" cssStyle="width:30%"/>
                <e:gridColumn label="所属机构" displayColumn="teachOrg" orderable="false"
                              cssClass="text-center" cssStyle="width:40%"/>
                <e:gridColumn label="" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:0%" />
            </e:grid>
        </div>
    </div>
</div>

</body>
</html>
