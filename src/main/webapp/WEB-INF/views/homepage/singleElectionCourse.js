var selIds = '';
var selName = '';
$(document).ready(function () {
    $(window).keydown( function(e) {
        var key = window.event?e.keyCode:e.which;
        if(key.toString() == "13"){
            return false;
        }
    });
    selIds = $("#selIds").val();

    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        search();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/homepageConfig/queryChooseCourse", $("#queryForm").formSerialize());
}
function rcIndex(data, type, row, meta) {
    var str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;" onclick="selCompany(this)">';
        str +=
            '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    str += '<label></label>';
    str += '<input type="hidden" name="courseId" value="' + data.id + '">'
    str += '</div>';
    return str;
}

function courseName(data, type, row, meta) {
    var str = '<div style="cursor:pointer;width: 100%" onclick="selCourseName(this)">'+data.courseName+'</div>'
    return str;
}

function selCourseName(item) {
    var chk = $(item).parent().prev().find('input').eq(0);
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    var courseName = $(item).html()//课程名称
    chk.prop('checked',true);
    if (chk.prop('checked')) {
        $(".selDeclare").each(function (n,obj) {
            $(obj).prop('checked',false)
        });
        chk.prop('checked',true);
        selIds = id;
        selName = courseName
    } else {
        selIds = '';
        selName = ''
    }
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    var courseName = $(item).parent().parent().find('td').eq(1).children().html();//课程名称
    chk.prop('checked',true);
    if (chk.prop('checked')) {
        $(".selDeclare").each(function (n,obj) {
            $(obj).prop('checked',false)
        });
        chk.prop('checked',true);
        selIds = id;
        selName = courseName
    } else {
        selIds = '';
        selName = ''
    }
}
function submitOn() {
    if (selIds!=''&&selIds!=null){
        var data = {
            courseId : selIds,
            courseName : selName
        }
        closeWinCallBack(data)
    }else {
        popMsg("请选择课程")
    }

}