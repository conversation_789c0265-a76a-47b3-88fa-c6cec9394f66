<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style type="text/css">
        .sch-big-div {
            padding-left: 20px;
        }

        .sch-big-font {
            font-size: 22px;
            font-weight: 600;
        }

        .sch-td {
            background-color: #f1f1f1;
            FONT-WEIGHT: 600;
        }

        .sch-btn {
            color: #0a67fb;
            cursor: pointer;
        }

        .sch-sdiv {
            text-align: right;
        }

        .sch-row {
            margin-top: 5px;
        }
        .rectangles{
            width: 6px;
            height: 18px;
            float: left;
            margin-top: 2px;
            background: #1d89cf;
        }
        .bannerClass{
            word-wrap:break-word;word-break:break-all
        }
        .icon-class{
            cursor: pointer;
            margin: 0 5px;
        }
        .table-bordered tbody tr.even {
            background-color: #ffffff;
        }

        .table-bordered tbody tr.odd {
            background-color: #eeeeee;
        }

        .ellipsis {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

    </style>
</head>
<body>
<div class="panel">
    <div class="sch-banner sch-big-div">
        <div class="row">
            <span class="sch-big-font">WEB轮播设置</span>
                <span id="btn_add_banner" class="btn btn-primary" style="margin-top: -8px;margin-left: 20px" onclick="addBanner('1')">新增</span>
<%--            <sec:authorize access="hasAuthority('RES_REFRESH_HOME_AUTHORITY_3')" >--%>
<%--                <span id="btn_refresh" class="btn btn-primary" style="margin-top: 4px;margin-right: 20px;float: right" onclick="refreshHome()">刷新首页缓存</span>--%>
<%--            </sec:authorize>--%>
        </div>
        <div class="panel panel-default" id="schBanner">
            <table class="table table-bordered no-margin" id="schBannerTable" style="text-align: center;">
                <thead>
                <tr>
                    <td width="10%" class="sch-td">序号</td>
                    <td width="20%" class="sch-td">标题</td>
                    <td width="20%" class="sch-td">关联课程/专题/链接</td>
                    <td width="20%" class="sch-td">轮播图</td>
                    <td width="10%" class="sch-td">关联类型</td>
                    <td width="15%" class="sch-td">操作</td>
                </tr>
                </thead>
                <tbody id="schBannerBody">
                <c:forEach items="${schHomeBanner}" var="item" varStatus="status">
                    <tr data-id="${item.id}" data-index="${status.index}">
                        <td class="serialNumber bannerClass">
                                ${status.index+1}
                        </td>
                        <td class="tiele bannerClass">
                                ${item.title}
                        </td>
                        <td class="relationItemName bannerClass">
                            <span title="${item.relationItemName}">
                                <c:set var="relationItemName" value="${item.relationItemName}" />
                                <c:choose>
                                    <c:when test="${fn:length(relationItemName) > 40}">
                                        <span class="ellipsis" style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">${fn:substring(relationItemName, 0, 40)}......</span>
                                        <span id="btn_copy" class="btn btn-primary btn-xs" style="float: right; margin-left: 5px; vertical-align: middle;" onclick="copyText('${relationItemName}')">复制</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span>${relationItemName}</span>
                                    </c:otherwise>
                                </c:choose>
                            </span>
                        </td>
                        <td class="imageName bannerClass">
                                ${item.imageName}
                        </td>
                        <td class="aliveName bannerClass">
                                ${item.relationTypeName}
                        </td>
                        <td class="bannerClass">
                            <sec:authorize access="hasAuthority('RES_BANNER_INIT_AUTHORITY_3')" >
                                <i class="fa fa-edit icon-class" title="编辑" onclick="editBanner('${item.useType}','${item.id}',this)"></i>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_BANNER_DELETE_AUTHORITY_3')" >
                                <i class="fa fa-trash icon-class" title="删除" onclick="deleteBanener('${item.useType}','${item.id}',this)"></i>
                            </sec:authorize>
                            <sec:authorize access="hasAuthority('RES_BANNER_SAVE_MOVE_DETAIL_AUTHORITY_3')" >
                                <i class="fa fa-arrow-circle-up icon-class" title="上移" onclick="moveDetail('up','${item.useType}','${item.id}',this)"></i>
                                <i class="fa fa-arrow-circle-down icon-class" title="下移" onclick="moveDetail('down','${item.useType}','${item.id}',this)"></i>
                            </sec:authorize>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>

<%--        <div class="row">--%>
<%--            <span class="sch-big-font">小程序轮播设置</span>--%>
<%--            <sec:authorize access="hasAuthority('RES_BANNER_INIT_AUTHORITY_3')" >--%>
<%--                <span id="btn_add_banner" class="btn btn-primary" style="margin-top: -8px;margin-left: 20px" onclick="addBanner('2')">新增</span>--%>
<%--            </sec:authorize>--%>
<%--        </div>--%>
<%--        <div class="panel panel-default" id="schBannerAPP">--%>
<%--            <table class="table table-bordered no-margin" id="schBannerTableAPP" style="text-align: center;">--%>
<%--                <thead>--%>
<%--                <tr>--%>
<%--                    <td width="10%" class="sch-td">序号</td>--%>
<%--                    <td width="20%" class="sch-td">标题</td>--%>
<%--                    <td width="20%" class="sch-td">关联课程/专题/链接</td>--%>
<%--                    <td width="20%" class="sch-td">轮播图</td>--%>
<%--                    <td width="10%" class="sch-td">关联类型</td>--%>
<%--                    <td width="15%" class="sch-td">操作</td>--%>
<%--                </tr>--%>
<%--                </thead>--%>
<%--                <tbody id="schBannerBodyAPP">--%>
<%--                <c:forEach items="${schHomeBannerAPP}" var="item" varStatus="status">--%>
<%--                    <tr data-id="${item.id}" data-index="${status.index}">--%>
<%--                        <td class="serialNumber bannerClass">--%>
<%--                                ${status.index+1}--%>
<%--                        </td>--%>
<%--                        <td class="tiele bannerClass">--%>
<%--                                ${item.title}--%>
<%--                        </td>--%>
<%--                        <td class="relationItemName bannerClass">--%>
<%--                            <span title="${item.relationItemName}">--%>
<%--                                <c:set var="relationItemName" value="${item.relationItemName}" />--%>
<%--                                <c:choose>--%>
<%--                                    <c:when test="${fn:length(relationItemName) > 40}">--%>
<%--                                        <span class="ellipsis" style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">${fn:substring(relationItemName, 0, 40)}......</span>--%>
<%--                                        <span id="btn_copy" class="btn btn-primary btn-xs" style="float: right; margin-left: 5px; vertical-align: middle;" onclick="copyText('${relationItemName}')">复制</span>--%>
<%--                                    </c:when>--%>
<%--                                    <c:otherwise>--%>
<%--                                        <span>${relationItemName}</span>--%>
<%--                                    </c:otherwise>--%>
<%--                                </c:choose>--%>
<%--                            </span>--%>
<%--                        </td>--%>
<%--&lt;%&ndash;                        <td class="relationItemName bannerClass">&ndash;%&gt;--%>
<%--&lt;%&ndash;                            <c:if test="${not empty item.relationItemName}">&ndash;%&gt;--%>
<%--&lt;%&ndash;                                ${item.relationItemName}&ndash;%&gt;--%>
<%--&lt;%&ndash;                                <input type="hidden" value="${item.relationItem}">&ndash;%&gt;--%>
<%--&lt;%&ndash;                            </c:if>&ndash;%&gt;--%>
<%--&lt;%&ndash;                        </td>&ndash;%&gt;--%>
<%--                        <td class="imageName bannerClass">--%>
<%--                                ${item.imageName}--%>
<%--                        </td>--%>
<%--                        <td class="aliveName bannerClass">--%>
<%--                                ${item.relationTypeName}--%>
<%--                        </td>--%>
<%--                        <td class="bannerClass">--%>
<%--                            <sec:authorize access="hasAuthority('RES_BANNER_INIT_AUTHORITY_3')" >--%>
<%--                                    <i class="fa fa-edit icon-class" title="编辑" onclick="editBanner('${item.useType}','${item.id}',this)"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_BANNER_DELETE_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-trash icon-class" title="删除" onclick="deleteBanener('${item.useType}','${item.id}',this)"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_BANNER_SAVE_MOVE_DETAIL_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-arrow-circle-up icon-class" title="上移" onclick="moveDetail('up','${item.useType}','${item.id}',this)"></i>--%>
<%--                                <i class="fa fa-arrow-circle-down icon-class" title="下移" onclick="moveDetail('down','${item.useType}','${item.id}',this)"></i>--%>
<%--                            </sec:authorize>--%>
<%--                        </td>--%>
<%--                    </tr>--%>
<%--                </c:forEach>--%>
<%--                </tbody>--%>
<%--            </table>--%>
<%--        </div>--%>

<%--        <div class="row">--%>
<%--            <span class="sch-big-font">热门课程</span>--%>
<%--            &lt;%&ndash;            <span id="btn_add_course" class="btn btn-primary" style="margin-top: -8px;margin-left: 20px" onclick="addCourse()">选择课程</span>&ndash;%&gt;--%>
<%--        </div>--%>
<%--        <div class="panel panel-default">--%>
<%--            <table class="table table-bordered no-margin" style="text-align: center;">--%>
<%--                <thead>--%>
<%--                <tr>--%>
<%--                    <td width="10%" class="sch-td">序号</td>--%>
<%--                    <td width="30%" class="sch-td">课程名称</td>--%>
<%--                    <td width="20%" class="sch-td">课程类型</td>--%>
<%--                    <td width="20%" class="sch-td">操作</td>--%>
<%--                </tr>--%>
<%--                </thead>--%>
<%--                <tbody id="courseInfoTable">--%>
<%--                <c:forEach items="${configCourseList}" var="item" varStatus="status">--%>
<%--                    <tr>--%>
<%--                        <td>${status.index+1}</td>--%>
<%--                        <td>--%>
<%--                            <c:if test="${item.configId != null && item.configId != ''}">--%>
<%--                                ${item.courseName}--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.configId == null || item.configId == ''}">--%>
<%--                                ---%>
<%--                            </c:if>--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <c:if test="${item.courseType == '0' }">--%>
<%--                                远程课程--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.courseType == '1'}">--%>
<%--                                直播课程--%>
<%--                            </c:if>--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDITCOURSE_INIT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-edit icon-class" title="选择远程课程" onclick="editCourseInfo('${item.id}','${item.configId}','${item.sort}','${item.courseName}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-edit icon-class" title="选择直播课程" onclick="editLiveCourseInfo('${item.id}','${item.configId}','${item.sort}','${item.courseName}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_TEACHER_SORT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-arrow-circle-up icon-class" onclick="editCourseTeacherUp('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-arrow-circle-down icon-class" onclick="editCourseTeacherDown('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                                &lt;%&ndash;                            <i class="fa fa-trash icon-class" title="删除" onclick="deleteCourseInfo('${item.id}','${item.configType}')"></i>&ndash;%&gt;--%>
<%--                        </td>--%>
<%--                    </tr>--%>
<%--                </c:forEach>--%>
<%--                </tbody>--%>
<%--            </table>--%>
<%--        </div>--%>

<%--        <div class="row">--%>
<%--            <span class="sch-big-font">培训通知</span>--%>
<%--            <sec:authorize access="hasAuthority('RES_EDIT_NOTICE_INIT_AUTHORITY_3')" >--%>
<%--                <span id="btn_add_course" class="btn btn-primary" style="margin-top: -8px;margin-left: 20px" onclick="addNotice()">新增</span>--%>
<%--            </sec:authorize>--%>
<%--        </div>--%>
<%--        <div class="panel panel-default">--%>
<%--            <table class="table table-bordered no-margin" style="text-align: center;">--%>
<%--                <thead>--%>
<%--                <tr>--%>
<%--                    <td width="10%" class="sch-td">序号</td>--%>
<%--                    <td width="70%" class="sch-td">标题</td>--%>
<%--                    <td width="20%" class="sch-td">操作</td>--%>
<%--                </tr>--%>
<%--                </thead>--%>
<%--                <tbody id="noticeInfoTable">--%>
<%--                <c:forEach items="${configNoticeList}" var="item" varStatus="status">--%>
<%--                    <tr>--%>
<%--                        <td>${status.index + 1}</td>--%>
<%--                        <td>--%>
<%--                            <c:if test="${item.configId != null && item.configId != ''}">--%>
<%--                                ${item.noticeName}--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.configId == null || item.configId == ''}">--%>
<%--                                ---%>
<%--                            </c:if>--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_NOTICE_INIT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-edit icon-class" title="编辑"--%>
<%--                                   onclick="editNoticeInfo('${item.id}','${item.configId}','${item.sort}','${item.noticeName}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_TEACHER_SORT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-arrow-circle-up icon-class" title="上移"--%>
<%--                                   onclick="editCourseTeacherUp('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-arrow-circle-down icon-class" title="下移"--%>
<%--                                   onclick="editCourseTeacherDown('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_DELETE_TEACHER_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-trash icon-class" title="删除" onclick="deleteCourseInfo('${item.id}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                        </td>--%>
<%--                    </tr>--%>
<%--                </c:forEach>--%>
<%--                </tbody>--%>
<%--            </table>--%>
<%--        </div>--%>
<%--        <div class="row">--%>
<%--            <span class="sch-big-font">精彩回顾</span>--%>
<%--            <sec:authorize access="hasAuthority('RES_EDIT_REVIEW_INIT_AUTHORITY_3')" >--%>
<%--                <span id="btn_add_review" class="btn btn-primary" style="margin-top: -8px;margin-left: 20px" onclick="addReview()">新增</span>--%>
<%--            </sec:authorize>--%>
<%--        </div>--%>
<%--        <div class="panel panel-default">--%>
<%--            <table class="table table-bordered no-margin" style="text-align: center;">--%>
<%--                <thead>--%>
<%--                <tr>--%>
<%--                    <td width="10%" class="sch-td">序号</td>--%>
<%--                    <td width="70%" class="sch-td">标题</td>--%>
<%--                    <td width="20%" class="sch-td">操作</td>--%>
<%--                </tr>--%>
<%--                </thead>--%>
<%--                <tbody id="reviewInfoTable">--%>
<%--                <c:forEach items="${configReviewList}" var="item" varStatus="status">--%>
<%--                    <tr>--%>
<%--                        <td>${status.index + 1}</td>--%>
<%--                        <td>--%>
<%--                            <c:if test="${item.configId != null && item.configId != ''}">--%>
<%--                                ${item.noticeName}--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.configId == null || item.configId == ''}">--%>
<%--                                ---%>
<%--                            </c:if>--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_REVIEW_INIT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-edit icon-class" title="编辑"--%>
<%--                                   onclick="editReviewInfo('${item.id}','${item.configId}','${item.sort}','${item.noticeName}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_TEACHER_SORT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-arrow-circle-up icon-class" title="上移"--%>
<%--                                   onclick="editCourseTeacherUp('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-arrow-circle-down icon-class" title="下移"--%>
<%--                                   onclick="editCourseTeacherDown('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_DELETE_TEACHER_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-trash icon-class" title="删除" onclick="deleteCourseInfo('${item.id}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                        </td>--%>
<%--                    </tr>--%>
<%--                </c:forEach>--%>
<%--                </tbody>--%>
<%--            </table>--%>
<%--        </div>--%>

<%--        <div class="row">--%>
<%--            <span class="sch-big-font">热门专题</span>--%>
<%--            <sec:authorize access="hasAuthority('RES_EDITCOURSE_TYPE_INIT_AUTHORITY_3')" >--%>
<%--                <span class="btn btn-primary" style="margin-top: -8px;margin-left: 20px" onclick="addCourseType()">新增</span>--%>
<%--            </sec:authorize>--%>
<%--        </div>--%>
<%--        <div class="panel panel-default">--%>
<%--            <table class="table table-bordered no-margin" style="text-align: center;" id="configCourseType">--%>
<%--                <thead>--%>
<%--                <tr>--%>
<%--                    <td width="20%" class="sch-td">序号</td>--%>
<%--                    <td width="40%" class="sch-td">分类名称</td>--%>
<%--                    <td width="20%" class="sch-td">状态</td>--%>
<%--                    <td width="20%" class="sch-td">操作</td>--%>
<%--                </tr>--%>
<%--                </thead>--%>
<%--                <tbody id="courseTableId">--%>
<%--                <c:forEach items="${configCourseTypeList}" var="item" varStatus="status">--%>
<%--                    <tr>--%>
<%--                        <td>--%>
<%--                                ${status.index+1}--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <c:if test="${item.configId != null && item.configId != ''}">--%>
<%--                                ${item.courseTypeName}--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.configId == null || item.configId == ''}">--%>
<%--                                ---%>
<%--                            </c:if>--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <c:if test="${item.configId != null && item.configId != ''}">--%>
<%--                                固定--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.configId == null || item.configId == ''}">--%>
<%--                                无--%>
<%--                            </c:if>--%>
<%--                        </td>--%>
<%--                        <td>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDITCOURSE_TYPE_INIT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-edit icon-class" title="编辑" onclick="editCourseType('${item.id}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_TEACHER_SORT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-arrow-circle-up icon-class" title="上移" onclick="editCourseTeacherUp('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-arrow-circle-down icon-class" title="下移" onclick="editCourseTeacherDown('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_DELETE_TYPE_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-trash icon-class" title="取消固定" onclick="deleteCourseType('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                        </td>--%>
<%--                    </tr>--%>
<%--                </c:forEach>--%>
<%--                </tbody>--%>
<%--            </table>--%>
<%--        </div>--%>

<%--        <div class="row">--%>
<%--            <span class="sch-big-font">特聘专家</span>--%>
<%--            <sec:authorize access="hasAuthority('RES_EDITCOURSE_TEACHER_INIT_AUTHORITY_3')" >--%>
<%--                <span id="addCourseTeacher" class="btn btn-primary" style="margin-bottom: 6px;margin-left: 20px"  onclick="addCourseTeacher()">选择导师</span>--%>
<%--            </sec:authorize>--%>
<%--            <input type="hidden" id="sort" value="${sort}">--%>
<%--        </div>--%>
<%--        <div class="panel panel-default">--%>
<%--            <table class="table table-bordered no-margin" style="text-align: center;">--%>
<%--                <thead>--%>
<%--                <tr>--%>
<%--                    <td width="10%" class="sch-td">序号</td>--%>
<%--                    <td width="30%" class="sch-td">讲师名称</td>--%>
<%--                    <td width="20%" class="sch-td">所属机构</td>--%>
<%--                    <td width="20%" class="sch-td">操作</td>--%>
<%--                </tr>--%>
<%--                </thead>--%>
<%--                <tbody id="courseTeacherTableId">--%>
<%--                <c:forEach items="${configCourseTeacherList}" var="item" varStatus="status">--%>
<%--                    <tr class="${status.index % 2 == 0 ? 'even' : 'odd'}">--%>
<%--                        <td>${status.index+1}</td>--%>
<%--                        <td>${item.teacherName}</td>--%>
<%--                        <td>${item.teachOrg}</td>--%>
<%--                        <td>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EB_SCHOOL_LECTURER_ADD_INIT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-edit" title="编辑" onclick="editCourseTeacher('${item.id}','${item.configId}','${item.sort}','${item.teacherName}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_DELETE_TEACHER_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-trash" title="删除" onclick="deleteCourseTeacher('${item.id}','${item.configId}','${item.sort}','${item.configType}','${item.teacherName}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                            <sec:authorize access="hasAuthority('RES_EDIT_COURSE_TEACHER_SORT_AUTHORITY_3')" >--%>
<%--                                <i class="fa fa-arrow-circle-up" title="上移" onclick="editCourseTeacherUp('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-arrow-circle-down" title="下移" onclick="editCourseTeacherDown('${item.id}','${item.configId}','${item.sort}','${item.configType}')"></i>--%>
<%--                                <i class="fa fa-exchange iconStyle" title="顺序调整" onclick="quickMoveInfoTo('${item.id}' , '${status.index+1}' , '${item.sort}','${fn:length(configCourseTeacherList)}')"></i>--%>
<%--                            </sec:authorize>--%>
<%--                        </td>--%>
<%--                    </tr>--%>
<%--                </c:forEach>--%>
<%--                </tbody>--%>
<%--            </table>--%>
<%--        </div>--%>
    </div>
</div>

</body>
</html>
