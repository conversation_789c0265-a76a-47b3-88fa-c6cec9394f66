$(document).ready(function() {
    //查询
    $("#btnQuery").bind("click",function() {
        var codeValue =  $("#codeValue").val();
        var param = {
            codeValue:codeValue//公司代码
        }
        ajaxTableQuery("table1", "/labelManagementList/labelManagementList",param);
    });
    //清空
    $("#btnClear").bind("click", function () {
        var param = {}
        document.getElementById("newsForm").reset();
        ajaxTableQuery("table1", "/labelManagementList/labelManagementList",param);
    });
    //新增
    $("#btnAdd").bind("click", function() {
        var param = {}
        popWin("新闻联络员新增", "/labelManagementList/labelManagementAddInit", param, "900px", "600px", winCallback, "",winCallback);
    });
    //导出
    $("#exportTableBtn").bind("click",function () {
        window.open(contextPath + "/labelManagementList/exportLabelManagement?" + $("#newsForm").serialize());
    });
    //导入
    $("#importRenewal").bind("click",function () {
        importRenewal()
    });
    // 模板下载
    $("#cmTemplateDownload").bind("click", function() {
        window.open(contextPath+"/labelManagementList/labelManagementDownload","_blank");
    });
});

function importRenewal() {
    
    $("#importRenewal").fileupload({
        url : contextPath + "/labelManagementList/importLabelManagement",
        dataType : "json",
        autoUpload : true,
        formData:{importMonth:1},
        add : function(e,data) {
            if(data.files[0].name.indexOf("xlsx") == -1) {
                popMsg("只支持.xlsx格式文件上传");
                return;
            }
            data.submit();
        },
        submit : function(e, data) {
            index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
        },
        done : function(e, data) {
            layer.close(index);
            var rtnRlt = data.result;
            if(rtnRlt != null){
                _cmImportResultDto = rtnRlt.result;
                if (_cmImportResultDto) {
                    popAlert("导入成功");
                    // 刷新数据列表
                    winCallback();
                } else {
                    popAlert("导入失败,请检查导入数据");
                }
            }
        },
        fail : function(e, data) {
            layer.close(index);
            popAlert("导入失败,请检查导入数据");
        }
    });
}

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

// 操作
function renderColumnOperation(data, type, row, meta){
    var str2 =	'<a href="#" onClick="labelInfoDel(\''+data.id+'\')" style="margin-right:4px;">删除</a>';
    return str2;
}


//删除
function labelInfoDel(id){
    popConfirm("确认删除该数据吗？",function(){
        var param = {
            id:id
        };
        ajaxData("/labelManagementList/labelManagementdel",param,function(data) {
            
            if(data.length > 0) {
                ajaxTableReload("table1",false);
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}

function winCallback() {
    ajaxTableReload("table1", false);
}
