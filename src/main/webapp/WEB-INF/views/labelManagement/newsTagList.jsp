<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            text-align: center;
            margin-top: 6px;
        }

        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">新闻联络员列表</i>
    </div>
    <div class="panel-body">
        <form:form modelAttribute="codeDto" id="newsForm">
            <div class="row">
                <label class=" col-md-1 control-label">公司代码</label>
                <div class="col-md-3">
                    <form:input path="codeValue" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
                </div>
            </div>
            <div class="row" style="margin-bottom: 8px;float: right;">
                <span id="btnAdd" class="btn btn-primary">新增</span>
                <span id="exportTableBtn" class="btn btn-primary">导出</span>
                <span class="btn btn-primary fileinput-button"><span class="">导入</span><input id="importRenewal" type="file" name="files" class="btn btn-primary"/></span>
                <button type="button" id="cmTemplateDownload" class="btn btn-primary">模板下载</button>
                <span id="btnQuery" class="btn btn-primary">查询</span>
                <span id="btnClear" class="btn btn-default">清空</span>
            </div>
        </form:form>
        <div class="row">
            <e:grid id="table1" action="/labelManagementList/labelManagementList"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:3%"/>
                <e:gridColumn label="公司代码" displayColumn="codeValue" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="创建时间" displayColumn="createTeacherTime" orderable="false"
                              cssClass="text-center" cssStyle="width:8%;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                              cssClass="text-center" cssStyle="width:6%;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>

