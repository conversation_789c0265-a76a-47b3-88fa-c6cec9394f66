$(document).ready(function() {
    // 保存
    $("#btnSave").bind("click", function () {
        checkFirm();
    });
});

function checkFirm() {
    if ($("#codeForm").valid()) {
        
        if ($("#codeValue").val() == "" || $("#codeValue").val() == null) {
            popAlert("公司代码为空，不能保存");
        } else {
            var param= {
                codeValue:$("#codeValue").val()
            }
            ajaxData("/labelManagementList/labelManagementAddSave", param, closeWinCallBack);
        }
    }

}