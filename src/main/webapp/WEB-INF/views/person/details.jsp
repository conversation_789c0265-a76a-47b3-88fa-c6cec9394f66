<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js />
</head>
<body>

    <div class="panel" id="myPanel">
        <div class="panel-body">
            <legend>高管信息</legend>
            <form:form modelAttribute="personListDto" id="testForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.personName" />
                    </div>
                    <label class="col-md-1 control-label">外文名</label>
                    <div class="col-md-3">
                        <e:date cssClass="form-control" path="personDto.foreignName" />
                    </div>
                    <label class="col-md-1 control-label">国籍</label>
                    <div class="col-md-3">
                        <e:date cssClass="form-control" path="personDto.nationality" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">证件类型</label>
                    <div class="col-md-3">
                        <e:date cssClass="form-control" path="personDto.certificateType" />
                    </div>
                    <label class="col-md-1 control-label">称谓</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.titleName" />
                    </div>
                    <label class="col-md-1 control-label">证件号码</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.certificate" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">移动电话</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.telephone" />
                    </div>
                    <label class="col-md-1 control-label">出生年月</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.birthday" />
                    </div>
                    <label class="col-md-1 control-label">电子邮箱</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.mail" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">固定电话</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.phone" />
                    </div>
                    <label class="col-md-1 control-label">传真</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.fax" />
                    </div>
                    <label class="col-md-1 control-label">邮编</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.post" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">地址</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.address" />
                    </div>
                    <label class="col-md-1 control-label">岗位</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personDto.jobName" />
                    </div>
                    <label class="col-md-1 control-label">性别</label>
                    <div class="col-md-3">
                        <label class='radio-inline'><form:radiobutton path="personDto.sex" value="0" />男</label> <label
                            class='radio-inline'><form:radiobutton path="personDto.sex" value="1" />女</label>
                    </div>
                </div>
            </form:form>
        </div>
    </div>

    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table action="/person/query" col="4" cssClass="table table-hover table-striped">
                <tr style="background-color: #EDEDED">
                    <th width="25%" style="text-align: left">学校</th>
                    <th width="25%" style="text-align: left">学历/学位</th>
                    <th width="25%" style="text-align: left">学习时间</th>
                    <th width="25%" style="text-align: left">专业情况</th>
                </tr>
                <c:forEach var="item" items="${personListDto.personDegreesDtoList}" varStatus="status">
                    <tr>
                        <td align="left"><c:out value="${item.school}" /></td>
                        <td align="left"><c:out value="${item.degrees}" /></td>
                        <td align="left"><fmt:formatDate value="${item.accessDate}" pattern="yyyy-MM-dd" />-<fmt:formatDate
                                value="${item.graduatedDate}" pattern="yyyy-MM-dd" /></td>
                        <td align="left"><c:out value="${item.major}" /></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>

    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table col="5" action="/person/query" cssClass="table table-hover table-striped">
                <tr>
                    <th width="20%" style="text-align: left">组织机构</th>
                    <th width="20%" style="text-align: left">届次</th>
                    <th width="20%" style="text-align: left">岗位名称</th>
                    <th width="20%" style="text-align: left">任职起止时间</th>
                    <th width="20%" style="text-align: left">任职状态</th>
                </tr>
                <c:forEach var="item" items="${personListDto.personResumeDtoList}" varStatus="status">
                    <tr>
                        <td align="left"><c:out value="${item.orgNo}" /></td>
                        <td align="left"><c:out value="${item.lineup}" /></td>
                        <td align="left"><c:out value="${item.jobsName}" /></td>
                        <td align="left"><fmt:formatDate value="${item.officeBeginDate}" pattern="yyyy-MM-dd" />-<fmt:formatDate
                                value="${item.officeEndDate}" pattern="yyyy-MM-dd" /></td>
                        <td align="left"><c:out value="${item.takeStatus}" /></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>

    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table col="6" action="/person/query" cssClass="table table-hover table-striped">
                <tr>
                    <th width="10%" style="text-align: left">课程</th>
                    <th width="18%" style="text-align: left">培训类型</th>
                    <th width="18%" style="text-align: left">培训机构</th>
                    <th width="18%" style="text-align: left">培训时间</th>
                    <th width="18%" style="text-align: left">培训地点</th>
                    <th width="18%" style="text-align: left">培训状态</th>
                </tr>
                <c:forEach var="item" items="${personListDto.trainDtoList}" varStatus="status">
                    <tr>
                        <td align="left"><c:out value="${item.course}" /></td>
                        <td align="left"><c:out value="${item.trainType}" /></td>
                        <td align="left"><c:out value="${item.trainOrg}" /></td>
                        <td align="left"><fmt:formatDate value="${item.trainDate}" pattern="yyyy-MM-dd" /></td>
                        <td align="left"><c:out value="${item.trainAddr}" /></td>
                        <td align="left"><c:out value="${item.trainStatus}" /></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>

    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table col="6" action="/person/query" cssClass="table table-hover table-striped">
                <tr>
                    <th width="10%" style="text-align: left">姓名</th>
                    <th width="18%" style="text-align: left">与本人关系</th>
                    <th width="18%" style="text-align: left">性别</th>
                    <th width="18%" style="text-align: left">出生日期</th>
                    <th width="18%" style="text-align: left">国籍</th>
                    <th width="18%" style="text-align: left">移动电话</th>
                </tr>
                <c:forEach var="item" items="${personListDto.relativesDtoList}" varStatus="status">
                    <tr>
                        <td align="left"><c:out value="${item.relativesName}" /></td>
                        <td align="left"><c:out value="${item.relation}" /></td>
                        <td align="left"><c:out value="${item.sex}" /></td>
                        <td align="left"><fmt:formatDate value="${item.birth}" pattern="yyyy-MM-dd" /></td>
                        <td align="left"><c:out value="${item.citizenship}" /></td>
                        <td align="left"><c:out value="${item.teltphone}" /></td>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>
</body>
</html>
