<%--
/***************************************************************
* 程序名 : query.jsp
* 日期  :  2015-7-7
* 作者  :  zhaoyang
* 模块  :  测试
* 描述  :  分页查询页面
* 备注  :
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js />
</head>
<body>
    <div class="panel" id="myPanel">
        <div class="panel-body">
            <form:form action="/person/query" modelAttribute="personDto">
                <div class="form-group">
                    <label class="col-md-1 control-label">公司代码</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="companyId" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input cssClass="form-control" path="personName" />
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-2 control-label">
                        <input type="button" id="btnQuery" class="btn btn-primary" value="查询" />
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12 control-label" style="padding-top: 10px;">
                        <label>董监高数量：<span id="directorNum">0</span></label>
                        <label style="padding-left: 30px;">董秘数量：<span id="secretaryNum">0</span></label>
                        <input type="button" id="btnStatistics" style="margin-left: 30px;" class="btn btn-default" value="统计" />
                    </div>
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-ul"></i> 查询结果
        </div>
        <div class="panel-body">
            <e:table action="/person/query" col="5" cssClass="table table-hover table-striped">
                <tr>
                    <th width="5%" style="text-align:center"">序号</th>
                    <th width="10%" style="text-align:center">名称</th>
                    <th width="10%" style="text-align:center">岗位</th>
                    <th width="10%" style="text-align:center">公司简称</th>
                    <th width="10%" style="text-align:center">所属机构</th>
                    <th width="10%" style="text-align:center">出生年月</th>
                    <th width="5%" style="text-align:center">性别</th>
                    <th width="10%" style="text-align:center">邮箱</th>
                    <th width="10%" style="text-align:center">手机</th>
                    <th width="10%" style="text-align:center">职务起始时间</th>
                    <th width="10%" style="text-align:center">职务结束时间</th>
                    <sec:authorize access="hasAuthority('RES_MANAGER_CRU')" >
                    <sec:authorize access="hasAuthority('RES_MANAGER_CRU_AUTHORITY_3')" >
<!--                     <th width="5%" align="center">操作</th> -->
                     </sec:authorize>
                     </sec:authorize>
                </tr>
                <c:forEach var="item" items="${queryList}" varStatus="status">
                    <tr>
                        <td align="center"><c:out value="${status.count}" /></td>
                        <td align="center"><c:out value="${item.personName}" /></td>
                        <td align="center"><c:out value="${item.jobName}" /></td>
                        <td align="center"><c:out value="${item.zhShortName}" /></td>
                        <td align="center"><c:out value="${item.orgName}" /></td>
                        <td align="center"><fmt:formatDate value="${item.birthday}" pattern="yyyy-MM-dd" /></td>
                        <c:if test="${item.sex == 0}">
                            <td align="center"><c:out value="男" /></td>
                        </c:if>
                        <c:if test="${item.sex == 1}">
                            <td align="center"><c:out value="女" /></td>
                        </c:if>
                        <td align="center"><c:out value="${item.mail}" /></td>
                        <td align="center"><c:out value="${item.telephone}" /></td>
                        <td align="center"><fmt:formatDate value="${item.officeBeginDate}" pattern="yyyy-MM-dd" /></td>
                        <td align="center"><fmt:formatDate value="${item.officeEndDate}" pattern="yyyy-MM-dd" /></td>
                        <sec:authorize access="hasAuthority('RES_MANAGER_CRU')" >
                    <sec:authorize access="hasAuthority('RES_MANAGER_CRU_AUTHORITY_3')" >
<!--                         <td align="center"> -->
<!--                         <span class="widget-icon"> <a -->
<%--                                 href="javascript:movePage('/person/modify?id=<c:out value="${item.id}" />')"><i --%>
<!--                                     class="icon icon-edit"></i></a> -->
<!--                         </span> -->
<!--                         </td> -->
                        </sec:authorize>
                        </sec:authorize>
                    </tr>
                </c:forEach>
            </e:table>
        </div>
    </div>
</body>
</html>
