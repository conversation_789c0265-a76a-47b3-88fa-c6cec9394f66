//@ sourceURL=query.js
$(document).ready(function() {

	// 查询
	if(document.getElementById("btnQuery")){
		$("#btnQuery").bind("click", function() {
			submitForm("/person/query");
		});
	}
	//统计
    if(document.getElementById("btnStatistics")){
        $("#btnStatistics").bind("click", function() {
            ajaxData("/person/statisticsDirectorNum", null, function (data) {
				if(data != null && data != undefined){
					$("#directorNum").text(data.directorNum);
					$("#secretaryNum").text(data.secretaryNum);
				}
            })
        });
    }
});
