<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style>


    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body" style="height: calc(100vh - 94px - 20px)">
        <div>
            <span>下面勾选的机构为合作单位，将在前面其他证监局列表展示</span>
            <sec:authorize access="hasAuthority('RES_WEB_SAVEORG_AUTHORITY_3')" >
                <span id="btnSave" class="identity-limit btn btn-primary" style="float: right;margin-right: 40px">保存</span>
            </sec:authorize>
        </div>
        <div id="orgListId" style="margin-left: 92px;">
            <c:forEach var="item" items="${showList}"  varStatus="status">
                <div class="col-md-12 user_type_row">
                    <input type="text" value="${item.orgNo}" hidden>
                    <div class="col-md-4" style="width: 250px">
                        <div style="width: 100%;line-height: 28px;margin-right: 50px;">
                            <input type="checkbox" name="interest" id="codeValue" value="${item.orgNo}"
                                <c:if test="${item.showFlag == '1'}">
                                    checked="checked"
                                </c:if>
                            />${item.orgName}
                        </div>
                    </div>
                </div>
            </c:forEach>
        </div>
    </div>
</div>
</body>
</html>