$(document).ready(function () {
    tSelectInit();
})


//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack,
        id: 'orgNo',
        name: 'orgName',
        value: 'orgNo',
        grade: 1,
        resultType: 'all',
        inputType: 'radio', // 单选
        style: {},
    };
    $('#orgId').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('orgId') + '"]').val(d.value);
    search();
}
function search() {
    ajaxTableQuery("tableAll", "/basicInformation/queryRepCaseInfoList",
        $("#queryForm").formSerialize());
}
function columnOperation(data, type, row, meta) {
    var str = '';
    if(document.getElementById("checkAuth")) {
        str = '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="去审核" onclick="editCourse(\'' + data.id + '\',\'' + data.releaseFlag + '\')"></i>';
    } else {
        str = '-';
    }
    return str;
}

function editCourse(id, releaseFlag) {
    var param = {
        id: id,
        releaseFlag: releaseFlag == '是' ? '1' : '0'
    };
    parent.popWin('审核课程信息', '/basicInformation/addCourseManageInit', param, '98%', '98%', callBackAddCourse, '', callBackAddCourse);
}
function callBackAddCourse() {
    ajaxTableReload("tableAll", false);
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}
function renderTeacher(data, type, row, meta) {
    var str = data.teacher;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.teacher + '">' + str + '</span>';
}

function renderApplyPerson(data, type, row, meta) {
    var str = data.applyPerson;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPerson + '">' + str + '</span>';
}
function renderApplyPlate(data, type, row, meta) {
    var str = data.applyPlate;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPlate + '">' + str + '</span>';
}
function renderCourseName(data, type, row, meta) {
    var str = data.courseName;
    // if (str!=null && str !=''){
    // 	if (str.length>12){
    // 		str = str.substring(0,12) + "...";
    // 	}
    // }
    return str;
}

function renderCourseType(data, type, row, meta) {
    var str = data.courseType;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return '<span title="' + data.courseType + '">' + str + '</span>';
}

