<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style>


    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body" style="height: calc(100vh - 94px - 20px)">
        <form:form modelAttribute="orgManagerDto" id="queryForm" >
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-1 control-label" style="top: 5px;width: 130px;"><font class="required-logo">*&nbsp;</font>需要审核的协会:</label>
                <div class="form-group" style="width: 400px;">
                    <div class="col-md-11 ">
                        <input id="orgId" type="text" class="t-select" json-data='${localAssociation}' selected-ids="${selectId}"/>
                        <input name="orgId" type="hidden" placeholder="请选择适用用户类型" />
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_CHECK_COURSE_AUTHORITY_3')" >
            <%--审核权限--%>
            <input type="hidden" id="checkAuth" value="true">
        </sec:authorize>
        <div class="row">
            <e:grid id="tableAll" action="/basicInformation/queryRepCaseInfoList?orgId=${selectId}" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="课程名称" renderColumn="renderCourseName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程类型" renderColumn="renderCourseType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="适用板块" renderColumn="renderApplyPlate" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="适用人群" renderColumn="renderApplyPerson" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程讲师" renderColumn="renderTeacher" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <%--                    <e:gridColumn label="总播放量" displayColumn="amount" orderColumn="amount"--%>
                <%--                                  cssClass="text-center" cssStyle="width:8%;" />--%>
                <e:gridColumn label="发布时间" displayColumn="releaseTime" orderColumn="release_time" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>