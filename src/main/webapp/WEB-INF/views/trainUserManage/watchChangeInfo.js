//@ sourceURL=watchChangeInfo.js
$(document).ready(function () {
    selectInit()
    $("#btnCheck").bind("click", function () {
        checkFirm();
    });
    $("#btnClose").bind("click", function () {
        closeWinCallBack();
    });
    compareHtml();
});

function selectInit() {
    var str = '<select id="checkState" name class="form-control">'
    str += '<option value="">请选择</option>'
    var list = JSON.parse(checkStatusList)
    for (var i = 0; i < list.length; i++) {
        var codeValue = list[i].codeValue
        var codeName = list[i].codeName
        str += '<option value="' + codeValue + '">' + codeName + '</option>'
    }
    str += '</select>'
    $('#selectCheck').append(str)
}

function checkFirm() {
    var id = $('#id').val()
    var userId = $('#userId').val()
    var personType = $("#personType").val()
    var realName = $("#realName").val()
    var remark = $('#remark').val()
    var companyCode = $('#companyCode').val()
    var companyName = $('#companyName').val()
    var belongCommission = $('#belongCommission').val()
    var userPhone = $('#userPhone').val()
    var post = $('#post').val()
    var orgName = $('#orgName').val()
    var jobName = $('#jobName').val()
    var checkState = $('#checkState').val()
    var mail = $('#mail').val()
    var mailingAddress = $('#mailingAddress').val()
    var postalCode = $('#postalCode').val()
    var attUrl = $('#attUrl').val()
    var attName = $('#attName').val()
    var businessType = $('#businessType').val()

    var param = {
        id: id,
        userId: userId,
        personType: personType,
        realName: realName,
        mail: mail,
        mailingAddress: mailingAddress,
        postalCode: postalCode,
        remark: remark,
        companyCode: companyCode,
        companyName: companyName,
        belongCommission: belongCommission,
        orgName: orgName,
        jobName: jobName,
        phone: userPhone,
        post: post,
        attUrl: attUrl,
        attName: attName,
        businessType: businessType,
        status: checkState
    };
    ajaxData("/trainUserChange/userInfoChange", param, closeWinCallBack);
}


function imageClick(index) {
    const viewer = new Viewer(document.querySelectorAll(".ImgPr")[index], {
        inline: false
    });
}


function imageClick1(index) {
    const viewer1 = new Viewer(document.querySelectorAll(".ImgPr1")[index], {
        inline: false
    });
}

function compareHtml( ) {
    var div1 = document.getElementById("oldTest").innerText.trim();
    var div2 = document.getElementById("newTest").innerText.trim();
    var dif = []
    var aArr = div1.split('\n')
    var bArr = div2.split('\n')
    for (let i = 0; i < aArr.length; i++) {
        if (aArr[i] !== bArr[i] && bArr[i].trim() !== '申请修改的个人信息：') {
            dif.push(bArr[i]);
        }
    }
    var content = document.getElementById("newTest").innerHTML;
    for (let i = 0; i < dif.length; i++) {
        content = content.replaceAll(dif[i], '<span class="highlight">' + dif[i] + "</span>");
    }
    document.getElementById("newTest").innerHTML = content;
    return;
}



