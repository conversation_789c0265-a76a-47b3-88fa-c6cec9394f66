<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label {
            padding-top: 7px;
        }

        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }

        .iconColor {
            color: #145ccd;
            margin: 0 3px;
        }

        .icon {
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="userChangeDto" id="queryForm" onkeydown="bindEnter(event)">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">用户类型</label>
                    <div class="col-md-3">
                        <input id="personType" type="text" class="t-select" json-data='${personTypeList}'
                               placeholder="请输入用户类型"/>
                        <input name="personType" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">证券代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control"
                                    autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">公司名称</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control"
                                    autocomplete="off"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="realName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">手机号</label>
                    <div class="col-md-3">
                        <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">审核状态</label>
                    <div class="col-md-3">
                        <input id="checkState" type="text" class="t-select" json-data='${checkStatusList}'
                               placeholder="请输入用户类型"/>
                        <input name="checkState" type="hidden"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-12 text-right">
                        <sec:authorize access="hasAuthority('RES_TRAIN_USER_CHANGE_LIST_AUTHORITY_3')" >
                            <%--查询用户修改列表权限--%>
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空</span>
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_TRAIN_CHANGE_USER_INFO_AUTHORITY_3')" >
            <%--查看修改用户信息权限--%>
            <input type="hidden" id="changeUserInfoAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_TRAIN_DEL_USER_CHANGE_AUTHORITY_3')" >
            <%--删除修改信息权限--%>
            <input type="hidden" id="delUserChangeAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/trainUserChange/getUserChangeInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%"/>
                <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%;"/>
                <e:gridColumn label="证券代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="公司名称/学校" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="部门/年级" displayColumn="orgName" orderable="false"
                              cssClass="text-center" cssStyle="width:7%"/>
                <e:gridColumn label="职务" displayColumn="post" orderable="false"
                              cssClass="text-center" cssStyle="width:7%"/>
                <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                              cssClass="text-center" cssStyle="width:8%"/>
                <e:gridColumn label="审核状态" renderColumn="checkState" orderable="false"
                              cssClass="text-center" cssStyle="width:5%"/>
                <e:gridColumn label="审核人" renderColumn="checkPerson" orderable="false"
                              cssClass="text-center" cssStyle="width:7%"/>
                <e:gridColumn label="审核时间" renderColumn="checkTime" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:10%"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
