<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/viewer.js"></script>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/viewer.css">
    <e:base/>
    <e:js/>
</head>
<style>
    .row {
        margin: 0;
    }

    .startingLabel {
        border-radius: 2px;
        color: #14BCF5;
        background: #E8F8FE;
        border: 1px solid #D0F2FD;
        font-size: 12px;
        width: 45px;
        float: right;
        padding: 0 3px;
        display: inline-block;
        text-align: center;
    }

    .finishLabel {
        border: 1px solid #d1d1cc;
        border-radius: 2px;
        background-color: #F5F5F5;
        color: #999999;
        font-size: 12px;
        width: 45px;
        padding: 0 2px;
        float: right;
        display: inline-block;
        text-align: center;
    }
    .highlight{
        color: red;
    }
</style>
<script>
    var checkStatusList = '${checkStateList}'
</script>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="changeUserForm" modelAttribute="userChangeDto" cssClass="form-horizontal">

            <div class="row" style="padding-bottom: 5px">
                <div class="col-md-12 text-center">
                    <div class="col-md-6">
                        <c:if test="${userChangeDto.id!=null}">
                            <label class="col-xs-2 no-padding control-label text-right">用户审核结果:</label>
                            <label class="col-xs-4 control-label text-left">${userChangeDto.checkStateStr}</label>
                        </c:if>
                    </div>
                </div>
            </div>

            <div class="col-md-6" id="oldTest">
                <div class="row">当前个人信息：</div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12 text-center">
                        <div class="col-md-6">
                            <label class="col-xs-6 no-padding control-label text-right">姓名:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.realName != null && userDto.realName != '') ?userDto.realName:'--'}</label>
                        </div>
                        <div class="col-md-6">
                            <label class="col-xs-6 no-padding control-label text-right">用户类型:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.personTypeStr != null && userDto.personTypeStr != '') ?userDto.personTypeStr:'--'}</label>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12 text-center">
                        <div class="col-md-6">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right"
                                       id="companyNameLabel">公司名称/学校:</label>
                                <label class="col-xs-6 control-label text-left">${(userDto.companyName != null && userDto.companyName != '') ?userDto.companyName:'--'}</label>
                            </c:if>
                        </div>
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right">证券代码:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.companyCode != null && userDto.companyCode != '') ?userDto.companyCode:'--'}</label>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right">所在辖区:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.belongCommissionStr != null && userDto.belongCommissionStr != '') ?userDto.belongCommissionStr:'--'}</label>
                        </div>
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right">部门/年级:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.orgName != null && userDto.orgName != '') ?userDto.orgName:'--'}</label>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right">职务:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.postStr != null && userDto.postStr != '') ?userDto.postStr:'--'}</label>
                        </div>
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right">岗位:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.jobName != null && userDto.jobName != '') ?userDto.jobName:'--'}</label>
                        </div>

                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right">公司邮箱:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.mail != null && userDto.mail != '') ?userDto.mail:'--'}</label>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right" >邮寄地址:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.mailingAddress != null && userDto.mailingAddress != '') ?userDto.mailingAddress:'--'}</label>
                        </div>
                        <div class="col-md-6 text-center">
                            <label class="col-xs-6 no-padding control-label text-right" >邮政编码:</label>
                            <label class="col-xs-6 control-label text-left">${(userDto.postalCode != null && userDto.postalCode != '') ?userDto.postalCode:'--'}</label>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <c:if test="${userDto.attUrl!=null}">
                        <div class="col-md-12">
                            <div class="col-md-6 text-center">
                                <label class="col-xs-6 no-padding control-label text-right">工作证明:</label>
                                <span class="col-xs-6 no-padding control-label text-left" style="color: red;margin-left: 20px;">（注：双击可查看工作证明图片）</span>
                            </div>
                        </div>
                    </c:if>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <c:if test="${userDto.fileList!=null}">
                        <div style="margin: 25px;display: flex;flex-wrap: wrap;justify-content: flex-start">
                            <c:forEach var="item" items="${userDto.fileList}" varStatus="status">
                                <div id="imgId"  class="ImgPr" style="width: 25%;" onclick="imageClick(${status.index})" >
                                    <img src="${fileViewPath}${item}" style="position: relative;cursor: pointer"/>
                                </div>
                            </c:forEach>
                        </div>
                    </c:if>
                </div>
            </div>

            <div class="col-md-6" id="newTest">
                <div class="row">申请修改的个人信息：</div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12 text-center">
                        <div class="col-md-6">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right">姓名:</label>
                                <label class="col-xs-6 control-label text-left">${userChangeDto.realName}</label>
                            </c:if>
                        </div>
                        <div class="col-md-6">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right">用户类型:</label>
                                <label class="col-xs-6 control-label text-left">${userChangeDto.personTypeStr}</label>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12 text-center">
                        <div class="col-md-6">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right"
                                       id="companyNameLabel">公司名称/学校:</label>
                                <label class="col-xs-6 control-label text-left">${userChangeDto.companyName}</label>
                            </c:if>
                        </div>
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right">证券代码:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.companyCode != null && userChangeDto.companyCode != '') ?userChangeDto.companyCode:'--'}</label>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right">所在辖区:</label>
                                <label class="col-xs-6 control-label text-left"
                                       id="belongCommissionStr">${(userChangeDto.belongCommissionStr != null && userChangeDto.belongCommissionStr != '') ?userChangeDto.belongCommissionStr:'--'}</label>
                            </c:if>
                        </div>
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right" id="orgNameLabel">部门/年级:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.orgName != null && userChangeDto.orgName != '') ?userChangeDto.orgName:'--'}</label>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right" id="postLabel">职务:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.postStr != null && userChangeDto.postStr != '') ?userChangeDto.postStr:'--'}</label>
                            </c:if>
                        </div>
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right" id="jobNameLabel">岗位:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.jobName != null && userChangeDto.jobName != '') ?userChangeDto.jobName:'--'}</label>
                            </c:if>
                        </div>

                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right" id="mailLabel">公司邮箱:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.mail != null && userChangeDto.mail != '') ?userChangeDto.mail:'--'}</label>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <div class="col-md-12">
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right"
                                       id="mailingAddressLabel">邮寄地址:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.mailingAddress != null && userChangeDto.mailingAddress != '') ?userChangeDto.mailingAddress:'--'}</label>
                            </c:if>
                        </div>
                        <div class="col-md-6 text-center">
                            <c:if test="${userChangeDto.id!=null}">
                                <label class="col-xs-6 no-padding control-label text-right"
                                       id="postalCodeLabel">邮政编码:</label>
                                <label class="col-xs-6 control-label text-left">${(userChangeDto.postalCode != null && userChangeDto.postalCode != '') ?userChangeDto.postalCode:'--'}</label>
                            </c:if>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <c:if test="${userChangeDto.id!=null && userChangeDto.attUrl!=null}">
                        <div class="col-md-12">
                            <div class="col-md-6 text-center">
                                <label class="col-xs-6 no-padding control-label text-right">工作证明:</label>
                                <span class="col-xs-6 no-padding control-label text-left"
                                      style="color: red;margin-left: 20px;">（注：双击可查看工作证明图片）</span>
                            </div>
                        </div>
                    </c:if>
                </div>
                <div class="row" style="padding-bottom: 5px">
                    <c:if test="${userChangeDto.id!=null && userChangeDto.fileList!=null}">
                        <div style="margin: 25px;display: flex;flex-wrap: wrap;justify-content: flex-start">
                            <c:forEach var="item" items="${userChangeDto.fileList}" varStatus="status">
                                <div id="imgId"  class="ImgPr1" style="width: 25%;" onclick="imageClick1(${status.index})" >
                                    <img src="${fileViewPath}${item}" style="position: relative;cursor: pointer"/>
                                </div>
                            </c:forEach>
                        </div>
                    </c:if>
                </div>
            </div>
            <c:if test="${userChangeDto.id!=null}">
                <c:if test="${userChangeDto.status!='0'}">
                    <div class="col-md-12 no-padding stuDiv">
                        <div class="col-md-12">
                            <label class="col-md-2 no-padding control-label text-right" style="width: 80px;">审核备注:</label>
                            <label class="col-md-10 control-label">${userChangeDto.remark}</label>
                        </div>
                    </div>
                </c:if>
            </c:if>
            <div class="col-xs-12 no-padding" style="margin-top: 20px">
                <div class="col-xs-12 text-center">
                    <c:if test="${userChangeDto.id!=null}">
                        <c:if test="${userChangeDto.status=='0'}">
                            <input id="Button1" type="button" value="审核" class="btn btn-primary" data-toggle="modal"
                                   data-target="#myModal"/>
                        </c:if>
                    </c:if>
                    <span id="btnClose" class="btn btn-default">关闭</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="modal fade" id="myModal">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close">
                    </button>
                    <h4 class="modal-title" id="myModalLabel" style="text-align: center">
                        审核
                    </h4>
                </div>

                <div class="modal-body">
                    <label class="col-md-12  control-label text-left"
                           style="margin-top: 5px;">审核结果:</label>
                    <div id="selectCheck" class="col-md-12 no-padding text-center"
                         style="width: 300px;">
                    </div>
                    <label class="col-md-12  control-label text-left" style="margin-top: 5px;">备注:</label>
                    <textarea id="remark" class="form-control" value="${userChangeDto.remark}"></textarea>
                </div>

                <div class="modal-footer">
                    <div class="col-md-12 text-center" style="margin-top: 8px; display: inline-block">
                        <button type="button" class="btn btn-default"
                                data-dismiss="modal">关闭
                        </button>
                        <sec:authorize access="hasAuthority('RES_TRAIN_USER_INFO_CHANGE_AUTHORITY_3')" >
                            <%--修改用户信息权限--%>
                            <button type="button" id="btnCheck" class="btn btn-primary">
                                确认
                            </button>
                        </sec:authorize>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="id" value="${userChangeDto.id}">
    <input type="hidden" id="userId" value="${userChangeDto.userId}">
    <input type="hidden" id="realName" value="${userChangeDto.realName}">
    <input type="hidden" id="personType" value="${userChangeDto.personType}">
    <input type="hidden" id="userPhone" value="${userChangeDto.phone}">
    <input type="hidden" id="companyCode" value="${userChangeDto.companyCode}">
    <input type="hidden" id="companyName" value="${userChangeDto.companyName}">
    <input type="hidden" id="belongCommission" value="${userChangeDto.belongCommission}">
    <input type="hidden" id="orgName" value="${userChangeDto.orgName}">
    <input type="hidden" id="jobName" value="${userChangeDto.jobName}">
    <input type="hidden" id="post" value="${userChangeDto.post}">
    <input type="hidden" id="mail" value="${userChangeDto.mail}">
    <input type="hidden" id="mailingAddress" value="${userChangeDto.mailingAddress}">
    <input type="hidden" id="postalCode" value="${userChangeDto.postalCode}">
    <input type="hidden" id="attUrl" value="${userChangeDto.attUrl}">
    <input type="hidden" id="attName" value="${userChangeDto.attName}">
    <input type="hidden" id="businessType" value="${userChangeDto.businessType}">
    <input type="hidden" id="status" value="${userChangeDto.status}">
</div>
</body>
</html>


