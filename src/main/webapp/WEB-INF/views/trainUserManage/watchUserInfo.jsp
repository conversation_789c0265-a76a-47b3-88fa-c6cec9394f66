<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>

    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/viewer.js"></script>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/viewer.css">
    <e:base />
    <e:js />
</head>
<style>
    .row{
        margin: 0;
    }
    .startingLabel{
        border-radius:2px;
        color:#14BCF5;
        background: #E8F8FE;
        border: 1px solid #D0F2FD;
        font-size: 12px;
        width: 45px;
        float: right;
        padding: 0 3px;
        display: inline-block;
        text-align: center;
    }
    .finishLabel{
        border: 1px solid #d1d1cc;
        border-radius:2px;
        background-color:#F5F5F5;
        color:#999999;
        font-size: 12px;
        width: 45px;
        padding: 0 2px;
        float: right;
        display: inline-block;
        text-align: center;
    }
    .notReviewedLabel{
        color: red;
    }
</style>
<script>
    var checkStatusList ='${checkStatusList}'
</script>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="schUserInForm" modelAttribute="schUserInfoDto" cssClass="form-horizontal">
            <form:hidden path="password"></form:hidden>
            <form:hidden  path="historyData" value="${historyData}"/>
            <form:hidden  path="region" value="${schUserInfoDto.region}"/>
            <form:hidden  path="watchType" value="${schUserInfoDto.watchType}"/>
            <c:if test="${schUserInfoDto.id!=null}">
                <div class="row">
                    <div class="col-md-12">
                        <c:if test="${schUserInfoDto.checkStateStr=='未通过'}">
                            <label class="col-md-12 no-padding notReviewedLabel">当前用户<span style="font-size: 16px;font-weight: 600">${schUserInfoDto.checkStateStr}</span></label>
                        </c:if>
                        <c:if test="${schUserInfoDto.checkStateStr!='未通过'}">
                             <label class="col-md-12 no-padding control-label">当前用户<span style="font-size: 16px;font-weight: 600">${schUserInfoDto.checkStateStr}</span></label>
                        </c:if>
                    </div>
                </div>
            </c:if>
            <div class="row" style="padding-bottom: 5px">
                <div class="col-md-3" >
                    <label>来源:</label>
                    <label>${schUserInfoDto.userTypeStr}</label>
                </div>
            </div>
            <div class="row" style="padding-bottom: 5px">
                <div class="col-md-3">
                    <label>姓名:</label>
                    <label>${schUserInfoDto.realName}</label>
                </div>
                <div class="col-md-3">
                    <label>手机:</label>
                    <label>${schUserInfoDto.phone}</label>
                </div>
                <c:if test="${schUserInfoDto.idNumber != '' && schUserInfoDto.idNumber != null }">
                    <div class="col-md-3">
                        <label>身份证号:</label>
                        <label>${schUserInfoDto.idNumber}</label>
                    </div>
                </c:if>
            </div>
            <div class="row" style="padding-bottom: 5px">
                <div class="col-md-3 companyCodeDiv">
                    <label>辖区:</label>
                    <label>${schUserInfoDto.belongCommission}</label>
                </div>
                <div class="col-md-3">
                    <label  id="postStrLabel">职务:</label>
                    <label>${schUserInfoDto.postStr}</label>
                    <c:if test="${schUserInfoDto.jobName != '' && schUserInfoDto.jobName != null}">
                        <label>:${schUserInfoDto.jobName}</label>
                    </c:if>
                </div>
            </div>
            <div class="row" style="padding-bottom: 5px">
                <div class="col-md-3 companyCodeDiv">
                    <label>股票代码:</label>
                    <label>${schUserInfoDto.companyCode}</label>
                </div>
                <div class="col-md-3">
                    <label>公司名称:</label>
                    <label>${schUserInfoDto.companyName}</label>
                </div>
            </div>
            <c:forEach var="item" items="${schUserInfoDto.partTimeCompanyList}" varStatus="status">
                <div class="row removeDiv">
                    <div class="col-md-3 companyCodeDiv"  style="background-color: #EAEEF2">
                        <span>兼职股票代码:</span>
                        <span >${item.companyCode}</span>
                    </div>
                    <div class="col-md-3" style="background-color: #EAEEF2">
                        <span  id="partTimecompanyNameLabel">兼职公司名称:</span>
                        <span>${item.companyName}</span>
                    </div>
                    <div class="col-md-3" style="background-color: #EAEEF2">
                        <span  id="partTimePostStrLabel">兼职职务:</span>
                        <span>${item.postStr}</span>
                    </div>
                </div>
            </c:forEach>


            <div class="col-md-12 stuDiv no-padding" style="margin-top: 10px">
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-left" style="width: 80px;">工作证明:</label>
                    <span style="color: red;margin-left: 20px">（注：双击可查看工作证明图片）</span>
                </div>

                <div class="col-md-12">
                    <c:forEach var="item" items="${schUserInfoDto.picList}" varStatus="status">
                        <div id="imgId" class="ImgPr" class="col-md-1" style="width: 100%;margin-left: 40px" onclick="imgeClick(${status.index})">
                            <img id="ImgPr"  src="${item}"  style="width: 300px;height: 300px;position: relative;cursor: pointer"/>
                        </div>
                    </c:forEach>

                </div>
            </div>
            <div class="col-md-12 no-padding examineDiv" style="margin-top: 20px">
                <div class="col-md-12 text-center" >
                    <c:if test="${auditBtn  != '1'}">
<%--                    <sec:authorize access="hasAuthority('RES_TRAIN_USER_MANAGER_SAVEINFO_AUTHORITY_3')" >--%>
                        <%--用户管理保存用户信息权限--%>
                            <input id="Button1" type="button" value="审核" class="btn btn-primary"/>
<%--                    </sec:authorize>--%>
                    </c:if>
                    <span id="btnClose" class="btn btn-default">关闭</span>
                </div>
            </div>
    </form:form>
    </div>
    <div class="modal fade" id="myModal"  >
        <div class="modal-dialog">
            <div class="modal-content">

<%--            <sec:authorize access="hasAuthority('RES_TRAIN_USER_MANAGER_SAVEINFO_AUTHORITY_3')" >--%>
                <%--用户管理保存用户信息权限--%>
                <div class="modal-header">
                    <button type="button" class="close"  >
                    </button>
                    <h4 class="modal-title" id="myModalLabel">
                        审核
                    </h4>
                </div>
<%--            </sec:authorize>--%>
                <div class="modal-body">
                    <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">审核结果</label>
                    <div id="selectCheck" class="col-md-10 no-padding" style="width: 400px;margin-bottom: 10px;"></div>
                    <div id="remarkDiv" style="visibility: hidden">
                    <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;">备注</label>
                    <textarea id="remark" type="text"  style="width: 400px;" class="form-control" value="${schUserInfoDto.remark}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal">关闭
                    </button>
                    <button type="button" id="btnCheck" class="btn btn-primary">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div>
    </div>
    <input type="hidden" id="id" value="${schUserInfoDto.id}">
    <input type="hidden" id="personType1" value="${schUserInfoDto.personType}">
    <input type="hidden" id="userPhone" value="${schUserInfoDto.phone}">
    <input type="hidden" id="mail" value="${schUserInfoDto.mail}">
    <input type="hidden" id="companyCode" value="${schUserInfoDto.companyCode}">
    <input type="hidden" id="companyName" value="${schUserInfoDto.companyName}">
    <input type="hidden" id="realName" value="${schUserInfoDto.realName}">
</div>
</body>
</html>
