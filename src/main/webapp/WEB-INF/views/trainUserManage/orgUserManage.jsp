
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="capcoTrainMemberUnitDto" id="queryForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">股票代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入公司代码" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">公司全称</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入公司全称" class="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">公司简称</label>
                    <div class="col-md-3">
                        <form:input path="companyShortName" placeholder="请输入公司简称" class="form-control" autocomplete="off"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <span id="btnClear" class="btn btn-default">清空全部</span>
                    <sec:authorize access="hasAuthority('RES_ORG_USER_MANAGE_LIST_AUTHORITY_3')">
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_TRAIN_USER_REFRESHMEMBER_AUTHORITY_3')">
                        <span id="btnRefresh" class="btn btn-primary">同步</span>
                    </sec:authorize>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/trainUserManage/getOrgInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false"
                              cssClass="text-center" cssStyle="width:5%" />
                <e:gridColumn label="股票代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:15%" />
                <e:gridColumn label="公司全称" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="公司简称" displayColumn="companyShortName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="所属辖区" displayColumn="region" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否为会员单位" displayColumn="memberFlag" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="会员性质" renderColumn="renderMemberProperty" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />

            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
