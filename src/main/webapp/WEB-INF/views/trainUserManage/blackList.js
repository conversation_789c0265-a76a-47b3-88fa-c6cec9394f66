
$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();

        search();

    });


    $('#blackListAdd').click(function () {
        blackListAdd();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/blackList/blackListQuery",
        $("#queryForm").formSerialize());
}
function blackListAdd() {
    popWin('新增用户信息', '/blackList/blackListAddInit', '', '600px', '300px', function () {
        ajaxTableReload("tableAll",false);
    },'',function () {
        ajaxTableReload("tableAll",false);
    });
}

function columnOperation(data, type, row, meta) {
    let del = false;
    var str = ''

    if (document.getElementById("blackListDelAuth")) {
        del = true;
    }

    if (del) {
        str += '<i class="icon icon-trash" title="删除" onclick="delPhone(\'' + data.id + '\')"></i>';
    }

    if(!str) {
        str += "--"
    }
    return str;
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
function delPhone(id) {
    ajaxData("/blackList/blackListDel",{id:id},function () {
        popMsg("删除成功！")
        ajaxTableReload("tableAll",false)
    })
}


