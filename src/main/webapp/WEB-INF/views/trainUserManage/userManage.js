//@ sourceURL=userManage.js

var SchUserInfoList = [];
$(document).ready(function () {
    // 下拉初始化
	tSelectInit();
	selectInit()
	$('#btnQuery').click(function () {
		search();
	});

	$("#btnCheck").bind("click", function () {
		checkFirm();
	});
	//统计导出
	$("#exportQuery").bind("click", function() {
		exportStatisticsTableData();
	});

    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
		$('input[name="post"]').val("");
		$('input[name="checkStatus"]').val("");
		$('#post').val("");
		$('#checkStatus').val("");
		$('#phone').val("");
		$('#realName').val("");
		$('#companyCode').val("");
		$('#companyName').val("");
		search();
    });
	$('#allAudit').click(function () {
		if (SchUserInfoList.length == 0){
			popMsg("请先勾选需要审核的用户");
		}else{
			$("#checkState").val(null)
			$('#remark').val(null)
			$("#remarkDiv").css("visibility", "hidden");
			$('#myModal').modal('show');
		}
	});

	$("#exportTableBtn").bind("click", function() {
		exportTableData();
	});

	$('#addUser').click(function () {
		addUser();
	});
});
function UserListClear() {
	SchUserInfoList = [];
}
function checkFirm() {
	let checkState = $("#checkState").val()
	let remark = $('#remark').val()
	let param = {
		idList : SchUserInfoList,
		checkState: checkState,
		remark: remark
	};
	var jsonText = JSON.stringify(param)
	ajaxData("/trainUserManage/batchApprovals", jsonText, function (res) {
		if (res === true) {
			popMsg("批量审核成功")
			$('#myModal').modal('hide');
			search();
			UserListClear();
		}else {
			parent.popAlert("批量审核失败！")
		}
	},"application/json;charset=UTF-8");

}

function selState(value) {
	if (value == '1') {
		$("#remarkDiv").css("visibility", "hidden");
		$("#remark").val("")
	} else if (value == '2'){
		$("#remarkDiv").css("visibility", "visible");
		$("#remark").val("用户您好！请据实正确填写用户注册信息。工作证明是指您在所属公司的名片、工作证、聘书、盖章版在职证明、公告信息截图等能证明您与所属公司关联关系的资料。")
	}else{
		$("#remarkDiv").css("visibility", "hidden");
	}
}
function selectInit() {
	var str='<select onchange="selState(this.value)" id="checkState" name class="form-control">'
	str+='<option value="">请选择</option>'
	var list=JSON.parse(checkStatusList)
	for (var i=0;i<list.length;i++){
		if (list[i].codeValue!="0"){
			var codeValue= list[i].codeValue
			var codeName= list[i].codeName
			str+='<option value="'+codeValue+'">'+codeName+'</option>'
		}
	}
	str+='</select>'
	$('#selectCheck').append(str)
}



function search() {
	ajaxTableQuery("tableAll", "/trainUserManage/getUserInfoList",
		$("#queryForm").formSerialize()+'&userAll=1');
}


function addUser() {
	parent.popWin('新增用户信息', '/trainUserManage/addUserInfoInit', '', '98%', '98%', callBackAddUser,'',callBackAddUser);
}

function watchUserInfo(id,realName) {
	UserListClear();
	var param = {
		id : id
	};
	parent.popWin('查看'+realName+'信息', '/trainUserManage/addUserInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);
}

function editUserInfo(id) {
	UserListClear();
	var param = {
		id : id,
		editType :1
	};
	parent.popWin('修改用户信息', '/trainUserManage/addUserInfoInit', param, '70%', '80%', callBackAddUser,'',callBackAddUser);

}

function exportStatisticsTableData(){
	let realName = $('#realName').val();
	let companyCode = $('#companyCode').val();
	let companyName = $('#companyName').val();
	let phone = $('#phone').val();
	let post=$('input[name="post"]').val();
	let checkStatus=$('input[name="checkStatus"]').val();
	window.open(contextPath + "/trainUserManage/exportUser?realName="+realName+"&companyCode="+companyCode+"&companyName="+companyName+"&phone="+phone+"&post="+post+"&checkStatus="+checkStatus);
}

function editLock(id,status) {
	var str='确认锁定该用户?'
	var lockState='1'
	if (status=='1'){
		str='确认取消锁定该用户?'
		lockState='0'
	}
	parent.popConfirm(str, function () {
		var param ={
			id:id,
			lockState:lockState
		};
		ajaxData("/trainUserManage/userInfoSave", param, callBackAddUser);
	});

}

function callBackAddUser() {
    ajaxTableReload("tableAll",false);
}

//下拉初始化
function tSelectInit() {
	var teaSelectOptions = {
		id: 'codeValue',
		name: 'codeName',
		value: 'codeValue',
		grade: 1,
		resultType: 'all',
		style: {},
		customCallBack: tSelectCustomCallBack,
		submitCallBack: tSelectSubmitCallBack
	};
	$('#post').tselectInit(null, teaSelectOptions);
	$('#checkStatus').tselectInit(null, teaSelectOptions);

	// 初始化课程选择下拉框
	initCourseSelect();
}

// 初始化课程选择下拉框
function initCourseSelect() {
	var courseSelectOptions = {
		id: 'businessId',
		name: 'businessName',
		value: 'businessId',
		grade: 1,
		resultType: 'all',
		inputSearch: true,
		style: {},
		customCallBack: tSelectCustomCallBack,
		submitCallBack: tSelectSubmitCallBack
	};
	$('#courseIds').tselectInit(null, courseSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
	$('input[name="' + t.attr('id') + '"]').val(d.value);
}
function columnOperation(data, type, row, meta) {
	let str = '';
		str += '<a href="#" onclick="watchUserInfo(\'' + data.id + '\',\''+ data.realName + '\')" style="margin-right:4px;">审核</a>';
		str += ' | <a href="#" onclick="delUserInfo(\'' + data.id + '\')" style="margin-right:4px;">删除</a>';
		str += ' | <a href="#" onclick="initPassword(\'' + data.id + '\')" style="margin-right:4px;">重置密码</a>';
		str += ' | <a href="#" onclick="initPhone(\'' + data.id + '\')" style="margin-right:4px;">修改手机号</a>';
	if(!str) {
		str += "--"
	}
	return str;
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
	return meta.row + 1;
}
function rcIndex(data, type, row, meta) {
	var tempWrite
	if (data.checkState==='通过'){
		tempWrite = '<input type="checkbox" onclick="clickBox(this)" name="checkRow" value="' + data.id + '" disabled style="opacity: 0.5; cursor: not-allowed;"/>';
	}else{
		tempWrite = '<input type="checkbox" onclick="clickBox(this)" name="checkRow" value="' + data.id + '"/>';
	}
	return tempWrite;
}
function clickBox(obj) {
	debugger
	var feedId = $(obj).parents("tr").eq(0).find("input[type='checkbox']").val();
	if ($(obj).prop("checked") && !$(obj).prop("disabled")) {
		SchUserInfoList.push(feedId)
	} else {
		document.getElementById("allCheck").checked = false;
		for (var i = 0; i < SchUserInfoList.length; i++) {
			if (feedId == SchUserInfoList[i]) {
				SchUserInfoList.splice(i, 1);
				break;
			}
		}
	}
	console.log(SchUserInfoList);
}

function checkAll() {
	debugger
	if ($("#allCheck").prop("checked")) {
		$("#tableAll tbody tr").find("td:first").find("input[type='checkbox']").prop('checked', true);
		SchUserInfoList = [];
		var rows = $("#tableAll tbody tr");
		for (var i = 0; i < rows.length; i++) {
			var checkbox = rows.eq(i).find("input[type='checkbox']");
			if (checkbox.prop("checked") && !checkbox.prop("disabled")) {
				SchUserInfoList.push(checkbox.val())
			}
		}
		console.log(SchUserInfoList)
	} else {
		$("#tableAll tbody tr").find("td:first").find("input[type='checkbox']").prop('checked', false);
		SchUserInfoList = [];
	}
	console.log(SchUserInfoList);
}
function delUserInfo(id) {
	parent.popConfirm("确认删除?", function () {
		var param ={
			id:id,
			status:'D'
		};
		ajaxData("/trainUserManage/delUserInfo", param, callBackAddUser);
	});
}

function initPassword(id) {
	parent.popConfirm("确认重置密码?", function () {
		var param ={
			id:id
		};
		ajaxData("/trainUserManage/initPassword", param, callBackAddUser);
	});
}

function initPhone(id) {
	var param = {
		id : id
	};
	parent.popWin('修改手机号', '/trainUserManage/changePhoneInit', param, '50%', '50%', callBackAddUser,'',callBackAddUser);

}

function colUserInfo(id) {
	var param = {
		id : id
	};
	parent.popWin('用户收藏信息', '/trainUserManage/collectInfoInit', param, '98%', '98%', callBackAddUser,'',callBackAddUser);

}

function exportTableData(){
	window.open(contextPath + "/trainUserManage/export?" + $("#queryForm").serialize());
}
