<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>易董 云端管理平台</title>
  <e:js />
</head>
<body>
<div class="panel">
  <ul id="myTab" class="nav nav-tabs">
    <sec:authorize access="hasAuthority('RES_ORDER_MANAGE_AUTHORITY_3')" >
      <li class="active"><a href="#tab1" style="font-weight: 600" data-toggle="tab" onclick="toPage('orderManage')">订单查询</a></li>
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_ORDER_DETAILS_INIT_AUTHORITY_3')" >
      <li><a href="#tab2" style="font-weight: 600" data-toggle="tab" onclick="toPage('orderDetail')">订单详情</a></li>
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_COURSE_PLAYBACK_RECORD_INIT_AUTHORITY_3')" >
      <li><a href="#tab3" style="font-weight: 600" data-toggle="tab" onclick="toPage('courseRecord')">课程播放查询</a></li>
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_PLAYBACK_RECORD_INIT_AUTHORITY_3')" >
      <li><a href="#tab4" data-toggle="tab" style="font-weight: 600" onclick="toPage('playBack')">学习记录查询</a></li>
    </sec:authorize>
    <sec:authorize access="hasAuthority('RES_USER_LOGIN_LOG_INIT_AUTHORITY_3')" >
      <li><a href="#tab5" style="font-weight: 600" data-toggle="tab" onclick="toPage('loginLog')">登录查询</a></li>
    </sec:authorize>
  </ul>
  <div class="panel-body">
    <div class="tab-content">

      <sec:authorize access="hasAuthority('RES_ORDER_MANAGE_AUTHORITY_3')" >
        <div class="tab-pane fade in active" id="tab1">
          <iframe id="orderManage" scrolling="no" style="width:100%;border:0px;"></iframe>
        </div>
      </sec:authorize>

      <sec:authorize access="hasAuthority('RES_ORDER_DETAILS_INIT_AUTHORITY_3')" >
        <div class="tab-pane fade in" id="tab2">
          <iframe id="orderDetail" scrolling="no" style="width:100%;border:0px;"></iframe>
        </div>
      </sec:authorize>

      <sec:authorize access="hasAuthority('RES_COURSE_PLAYBACK_RECORD_INIT_AUTHORITY_3')" >
        <div class="tab-pane fade in" id="tab3">
          <iframe id="courseRecord" scrolling="no" style="width:100%;border:0px;"></iframe>
        </div>
      </sec:authorize>

      <sec:authorize access="hasAuthority('RES_TRAIN_USER_MANAGE_INIT_AUTHORITY_3')" >
        <div class="tab-pane fade in" id="tab4">
          <iframe id="playBack" scrolling="no" style="width:100%;border:0px;"></iframe>
        </div>
      </sec:authorize>

      <sec:authorize access="hasAuthority('RES_USER_LOGIN_LOG_INIT_AUTHORITY_3')" >
        <div class="tab-pane fade in" id="tab5">
          <iframe id="loginLog" scrolling="no" style="width:100%;border:0px;"></iframe>
        </div>
      </sec:authorize>
    </div>
  </div>
</div>

</body>
</html>
