
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="blackListDto" id="queryForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">手机号</label>
                    <div class="col-md-3">
                        <form:input path="number" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <span id="btnClear" class="btn btn-default">清空全部</span>

                    <sec:authorize access="hasAuthority('RES_TRAIN_BLACK_LIST_QUERY_AUTHORITY_3')" >
                        <%--短信黑名单查询权限--%>
                        <span id="blackListAdd" class="btn btn-default">添加</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_TRAIN_BLACK_LIST_ADD_INIT_AUTHORITY_3')" >
                        <%--短信黑名单添加页面初始化权限--%>
                        <span id="btnQuery" class="btn btn-primary">查询</span
                    </sec:authorize>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_TRAIN_BLACK_LIST_DEL_AUTHORITY_3')" >
            <%--短信黑名单删除权限--%>
            <input type="hidden" id="blackListDelAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/blackList/blackListQuery" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false"
                              cssClass="text-center" cssStyle="width:5%" />
                <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="电话号" displayColumn="number" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="公司名" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="公司代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="岗位" displayColumn="jobName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="职务" displayColumn="post" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:10%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
