<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
</head>
<style>
    .row{
        margin: 0;
    }
</style>
<script>
    var checkStatusList ='${checkStatusList}'
</script>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="watchUserInfoEdit" modelAttribute="schUserInfoDto" cssClass="form-horizontal">
            <form:hidden path="id" value="${schUserInfoDto.id}"/>
<%--            <label class="col-md-2 no-padding control-label text-right" style="width: 80px;">姓名:</label>--%>
<%--            <label class="col-md-9 control-label">${schUserInfoDto.realName}</label>--%>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>姓名:</label>
                <div class="col-md-10" style="width: 80%;">
                    <form:input path="realName" cssClass="form-control"  placeholder="请输入姓名" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>


            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>用户类型:</label>
                <div class="col-md-10" style="width: 80%;">
                    <input id="personType" type="text" class="t-select" json-data='${personTypeList}' selected-ids="${schUserInfoDto.personType}" />
                    <input name="personType" type="hidden" placeholder="请输入用户类型" value='${schUserInfoDto.personType}'/>
                </div>
            </div>
            <div class="col-md-12" style="margin-top: 10px;display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;" id="companyNameLabel">公司名称:</label>
                <div class="col-md-10" style="width: 80%;">
                    <form:input path="companyName" cssClass="form-control"  placeholder="请输入公司名称" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">证券代码:</label>
                <div class="col-md-10" style="width: 80%;">
                    <form:input path="companyCode" cssClass="form-control"  placeholder="请输入证券代码" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">所在辖区:</label>
                <div class="col-md-10" style="width: 80%;display: inline-block">
                    <input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}'  selected-ids="${schUserInfoDto.belongCommissionValue}" />
                    <input name="belongCommission" type="hidden" placeholder="请输入所在辖区" value="${schUserInfoDto.belongCommissionValue}"/>
                </div>
            </div>
            <div class="col-md-12"  style="margin-top: 10px;display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;" id="orgNameLabel">部门:</label>
                <div class="col-md-10" style="width: 80%;">
                    <form:input path="orgName" cssClass="form-control"  placeholder="请输入部门" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">职务:</label>
                <div class="col-md-10" style="width: 80%;">
                    <input id="post" type="text" class="t-select" json-data='${postList}' selected-ids="${schUserInfoDto.post}" />
                    <input name="post" type="hidden" placeholder="请输入用户类型" value='${schUserInfoDto.post}'/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;" id="jobNameLabel">岗位:</label>
                <div class="col-md-10" style="width: 80%;">
                    <form:input path="jobName" cssClass="form-control"  placeholder="请输入岗位" maxlength="45"
                                autocomplete="off"/>
                </div>
            </div>

            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">人物标签:</label>
                <div class="col-md-10" style="width: 80%;">
                    <input id="personLabel" type="text" class="t-select" json-data='${personLabelList}' selected-ids="${schUserInfoDto.personLabel}" placeholder="请选择人物标签" />
                    <input name="personLabel" type="hidden" value='${schUserInfoDto.personLabel}'/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">其他证券代码:</label>
                <div class="col-md-10" style="width: 80%;">
                    <form:input path="otherCompanyCode" cssClass="form-control"  placeholder="请输入兼任其它上市公司的证券代码" maxlength="45" autocomplete="off"/>
                </div>
            </div>
            </form:form>
        <div class="col-md-12 no-padding" style="margin-top: 20px">
            <div class="col-md-12 text-center" >
                <sec:authorize access="hasAuthority('RES_TRAIN_USER_MANAGER_SAVEINFO_UPDATE_AUTHORITY_3')" >
                    <%--更新用户信息权限--%>
                    <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存" />
                </sec:authorize>
                <span id="btnClose" class="btn btn-default">关闭</span>
            </div>
        </div>
    </div>
</div>
</body>
</html>
