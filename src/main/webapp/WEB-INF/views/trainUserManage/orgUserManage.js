$(document).ready(function () {
    completeInit();

    btnInit();

    dataRangePickInit();

    $("#btnQuery").bind("click", function () {
        ajaxTableQuery("tableAll", "/trainUserManage/getOrgInfoList",
            $("#queryForm").formSerialize());
    })

    $("#btnRefresh").bind("click", function () {
        ajaxData("/trainUserManage/refreshMember", "", function (res) {
            //删除成功
            parent.popMsg("同步成功");
            editCallback();
        })
    })
});

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function completeInit() {
    $("#companyName").autocomplete(contextPath + "/trainUserManage/companyQuery", {
        delay: 0,
        max: 1000,
        cacheLength: 0,
        extraParams: {
            companyStr: function () {
                return $("#companyName").val();
            }
        },
        formatItem: function (row) {
            return row.zhName + " (" + row.companyCode + ")";
        },
        formatResult: function (row) {
            return row.zhName + " (" + row.companyCode + ")";
        }
    }).result(function (event, data, formatted) {
        $("#companyId").val(data.companyId);
    })
}

function renderTotalNum(data, type, row, meta) {
    var giveNum = data.giveNum || 0;
    var buyNum = data.buyNum || 0;
    return parseInt(buyNum) + parseInt(giveNum);
}

function columnOperation(data, type, row, meta) {
    var str = '';
    str = '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑机构信息" onclick="editOrgInfo(\'' + data.orgId + '\')"></i>'
        + '<i class="fa fa-trash" style="cursor: pointer;margin: 0 5px;" title="删除机构信息" onclick="delOrgInfo(\'' + data.orgId + '\')"></i>';
    return str;
}

function editOrgInfo(orgId) {
    parent.popWin("编辑机构信息", "/trainUserManage/editOrgInfo", {orgId: orgId}, "100%", "100%", editCallback, null, editCallback);
}

function editCallback() {
    ajaxTableReload('tableAll', false);
}

function btnInit() {
    $("#addUser").bind("click", function () {
        parent.popWin("编辑机构信息", "/trainUserManage/editOrgInfo", {orgId: ''}, "100%", "100%", editCallback, null, editCallback);
    });

    $("#btnClear").bind("click", function () {
        clearAll();
        ajaxTableQuery("tableAll", "/trainUserManage/getOrgInfoList",
            $("#queryForm").formSerialize());
    });

    $("#companyName").bind("change", function () {
        $("#companyId").val('');
    })


}

function dataRangePickInit() {
    dataRangePickerInit($('#startTime'), null, null, function () {
    }, function () {
    });
    dataRangePickerInit($('#endTime'), null, null, function () {
    }, function () {
    });
}

function delOrgInfo(orgId) {
    var confirmText = "是否删除该机构信息？";
    parent.popConfirm(confirmText, function () {
        ajaxData("/trainUserManage/delOrgInfo", {orgId}, function (res) {
            //删除成功
            parent.popMsg("删除成功");
            editCallback();
        });
    })

}

function clearAll() {
    $('#queryForm')[0].reset();
    $("#companyId").val("");
}

function rcIndex(data, type, row, meta) {
    return meta.row + 1;
}
function renderMemberProperty(data, type, row, meta) {
    if(data.memberPropertyStr){
        return data.memberPropertyStr
    }
    return '--';
}

