//@ sourceURL=changePassword.js
$(document).ready(function() {
	// 设定表单校验规则
	$("#userForm").validate({
		rules : {
			/*
			 * "passwordOld" : { required : true, // 后台校验 remote: { url:
			 * contextPath + "/userManager/passwordRemoteValidate", type:
			 * "post", data: { name: function() { return $("#name").val(); } } },
			 * maxlength: 40 },
			 */
			"password" : {
				required : true,
				maxlength : 40
			}
		},
		/*
		 * messages : { "passwordOld" : { remote: "旧密码输入错误！" } },
		 */
		tooltip_options : {
			"_all_" : {
				trigger : 'focus'
			}
		}
	});
	// 确定
	$("#btnConfirm").bind("click", function() {
		if ($("#userForm").valid()) {
			$("#password").val($.md5($("#password").val()));
			ajaxSubmitForm("/trainUserManage/changePasswordSave", null, callBack, 0);
		}
	});
	// 关闭
	$("#btnClose").bind("click", function() {
		closeWin();
	});
});
//保存成功
function callBack(data) {
	var jsonObj = {
			result : [ {
				name : "",
				testDate : ""
			}]
		};
	//closeWin("保存成功");
	closeWinCallBack(jsonObj);
	//popMsg("保存成功");
}