var dizhi = {
    "orderManage": contextPath + "/orderManage/orderManageInit",
    "orderDetail": contextPath + "/orderDetails/orderDetailsInit",
    "courseRecord": contextPath + "/coursePlaybackRecord/coursePlaybackRecordInit",
    "playBack": contextPath + "/playbackRecord/playbackRecordInit",
    "loginLog": contextPath + "/userLoginLog/userLoginLogInit"

};

var reHeightTimer;
var reHeightFrame;
var reHeightTag;

$(document).ready(function () {
    $("#orderManage").attr('src',dizhi['orderManage']);
    reHeightFrame = '#orderManage';
    //动态调整高度
    reHeightInteval();
});

// 更换tab页
function toPage(target){
    $("#"+target).attr('src',dizhi[target]);
    reHeightTimer = false;
    reHeightFrame = '#'+target;
    // 动态调整高度
    reHeightInteval();
}
//调整iframe高度定时器
function reHeightInteval() {
    if (!reHeightTimer) {
        reHeightTag = true;
        reHeightTimer = setInterval(function() {
            if (reHeightTag) {
                adjust_R_e_g_u_l_atoryHeight(reHeightFrame);
            } else {
                closeReHeightInteval();
            }
        }, 500);
    }
}
function adjust_R_e_g_u_l_atoryHeight(frameLabel) {
    if (Object.prototype.toString.call(frameLabel) === "[object String]") {
        frameLabel = '#' + frameLabel.replace(/\#/g, "");
        var frame = $(frameLabel);
        if (frame.length > 0) {
            if (frame[0].contentWindow.$) {
                var frameHeight = frame[0].contentWindow.$("body").height() + 30;
                if (frameHeight < 500) {
                    frame.height(500);
                } else {
                    frame.height(frameHeight);
                }
            }
        }
    }
}
//关闭调整iframe高度定时器
function closeReHeightInteval() {
    clearInterval(reHeightTimer);
    reHeightTimer = false;
}
