<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="schUserInfoDto" id="queryForm" >
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">用户类型</label>
                    <div class="col-md-3">
                        <input id="personType" type="text" class="t-select" json-data='${personTypeList}' placeholder="请输入用户类型" />
                        <input name="personType" type="hidden"  />
                    </div>
                    <label class="col-md-1 control-label">证券代码</label>
                    <div class="col-md-3">
                        <form:input path="companyCode" placeholder="请输入证券代码" cssClass="form-control" autocomplete="off" />
                    </div>
                    <label class="col-md-1 control-label">公司名称</label>
                    <div class="col-md-3">
                        <form:input path="companyName" placeholder="请输入公司名称" cssClass="form-control" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="realName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">开始时间</label>
                    <div class="col-md-3">
                        <form:input type="date" path="startTime"  cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">结束时间</label>
                    <div class="col-md-3">
                        <form:input type="date" path="endTime"  cssClass="form-control" autocomplete="off"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right" >
                    <sec:authorize access="hasAuthority('RES_USER_LOGIN_LOG_INFOLIST_AUTHORITY_3')" >
                        <%--查询用户登录信息列表权限--%>
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                    </sec:authorize>
                    <sec:authorize access="hasAuthority('RES_USER_LOGIN_LOG_EXPORT_AUTHORITY_3')" >
                        <%--用户登录信息导出权限--%>
                        <span id="exportTableBtn" class="btn btn-primary">导出</span>
                    </sec:authorize>
                    <span id="btnClear" class="btn btn-default">清空</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/userLoginLog/getUserLoginLogList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="ID" displayColumn="id" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="证券代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="公司名称/学校" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="登录时间" displayColumn="loginTime" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
                <e:gridColumn label="登录来源" renderColumn="fromTypeFormat" orderable="false"
                              cssClass="text-center" cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
