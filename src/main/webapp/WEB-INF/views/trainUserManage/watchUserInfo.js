//@ sourceURL=watchUserInfo.js
$(document).ready(function () {

    selectInit()
    $('#Button1').click(function () {
        $("#checkState").val(null)
        $('#remark').val(null)
        $("#remarkDiv").css("visibility", "hidden");
        $('#myModal').modal('show');
    });
    $("#btnCheck").bind("click", function () {
        checkFirm();
    });
    $("#btnBelongCommission").bind("click", function () {
        var param={
            id:$('#id').val(),
            region:$('#region').val()
        }
        ajaxData("/trainUserManage/updateBelongCommission", param, function (res) {
            parent.popMsg("更改成功")
            $('#belongCommissionStr').html(res)
            $('.idBelongCommission').hide()
        });
    });
    $("#btnClose").bind("click", function () {
        closeWinCallBack();
    });


    tSelectInit()

    pageEdit()

debugger
    if($('#watchType').val() == '1'){
        $(".examineDiv").css("display","none")
    }
});


function selectInit() {
    var str='<select onchange="selState(this.value)" id="checkState" name class="form-control">'
    str+='<option value="">请选择</option>'
    var list=JSON.parse(checkStatusList)
    for (var i=0;i<list.length;i++){
        var codeValue= list[i].codeValue
        var codeName= list[i].codeName
        str+='<option value="'+codeValue+'">'+codeName+'</option>'
    }
    str+='</select>'
    $('#selectCheck').append(str)
}

function selState(value) {
    if (value == '1') {
        $("#remarkDiv").css("visibility", "hidden");
        $("#remark").val("")
    } else if (value == '2'){
        $("#remarkDiv").css("visibility", "visible");
        $("#remark").val("用户您好！请据实正确填写用户注册信息。工作证明是指您在所属公司的名片、工作证、聘书、盖章版在职证明、公告信息截图等能证明您与所属公司关联关系的资料。")
    }else{
        $("#remarkDiv").css("visibility", "hidden");
    }
}


function pageEdit() {
    $('.tooltip').remove()
    $('.error').removeClass("error")

    var personType = $('#personType1').val()
    var font = '<font color="#FF0000">*</font>'
    if($('#id').val()){
        font = ''
    }
    $('#companyNameLabel').html(font+"公司名称:")
    $('#orgNameLabel').html(font+"部门:")
    $('#jobNameLabel').html(font+"岗位:")
    $('#companyName').attr('placeholder','请输入公司名称')
    $('#orgName').attr('placeholder','请输入部门')
    $('#jobName').attr('placeholder','请输入岗位')
    // $('.companyCodeDiv').show();
    // $('.removeDiv').addClass('row')
    // if(personType=='002'||personType=='003'||personType=='004'||personType=='005'||personType=='006'||personType=='999'){
    //     $('.companyCodeDiv').hide();
    //     $('.removeDiv').removeClass('row')
    // }else
    if(personType!='000'&&personType!='001'){
        $('.typeHide').hide()
    }else {
        $('.typeHide').show()
    }
        if (personType=='007'){
        $('#companyNameLabel').html(font+"学校:")
        $('#orgNameLabel').html(font+"年级:")
        $('#jobNameLabel').html(font+"专业:")
        $('#companyName').attr('placeholder','请输入学校')
        $('#orgName').attr('placeholder','请输入年级')
        $('#jobName').attr('placeholder','请输入专业')
    }
}

function checkFirm() {
    let id = $('#id').val()
    let checkState = $("#checkState").val()
    let remark = $('#remark').val()
    let userPhone = $('#userPhone').val()
    let mail = $("#mail").val();
    let companyCode = $("#companyCode").val();
    let companyName = $("#companyName").val();
    let param = {
        id: id,
        checkState: checkState,
        remark: remark,
        phone: userPhone,
        mail: mail,
        companyCode: companyCode,
        companyName: companyName
    };
    ajaxData("/trainUserManage/userInfoSave", param, function (res) {
        if (res === '1') {
            closeWinCallBack()
        }else {
            parent.popAlert("手机号或邮箱已被使用！")
        }
    });
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        inputType: 'radio',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    // $('#checkState').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#post').tselectInit(null, teaSelectOptions);
    var id = $('#id').val()
    if(!id){
        $('#personType').tselectInit(null, teaSelectOptions);
    }
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
    if (t.attr('id')=='personType'){
        // $("#schUserInForm")[0].reset();
        $('#personType1').val(d.value)
        // personTypeSelectInit()
        pageEdit()
    }
}
//下拉初始化
function personTypeSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        inputType: 'radio',
        style: {},
        selectedIds:$('#personType1').val(),
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personType').tselectInit(null, teaSelectOptions);

}

function time() {
}



function imgeClick(index) {
    const viewer = new Viewer(document.querySelectorAll(".ImgPr")[index], {
        inline: false
    });
}
