//@ sourceURL=insertTeacherManagementInit.js
$(document).ready(function () {
    personTypeSelectInit()//用户类型下拉
    personLabelSelectInit()
    // 保存
    $("#btnSave").bind("click", function () {
        userInfoSave();
    });
    //关闭
    $("#btnClose").bind("click", function () {
        closeWinCallBack();
    });
});
//下拉初始化
function personTypeSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade:2,
        resultType: 'all',
        inputType: 'radio',
        style: {},
        selectedIds:$('#personType1').val(),
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#post').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);

}
function personLabelSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade:2,
        resultType: 'all',
        style: {},
        selectedIds:$('#personType1').val(),
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#personLabel').tselectInit(null, teaSelectOptions);

}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
    if (t.attr('id')=='personType'){
        // $("#schUserInForm")[0].reset();
        $('#personType1').val(d.value)
        // personTypeSelectInit()
        //pageEdit()
    }
}

function userInfoSave() {
    var personType = $('#personType1').val()
    if (!$("input[name='personType']").val()){
        parent.popMsg("请选择用户类型")
        return
    }
    if ($('#realName').val() == null || $('#realName').val() == "" ||  $('#realName').val().trim() == ""){
        parent.popMsg("姓名不能为空")
        return
    }
    if ($("#watchUserInfoEdit").valid()) {
        //密码加密
        $("#password").val($.md5('88888888'));
        if (personType=='000'||personType=='001'){
            if (!$("input[name='belongCommission']").val()){
                parent.popMsg("请选择所在辖区")
                return
            }
        }
        ajaxSubmitForm('/trainUserManage/userInfoUpdate', '', function (res) {
            if (res == '1') {
                popMsg("保存成功");
                closeWinCallBack();
            } else {
                popMsg("操作失败，请稍后再试");
            }
        })
    }
}


