//@ sourceURL=userChange.js
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    $('#btnQuery').click(function () {
        search();
    });
    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
        $('input[name="personType"]').val("");
        $('input[name="checkState"]').val("");
        $('#personType').val("")
        search();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/trainUserChange/getUserChangeInfoList",
        $("#queryForm").formSerialize());
}


function watchUserInfo(id) {
    var param = {
        id: id
    };
    parent.popWin('审核用户修改信息', '/trainUserChange/changeUserinfo', param, '90%', '90%', callBackAddUser, '', callBackAddUser);
}


function callBackAddUser() {
    ajaxTableReload("tableAll", false);
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#userType').tselectInit(null, teaSelectOptions);
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#checkState').tselectInit(null, teaSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function columnOperation(data, type, row, meta) {
    var str = ""
    let change = false;
    let del = false;

    if (document.getElementById("changeUserInfoAuth")) {
        change = true;
    }
    if (document.getElementById("delUserChangeAuth")) {
        del = true;
    }

    if (change) {
        str += '<i class="icon icon-eye-open iconColor" title="查看" onclick="watchUserInfo(\'' + data.id + '\')"></i>'
    }

    if (del) {
        str += '<i class="icon icon-trash" title="删除" onclick="delUserInfo(\'' + data.id + '\')"></i>';
    }

    if(!str) {
        str += "--"
    }
    return str;
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
//
function checkState(data, type, row, meta) {
    switch (data.status){
        case '0' : return "待审核"
        case '1' : return "审核通过"
        case '2' : return "审核不通过"
    }
}
//审核人
function checkPerson(data) {
    if (data.updateUser != null && data.updateUser != "") {
        return data.updateUser;
    } else {
        return "-"
    }
}

//审核时间
function checkTime(data) {
    if (data.updateTime != null && data.updateTime != "") {
        return data.updateTime;
    } else {
        return "-"
    }
}

function delUserInfo(id) {
    parent.popConfirm("确认删除?", function () {
        var param ={
            id:id,
        };
        ajaxData("/trainUserChange/delUserInfo", param, callBackAddUser);
    });
}


