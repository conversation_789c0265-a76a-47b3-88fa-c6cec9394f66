//@ sourceURL=changePassword.js
$(document).ready(function() {
	// 设定表单校验规则
	// 确定
	$("#btnConfirm").bind("click", function() {
		if ($("#phone").val() == '' || $("#phone").val() == null) {
			popMsg("请输入手机号");
			return;
		}
		var param ={
			id:$("#id").val(),
			phone:$("#phone").val()
		};
		ajaxData("/trainUserManage/changePhone", param, function (res) {
			if (res == 'success') {
				popMsg("手机号修改成功")
				callBack(res)
			}else {
				popMsg(res)
			}
		});
	});
	// 关闭
	$("#btnClose").bind("click", function() {
		closeWin();
	});
});
//保存成功
function callBack(data) {
	var jsonObj = {
			result : [ {
				name : "",
				testDate : ""
			}]
		};
	closeWinCallBack(jsonObj);
}