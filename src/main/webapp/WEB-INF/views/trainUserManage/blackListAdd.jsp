<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base />
    <e:js />
</head>
<body style="padding-top:0px;">
<div class="panel">
    <div class="panel-heading">
        <i class="icon icon-list-ul"></i> 添加黑名单
    </div>
    <div class="panel-body">
        <form:form modelAttribute="blackListDto" id="queryForm">
            <div class="form-group">
                <label class="col-md-1 control-label">黑名单电话号</label>
                <div class="col-md-3">
                    <form:input cssClass="form-control"  path="number" />
                </div>
                <form:hidden  cssClass="form-control"  path="type" value="0" />
            </div>
            <div class="form-group">
                <div class="col-md-offset-1 col-md-2" align="right">
                    <sec:authorize access="hasAuthority('RES_TRAIN_BLACK_LIST_ADD_AUTHORITY_3')" >
                        <%--短信黑名单添加权限--%>
                        <input type="button" id="btnConfirm" class="btn btn-primary" value="保存" />
                    </sec:authorize>
                    <input type="button" id="btnClose" class="btn btn-default" value="关闭" />
                </div>
            </div>
        </form:form>
    </div>
</div>

</body>
</html>
