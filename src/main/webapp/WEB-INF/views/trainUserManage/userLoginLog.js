//@ sourceURL=userManage.js
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
        $('input[name="personType"]').val("");
        $('input[name="checkStatus"]').val("");
        $('input[name="belongCommission"]').val("");
        $('#personType').val("")
        $('#belongCommission').val("")
        search();
    });

    $("#exportTableBtn").bind("click", function() {
        exportTableData();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/userLoginLog/getUserLoginLogList",
        $("#queryForm").formSerialize());
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    // $('#userType').tselectInit(null, teaSelectOptions);
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#checkStatus').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#personLabel').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function fromTypeFormat(data, type, row, meta) {
    if(data.fromType == "1"){
        return "h5"
    }else if(data.fromType == "0"){
        return "web"
    }else {
        return ""
    }
}

function exportTableData(){
    window.open(contextPath + "/userLoginLog/export?" + $("#queryForm").serialize());
}
