$(document).ready(function() {
    // 设定表单校验规则
    $("#queryForm").validate({
        rules : {
            /*
             * "passwordOld" : { required : true, // 后台校验 remote: { url:
             * contextPath + "/userManager/passwordRemoteValidate", type:
             * "post", data: { name: function() { return $("#name").val(); } } },
             * maxlength: 40 },
             */
            "number" : {
                required : true,
                maxlength : 11
            }
        },
        tooltip_options : {
            "_all_" : {
                trigger : 'focus'
            }
        }
    });
    // 确定
    $("#btnConfirm").bind("click", function() {
        if ($("#queryForm").valid()) {
            ajaxSubmitForm("/blackList/blackListAdd", null, function () {
                closeWinCallBack();
            }, 0);
        }
    });
    // 关闭
    $("#btnClose").bind("click", function() {
        closeWin();
    });
});