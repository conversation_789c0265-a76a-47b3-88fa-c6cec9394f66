let _ImgUploadUrl = contextPath + '/filetempupload';
$(document).ready(function () {
    //初始化背景图
    cardPicInit();
    //初始化发布状态
    releaseFlagInit();

    var userIds = $('#userIds').val()

    $("#packageName").text($("#packName").val())

    $("#membershipCardDto").validate({
        rules: {
            "cardName": {
                required: true,
                maxlength: 45,
            },
            "openStartTime": {
                required: true,
                maxlength: 45
            },
            "openEndTime": {
                required: true,
                maxlength: 45,
            },
            "validityTime": {
                required: true,
                maxlength: 45,
            },
            "cardDesc": {
                required: true,
                maxlength: 45,
            }
        },
    });
    // 取消
    $('#btnCancel').bind('click', function () {
        closeWinCallBack();
    });

    // 打包管理
    $('#btnPack').bind('click', function () {
        packageInit();
    });


    // 保存
    $("#btnSave").bind("click", function () {
        if(getValue($("#imageUrl").val()) === "") {
            popMsg("请上传课程图片");
            return;
        }
        if($("#openEndTime").val() < $("#openStartTime").val()) {
            popMsg("开放结束日期不能小于开始日期");
            return;
        }
        var today=new Date();
        var endDate = new Date(Date.parse($("#openEndTime").val().replace(/-/g,"/")));
        var year=today.getFullYear();
        var month=today.getMonth()+1;
        var day=today.getDate();
        if(month<=9){
            month="0"+month;
        }
        if(day<=9){
            day="0"+day;
        }
        today=year+"-"+month+"-"+day;
        year = endDate.getFullYear();
        month = endDate.getMonth()+1;
        day = endDate.getDate();
        if(month<=9){
            month="0"+month;
        }
        if(day<=9){
            day="0"+day;
        }
        endDate=year+"-"+month+"-"+day;
        if(endDate < today) {
            popMsg("开放结束日期不能小于今天");
            return;
        }
        if ($("#membershipCardDto").valid()) {
            let param = {
                cardName: $("#cardName").val(),
                openStartTime: $("#openStartTime").val(),
                cardDesc: $('textarea[name="cardDesc"]').val(),
                openEndTime: $("#openEndTime").val(),
                validityTime: $("#validityTime").val(),
                releaseFlag: $("input[name='releaseFlag']:checked").val(),
                imageUrl:$("#imageUrl").val(),
                status:"1",
                id:$("#id").val(),
                packageId:$("#packageId").val(),
                userIds:userIds
            };
            ajaxData("/membershipCardConfig/saveMembershipCardInfo", param, function (data) {
                popMsg("保存成功");
                closeWinCallBack(data);
            });
        }
    })

    $('#btnSendUser').bind("click", function () {
        let id = $("#id").val();
        if(getValue(id) == ""){
            popMsg("请先保存数据")
            return ;
        }
        let params = {
            userIds:userIds,
            id: $("#id").val()
        }
        parent.popWin('选择用户','/membershipCardConfig/selectSendUser', params,'100%', '100%', function (res){
            userIds = res
        })
    })

    $("#btnSelectCost").bind("click", function() {
        var relationId = $("#id").val()
        if(getValue(relationId) == ""){
            popMsg("请先保存数据")
            return ;
        }
        var param = {
            existPersonType:true,
            existIfFree:true,
            existIfDiscount:true,
            existDiscount:true,
            existPrice:true,
            relationId:relationId
        }
        popWin("选择用户类型", "/selectTrainTypeCost", param, "80%", "80%");
    });
})


function cardPicInit() {
    $('#cardPicRemoveBtn').bind('click', function () {
        clearLivePic();
    });
    $('#cardPicFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#cardPicFileId").val(file.fileRelaId);
                $("#imageUrl").val(file.fileRelaId);
                $("#picAttId").val("");
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });

    $("#cardPicFile").imageUrlUploadPreview({
        Img: "cardPicImgSrc",
        Width: 50,
        Height: 50
    });
}

function clearLivePic() {
    popConfirm("确认删除图片吗？", function () {
        $("#cardPicFileId").val("");
        $("#cardPic").css("display", "none");
        $("#cardPicFile").val("");
        $("#imageUrl").val("");
    });
}

$.fn.extend({
    imageUrlUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "cardPicImgSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#imageUrl").val("");
            $("#cardPic").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});

function packageInit(){
    let param = {
        id: $("#packageId").val(),
        releaseFlag: "1"
    }
    parent.popWin('选择课程包','/membershipCardConfig/selectCoursePack', param,'70%', '70%', function (res){
        $("#packageId").val(res);
        param.id = res
        ajaxData("/packManagement/getPackInfo",param, function (packInfo) {
            $("#packageName").text(packInfo.packageName);
        })
    });
}

function releaseFlagInit(){
    let releaseFlag = $('#releaseFlag').val();
    if (releaseFlag === '1'){
        $("#releaseFlag2").attr('checked','true');
    }else if(releaseFlag === '0' || releaseFlag === null || releaseFlag === "" || releaseFlag === undefined){
        $("#releaseFlag1").attr('checked','true');
    }
}

