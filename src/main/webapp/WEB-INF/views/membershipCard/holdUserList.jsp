<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .iconColor{
            color: #145ccd;
            margin: 0 3px;
        }
        .icon{
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/membershipCardConfig/getCurrentCardHoldUser?id=${membershipCardDto.id}" cssClass="table table-striped table-hover">
                <e:gridColumn label="姓名" displayColumn="realName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="用户类型" displayColumn="personType" orderable="false"
                              cssClass="text-center" cssStyle="width:15%;" />
                <e:gridColumn label="证券代码" displayColumn="companyCode" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="公司名称/学校" displayColumn="companyName" orderable="false"
                              cssClass="text-center" cssStyle="width:15%" />
                <e:gridColumn label="部门/年级" displayColumn="orgName" orderable="false"
                              cssClass="text-center" cssStyle="width:5%" />
                <e:gridColumn label="职务" displayColumn="post" orderable="false"
                              cssClass="text-center" cssStyle="width:5%" />
                <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="购买时间" displayColumn="createTime" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="到期时间" displayColumn="expireTime" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="有效时间（天）" displayColumn="validityTime" orderable="false"
                              cssClass="text-center" cssStyle="width:5%" />
            </e:grid>
        </div>
        <div class="row">
            <div class="col-xs-12 text-center" >
                <sapn id="btnSubmit" class=" btn btn-primary">返回</sapn>
            </div>
        </div>
    </div>
</div>
</body>
</html>
