<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <script type="text/javascript">
    </script>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="membershipCardDto" id="membershipCardDto" >
            <div class="row" style="margin-top: 10px">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="width: 100px">会员卡名称：</label>
                    <div class="col-md-3">
                        <form:input path="cardName" placeholder="请输入会员卡名称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label" style="width: 100px">发布状态：</label>
                    <div class="col-md-3">
                        <form:select path="releaseFlag" cssClass="form-control">
                            <form:option value="">全部</form:option>
                            <form:option value="1">是</form:option>
                            <form:option value="0">否</form:option>
                        </form:select>
                    </div>

                </div>
                <div class="col-md-12">
                    <div class="col-md-12 text-right" >
                        <span id="btnClear" class="btn btn-default">清空</span>
                        <sec:authorize access="hasAuthority('RES_QUERY_MEMBERSHIP_CARD_LIST_AUTHORITY_3')" >
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_EDIT_MEMBERSHIP_CARD_INFO_AUTHORITY_3')" >
                            <span id="btnSet" class="btn btn-primary">新建</span>
                        </sec:authorize>
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_SHOW_CARD_HOLD_USER_AUTHORITY_3')" >
            <%--查看持有权限--%>
            <input type="hidden" id="showCardHoldUserAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_EDIT_MEMBERSHIP_CARD_INFO_AUTHORITY_3')" >
            <%--编辑权限--%>
            <input type="hidden" id="editCardAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_UPDATE_MEMBERSHIP_CARD_INFO_AUTHORITY_3')" >
            <%--发布/取消发布、删除权限--%>
            <input type="hidden" id="updateCardStatusAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/membershipCardConfig/queryMembershipCardList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="会员卡名称" displayColumn="cardName" orderable="false"
                              cssClass="text-center" cssStyle="width:14%" />
                <e:gridColumn label="课程数量" displayColumn="courseNum" orderable="false"
                              cssClass="text-center" cssStyle="width:6%" />
                <e:gridColumn label="开放时间" renderColumn="buyOpenTime" orderable="false"
                              cssClass="text-center" cssStyle="width:12%" />
                <e:gridColumn label="发布状态" renderColumn="releaseFlag"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="有效天数" displayColumn="validityTime"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:14%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
