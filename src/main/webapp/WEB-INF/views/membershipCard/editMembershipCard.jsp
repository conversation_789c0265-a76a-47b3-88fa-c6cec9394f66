<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        #cardPicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        .panel-body{
            height: 100%;
        }
        textarea.form-control {
            height: 100px;
        }
        .panel{
            padding: 0;
            margin: 0;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="membershipCardDto" modelAttribute="membershipCardDto" cssClass="form-horizontal">
            <form:hidden path="id"/>
            <form:hidden path="packageId"/>
            <form:hidden path="releaseFlag"/>
            <input type="hidden" id="packName" value="${membershipCardDto.packageName}"/>
            <input type="hidden" id="userIds" value="${membershipCardDto.userIds}"/>
            <div class="col-md-12" style="display: flex;margin-top: 30px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"> <span style="color: #FF0000;font-weight: bold;">*</span>会员卡名称</label>
                <div class="col-md-10 no-padding" style="margin-left: 20px;width:600px">
                    <form:input path="cardName" cssClass="form-control" placeholder="请输入会员卡名称"
                                autocomplete="off" maxlength="100"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"> <span style="color: #FF0000;font-weight: bold;">*</span>开放时间</label>
                <div class="col-md-3 no-padding" style="margin-left: 20px">
                    <e:date cssClass="form-control" path="openStartTime" autocomplete="off" placeholder="请输入开始时间" format="yyyy-mm-dd"/>
                </div>
                <label class="col-md-1 no-padding control-label text-center" style="margin-top: 5px;width: 30px;margin-left: 10px">至</label>
                <div class="col-md-3 no-padding" >
                    <e:date cssClass="form-control" path="openEndTime" autocomplete="off" placeholder="请输入结束时间" format="yyyy-mm-dd"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"> <span style="color: #FF0000;font-weight: bold;">*</span>购买有效时间</label>
                <div class="col-md-3 no-padding" style="margin-left: 20px">
                    <form:input path="validityTime" type="number" cssClass="form-control" placeholder="请输入购买有效时间"
                                autocomplete="off" max="1095" min="1"/>
                </div><span style="margin-top: 5px;margin-left: 10px">天</span>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"> <span style="color: #FF0000;font-weight: bold;">*</span>背景图片</label>
                <div class="col-md-10 control-label" style="margin-left: 20px">
                    <div id="cardPic" class="col-xs-12 no-padding"
                         <c:if test="${empty membershipCardDto.imageUrl}" >style="display: none;" </c:if>
                    >
                        <img id="cardPicImgSrc" src="${membershipCardDto.imageUrl}" style="width: 240px;height: 140px;"/>
                        <input type="hidden" name="cardPicFileId" id="cardPicFileId" value="${membershipCardDto.imageUrl}"/>
                        <form:hidden path="imageUrl"/>
                    </div>
                    <div class="col-xs-12 no-padding controls live-pic-btn-row">
                        <a href="javascript:void(0);" id="cardPicUploadBtn" class="file btn btn-warning btn-facebook btn-outline">
                            <i class="fa fa-upload"> </i> 上传图片
                            <input id="cardPicFile" type="file" name="files"/>
                        </a>
                        <input type="button" id="cardPicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                    </div>
                </div>
            </div>

            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"> <span style="color: #FF0000;font-weight: bold;">*</span>是否发布</label>
                <div class="col-md-10 no-padding" style="margin-left: 20px">
                    <form:radiobutton path="releaseFlag" value="0" id="releaseFlag1" name="releaseFlag" onchange="releaseFlagInit()"/>
                    <label for="releaseFlag1">否</label>
                    <form:radiobutton path="releaseFlag" value="1" id="releaseFlag2" name="releaseFlag" onchange="releaseFlagInit()" cssStyle="margin-left:6px"/>
                    <label for="releaseFlag2">是</label>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;"> <span style="color: #FF0000;font-weight: bold;">*</span>会员卡描述</label>
                <div class="col-md-10 no-padding" style="width: 600px;margin-left: 20px">
                    <form:textarea path="cardDesc" name="cardDesc" cssClass="form-control" autocomplete="off" placeholder="请输入会员卡描述" maxlength="100"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">关联课程包</label>
                <div class="col-md-10 no-padding" style="margin-left: 20px;">
                    <input type="button" style="margin-right: 15px;" class="btn"  value="选择课程包" id="btnPack"/>
                    <span style="margin-left: 20px" id="packageName"></span>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">收费设置</label>
                <div class="col-md-10 no-padding" style="margin-left: 20px;">
                    <input id="btnSelectCost" type="button" style="margin-right: 15px;" class="btn" value="选择用户类型"/>
                    <form:hidden path="personTypes" value="${membershipCardDto.personTypes}"/>
                </div>
            </div>
            <div class="col-md-12" style="display: flex;margin-top: 20px">
                <label class="col-md-2 no-padding control-label text-right" style="margin-top: 5px;width: 100px;">用户</label>
                <div class="col-md-10 no-padding" style="margin-left: 20px;">
                    <input type="button" style="margin-right: 15px;" class="btn"  value="选择赠送用户" id="btnSendUser"/>
                </div>
            </div>
            <div class="col-md-12" style="margin-top: 30px;text-align: center">
                <input type="button" id="btnCancel" style="margin-right: 15px;" class="btn btn-default" value="关闭"/>
                <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存"/>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
