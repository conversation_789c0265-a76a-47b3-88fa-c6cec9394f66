$(document).ready(function () {
    $('#btnQuery').click(function () {
        search();
    });

    $("#btnClear").bind("click", function() {
        document.getElementById("membershipCardDto").reset();
        search();
    });

    $("#btnSet").bind("click", function() {
        setMembershipCart();
    });

})
function search(){
    ajaxTableQuery("tableAll", "/membershipCardConfig/queryMembershipCardList", $("#membershipCardDto").formSerialize());
}


// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function buyOpenTime(data, type, row, meta) {
    if(data.openStartTime === "" || data.openStartTime === null){
        return "- -";
    }else{
        return data.openStartTime + " 至 " + data.openEndTime;
    }
}

function releaseFlag(data, type, row, meta) {
    if(data.releaseFlag === '1'){
        return "是";
    }else{
        return "否";
    }
}

function columnOperation(data, type, row, meta) {
    let str = '';
    let hold = '';
    let edit = '';
    let update = '';
    if (document.getElementById("showCardHoldUserAuth")) {
        hold = true
    }
    if (document.getElementById("editCardAuth")) {
        edit = true
    }
    if (document.getElementById("updateCardStatusAuth")) {
        update = true
    }
    if (hold) {
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="showCurrentCardHoldUser(\'' + data.id + '\')">查看持有</span>';
    }
    if (edit) {
        str += '<span style="color: #00a0e9">|</span>';
        str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="editMembershipCart(\'' + data.id + '\')">编辑</span>';
    }
    if (update) {
        if (data.releaseFlag === '0') {
            str += '<span style="color: #00a0e9">|</span>';
            str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="updateCard(\'' + data.id + '\',\'' + 1 + '\')">发布</span>';
        }else{
            str += '<span style="color: #00a0e9">|</span>';
            str += '<span style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="updateCard(\'' + data.id + '\',\'' + 0 + '\')">取消发布</span>';
        }
        str += '<span style="color: #00a0e9">|</span>';
        str += '<span  style="cursor: pointer;margin: 0 5px;color: #00a0e9" onclick="deleteCard(\'' + data.id + '\')">删除</span>';
    }
    if(str === '') {
        str = '-';
    }
    return str;
}

function setMembershipCart(){
    let param = {}
    popWin('新建会员卡', '/membershipCardConfig/queryMembershipCardInfo', param, '100%', '100%', callBackAddExam, '', callBackAddExam);
}

function editMembershipCart(id){
    let param = {
        id:id
    }
    popWin('编辑会员卡', '/membershipCardConfig/queryMembershipCardInfo', param, '100%', '100%', callBackAddExam, '', callBackAddExam);
}

function showCurrentCardHoldUser(id){
    let param = {
        id:id
    }
    popWin('已持有用户','/membershipCardConfig/showCurrentCardHoldUser',param,'100%', '100%',callBackAddExam, '', callBackAddExam)
}


function deleteCard(id){
    popConfirm("当前操作不可恢复，确认删除?", function () {
        let param = {
            id:id,
            status:"0"
        }
        ajaxData("/membershipCardConfig/updateMembershipCardStatus",param, function (res) {
            if(res){
                popMsg("删除成功");
            }else{
                popMsg("删除失败");
            }
            callBackAddExam();
        })
    });
}

function updateCard(id,releaseFlag){
    popConfirm("当前操作不可恢复，确认操作?", function () {
        let param = {
            id:id,
            releaseFlag:releaseFlag
        }
        ajaxData("/membershipCardConfig/updateMembershipCardStatus",param, function (res) {
            if(res){
                popMsg("已保存");
            }else{
                popMsg("修改失败");
            }
            callBackAddExam();
        })
    });
}



function callBackAddExam(){
    ajaxTableReload("tableAll", false);
}
