//@ sourceURL=userManage.js

var selUser = [];
var selUserPhones = [];
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // dateInit();
    selUser = $("#userIds").val().split(",")


    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        // tSelectInit();
        // dateInit();
        $('input[name="personType"]').val("");
        $('input[name="checkStatus"]').val("");
        $('input[name="belongCommission"]').val("");
        $('input[name="belongsPlate"]').val("");
        $('input[name="personLabel"]').val("");
        $('#personType').val("")
        $('#belongCommission').val("")
        search();
    });

    $('#btnSubmit').click(function () {
        submitOn()
    });

});
function search() {
    console.log($("#queryForm").formSerialize())
    ajaxTableQuery("tableAll", "/trainUserManage/getUserInfoList",
        $("#queryForm").formSerialize());
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    // $('#userType').tselectInit(null, teaSelectOptions);
    $('#personType').tselectInit(null, teaSelectOptions);
    $('#checkStatus').tselectInit(null, teaSelectOptions);
    $('#belongCommission').tselectInit(null, teaSelectOptions);
    $('#belongsPlate').tselectInit(null, teaSelectOptions);
    $('#personLabel').tselectInit(null, teaSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function selCompany(item) {
    var chk = $(item).find('input');//获取选择的input按钮
    var id = chk.eq(0).attr('d-id');//通过选择的input按钮获取ID
    var phone = $(item).parent().siblings().eq(6).text()
    if (chk.prop('checked')) {
        if(selUser.indexOf(id) === -1){
            selUser.push(id)
            selUserPhones.push(phone)
        }
    } else {
        selUser.some((item,i)=>{
            if(item === id){
                selUser.splice(i,1)
            }
        })
        selUserPhones.some((item,i)=>{
            if(item === phone){
                selUserPhones.splice(i,1)
            }
        })
    }
}

function rcIndex(data, type, row, meta) {
    var flag = false;

    selUser.forEach((item)=>{
        if(item == data.id){
            flag = true
        }
    })

    var str = '';
    str += '<div class="classCheckBox case-opt" style="cursor:pointer;" onclick="selCompany(this)">';
    if(flag){
        str += '<input  type="checkbox" checked name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }else {
        str += '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id
            + '" class="hidden">';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="courseId" value="' + data.id + '">'
    str += '</div>';
    return str;
}

function submitOn() {
    let param = {
        userIds: selUser.toString(),
        phones: selUserPhones.toString(),
        id: $("#id").val(),
        validityTime: ''
    }
    console.log(param)
    if (selUser.length >0){
        parent.popWin('选择赠送时长','/membershipCardConfig/selectTimeInit', null,'30%', '25%', function (res){
            param.validityTime = res
            ajaxData("/membershipCardConfig/sendMembershipCard",param, function (packInfo) {
                closeWin();
            })
        })
        // closeWinCallBack(selUser.toString())
    }else {
        popMsg("请选择用户")
    }
}