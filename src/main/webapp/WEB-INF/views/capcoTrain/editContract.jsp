<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div style="width:1200px;margin:auto">
            <form:form id="schContractForm" modelAttribute="schContractDto" cssClass="form-horizontal">
                <form:hidden path="contractId"/>
                <form:hidden path="orgId"/>
                <div class="row">
                    <label class="col-md-1 control-label" style="width:105px"><label style="color:red">*</label>内部合同编码</label>
                    <div class="col-md-3">
                        <form:input path="contractNo" placeholder="请输入内部合同编码" cssClass="form-control" autocomplete="off"/>
                    </div>
                </div>

                <div class="row">
                    <label class="col-md-1 control-label">授权时间</label>
                    <label class="col-md-2 control-label" style="width:110px"><label style="color:red">*</label>授权起始日期</label>
                    <div class="col-md-3">
                        <e:date path="startTime" placeholder="请输入授权起始日期" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-2 control-label" style="width:110px"><label style="color:red">*</label>授权截止日期</label>
                    <div class="col-md-3">
                        <e:date path="endTime" placeholder="请输入授权截止日期" cssClass="form-control" autocomplete="off"/>
                    </div>
                </div>

                <div class="row">
                    <label class="col-md-1 control-label">授权数量</label>
                    <label class="col-md-1 control-label"><label style="color:red">*</label>购买数量</label>
                    <div class="col-md-3">
                        <form:input path="buyNum" placeholder="请输入购买数量" cssClass="form-control" onchange="changeNum()" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">赠送数量</label>
                    <div class="col-md-3">
                        <form:input path="giveNum" placeholder="请输入赠送数量" cssClass="form-control" onchange="changeNum()" autocomplete="off"/>
                    </div>
                    <label class="col-md-2 control-label">共计授权数量：<span id="totalNum">0</span></label>
                </div>
                <div class="row">
                    <label class="col-md-1 control-label">备注</label>
                    <div class="col-md-3">
                        <form:textarea rows="3" cols="150" path="remark" placeholder="请输入备注" cssClass="form-control" autocomplete="off"/>
                    </div>
                </div>
            </form:form>
            <div class="row">
                <div class="col-md-12 text-right">
                    <span id="btnSave" class="btn btn-primary">保存</span>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
