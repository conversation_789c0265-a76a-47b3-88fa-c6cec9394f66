<%--
  Created by IntelliJ IDEA.
  User: wangwennan
  Date: 2021/8/17
  Time: 17:15
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>易董 云端管理平台</title>
  <e:base/>
  <e:js/>
  <style>
      .control-label {
          text-align: right !important;
          text-align: center;
          margin-top: 6px;
          padding-top: 5px;
      }
  </style>
</head>
<body>
<div class="panel">
  <div class="panel-heading">
    <form:form modelAttribute="schUserContractOverviewDto" id="queryForm">
      <div class="row">
        <div class="col-md-12">
          <label class="col-md-1 control-label">用户编号</label>
          <div class="col-md-3 ">
            <form:input path="accId" placeholder="请输入用户编号" cssClass="form-control" autocomplete="off"/>
          </div>
          <label class="col-md-1 control-label">用户姓名</label>
          <div class="col-md-3">
            <form:input path="nickName" placeholder="请输入用户姓名" cssClass="form-control" autocomplete="off"/>
          </div>
          <label class="col-md-1 control-label">机构名称</label>
          <div class="col-md-3">
            <form:input path="orgName" placeholder="请输入机构名称" cssClass="form-control" autocomplete="off"/>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 control-label">
          <label class="col-md-1 control-label">手机号</label>
          <div class="col-md-3">
            <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off" />
          </div>
          <label class="col-md-1 control-label" >授权截止日</label>
          <div class="col-md-3 daterange">
            <form:input path="endTime" placeholder="请选择授权截至时间" cssClass="form-control" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-right" >
          <span id="btnClear" class="btn btn-default">清空全部</span>
          <span id="btnQuery" class="btn btn-primary">查询</span>
        </div>
      </div>
    </form:form>
  </div>
  <div class="panel-body">
    <div class="row">
      <div class="col-md-12">
        <e:grid id="tableAll" action="/schUserManage/getUserContractOverviewList" cssClass="table table-striped table-hover">

          <e:gridColumn label="用户编号" displayColumn="accId" orderable="false" cssClass="text-center"
                        cssStyle="width:10%" />

          <e:gridColumn label="用户姓名" displayColumn="nickName" orderable="false"
                        cssClass="text-center" cssStyle="width:10%" />

          <e:gridColumn label="手机号" displayColumn="phone" orderable="false"
                        cssClass="text-center" cssStyle="width:10%" />

          <e:gridColumn label="所属机构名称" displayColumn="orgName" orderable="false"
                        cssClass="text-center" cssStyle="width:10%" />

          <e:gridColumn label="授权截止日" displayColumn="endTime" orderable="true" orderColumn="endTime"
                        cssClass="text-center" cssStyle="width:10%" />

          <e:gridColumn label="授权次数" renderColumn="count" orderable="true" orderColumn="count"
                        cssClass="text-center" cssStyle="width:10%" />

          <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                        cssStyle="width:10%" />
        </e:grid>
      </div>
    </div>
  </div>
</div>
</body>
</html>
