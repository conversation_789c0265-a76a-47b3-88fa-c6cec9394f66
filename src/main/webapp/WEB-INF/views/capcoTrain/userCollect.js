//@ sourceURL=userCollect.js
$(document).ready(function(){

    tabInit();
})
function tabInit(){
    $("#collectTab1").bind("click", function() {
        search('1');
    });
    $("#collectTab2").bind("click", function() {
        search('2');
    });
    $("#collectTab3").bind("click", function() {
        search('3');
    });
    $("#collectTab4").bind("click", function() {
        search('4');
    });
    $("#collectTab5").bind("click", function() {
        search('5');
    });
}
function search(listFlag) {
    var userId=$('#userId').val();
    var param={
        userId:userId
    }
    var param={
        userId:userId,
        listFlag:listFlag
    }
    if (listFlag=='1'){
        ajaxTableQuery("table_id1", "/schUserManage/getUserColInfo",param);
    } else if (listFlag=='2'){
        ajaxTableQuery("table_id2", "/schUserManage/getUserColInfo",param);
    }else if (listFlag=='3'){
        ajaxTableQuery("table_id3", "/schUserManage/getUserColInfo",param);
    }else if (listFlag=='4'){
        ajaxTableQuery("table_id4", "/schUserManage/getUserColInfo",param);
    }else if (listFlag=='5'){
        ajaxTableQuery("table_id5", "/schUserManage/getUserColInfo",param);
    }
}
function courseType(data, type, row, meta) {
    var str = '';
    if (data.courseContentType=='PRODUCT') {
        str='产品课程'
    }else if (data.courseContentType=='BUSINESS') {
        str='业务课程'
    }else {
        str='直播'
    }

    // str = '<a style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editUserInfo(\'' + data.id + '\')">编辑</a>'
    //     + '|<a style="cursor: pointer;margin: 0 5px;" title="删除" onclick="delUserInfo(\'' + data.id + '\')">删除</a>';
    // if (data.personType!='2') {
    //     str += '|<a style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editUserInfo(\'' + data.id + '\')">学习记录</a>'
    //         + '|<a style="cursor: pointer;margin: 0 5px;" title="删除" onclick="colUserInfo(\'' + data.id + '\')">收藏</a>';
    // }
    return str;
}

function lawsName(data, type, row, meta) {
    var str = '';
    if (data.lawShowFlagStr) {
        if (data.lawShowFlagStr!='0'){
            str+='<span style="color: red;">'+data.lawShowFlagStr+'</span>'
        }
        str+=data.lawsName
    }else {
        str = '- -';
    }
    return str;
}

function lawSourceText(data, type, row, meta) {
    var str='- -'
    if (data.lawSourceText){
        str=data.lawSourceText
    }
    return str
}

function violateTitle(data, type, row, meta) {
    var str='- -'
    if (data.violateTitle){
        str=data.violateTitle
    }
    return str
}

function disciplinaryOrg(data, type, row, meta) {
    var str='- -'
    if (data.disciplinaryOrg){
        str=data.disciplinaryOrg
    }
    return str
}

function disciplinaryTypeText(data, type, row, meta) {
    var str='- -'
    if (data.disciplinaryTypeText){
        str=data.disciplinaryTypeText
    }
    return str
}

function punishTimeEnd(data, type, row, meta) {
    var str='- -'
    if (data.punishTimeEnd){
        str=data.punishTimeEnd
    }
    return str
}

function progressText(data, type, row, meta) {
    var str='- -'
    if (data.progressText){
        str=data.progressText
    }
    return str
}


function fileSize(data, type, row, meta) {
    var size = "";
    var limit=data.fileSize
    if(limit < 0.1 * 1024){                            //小于0.1KB，则转化成B
        size = limit.toFixed(2) + "B"
    }else if(limit < 0.1 * 1024 * 1024){            //小于0.1MB，则转化成KB
        size = (limit/1024).toFixed(2) + "KB"
    }else if(limit < 0.1 * 1024 * 1024 * 1024){        //小于0.1GB，则转化成MB
        size = (limit/(1024 * 1024)).toFixed(2) + "MB"
    }else{                                            //其他转化成GB
        size = (limit/(1024 * 1024 * 1024)).toFixed(2) + "GB"
    }
    var sizeStr = size + "";                        //转成字符串
    var index = sizeStr.indexOf(".");                    //获取小数点处的索引
    var dou = sizeStr.substr(index + 1 ,2)            //获取小数点后两位的值
    if(dou == "00"){                                //判断后两位是否为00，如果是则删除00
        return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2)
    }
    return size;
}