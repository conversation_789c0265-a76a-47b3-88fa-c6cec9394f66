<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div class="row">
            <div style="width:1200px;margin:auto">
                <form:form id="schOrgInForm" modelAttribute="schOrgUserInfoDto" cssClass="form-horizontal">
                    <form:hidden path="orgId"/>
                    <div class="row">
                        <span style="padding-right:20px;font-size:16px;font-weight:bold">机构信息</span>
                    </div>
                    <div class="row">
                        <label class="col-md-1 control-label"><label style="color:red">*</label>机构名称</label>
                        <div class="col-md-4">
                            <form:input path="orgName" placeholder="请输入机构名称" cssClass="form-control" autocomplete="off"/>
                        </div>
                    </div>

                    <div class="row">
                        <label class="col-md-1 control-label">关联易董公司</label>
                        <div class="col-md-4">
                            <form:input path="companyName" placeholder="请输入关联公司" cssClass="form-control" autocomplete="off"/>
                            <form:hidden path="companyId"></form:hidden>
                        </div>
                    </div>

                    <div class="row">
                        <label class="col-md-1 control-label">备注</label>
                        <div class="col-md-4">
                            <form:textarea path="remark" rows="3" cols="150" cssClass="form-control" autocomplete="off" placeholder="请输入备注"/>
                        </div>
                    </div>
                </form:form>

                <div class="row">
                    <span style="padding-right:20px;font-size:16px;font-weight:bold">授权信息</span>
                    <div style="display:inline-block;float:right"><span id="userOverview" style="color:#0356E0;cursor:pointer">用户总览</span>&nbsp;<span class="btn btn-primary" id="addContract">添加授权</span></div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <e:grid id="tableAll" action="/schUserManage/queryContractInfo?orgId=${schOrgUserInfoDto.orgId}" cssClass="table table-striped table-hover">
                            <e:gridColumn label="序号" renderColumn="rcIndex" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="内部合同编码" displayColumn="contractNo" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="授权起始日" displayColumn="startTime" orderable="true" orderColumn="startTime" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="授权截止日" displayColumn="endTime" orderable="true" orderColumn="endTime" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="购买数量" displayColumn="buyNum" orderable="true" orderColumn="buyNum" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="赠送数量" displayColumn="giveNum" orderable="true" orderColumn="giveNum" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="授权数量总计" displayColumn="totalNum" orderable="true" orderColumn="totalNum" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="已使用量" displayColumn="useNum" orderable="true" orderColumn="useNum" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                            <e:gridColumn label="操作" renderColumn="renderColumnOption" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        </e:grid>




                        <%--<table id="contractTable" class="table table-bordered no-margin"--%>
                               <%--style="text-align: center;border-color: #D7D7D7;margin-top: 10px !important;width:100%">--%>
                            <%--<thead>--%>
                            <%--<th class="text-center table-th" width="10%">序号</th>--%>
                            <%--<th class="text-center table-th" width="10%">内部合同编码</th>--%>
                            <%--<th class="text-center table-th" width="10%">授权起始日</th>--%>
                            <%--<th class="text-center table-th" width="10%">授权截止日</th>--%>
                            <%--<th class="text-center table-th" width="10%">购买数量</th>--%>
                            <%--<th class="text-center table-th" width="10%">赠送数量</th>--%>
                            <%--<th class="text-center table-th" width="10%">授权数量总计</th>--%>
                            <%--<th class="text-center table-th" width="10%">已使用量</th>--%>
                            <%--<th class="text-center table-th" width="30%">操作</th>--%>
                            <%--</thead>--%>
                            <%--<tbody id="contractBody">--%>
                            <%--</tbody>--%>
                        <%--</table>--%>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 text-right" style="padding-top:20px">
                        <span id="btnSave" class="btn btn-primary">保存</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
</body>
</html>
