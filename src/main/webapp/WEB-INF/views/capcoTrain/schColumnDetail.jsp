<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .table-th {
            background-color: #37C8F4 !important;
            color: white;
            padding: 8px;
            text-align: center;
        }

        .table-td {
            padding: 8px;
            text-align: center;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
<div class="panel" style="min-height: 430px;">
    <form:form modelAttribute="schColumnDto" id="schColumnForm" autocomplete="off">
        <form:hidden path="columnId"/>
        <div class="row" style="padding-top: 5px;padding-bottom: 10px">
            <div class="col-md-12">
                <div class="col-md-6 text-left">
                    <div style="font-size: 20px;font-weight: bold"><span style="color: #FF0000;font-weight: bold;">*</span>专栏基本信息</div>
                </div>
                <div class="col-md-6 text-right">
                    <span id="btnSave" class="btn btn-primary">保存基本信息</span>
                </div>
            </div>
        </div>
        <div class="row" style="display: flex;align-items: center;">
            <label class="col-md-1 control-label" style="text-align: right;">专栏名称</label>
            <div class="col-md-6">
                <form:input cssClass="form-control" path="columnName" placeholder="请输入专栏名称"/>
            </div>
        </div>

        <div class="row" style="margin-top:20px">
            <input type="button" id="relaCourseBtn" class="col-md-1 col-md-offset-1 btn btn-primary" value="关联课程"
                   onclick="showCourse()"/>
        </div>
        <div class="row" style="margin-top:10px;margin-bottom: 20px;">
            <div class="col-md-11 col-md-offset-1">
                <table class="table-bordered" style=";border-color: #D7D7D7;width: 95%;">
                    <thead>
                    <tr>
                        <th class="table-th" width="10%">序号</th>
                        <th class="table-th" width="10%">课程ID</th>
                        <th class="table-th" width="30%">课程名称</th>
                        <th class="table-th" width="15%">版本号</th>
                        <th class="table-th" width="20%">发布日期</th>
                        <th class="table-th" width="15%">操作</th>
                    </tr>
                    </thead>
                    <tbody id="relaCourse">
                    </tbody>
                </table>
            </div>
        </div>
    </form:form>

    <%--下拉模板--%>
    <script id="versionTypeSelectTpl" type="text/html">
        <select class="versionType" name="schCourseDtoList[{{n}}].versionType" disabled>
            <c:forEach items="${versionTypeList}" var="item">
                <option value="${item.value}" {{if versionType == ${item.value}}} selected{{/if}}>${item.label}</option>
            </c:forEach>
        </select>
    </script>
</div>
</body>
</html>
