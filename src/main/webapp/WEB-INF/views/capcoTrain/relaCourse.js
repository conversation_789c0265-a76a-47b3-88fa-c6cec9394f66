var ids = []
var _mlc = MaaLetter = {
    blankStr: '-',
    declareInfo: {},
    selDeclareIds: {}
};
var lostIds = [];
$(document).ready(function () {
    otherPageInit()
    // _mlc.resetSelNum();

    //日期控件初始化
    dateRangeInit();

    // 清空全部
    $("#btnClear").bind('click', function () {
        popConfirm('确认清空全部检索条件？', function () {
            clearAllOptions();
        });
    });

    $("#btnQuery").bind('click', function () {
        ajaxTableQuery("tableAll", "/schoolCourse/queryRepCaseInfoList", $("#queryForm").formSerialize());
    });

});

//回车事件
function bindEnter(obj) {
    if (obj.keyCode == 13) {
        ajaxTableQuery("tableAll", "/schoolCourse/queryRepCaseInfoList", $("#queryForm").formSerialize());
    }
}

function rcIndex(data, type, row, meta) {
    // 默认选中
    if ($('#selectedIds').val() != '') {
        var ids = $('#selectedIds').val().split(',');
        for (let i = 0; i < ids.length; i++) {
            var id = ids[i].trim();
            if (id != '') {
                _mlc.selDeclareIds[id] = true;
                var params = {
                    id: ids[i],
                };
                ajaxData("/schoolCourse/courseOfRela?relaFlag=0", params, function (data1) {
                    lostIds.push(data1);
                    _mlc.declareInfo[data1[0].id] = data1[0];
                });
            }
        }
        $('#selectedIds').val('');
    }
    // 暂存数据
    var id = data.id;
    _mlc.declareInfo[id] = $.extend({}, data);

    var checked = '';
    if (_mlc.selDeclareIds[id]) {
        checked = 'checked';
    }
    var str = '';
    str += '<div class="classCheckBox case-opt" onclick="_mlc.selDeclare(this)">';
    str += '<input type="checkbox" class="selDeclare" d-id="' + id + '" ' + checked + '>';
    str += '<label></label>';
    str += '</div>';
    return str;
}

_mlc.selDeclare = function (item) {
    
    var chk = $(item).find('input').eq(0);
    var id = chk.attr('d-id');
    if (!chk.prop('checked')) {
        chk.removeAttr('checked');
        _mlc.rmSelDeclare(id);
    } else {
        chk.prop('checked', 'checked');
        _mlc.addSelDeclare(id);
    }

}

// 删除选中
_mlc.rmSelDeclare = function (id) {
    
    $('div[selDeclareId="' + id + '"]').remove();
    // ajaxTableReload("tableAll", false);
    delete _mlc.selDeclareIds[id];
};

// 添加选中
_mlc.addSelDeclare = function (id) {
    
    var d = _mlc.declareInfo[id];
    var div = document.createElement('div');
    div.className = 'sel-block';
    div.setAttribute('selDeclareId', id);
    $('.sel-box').append(div);
    _mlc.selDeclareIds[id] = true;
};

function courseName(data, type, row, meta) {
    var str = data.courseName;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return str;
}

function courseType(data, type, row, meta) {
    var str = data.courseType;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return str;
}

function applyPlate(data, type, row, meta) {
    var str = data.applyPlate;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return str;
}

function applyPerson(data, type, row, meta) {
    var str = data.applyPerson;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return str;
}

function teacher(data, type, row, meta) {
    var str = data.teacher;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return str;
}

//时间控件初始化
function dateRangeInit() {
    // $(".daterangeclear").remove();
    dataRangePickerInit($('#releaseTime'), null, null, function () {

    }, function () {

    });
};

//清空
function clearAllOptions() {
    // 清空条件
    $('#releaseTime').val('');
    $('#courseName').val('');
    // 清空表排序，重新制表
    var table = $("#tableAll");
    //页面默认显示10条
    table.DataTable().page.len(10);
    table.DataTable().order([0, '']).draw();
    ajaxTableQuery("tableAll", "/schoolCourse/queryRepCaseInfoList", $("#queryForm").formSerialize());
};

// 其他控件初始化
function otherPageInit() {
    // 确定
    $('#btnSubmit').click(function () {
        var selDeclares = [];
        var id = '';
        var courseName = '';
        // $('.sel-box > div').each(function (i, o) {
        //     var id = $(o).attr('selDeclareId');
        //     if (_mlc.declareInfo[id]) {
        //         id += "," + _mlc.declareInfo[id].id;
        //         courseName += "," + _mlc.declareInfo[id].courseName;
        //     }
        // });
        // if (lostIds.length > 0) {
        //     for (let i = 0; i < lostIds.length; i++) {
        //         if (!_mlc.declareInfo[lostIds[i][0].id]) {
        //             id += "," + lostIds[i][0].id;
        //             courseName += "," + lostIds[i][0].courseName;
        //         }
        //     }
        // }
        var result = [];
        for (var n in _mlc.selDeclareIds) {
            for (var i in _mlc.declareInfo) {
                if (i == n && _mlc.selDeclareIds[n]) {
                    result.push(_mlc.declareInfo[i])
                }
            }
        }

        // var compare = function (o1, o2) {
        //     var val1 = new Date(o1.releaseTime).getTime();
        //     var val2 = new Date(o2.releaseTime).getTime();
        //     if (val1 < val2) {
        //         return 1;
        //     } else if (val1 > val2) {
        //         return -1;
        //     } else {
        //         return 0;
        //     }
        // }
        // result.sort(compare)
        closeWinCallBack(result);
    });
};
