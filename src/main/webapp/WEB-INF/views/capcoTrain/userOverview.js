$(document).ready(function () {
    // userTableInit();
    btnInit();
    dataRangePickInit();
    tSelectInit();
});

function userTableInit() {
    ajaxTableReload('tableAll', false);
}

function editUserInfo(userId, contractId) {
    parent.popWin('编辑用户信息', '/schUserManage/editOrgUserInit', {
        userId,
        contractId
    }, '98%', '98%', userTableInit, '', userTableInit);
}

function changeIsAccredit(item) {
    var id = $(item).parents("tr").find(".userContractId").val();
    var contractId = $(item).parents("tr").find(".contractIdData").val();
    var isAccredit = $(item).parents("tr").find(".isAccreditData").val() != 'null' ? $(item).parents("tr").find(".isAccreditData").val() : '0';
    isAccredit = isAccredit == '1' ? '0' : '1';
    ajaxData("/schUserManage/editOrgUserSave", {id, isAccredit,contractId}, function (res) {
        if (res == '1') {
            if (isAccredit == '1') {
                popMsg("授权成功");
            } else {
                popMsg("取消授权成功");
            }
            $(item).parents("tr").find(".isAccreditData").val(isAccredit)
            //刷新页面授权数量
        } else {
            if (isAccredit == '1') {
                popMsg("授权失败");
                $(item).prop("checked",false)
            } else {
                popMsg("取消授权失败");
            }
        }
    });
}

function btnInit() {
    $("#btnSearch").bind("click", function () {
        ajaxTableQuery("tableAll", "/schUserManage/queryContractUserInfo",
            $("#schUserForm").formSerialize());
    })
}

//下拉初始化
function tSelectInit() {
    var selectOption = {
        id: 'id',
        name: 'labelName',
        value: 'labelValue',
        grade: 1,
        inputType: 'checkbox',
        resultType: 'children',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#isAccredit').tselectInit(null, selectOption);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
}

function dataRangePickInit() {
    dataRangePickerInit($('#startTime'), null, null, function () {
    }, function () {
    });
    dataRangePickerInit($('#endTime'), null, null, function () {
    }, function () {
    });
}

function delUserInfo(userId, contractId) {
    popConfirm("确认删除", function () {
        ajaxData("/schUserManage/delOrgUserInfo", {userId, contractId}, function (res) {
            //删除成功
            parent.popMsg("删除成功");
            userTableInit()
        });
    })
}

function renderColumnOption(data, type, row, meta) {
    var str = '<span><input class="hidden userContractId" value="' + data.id + '"/>'
        + '<input class="hidden isAccreditData" value="' + data.isAccredit + '"/>'
        + '<input class="hidden contractIdData" value="' + data.contractId + '"/>'
        + '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑用户信息" onclick="editUserInfo(\'' + data.userId + '\',\'' + data.contractId + '\')"></i>'
        + '<i class="fa fa-trash" style="cursor: pointer;margin: 0 5px;" title="删除用户信息" onclick="delUserInfo(\'' + data.userId + '\',\'' + data.contractId + '\')"></i></span>';
    return str;
}

function rcIndex(data, type, row, meta) {
    return meta.row + 1;
}

function renderColumnIsAccredit(data, type, row, meta) {
    return data.isAccredit == '1' ? '<input type="checkbox" checked class="isAccredit" onchange="changeIsAccredit(this)"/>' : '<input type="checkbox" class="isAccredit" onchange="changeIsAccredit(this)"/>';
}
