<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <div class="" style="padding-top: 5px">
            <form:form modelAttribute="courseInfoDto" id="queryForm" >
                <input type="hidden" id="selIds" value="${selIds}"/>
                <input type="hidden"  id="selNames" value="" >
                <div class="row">
                    <div class="col-xs-3">
                        <form:input path="courseName" placeholder="请输入课程" cssClass="form-control"/>
                    </div>
                    <div class="col-xs-9 text-right" >
                        <sapn id="btnClear" class=" btn btn-primary">清空</sapn>
                        <sapn id="btnQuery" class=" btn btn-primary">查询</sapn>
                    </div>
                </div>
            </form:form>
        </div>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/schoolIndexConfig/queryChooseCourse"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="<input type='checkbox'  id='allCheck' disabled>"
                              renderColumn="rcIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:20%"/>
                <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false" cssClass="text-center"
                              cssStyle="width:90%" />
            </e:grid>
        </div>
        <div class="row">
            <div class="col-xs-12 text-center" >
                <sapn id="btnSubmit" class=" btn btn-primary">选择</sapn>
            </div>
        </div>
    </div>
</div>
</body>
</html>








