var selIds = '';
$(document).ready(function () {
    $(window).keydown(function (e) {
        var key = window.event ? e.keyCode : e.which;
        if (key.toString() == "13") {
            return false;
        }
    });
    selIds = $("#selIds").val();

    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        search();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/schoolIndexConfig/queryChooseCourseLY", $("#queryForm").formSerialize());
}

function rcIndex(data, type, row, meta) {
    var str = '';
    str += '<div class="classCheckBox case-opt"  onclick="selCompany(this)">';
    if (selIds == '' || selIds.indexOf(data.id) == -1) {
        str +=
            '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id + '"  value="' + data.id + '" class="hidden">';
    } else {
        str +=
            '<input checked type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.id + '"  value="' + data.id + '" \ >';
    }
    str += '<label></label>';
    str += '<input type="hidden" name="courseId" value="' + data.id + '">'
    str += '</div>';
    return str;
}

function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        selIds = selIds + ',' + id;
    } else {
        selIds = selIds.replace(id, '').replace(",,", ',');
    }
};

function submitOn() {
    
    closeWinCallBack(selIds)
}

function checkAll() {
    if ($("#allCheck")[0].checked) {
        $("#tableAll tbody tr").find("td:first").find("input").prop('checked', true);
        var ids = "";
        $(".selDeclare").each(function (n, obj) {
            ids += $(obj).val() + ','
        })
        selIds = selIds + ',' +ids;
    } else {
        $("#tableAll tbody tr").find("td:first").find("input").prop('checked', false);
        selIds = "";
    }
}
function dataTableLoadAfter() {
    $("#allCheck").removeAttr("checked");
}