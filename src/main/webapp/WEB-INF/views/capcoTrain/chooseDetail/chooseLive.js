var selId = '';
$(document).ready(function () {
    $(window).keydown( function(e) {
        var key = window.event?e.keyCode:e.which;
        if(key.toString() == "13"){
            return false;
        }
    });
    $('#btnQuery').click(function () {
        search();
    });
    $('#btnSubmit').click(function () {
        submitOn();
    });
    //清空条件
    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        $(".t-select").each(function (n, obj) {
            $('input[name="' + obj.id + '"]').val("");
        });
        search();
    });
});

function search() {
    ajaxTableQuery("tableAll", "/schoolIndexConfig/getLive", $("#queryForm").formSerialize());
}
function rcIndex(data, type, row, meta) {
    var str = '';
    str += '<div class="classCheckBox case-opt"  onclick="selCompany(this)">';
    str +=
            '<input  type="checkbox" name="checkRow" class="selDeclare"  d-id="' + data.liveId
            + '" class="hidden">';
    str += '<label></label>';
    str += '<input type="hidden" name="courseId" value="' + data.liveId + '">'
    str += '</div>';
    return str;
}
function selCompany(item) {
    var chk = $(item).find('input').eq(0);//获取选择的input按钮
    var id = chk.attr('d-id');//通过选择的input按钮获取ID
    if (chk.prop('checked')) {
        $(".selDeclare").each(function (n,obj) {
            $(obj).prop('checked',false)
        });
        chk.prop('checked',true);
        selId = id;
    } else {
        selId = "";
    }
};
function submitOn() {
    closeWinCallBack(selId)
}
// 选中
