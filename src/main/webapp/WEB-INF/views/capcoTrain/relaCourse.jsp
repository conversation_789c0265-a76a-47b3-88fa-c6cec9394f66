<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .table > thead > tr > th {
            vertical-align: middle !important;
        }
    </style>
</head>
<body>
<input id="selectedIds" value="${selectedIds}" type="hidden"/>
<div class="sel-box" style="display: none;">
</div>
<div class="panel info-box">
    <div class="panel-body">
        <form:form modelAttribute="courseInfoDto" id="queryForm" onkeydown="bindEnter(event)">
            <c:if test="${relaType == '01'}">
                <form:hidden path="relaFlag" value="1"></form:hidden>
            </c:if>
            <c:if test="${relaType == '02'}">
                <form:hidden path="eventRelaFlag" value="1"></form:hidden>
            </c:if>
            <div class="row" style="display: flex;align-items: center;margin-bottom: 10px;">
                <label class="col-md-1 control-label">课程名称</label>
                <div class="col-md-3">
                    <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control"/>
                </div>
                <label class="col-md-1 control-label">发布时间</label>
                <div class="col-md-3 daterange">
                    <form:input path="releaseTime" placeholder="请输发布时间" cssClass="form-control"/>
                </div>
            </div>
            <div class="col-md-12" style="text-align: right">
                <span id="btnClear" class="btn btn-default" style="margin-right: 10px;">清空条件</span>
                <span id="btnQuery" class="btn btn-primary" style="margin-right: 10px;">查询</span>
                <span id="btnSubmit" class="btn btn-primary">确定选中</span>
            </div>
        </form:form>
        <div id="searchPanel" class="col-md-12" style="">
            <div class="table-primary">
                <e:grid id="tableAll"
                        action="/schoolCourse/queryRepCaseInfoList?relaFlag=${relaType == '01' ? '1' : '0'}&eventRelaFlag=${relaType == '02' ? '1' : '0'}"
                        cssClass="table table-striped table-hover">
                    <e:gridColumn label="选择" renderColumn="rcIndex" orderable="false"
                                  cssClass="text-center" cssStyle="width:5%"/>
                    <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%"/>
                    <e:gridColumn label="课程类型" renderColumn="courseType" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%"/>
                    <e:gridColumn label="适用板块" renderColumn="applyPlate" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%"/>
                    <e:gridColumn label="适用人群" renderColumn="applyPerson" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%"/>
                    <e:gridColumn label="总播放量" displayColumn="amount" orderable="false"
                                  cssClass="text-center" cssStyle="width:8%;"/>
                    <e:gridColumn label="课程讲师" renderColumn="teacher" orderable="false"
                                  cssClass="text-center" cssStyle="width:10%;"/>
                    <e:gridColumn label="发布时间" displayColumn="releaseTime" orderable="false" cssClass="text-center"
                                  cssStyle="width:10%"/>
                </e:grid>
            </div>
        </div>
    </div>
    </form>
</div>
</body>
</html>
