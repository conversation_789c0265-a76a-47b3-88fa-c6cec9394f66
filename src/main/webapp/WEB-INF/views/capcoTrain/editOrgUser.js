$(document).ready(function () {
    $("#btnSave").bind("click", function () {
        if ($("#schUserInfoForm").valid()) {
            if (timeContrast()) {
                ajaxSubmitForm('/schUserManage/orgUserSave', '', function (res) {
                    if (res == '1') {
                        popMsg("保存成功");
                        closeWinCallBack();
                    } else {
                        popMsg("操作失败，请稍后再试");
                    }
                })
            }
        }
    });

    $("#schUserInfoForm").validate({
        rules: {
            "nickName": {
                required: true,
                maxlength: 45,
            },
            "phone": {
                required: true,
                checkPhone: true,
                remote: {
                    url: contextPath + "/schUserManage/checkContractUserOnly",
                    type: "post",
                    async: false,
                    data: {
                        contractId: function () {
                            return $("#contractId").val();
                        },
                        phone: function () {
                            return $("#phone").val();
                        },
                        id:function () {
                            return $("#id").val();
                        },
                    }
                }
            },
            "startTime": {
                required: true,
            },
            "endTime": {
                required: true,
            }
        },
            messages: {
                "phone": {
                    required: "必须填写",
                    checkPhone: "*请输入正确的手机号！",
                    remote: "手机已被绑定！"
                },
            }
    });

    $.validator.addMethod("checkPhone", function (value, element) {
        var length = value.length;
        return this.optional(element) || length == 11;
    }, "请正确填写您的手机号码");
});

function timeContrast() {
    var startTime = $("#startTime").val();
    var endTime = $("#endTime").val();
    if (new Date(endTime).getTime() < new Date(startTime).getTime()) {
        popMsg("截止日期需大于起始日期！");
        return false;
    } else {
        return true;
    }
}

