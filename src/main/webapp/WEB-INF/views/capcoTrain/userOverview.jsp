<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="schUserForm" modelAttribute="schUserInfoDto" cssClass="form-horizontal">
            <form:hidden path="orgId"/>
            <div class="row">
                    <label class="col-md-1 control-label">内部合同编码</label>
                    <div class="col-md-3">
                        <form:input path="contractNo" placeholder="请输入内部合同编码" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">授权起始日</label>
                    <div class="col-md-3 daterange">
                        <form:input path="startTime" placeholder="请输入授权起始日" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">授权截止日</label>
                    <div class="col-md-3 daterange">
                        <form:input path="endTime" placeholder="请输入授权截止日" cssClass="form-control" autocomplete="off"/>
                    </div>
            </div>

            <div class="row">
                    <label class="col-md-1 control-label">姓名</label>
                    <div class="col-md-3">
                        <form:input path="nickName" placeholder="请输入姓名" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">手机号</label>
                    <div class="col-md-3">
                        <form:input path="phone" placeholder="请输入手机号" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label">是否授权</label>
                    <div class="col-md-3">
                        <input id="isAccredit" type="text" class="t-select" json-data='${yesOrNo}' placeholder="是否授权"/>
                        <input name="isAccredit" type="hidden" placeholder="请选择" />
                    </div>
            </div>
        </form:form>
        <div class="row" style="text-align: right">
            <span class="btn btn-primary" style="margin-right: 10px;" id="btnSearch">查询</span>
        </div>
        <div class="row">
            <label style="font-size: 16px;font-weight: bold;margin-left: 10px;">用户信息</label>
        </div>

        <div class="row">
            <e:grid id="tableAll" action="/schUserManage/queryContractUserInfo?orgId=${schUserInfoDto.orgId}" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="rcIndex" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="内部合同编码" displayColumn="contractNo" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="姓名" displayColumn="nickName" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="手机号" displayColumn="phone" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="授权起始日期" displayColumn="startTime" orderable="true" orderColumn="startTime" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="授权截止日期" displayColumn="endTime" orderable="true" orderColumn="endTime" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="是否授权" renderColumn="renderColumnIsAccredit" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOption" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
            </e:grid>
            <%--<table id="userTable" class="table table-bordered no-margin"--%>
                   <%--style="text-align: center;border-color: #D7D7D7;margin-top: 10px !important;width:100%">--%>
                <%--<thead>--%>
                <%--<th class="text-center table-th" width="10%">序号</th>--%>
                <%--<th class="text-center table-th" width="10%">内部合同编码</th>--%>
                <%--<th class="text-center table-th" width="10%">姓名</th>--%>
                <%--<th class="text-center table-th" width="10%">手机号</th>--%>
                <%--<th class="text-center table-th" width="10%">授权起始日期</th>--%>
                <%--<th class="text-center table-th" width="10%">授权截止日</th>--%>
                <%--<th class="text-center table-th" width="10%">是否授权</th>--%>
                <%--<th class="text-center table-th" width="10%">操作</th>--%>
                <%--</thead>--%>
                <%--<tbody id="userBody">--%>
                <%--</tbody>--%>
            <%--</table>--%>
        </div>
    </div>
</div>
</body>
</html>
