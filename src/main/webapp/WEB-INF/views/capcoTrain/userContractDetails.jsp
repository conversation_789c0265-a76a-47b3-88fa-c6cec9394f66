<%--
  Created by IntelliJ IDEA.
  User: wangwennan
  Date: 2021/8/19
  Time: 10:45
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>易董 云端管理平台</title>
  <e:base/>
  <e:js/>
</head>
<body>
<div class="panel">
  <div class="panel-body">
      <div style="width:1200px;margin:auto">
        <div class="row" style="margin-bottom: 15px" >
          <span style="padding-left:15px;font-size:16px">用户：${schUserContractDetailsDto.nickName}</span>
        </div>
        <div class="row">
          <span style="padding-left:15px;font-size:16px;font-weight:bold">授权信息</span>
        </div>
        <div class="row">
          <div class="col-md-12">
            <e:grid id="tableAll" action="/schUserManage/getUserContractDetailsList?id=${schUserContractDetailsDto.id}" cssClass="table table-striped table-hover">

              <e:gridColumn label="序号" renderColumn="rcIndex" orderable="false" cssClass="text-center"
                            cssStyle="width:5%" />

              <e:gridColumn label="合同号" displayColumn="contractNo" orderable="false" cssClass="text-center"
                            cssStyle="width:10%" />

              <e:gridColumn label="公司名称" displayColumn="orgName" orderable="false"
                            cssClass="text-center" cssStyle="width:10%" />

              <e:gridColumn label="授权起始日" displayColumn="startTime" orderable="true" orderColumn="startTime"
                            cssClass="text-center" cssStyle="width:10%" />

              <e:gridColumn label="授权截止日" displayColumn="endTime" orderable="true" orderColumn="endTime"
                            cssClass="text-center" cssStyle="width:10%" />

            </e:grid>
          </div>
        </div>
      </div>
  </div>
</div>
</body>
</html>
