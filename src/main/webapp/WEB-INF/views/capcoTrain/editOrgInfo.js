$(document).ready(function () {

    completeInit();

    //查询授权合同信息
    // contractDataInit();

    btnInit();

    $("#schOrgInForm").validate({
        rules: {
            "orgName": {
                required: true,
                maxlength: 30,
            }
        },
        messages: {}
    });
});

function completeInit() {
    $("#companyName").autocomplete(contextPath + "/schUserManage/companyQuery", {
        delay: 0,
        max: 1000,
        cacheLength: 0,
        extraParams: {
            companyStr: function () {
                return $("#companyName").val();
            }
        },
        formatItem: function (row) {
            return row.zhName + " (" + row.companyCode + ")";
        },
        formatResult: function (row) {
            return row.zhName + " (" + row.companyCode + ")";
        }
    }).result(function (event, data, formatted) {
        $("#companyId").val(data.companyId);
        if(!$("#orgName").val()){
            $("#orgName").val(data.zhName)
        }
    })
}

function contractDataInit() {
    ajaxTableQuery("tableAll", "/schUserManage/queryContractInfo",
        $("#schOrgInForm").formSerialize());
}

function editContractInfo(contractId) {
    parent.popWin('编辑授权信息', '/schUserManage/editContract', {contractId: contractId}, '98%', '98%', contractDataInit, '', contractDataInit);
}

function editOrgUser(contractId) {
    parent.popWin('机构用户设置', '/schUserManage/orgUserSettingInit', {contractId: contractId}, '98%', '98%', contractDataInit, '', contractDataInit);
}


function btnInit() {
    $("#btnSave").bind("click", function () {
        if ($("#schOrgInForm").valid()) {
            ajaxSubmitForm('/schUserManage/orgInfoSave', '', function (res) {
                popMsg("保存成功");
                $("#orgId").val(res.orgId)
                // closeWinCallBack();
            })
        }
    });

    $("#addContract").bind("click", function () {
        if($("#orgId").val()){
            parent.popWin('新增授权信息', '/schUserManage/editContract', {contractId: '', orgId: $("#orgId").val()}, '98%', '98%', contractDataInit, '', contractDataInit);
        }else{
            parent.popMsg("请先保存机构信息")
        }

    });

    $("#userOverview").bind("click", function () {
        parent.popWin('机构用户总览', '/schUserManage/userOverview', {orgId: $("#orgId").val()}, '98%', '98%', contractDataInit, '', contractDataInit);
    })
}

function delContractInfo(contractId){
    popConfirm("确认删除",function(){
        ajaxData("/schUserManage/delContractInfo", {contractId}, function (res) {
            parent.popMsg("删除成功");
            contractDataInit();
        });
    })

}

function renderColumnOption(data, type, row, meta) {
    var str = '<span><i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑合同信息" onclick="editContractInfo(\'' + data.contractId + '\')"></i>'
    + '<i class="fa fa-users" style="cursor: pointer;margin: 0 5px;" title="编辑机构用户" onclick="editOrgUser(\'' + data.contractId + '\')"></i>'
    + '<i class="fa fa-trash" style="cursor: pointer;margin: 0 5px;" title="删除机构信息" onclick="delContractInfo(\'' + data.contractId + '\')"></i></span>';
    return str;
}

function rcIndex(data, type, row, meta) {
    return meta.row + 1;
}
