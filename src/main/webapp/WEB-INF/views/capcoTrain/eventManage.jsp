<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:js/>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="schEventDto" id="queryForm" onkeydown="bindEnter(event)">
            <div class="row" style="display: flex;align-items: center;">
                <label class="col-md-1 control-label" style="text-align: right;">事件名称</label>
                <div class="col-md-3">
                    <form:input cssClass="form-control" path="eventTitle" placeholder="事件名称"/>
                </div>
            </div>
            <div class="row" style="margin-top: 15px;">
                <div class="col-md-12 text-right">
                    <span id="btnQuery" class="btn btn-primary">查询</span>
                    <span id="addCourse" class="btn btn-primary">新增事件</span>
                    <span id="btnClear" class="btn btn-default">清空全部</span>
                    <span class="btn btn-primary" onclick="refreshEventAndColumn()" style="display: none">刷新专栏和事件</span>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/schoolCourse/getSchEventInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="rcIndex" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="事件标题" displayColumn="eventTitle" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="事件日期" displayColumn="eventDate" orderable="true" orderColumn="eventDate" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <%--<e:gridColumn label="事件描述" displayColumn="eventContent" orderable="false" cssClass="text-left;"/>--%>
                <e:gridColumn label="关联课程" renderColumn="renderColumnCourseName" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="更新人" displayColumn="updateUserName" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="更新时间" displayColumn="updateTimeStr" orderable="true" orderColumn="updateTimeStr" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
