<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .courseInfo{
            display: inline-block;
            padding: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div style="background-color: #FFFFFF;height: 50px;padding-left: 20px">
    <div class="courseInfo">已选：</div>
    <div class="courseInfo" id="courseInfoSelect">${schoolHomepageDto.courseTypeName}</div>
    <input type="hidden" id = "type" value="${schoolHomepageDto.type}">
    <input type="hidden" id ="id" value="${schoolHomepageDto.id}">
    <input type="hidden" id ="sort" value="${schoolHomepageDto.sort}">
    <input type="hidden" id ="courseId" value="${schoolHomepageDto.courseId}">
    <input type="hidden" id="releaseFlag" value="">
    <span id="saveCourseInfo" class="btn btn-primary" style="float: right;margin: 10px">确定选中</span>
</div>
<div class="panel">
    <div class="panel-heading" style="padding-bottom: 0;">
        <form:form modelAttribute="courseInfoDto" id="queryForm" onkeydown="bindEnter(event)">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">课程名称</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">课程类型</label>
                    <div class="col-md-3">
                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' />
                        <input name="courseType" type="hidden" placeholder="请选择课程类型" />
                    </div>
                    <div class="col-md-3">
                        <span id="btnQuery" class="btn btn-primary" style="float: right">查询</span>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body" style="padding-top: 0;">
        <div class="row">
            <e:grid id="tableAll" action="/schoolCourse/queryRepCaseInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:20%" />
                <e:gridColumn label="课程名称" displayColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="课程类型" renderColumn="courseType" orderable="false"
                              cssClass="text-center" cssStyle="width:20%" />
                <e:gridColumn label="总播放量" displayColumn="amount" orderable="false"
                              cssClass="text-center" cssStyle="width:20%;" />
                <e:gridColumn label="是否发布" displayColumn="releaseFlag" orderable="false" cssClass="text-center"
                              cssStyle="width:20%" />
                <e:gridColumn label="" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:0%" />

            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
