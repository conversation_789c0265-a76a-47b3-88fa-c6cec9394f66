$(document).ready(function () {
    //关闭
    $("#btnClose").bind('click',function () {
        closeWin();
    })
    $("#btnClose2").bind('click',function () {
        closeWin();
    })
    //确定
    $("#btnSave").bind('click',function () {
        var param = {
            type : $("#type").val(),
            id : $("#id").val(),
            courseId : $("input[name='itemName']:checked").val(),
            sort : $("#sort").val(),
        }
        ajaxData("/schoolIndexConfig/saveConfigCourseType",param,function (data) {
            closeWinCallBack(data);
        })
    })
    //确定
    $("#btnSave2").bind('click',function () {
        var param = {
            type : $("#type").val(),
            id : $("#id").val(),
            courseId : $("#courseId").val(),
            sort : $("#sort").val(),
        }
        ajaxData("/schoolIndexConfig/saveConfigCourseType",param,function (data) {
            closeWinCallBack(data);
        })
    })


})
function courseSpecia(id,itemName) {
    $("#courseId").val(id)
    $("#courseSpecia").html(itemName)
}