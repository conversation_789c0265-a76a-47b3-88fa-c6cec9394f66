$(document).ready(function () {
    //下拉初始化
    tSelectInit()
    $('#btnQuery').click(function () {
        search();
    });
    $("#saveCourseInfo").click(function () {
        saveCourseInfo();
    })

})
function dataTableLoadAfter() {
    clickCourse()
}

function search() {
    ajaxTableQuery("tableAll", "/schoolCourse/queryRepCaseInfoList",
        $("#queryForm").formSerialize());
}

//下拉初始化
function tSelectInit() {
    var courseTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch:true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, courseTypeSelectOptions);

}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
};
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
};
// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
function columnOperation(data, type, row, meta) {
    var str = '<input type="hidden" value="'+data.id+'">';
    return str;
}
function editCourseInfoSelect(id,courseName) {
    $("#courseId").val(id)
    $("#courseInfoSelect").html(courseName)
}

function saveCourseInfo() {
    var releaseFlag = $("#releaseFlag").val()
    if (releaseFlag=="否"){
        popMsg("当前选中课程未发布，请先去课程列表中发布改课程")
    }else {
        var param = {
            type : $("#type").val(),
            id : $("#id").val(),
            courseId : $("#courseId").val(),
            sort : $("#sort").val(),
        }
        ajaxData("/schoolIndexConfig/saveConfigCourseType",param,function (data) {
            closeWinCallBack(data);
        })
    }
}
function courseType(data, type, row, meta) {
    var str = data.courseType;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }
    return str;
}
function clickCourse() {
    $("#tableAll").find("tbody").find("tr").each(function (n,obj) {
        $(obj).css("cursor","pointer");
        $(obj).attr("onclick","courseSelect(this)");
    })
}

function courseSelect(obj) {
    var courseName = $(obj).find("td").eq(1).html()
    var courseId = $(obj).find("td").eq(5).find("input").val()
    var releaseFlag = $(obj).find("td").eq(4).html()
    $("#courseId").val(courseId)
    $("#courseInfoSelect").html(courseName)
    $("#releaseFlag").val(releaseFlag)

}

