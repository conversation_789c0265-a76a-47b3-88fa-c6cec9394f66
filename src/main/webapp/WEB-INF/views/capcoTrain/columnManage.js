$(document).ready(function () {
    // 清空全部
    $("#btnClear").bind('click', function () {
        // popConfirm('确认清空全部检索条件？', function () {
        clearAllOptions();
        // });
    });

    $("#addCourse").bind('click', function () {
        newColumn('');
    });

    $("#btnQuery").bind('click', function () {
        search();
    });
});

function rcIndex(data, type, row, meta) {
    return meta.row + 1;
}

function createTimeRender(data, type, row, meta) {
    let time = new Date(data.createTime);
    var y = time.getFullYear();
    var m = time.getMonth() + 1;
    m = parseInt(m) < 10 ? '0' + m : m;
    var d = time.getDate();
    d = parseInt(d) < 10 ? '0' + d : d;
    return y + '-' + m + '-' + d
}

function renderColumnCourseName(data, type, row, meta) {
    var courseName = "";
    if(data.courseName){
        courseName = data.courseName;
    }
    if (courseName.length > 12) {
        courseName = courseName.substring(0, 12) + "...";
    }
    return '<span style="text-overflow: ellipsis;white-space: nowrap;overflow: hidden;display: block;" title="' + data.courseName + '">' + courseName + '</span>';
}

function renderColumnOperation(data, type, row, meta) {
    var edit = '<span title="编辑" onclick="newColumn(\'' + data.columnId + '\')"><i style="cursor:pointer;margin-right: 6px" class="fa fa-edit"></i></span>';
    var del = '<span title="删除" onclick="deleteColumn(\'' + data.columnId + '\')"><i style="cursor:pointer" class="fa fa-trash-o"></i></span>';
    return edit + del;
}

function newColumn(columnId) {
    var param = {
        columnId: columnId
    };
    parent.popWin("专栏编辑页", "/schoolCourse/schColumnDetailInit", param, "90%", "85%", popCallBack, null, popCallBack);
}

function popCallBack(paraWin, paraCallBack) {
    ajaxTableReload("tableAll", false);
}

function deleteColumn(columnId) {
    var param = {
        columnId: columnId
    };
    popConfirm('确定删除？', function () {
        ajaxData("/schoolCourse/deleteColumn", param, function (data) {
            if(data == '1'){
                popMsg("删除成功!");
                ajaxTableReload("tableAll", false);
            }else{
                popMsg("该专栏已有关联课程，请手动删除!");
            }
        });
    });
}

function search() {
    ajaxTableQuery("tableAll", "/schoolCourse/getSchColumnInfoList", $("#queryForm").formSerialize());
}

//清空
function clearAllOptions() {
    $("#columnName").val('');
    // 清空表排序，重新制表
    var table = $("#tableAll");
    //页面默认显示10条
    table.DataTable().page.len(10);
    table.DataTable().order([0, '']).draw();
    ajaxTableQuery("tableAll", "/schoolCourse/getSchColumnInfoList", $("#queryForm").formSerialize());
};
