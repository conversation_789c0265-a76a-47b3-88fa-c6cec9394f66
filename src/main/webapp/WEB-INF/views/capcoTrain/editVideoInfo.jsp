<%--
/***************************************************************
* 程序名 : editVideoInfo.jsp
* 日期  :  2019-6-28
* 作者  :  
* 模块  :  运维管理
* 描述  :  视频编辑页
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:base />
<e:js />
</head>
<body>
    <div class="panel">
        <div class="panel-body">
            <form:form  modelAttribute="schVideoInfo" id="editForm">
                <form:hidden path="id"/>
                <div class="row">
                    <label class="col-xs-3 control-label label-padding" style="width: 90px;margin-top: 3px">视频类型</label>
                    <div class="col-xs-7">
                        <form:radiobutton path="videoType" value="1" id="videoType1" />
                        <label for="videoType1">操作演示</label>
                        <form:radiobutton path="videoType" value="2" id="videoType2" cssStyle="margin-left:6px" />
                        <label for="videoType2">课程视频</label>
                    </div>
                </div>
                <div class="row" style="margin-top: 40px">
                    <div class="col-md-offset-10 col-md-2" align="center">
                        <input type="button" id="btnSave" class="btn btn-primary" value="保存" />
                        <input type="button" id="btnClose" class="btn btn-default" value="关闭" />
                    </div>
                </div>
            </form:form>
        </div>
    </div>
</body>
<style>
.red_star{color:red;font-size:9px}
</style>
</html>
