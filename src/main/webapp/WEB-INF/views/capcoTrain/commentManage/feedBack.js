
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // dateInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        document.getElementById("queryForm").reset();
        tSelectInit();
        ajaxTableQuery("tableAll", "/feedBack/getFeedBackList",
            "");
    });


    $('#addUser').click(function () {
        addUser();
    });
});
function search() {
    ajaxTableQuery("tableAll", "/feedBack/getFeedBackList",
        $("#queryForm").formSerialize());
}

function callBackAddUser() {
    ajaxTableReload("tableAll",false);
}

function callBack(){
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'labelValue',
        name: 'labelName',
        value: 'labelValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#replyFlag').tselectInit(null, teaSelectOptions);
    $('#commentType').tselectInit(null, teaSelectOptions);
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function columnOperation(data, type, row, meta) {
    var str = '';

    if (document.getElementById("repleyAuth")) {
        str += '<a style="cursor: pointer;margin: 0 5px;" title="回复" onclick="repleyCommentInfo(\'' + data.id + '\',\'05\')">回复</a>';
    }

    if (document.getElementById("repleyHistoryAuth")) {
        if (str !== '') {
            str += '|';
        }
        str += '<a style="cursor: pointer;margin: 0 5px;" title="回复历史" onclick="repleyCommentHistoryInfo(\'' + data.id + '\',\'05\')">回复历史</a>';
    }

    if (document.getElementById("delAuth")) {
        if (str !== '') {
            str += '|';
        }
        str += '<a style="cursor: pointer;margin: 0 5px;" title="删除" onclick="delUserInfo(\'' + data.id + '\')">删除</a>';
    }

    if (!str) {
        str = '-';
    }
    return str;
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function delUserInfo(id) {
    parent.popConfirm("确认删除?", function () {
        var param ={
            id:id,
            status: '99'
        };
        ajaxData('/bunchPlanting/auditStatus', param, callBackAddUser);
    });
}

function repleyCommentInfo(id,commentType) {
    var param ={
        id:id,
        commentType:commentType
    };
    parent.popWin('回复', '/bunchPlanting/replyCommentInit', param, '50%', '350px',callBackAddUser,'',callBackAddUser);
}

function commentTypeColumn(data, type, row, meta) {
    if( data.commentType == '05'){
        return '意见反馈';
    }else {
        return '互动交流';
    }
}

function replyFlagColumn(data, type, row, meta) {
    if( data.replyFlag == '1'){
        return '是';
    }else {
        return '否';
    }
}

function nickNameColumn(data, type, row, meta) {
    if( data.nickName == undefined){
        return '';
    }else {
        return data.nickName;
    }
}

function batchUpdateUserCenterData(){
    ajaxData("/schUserManage/batchUpdateUserCenterData", '', function(){
        parent.parent.popMsg("刷新完成")
    });
}

function repleyCommentHistoryInfo(id,commentType) {
    var param ={
        id:id,
        commentType:commentType
    };
    parent.popWin('历史回复记录', '/bunchPlanting/replyHistoryInit', param, '700px', '70%', callBack,'',callBack);
}

