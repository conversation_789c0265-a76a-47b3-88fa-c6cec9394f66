<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:js />
</head>
<body>
<div class="panel">
    <ul id="myTab" class="nav nav-tabs">
        <sec:authorize access="hasAuthority('RES_SCH_FEED_BACK_INIT_AUTHORITY_3')" >
            <li class="active"><a href="#tab1" data-toggle="tab" style="font-weight: 600" onclick="toPage('feedBack')">我要提问</a></li>
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_BUNCH_PLANT_INIT_AUTHORITY_3')" >
            <li><a href="#tab2" style="font-weight: 600" data-toggle="tab" onclick="toPage('bunchPlant')">点播评论</a></li>
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_OFF_LINE_INIT_AUTHORITY_3')" >
            <li><a href="#tab3" style="font-weight: 600" data-toggle="tab" onclick="toPage('offlineComment')">线下培训</a></li>
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_LIVE_COMMENT_INIT_AUTHORITY_3')" >
            <li><a href="#tab3" style="font-weight: 600" data-toggle="tab" onclick="toPage('liveComment')">直播评论</a></li>
        </sec:authorize>
    </ul>
    <div class="panel-body">
        <div class="tab-content">
            <sec:authorize access="hasAuthority('RES_SCH_FEED_BACK_INIT_AUTHORITY_3')" >
                <div class="tab-pane fade in active" id="tab1">
                    <iframe id="feedBack" scrolling="no" style="width:100%;border:0px;"></iframe>
                </div>
            </sec:authorize>

            <sec:authorize access="hasAuthority('RES_SCH_BUNCH_PLANT_INIT_AUTHORITY_3')" >
                <div class="tab-pane fade in" id="tab2">
                    <iframe id="bunchPlant" scrolling="no" style="width:100%;border:0px;"></iframe>
                </div>
            </sec:authorize>

            <sec:authorize access="hasAuthority('RES_SCH_LIVE_WATCH_INIT_AUTHORITY_3')" >
                <div class="tab-pane fade in" id="tab3">
                    <iframe id="offlineComment" scrolling="no" style="width:100%;border:0px;"></iframe>
                </div>
            </sec:authorize>

            <sec:authorize access="hasAuthority('RES_SCH_LI1VE_WATCH_INIT_AUTHORITY_3')" >
                <div class="tab-pane fade in" id="tab4">
                    <iframe id="liveComment" scrolling="no" style="width:100%;border:0px;"></iframe>
                </div>
            </sec:authorize>
        </div>
    </div>
</div>

</body>
</html>
