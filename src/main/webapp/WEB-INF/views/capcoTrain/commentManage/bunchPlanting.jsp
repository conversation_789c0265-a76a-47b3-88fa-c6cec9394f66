<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .ov{
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="schCommentDto" id="queryForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="text-align: center">课程名称</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label" style="text-align: center">评论人员</label>
                    <div class="col-md-3">
                        <form:input path="nickName" placeholder="请输入评论人员" cssClass="form-control" autocomplete="off"/>
                    </div>
                    <label class="col-md-1 control-label" style="text-align: center">回复状态</label>
                    <div class="col-md-3">
                        <input id="replyFlag" type="text" class="t-select" style="color: #686868 " json-data='${whetherList}'/>
                        <input name="replyFlag" type="hidden"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12" >
                    <div class="col-md-6 text-left">
                        <label class="col-md-2 control-label" style="text-align: center;padding-left: 0px">审核状态</label>
                        <div class="col-md-6" style="padding-left: 4px;padding-right: 5px">
                            <input id="status" type="text" class="t-select" json-data='${checkStatusList}' />
                            <input name="status" type="hidden" placeholder="审核状态" />
                        </div>
                    </div>
                    <div class="col-md-6  text-right">
                        <sec:authorize access="hasAuthority('RES_SCH_BUNCH_PLANT_LIST_AUTHORITY_3')" >
                            <input type="button" id="btnQuery" class="btn btn-primary" value="查询">
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空</span>
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_SCH_AUDIT_STATUS_INIT_AUTHORITY_3')" >
            <%--审核权限--%>
            <input type="hidden" id="checkAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_REPLY_STATUS_INIT_AUTHORITY_3')" >
            <%--回复权限--%>
            <input type="hidden" id="repleyAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_REPLY_HISTROY_INIT_AUTHORITY_3')" >
            <%--回复历史权限--%>
            <input type="hidden" id="repleyHistoryAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_SCH_UPDATE_AUDIT_STATUS_AUTHORITY_3')" >
            <%--删除权限--%>
            <input type="hidden" id="delAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/bunchPlanting/getBunchPlantingList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="课程名称" renderColumn="courseNameColumn" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="评论人员" renderColumn="nickNameColumn" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />

                <e:gridColumn label="评论内容" displayColumn="content" orderable="false"
                              cssClass="text-center ov" cssStyle="width:10%" />

                <e:gridColumn label="评论时间" displayColumn="createTime" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />

                <e:gridColumn label="审核状态" renderColumn="statusColumn" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="审核人员" renderColumn="checkUserNameColumn" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否回复" renderColumn="replyFlagColumn" orderable="false"
                              cssClass="text-center" cssStyle="width:7%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:16%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>