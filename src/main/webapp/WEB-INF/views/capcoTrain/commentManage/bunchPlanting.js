//@ sourceURL=userManage.js
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    // dateInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        tSelectInit();
        ajaxTableQuery("tableAll", "/bunchPlanting/getBunchPlantingList",
            "");
    });


    $('#addUser').click(function () {
        addUser();
    });
});
function search() {
    ajaxTableQuery("tableAll", "/bunchPlanting/getBunchPlantingList",
        $("#queryForm").formSerialize());
}

function callBackAddUser() {
    ajaxTableReload("tableAll",false);
}

function callBack(){
}

//下拉初始化
function tSelectInit() {
    var teaSelectOptions = {
        id: 'labelValue',
        name: 'labelName',
        value: 'labelValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#status').tselectInit(null, teaSelectOptions);
    $('#replyFlag').tselectInit(null, teaSelectOptions);

}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function columnOperation(data, type, row, meta) {
    var str = '';

    if (data.status === '0' && document.getElementById("checkAuth")) {
        str += '<a style="cursor: pointer;margin: 0 5px;" title="审核" onclick="checkCommentInfo(\'' + data.id + '\',\'' + data.status + '\')">审核</a>';
    }

    if (document.getElementById("repleyAuth")) {
        str += '<a style="cursor: pointer;margin: 0 5px;" title="回复" onclick="repleyCommentInfo(\'' + data.id + '\')">回复</a>';
    }

    if (document.getElementById("repleyHistoryAuth")) {
        if (str !== '') {
            str += '|';
        }
        str += '<a style="cursor: pointer;margin: 0 5px;" title="回复历史" onclick="repleyCommentHistoryInfo(\'' + data.id + '\')">回复历史</a>';
    }

    if (document.getElementById("delAuth")) {
        if (str !== '') {
            str += '|';
        }
        str += '<a style="cursor: pointer;margin: 0 5px;" title="删除" onclick="delCommentInfo(\'' + data.id + '\')">删除</a>';
    }

    if (!str) {
        str = '-';
    }

    return str;
}


// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function courseNameColumn(data, type, row, meta) {
    if( data.courseName == undefined){
        return '--';
    }else {
        return data.courseName;
    }
}

function nickNameColumn(data, type, row, meta) {
    if( data.nickName == undefined){
        return '';
    }else {
        return data.nickName;
    }
}

function checkCommentInfo(id,status){
    var param ={
        id:id,
        status:status
    };
    parent.popWin('审核', '/bunchPlanting/auditStatusInit', param, '40%', '350px', callBackAddUser,'',callBackAddUser);
}

function delCommentInfo(id) {
    parent.popConfirm("确认删除?", function () {
        var param ={
            id:id,
            status: '99'
        };
        ajaxData('/bunchPlanting/auditStatus', param, callBackAddUser);
    });
}

function repleyCommentHistoryInfo(id) {
    var param ={
        id:id
    };
    parent.popWin('历史回复记录', '/bunchPlanting/replyHistoryInit', param, '700px', '70%', callBack,'',callBack);
}

function repleyCommentInfo(id) {
    var param ={
        id:id
    };
    parent.popWin('回复', '/bunchPlanting/replyCommentInit', param, '50%', '350px', callBackAddUser,'',callBackAddUser);
}

function replyFlagColumn(data, type, row, meta) {
    if( data.replyFlag == '1'){
        return '是';
    }else {
        return '否';
    }
}

function checkUserNameColumn(data, type, row, meta) {
    if( data.checkUserName == undefined){
        return '--';
    }else {
        return data.checkUserName;
    }
}

function statusColumn(data, type, row, meta) {
    if( data.status == '0'){
        return '待审核';
    }else if(data.status == '1'){
        return '通过';
    }else{
        return '未通过';
    }
}

function batchUpdateUserCenterData(){
    ajaxData("/schUserManage/batchUpdateUserCenterData", '', function(){
        parent.parent.popMsg("刷新完成")
    });
}
