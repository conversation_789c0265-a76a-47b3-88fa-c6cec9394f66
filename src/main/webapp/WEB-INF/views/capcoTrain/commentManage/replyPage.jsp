<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
    <style>
        .content{
            word-wrap: break-word;
            word-break: normal;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
    <form:form id="queryForm" modelAttribute="schCommentDto" >
        <input type="hidden" id="metaId" value="${schCommentDto.id}">
        <div class="col-xs-12" style="padding-top: 20px">
            <label class="col-xs-2 no-padding control-label" style="text-align: right">${str}:</label>
            <div class="col-xs-10 content" style="padding-right: 55px;">
                    ${schCommentDto.content1}
            </div>
        </div>
        <div class="col-xs-12" style="padding-top: 10px">
            <label class="col-xs-2 no-padding control-label" style="text-align: right"><font color="#FF0000">*</font>回复内容:</label>
            <div class="col-xs-10" style="padding-right: 60px;" >
                <form:textarea path="content" id="content" cssClass="form-control" rows="4" maxlength="1000" />
            </div>
        </div>
        <div class="col-xs-12" style="margin-bottom: 10px">
            <label class="col-xs-2"></label>
            <label class="col-xs-10" id="remind" style="color: red;font-size: 14px">此回复将以短信形式发送给客户</label>
        </div>
    </form:form>
    <input type="hidden" id="commentType" value="${schCommentDto.commentType}">
    <div  style="padding-top:20px;text-align: center;">
        <input type="button" id="btnSave" style="margin-right: 15px;margin-top: 20px" class="btn btn-primary" value="保存" />
        <input type="button" id="btnClose" class="btn btn-default" style="margin-top: 20px" value="取消" />
    </div>
    </div>
</div>
</body>
</html>
