<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
    <script type="text/javascript">
    </script>
    <style>
        .content{
            word-wrap: break-word;
            word-break: normal;
        }
    </style>
</head>
<body style="background-color: #fff">
<div class="panel">
    <div class="panel-body">
        <form:form id="queryForm" modelAttribute="schCommentDto">
            <div class="row" style="margin-top: 30px">
                <div class="col-xs-12">
                    <label class="col-xs-3 control-label" style="text-align: right">评论内容:</label>
                    <div class="col-xs-9 content">
                            ${schCommentDto.content}
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top: 30px">
                <div class="col-xs-12">
                    <label class="col-xs-3 control-label" style="text-align: right;margin-top: 5px;">审核结果:</label>
                    <div class="col-xs-9">
                        <select name="status" class="form-control" style="width: 222px">
                            <option value="1">通过</option>
                            <option value="2">未通过</option>
                        </select>
                    </div>
                </div>
            </div>
            <input type="hidden" id="id" name="id" value="${schCommentDto.id}"/>
            <div class="row" style="text-align: center;margin-top: 30px">
                <button type="button" class="btn btn-primary" style="margin-top:20px" id="btnSave">确定</button>
                <button type="button" class="btn btn-default" style="margin-top:20px" id="btnClose">取消</button>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>