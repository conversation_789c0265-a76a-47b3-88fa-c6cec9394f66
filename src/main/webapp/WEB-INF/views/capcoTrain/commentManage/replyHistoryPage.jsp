<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
    <script type="text/javascript">
    </script>
    <style>
        .content{
            word-wrap: break-word;
            word-break: normal;
        }
        .prog{

        }
    </style>
</head>
<body>
    <div class="panel">
        <div class="panel-body">
            <div>
                <div style="width: 12%;float: left;">
                    <span style="font-size: 16px;font-weight: bold;">${str}:</span>
                </div>
                <div style="font-size: 14px;width: 80%;display: inline-block;padding-top: 2px;" class="content">${content}</div>
            </div>
            <div style="margin-top: 16px;font-weight: bold">历史回复</div>
            <c:forEach var="item" items="${schCommentDto}" varStatus="status">
                <div style="font-size: 16px;padding-bottom: 10px">
                    <span >${item.nickName}</span>&nbsp;于&nbsp;<span>${item.createTime}</span>
                    &nbsp;回复:
                    <div>
                        <textarea readonly cols="65">${item.content}</textarea>
                    </div>
                </div>
            </c:forEach>
            <div style="margin-top: 40px;text-align: center;">
                <input type="button" id="btnClose" class="btn btn-default" value="关闭" />
            </div>
        </div>
    </div>
</body>
</html>