$(document).ready(function () {
    $('#btnSave').click(function () {
        if($('#content').val().replace(/(^\s*)|(\s*$)/g, "")==""){
            popMsg("回复不能为空")
        }else {
            var param={
                metaId:$("#metaId").val(),
                content:$("#content").val(),
                commentType:$("#commentType").val()
            }
            ajaxData("/bunchPlanting/saveReplyComment", param, function (res) {
                closeWinCallBack();
            })
        }
    });

    $('#btnClose').click(function () {
        closeWinCallBack();
    });

    hiddenRemind();
})

function hiddenRemind(){
    if($('#commentType').val() != '05'){
        $('#remind').hide()
    }
}