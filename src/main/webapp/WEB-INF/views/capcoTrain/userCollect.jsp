<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base />
    <e:js />
</head>
<body>
<div class="panel">
    <div class="panel-body">
    <input type="hidden" id="userId" value="${userId}">
        <div class="row">
            <ul id="myTab" class="nav nav-tabs">
                <li class="active"><a id="collectTab1" href="#tab1" data-toggle="tab">课程</a></li>
                <li><a id="collectTab2" href="#tab2" data-toggle="tab">法规</a></li>
                <li><a id="collectTab3" href="#tab3" data-toggle="tab">违规案例</a></li>
                <li><a id="collectTab4" href="#tab4" data-toggle="tab">资本运作</a></li>
                <li><a id="collectTab5" href="#tab5" data-toggle="tab">活动课件</a></li>
            </ul>
        </div>
        <div class="tab-content">
<%--            课程--%>
            <div class="tab-pane in active" id="tab1">
                <div class="row" style="margin-left: auto; margin-right: auto;">
                    <e:grid id="table_id1" action="/schUserManage/getUserColInfo?listFlag=1&userId=${userId}"
                            cssClass="table table-striped table-hover">
                        <e:gridColumn label="课程名" displayColumn="courseName" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                        <e:gridColumn label="讲师/嘉宾" displayColumn="teacher" orderable="false"
                                      cssClass="text-center" cssStyle="width:25%"/>
                        <e:gridColumn label="课程类型" renderColumn="courseType" orderable="false"
                                      cssClass="text-center" cssStyle="width:25%"/>
                        <e:gridColumn label="创建时间" displayColumn="createTime" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                    </e:grid>
                </div>
            </div>
<%--    法规--%>
            <div class="tab-pane " id="tab2">
                <div class="row" style="margin-left: auto; margin-right: auto;">
                    <e:grid id="table_id2" action="/schUserManage/getUserColInfo?listFlag=2&userId=${userId}"
                            cssClass="table table-striped table-hover">
                        <e:gridColumn label="标题" renderColumn="lawsName" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                        <e:gridColumn label="颁布时间" displayColumn="published" orderable="false"
                                      cssClass="text-center" cssStyle="width:25%"/>
                        <e:gridColumn label="法律位阶" displayColumn="lawClassText" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                        <e:gridColumn label="发文单位" renderColumn="lawSourceText" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                    </e:grid>
                </div>
            </div>
<%--    违规案例--%>
            <div class="tab-pane " id="tab3">
                <div class="row" style="margin-left: auto; margin-right: auto;">
                    <e:grid id="table_id3" action="/schUserManage/getUserColInfo?listFlag=3&userId=${userId}"
                            cssClass="table table-striped table-hover">
                        <e:gridColumn label="标题" renderColumn="violateTitle" orderable="false" cssClass="text-left"
                                      cssStyle="width:25"/>
                        <e:gridColumn label="处理人" renderColumn="disciplinaryOrg" orderable="false"
                                      cssClass="text-center" cssStyle="width:25%"/>
                        <e:gridColumn label="处罚类型" renderColumn="disciplinaryTypeText" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                        <e:gridColumn label="处罚日期" renderColumn="punishTimeEnd" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                    </e:grid>
                </div>
            </div>
<%--    资本运作--%>
            <div class="tab-pane " id="tab4">
                <div class="row" style="margin-left: auto; margin-right: auto;">
                    <e:grid id="table_id4" action="/schUserManage/getUserColInfo?listFlag=4&userId=${userId}"
                            cssClass="table table-striped table-hover">
                        <e:gridColumn label="公司代码" displayColumn="comCode" orderable="false" cssClass="text-center"
                                      cssStyle="width:10%"/>
                        <e:gridColumn label="标题" displayColumn="title" orderable="false"
                                      cssClass="text-center" cssStyle="width:25%"/>
                        <e:gridColumn label="进程" renderColumn="progressText" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                    </e:grid>
                </div>
            </div>
<%--    活动课件--%>
            <div class="tab-pane " id="tab5">
                <div class="row" style="margin-left: auto; margin-right: auto;">
                    <e:grid id="table_id5" action="/schUserManage/getUserColInfo?listFlag=5&userId=${userId}"
                            cssClass="table table-striped table-hover" cssStyle="width:100%">
                        <e:gridColumn label="标题" displayColumn="attName" orderable="false" cssClass="text-left"
                                      cssStyle="width:25%"/>
                        <e:gridColumn label="大小" renderColumn="fileSize" orderable="false"
                                      cssClass="text-center" cssStyle="width:25%"/>
                    </e:grid>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
<style>
    .dataTables_scrollHead{
        width: 100% !important;
    }
</style>