$(document).ready(function () {

  $('#btnQuery').click(function () {
    search();
  });

  dataRangePickInit();

  $('#btnClear').click(function () {
    $("#queryForm")[0].reset();
    search();
  });
});

function dataRangePickInit(){
  dataRangePickerInit($('#endTime'), null, null, function () {}, function () {});
}

function search() {
  ajaxTableQuery("tableAll", "/schUserManage/getUserContractOverviewList",
      $("#queryForm").formSerialize());
}

function columnOperation(data, type, row, meta) {
  var str = '';
  str =' <i class="icon-search" style="cursor: pointer;margin: 0 5px; color: #00a0e9" title="查看详情" onclick="userContractInfo(\'' + data.nickName + '\',\'' + data.accId + '\',\'' + data.phone + '\',\'' + data.id + '\')"></i>';
  return str;
}

function count(data, type, row, meta) {
  var str = '';
  str =' <span style="cursor: pointer;margin: 5px 5px; color: #00a0e9" onclick="userContractDetails(\'' + data.id + '\',\'' + data.nickName + '\')">' + data.count + '</span>';
  return str;
}

function userContractInfo(nickName,accId,phone,id) {
  var param = {
    nickName : nickName,
    accId : accId,
    phone : phone,
    id : id
  };
  parent.popWin('用户详情', '/schUserManage/userContractInfoInit', param, '98%', '98%', popCallBack, null, popCallBack);
}

function userContractDetails(id,nickName) {
  var param = {
    id : id,
    nickName : nickName
  };
  parent.popWin('用户授权次数详情', '/schUserManage/userContractDetailsInit', param, '98%', '98%', popCallBack, null, popCallBack);
}

function popCallBack(paraWin, paraCallBack) {
  ajaxTableReload("tableAll", false);
}