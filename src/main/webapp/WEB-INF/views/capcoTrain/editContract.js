$(document).ready(function () {
    $("#btnSave").bind("click", function () {
        if ($("#schContractForm").valid()) {
            if (timeContrast()) {
                ajaxSubmitForm('/schUserManage/contractInfoSave', '', function (res) {
                    if (res == '1') {
                        popMsg("保存成功");
                        closeWinCallBack();
                    } else {
                        popMsg("已有授权数量大于共计授权数量！");
                    }
                })
            }
        }
    });

    $("#schContractForm").validate({
        rules: {
            "contractNo": {
                required: true,
                maxlength: 30,
            },
            "buyNum": {
                required: true,
            },
            "startTime": {
                required: true,
            },
            "endTime": {
                required: true,
            }
        },
        messages: {}
    });

    changeNum();

    $("#giveNum").val() ? '' : $("#giveNum").val("0");
});

function changeNum() {
    var buyNum = $("#buyNum").val() || '0';
    var giveNum = $("#giveNum").val() || '0';
    var total = parseInt(buyNum) + parseInt(giveNum);
    $("#totalNum").html(total);
}

function timeContrast() {
    var startTime = $("#startTime").val();
    var endTime = $("#endTime").val();
    if (new Date(endTime) < new Date(startTime)) {
        popMsg("截止日期需大于起始日期！");
        return false;
    } else {
        return true;
    }
}
