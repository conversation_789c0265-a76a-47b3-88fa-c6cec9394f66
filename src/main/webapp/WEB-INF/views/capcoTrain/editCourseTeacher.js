$(document).ready(function() {
    //查询
    $("#btnQuery").bind("click",function() {
        var teachName = $("#teachName").val().replace(/(^\s*)|(\s*$)/g, "");
        var teachOrg =  $("#teachOrg").val();
        ajaxTableQuery("table1", "/ebSchoolLecturerInfo/queryLecturerInfoList","teachName=" + teachName + "&teachOrg=" + teachOrg);
    });
    //清空
    $("#btnClear").bind("click", function () {
        document.getElementById("teachInfoForm").reset();
    });
    //选中
    $("#saveCourseTeacher").bind("click",function () {
        saveCourseTeacher();
    })
})

//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

function columnOperation(data, type, row, meta) {
    var str = '<input type="hidden" value="'+data.id+'">';
    return str;
}
function editCourseInfoSelect(id,courseName) {
    $("#courseInfoSelect").html(courseName)
    $("#teacherId").val(id)
}
function saveCourseTeacher() {
    var param = {
        teacherId : $("#teacherId").val(),
        sort : $("#sort").val(),
    }
    ajaxData("/schoolIndexConfig/saveConfigCourseTeacher",param,function (data) {
        closeWinCallBack(data);
    })
}
function dataTableLoadAfter() {
    clickCourseTeacher();
}
function clickCourseTeacher() {
    $("#table1").find("tbody").find("tr").each(function (n,obj) {
        $(obj).css("cursor","pointer");
        $(obj).attr("onclick","courseSelectTeacher(this)");
    })
}
function courseSelectTeacher(obj) {
    var courseName = $(obj).find("td").eq(1).html()
    var courseId = $(obj).find("td").eq(3).find("input").val()

    $("#courseInfoSelect").html(courseName)
    $("#teacherId").val(courseId)
}