<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .table-th {
            background-color: #37C8F4 !important;
            color: white;
            padding: 8px;
            text-align: center;
        }

        .table-td {
            padding: 8px;
            text-align: center;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
<div class="panel" style="min-height: 430px;padding: 40px 0px">
    <form:form modelAttribute="courseInfoDto" id="courseInfoDtoForm" autocomplete="off">
        <form:hidden path="columnId"/>
        <form:hidden path="columnSort"/>
        <div class="row" style="display: flex;align-items: center;">
            <label class="col-md-1 control-label" style="text-align: right;">专栏名称</label>
            <div class="col-md-6">
                <form:input cssClass="form-control" path="columnName" placeholder="请输入专栏名称" readonly="true"/>
            </div>
        </div>

        <div class="row" style="margin-top:20px">
            <label class="col-md-1 control-label" style="text-align: right;">历史课程</label>
            <div class="col-md-6">
                <input type="button" id="relaCourseBtn" class="col-md-2 btn btn-primary" value="关联历史课程"
                       onclick="showCourse()"/>
            </div>
        </div>
        <div class="row" style="margin-top:10px;margin-bottom: 20px;">
            <div class="col-md-11 col-md-offset-1">
                <table class="table-bordered" style=";border-color: #D7D7D7;width: 95%;">
                    <thead>
                    <tr>
                        <th class="table-th" width="10%">序号</th>
                        <th class="table-th" width="10%">课程ID</th>
                        <th class="table-th" width="30%">课程名称</th>
                        <th class="table-th" width="15%">版本号</th>
                        <th class="table-th" width="10%">视频时长</th>
                        <th class="table-th" width="10%">发布日期</th>
                        <th class="table-th" width="10%">操作</th>
                    </tr>
                    </thead>
                    <tbody id="versionBody">

                    </tbody>
                </table>
            </div>
        </div>
        <div class="row" align="center">
            <input type="button" id="btnClose" class="btn btn-primary" value="取消">
            <input type="button" id="btnSave" class="btn btn-primary" value="保存">
        </div>
    </form:form>

    <%--下拉模板--%>
    <script id="versionTypeSelectTpl" type="text/html">
        <select class="versionType" name="schCourseDtoList[{{n}}].versionType">
            <c:forEach items="${versionTypeList}" var="item">
                <option value="${item.value}" {{if versionType == ${item.value}}} selected{{/if}}>${item.label}</option>
            </c:forEach>
        </select>
    </script>
</div>
</body>
</html>
