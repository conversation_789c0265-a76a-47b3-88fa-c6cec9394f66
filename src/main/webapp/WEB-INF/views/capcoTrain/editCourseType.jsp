<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="panel" style="margin:0px;overflow-y: auto;">
    <c:if test="${schoolHomepageDto.type == '0'}">
        <div style="height: 490px">
            <input type="hidden" id = "type" value="${schoolHomepageDto.type}">
            <input type="hidden" id ="id" value="${schoolHomepageDto.id}">
            <input type="hidden" id ="sort" value="${schoolHomepageDto.sort}">
            <input type="hidden" id ="courseId" value="${schoolHomepageDto.courseId}">

            <c:forEach items="${courseTypeList}" var="item" varStatus="status">
                <div class="col-md-12" style="padding: 10px;margin-left: 200px">
                    <input type="radio" name="itemName" value="${item.id}"
                        <c:if test="${schoolHomepageDto.courseId == item.id}">
                           checked="checked"
                        </c:if>
                    >${item.itemName}
                </div>
            </c:forEach>
            <div class="col-md-12" style="text-align: center">
                <span id="btnSave" class="btn btn-primary">确定</span>
                <span id="btnClose" class="btn">关闭</span>
            </div>
        </div>
    </c:if>


    <c:if test="${schoolHomepageDto.type == '2'}">
        <div style="height: 580px">
            <div style="margin: 10px;">
                <span style="font-size: 16px;font-weight: 800;" >已选择专题：</span>
                <span style="font-size: 16px;font-weight: 800;" id="courseSpecia">${schoolHomepageDto.courseTypeName}</span>
                <input type="hidden" id="courseId" value="${schoolHomepageDto.courseId}">
                <input type="hidden" id = "type" value="${schoolHomepageDto.type}">
                <input type="hidden" id ="id" value="${schoolHomepageDto.id}">
                <input type="hidden" id ="sort" value="${schoolHomepageDto.sort}">
            </div>
            <div style="margin-left: 240px">
                <c:forEach items="${courseTypeList}" var="item" varStatus="status">
                    <div style="padding: 8px;">
                        <div class="dropdown">
                            <button class="btn btn-default dropdown-toggle" type="button" data-toggle="dropdown">${item.itemName}
                                <span class="caret"></span></button>
                            <ul class="dropdown-menu" role="menu" aria-labelledby="menu1">
                                <c:forEach items="${courseSpecialList}" var="courseSpecia" varStatus="status">
                                    <c:if test="${courseSpecia.parentItemNo == item.itemNo}">
                                        <li role="presentation"><a role="menuitem" tabindex="-1" href="#" onclick="courseSpecia('${courseSpecia.id}','${courseSpecia.itemName}')">${courseSpecia.itemName}</a></li>
                                    </c:if>
                                </c:forEach>
                            </ul>
                        </div>
                    </div>
                </c:forEach>
            </div>
            <div class="col-md-12" style="text-align: center">
                <span id="btnSave2" class="btn btn-primary">确定</span>
                <span id="btnClose2" class="btn">关闭</span>
            </div>
        </div>
    </c:if>

</div>
</body>
</html>
