<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:js/>
    <style type="text/css">
        .sch-big-div {
            padding-left: 20px;
        }

        .sch-big-font {
            font-size: 22px;
            font-weight: 600;
        }

        .sch-td {
            background-color: #f1f1f1
        }

        .sch-btn {
            color: #0a67fb;
            cursor: pointer;
        }

        .sch-sdiv {
            text-align: right;
        }

        .sch-row {
            margin-top: 5px;
        }
        .rectangles{
            width: 6px;
            height: 18px;
            float: left;
            margin-top: 2px;
            background: #1d89cf;
        }
    </style>
    <script>

    </script>
</head>
<body>
<div class="panel">
    <div class="sch-banner sch-big-div">
        <div class="row">
            <span class="sch-big-font">轮播设置</span>
            <span id="btn_add_banner" class="btn btn-primary" style="margin-top: -8px" onclick="addBanner()">新增</span>
        </div>
        <div class="panel panel-default" id="schBanner">
            <table class="table table-bordered no-margin" id="schBannerTable" style="text-align: center;">
                <thead>
                <tr>
                    <td width="10%" class="sch-td">序号</td>
                    <td width="20%" class="sch-td">标题</td>
                    <td width="20%" class="sch-td">关联直播/课程</td>
                    <td width="20%" class="sch-td">轮播图</td>
                    <td width="10%" class="sch-td">是否启用</td>
                    <td width="15%" class="sch-td">操作</td>
                </tr>
                </thead>
                <tbody id="schBannerBody">
                <c:forEach items="${schoolIndexConfigDto.schBannerInfoDtoList}" var="item" varStatus="status">
                    <tr data-id="${item.id}" data-index="${status.index}">
                        <td class="serialNumber" style=" word-wrap:break-word;word-break:break-all;">
                                ${status.index+1}
                        </td>
                        <td class="tiele" style=" word-wrap:break-word;word-break:break-all;">
                                ${item.title}
                        </td>
                        <td class="relationItemName" style="word-wrap:break-word;word-break:break-all;">
                            <c:if test="${not empty item.relationItemName}">
                                ${item.relationItemName}
                                <input type="hidden" value="${item.relationItem}">
                            </c:if>
                        </td>
                        <td class="imageName" style=" word-wrap:break-word;word-break:break-all;">
                                ${item.imageName}
                        </td>
                        <td class="aliveName" style=" word-wrap:break-word;word-break:break-all;">
                                ${item.aliveName}
                        </td>
                        <td style="word-wrap:break-word;word-break:break-all;">
                            <span onclick="editBanner('${item.id}',this)" class="sch-btn">编辑</span>&nbsp;<span
                                style="color: #0a67fb">|</span>
                            <span onclick="deleteBanener('${item.id}',this)" class="sch-btn">删除</span>&nbsp;<span
                                style="color: #0a67fb">|</span>
                            <span onclick="moveDetail('up','${item.id}',this)" class="sch-btn">上移</span>&nbsp;<span
                                style="color: #0a67fb">|</span>
                            <span onclick="moveDetail('down','${item.id}',this)" class="sch-btn">下移</span>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>


    <div class="sch-banner sch-big-div">
        <div class="row">
            <span class="sch-big-font">热门板块设置</span>
            <div class="col-md-12">
                <div class="col-md-9 no-padding">
                    <div class="rectangles"></div>&nbsp;&nbsp;
                    <lable style="font-size: 16px">热门分类</lable>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <table class="table table-bordered no-margin" style="text-align: center;" id="configCourseType">
                <thead>
                <tr>
                    <td width="20%" class="sch-td">序号</td>
                    <td width="40%" class="sch-td">分类名称</td>
                    <td width="20%" class="sch-td">状态</td>
                    <td width="20%" class="sch-td">操作</td>
                </tr>
                </thead>
                <tbody id="courseTableId">
                    <c:forEach items="${configCourseTypeList}" var="item" varStatus="status">
                        <tr>
                            <td>
                                ${status.index+1}
                            </td>
                            <td>
                                <c:if test="${item.courseId != null && item.courseId != ''}">
                                    ${item.courseTypeName}
                                </c:if>
                                <c:if test="${item.courseId == null || item.courseId == ''}">
                                    -
                                </c:if>
                            </td>
                            <td>
                                <c:if test="${item.courseId != null && item.courseId != ''}">
                                    固定
                                </c:if>
                                <c:if test="${item.courseId == null || item.courseId == ''}">
                                    自动变更
                                </c:if>
                            </td>
                            <td>
                                <i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editCourseType('${item.type}','${item.id}','${item.courseId}','${item.sort}','${item.courseTypeName}')"></i>
                                <c:if test="${item.courseId != null && item.courseId != ''}">
                                    <i class="icon-link" title="取消固定" onclick="cancelCourseType('${item.type}','${item.id}')"></i>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="col-md-9 no-padding">
                    <div class="rectangles"></div>&nbsp;&nbsp;
                    <lable style="font-size: 16px">热门课程</lable>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <table class="table table-bordered no-margin" style="text-align: center;">
                <thead>
                <tr>
                    <td width="10%" class="sch-td">序号</td>
                    <td width="30%" class="sch-td">课程名称</td>
                    <td width="20%" class="sch-td">播放量（近三个月）</td>
                    <td width="20%" class="sch-td">状态</td>
                    <td width="20%" class="sch-td">操作</td>
                </tr>
                </thead>
                <tbody id="courseInfoTable">
                <c:forEach items="${configCourseInfoList}" var="item" varStatus="status">
                    <tr>
                        <td>${status.index+1}</td>
                        <td>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                ${item.courseTypeName}
                            </c:if>
                            <c:if test="${item.courseId == null || item.courseId == ''}">
                                -
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                ${item.amount}
                            </c:if>
                            <c:if test="${item.courseId == null || item.courseId == ''}">
                                -
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                固定
                            </c:if>
                            <c:if test="${item.courseId == null || item.courseId == ''}">
                                自动变更
                            </c:if>
                        </td>
                        <td>
                            <i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editCourseInfo('${item.type}','${item.id}','${item.courseId}','${item.sort}','${item.courseTypeName}')"></i>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                <i class="icon-link" title="取消固定" onclick="cancelCourseType('${item.type}','${item.id}')"></i>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>

    <div class="sch-banner sch-big-div">
        <div class="row">
            <span class="sch-big-font">热门专题</span>
        </div>
        <div class="panel panel-default">
            <table class="table table-bordered no-margin" style="text-align: center;">
                <thead>
                <tr>
                    <td width="10%" class="sch-td">序号</td>
                    <td width="30%" class="sch-td">专题名称</td>
                    <td width="20%" class="sch-td">状态</td>
                    <td width="20%" class="sch-td">操作</td>
                </tr>
                </thead>
                <tbody id="courseSpeciaTableId">
                <c:forEach items="${configCourseSpecialList}" var="item" varStatus="status">
                    <tr>
                        <td>
                            ${status.index+1}
                        </td>
                        <td>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                ${item.courseTypeName}
                            </c:if>
                            <c:if test="${item.courseId == null || item.courseId == ''}">
                                -
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                固定
                            </c:if>
                            <c:if test="${item.courseId == null || item.courseId == ''}">
                                自动变更
                            </c:if>
                        </td>
                        <td>
                            <i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editcourseSpecia('${item.type}','${item.id}','${item.courseId}','${item.sort}','${item.courseTypeName}')"></i>
                            <c:if test="${item.courseId != null && item.courseId != ''}">
                                <i class="icon-link" title="取消固定" onclick="cancelCourseType('${item.type}','${item.id}')"></i>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>


    <div class="sch-banner sch-big-div">
        <div class="row">
            <span class="sch-big-font">专家讲师</span>
            <span id="addCourseTeacher" class="btn btn-primary" style="float: right;margin-right:30px"  onclick="addCourseTeacher()">选择导师</span>
            <input type="hidden" id="sort" value="${sort-1}">
        </div>
        <div class="panel panel-default">
            <table class="table table-bordered no-margin" style="text-align: center;">
                <thead>
                <tr>
                    <td width="10%" class="sch-td">序号</td>
                    <td width="30%" class="sch-td">讲师名称</td>
                    <td width="20%" class="sch-td">所属机构</td>
                    <td width="20%" class="sch-td">操作</td>
                </tr>
                </thead>
                <tbody id="courseTeacherTableId">
                <c:forEach items="${configCourseTeacherList}" var="item" varStatus="status">
                    <tr>
                        <td>${status.index+1}</td>
                        <td>${item.courseTypeName}</td>
                        <td>${item.teachOrg}</td>
                        <td>
                            <i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editCourseTeacher('${item.id}','${item.teacherId}','${item.sort}','${item.courseTypeName}')"></i>
                            <i class="fa fa-trash" style="cursor: pointer;margin: 0 5px;" title="删除" onclick="deleteCourseTeacher('${item.id}','${item.teacherId}','${item.sort}','${item.courseTypeName}')"></i>
                            <i class="fa fa-arrow-circle-up" style="cursor: pointer;margin: 0 5px;" title="上移" onclick="editCourseTeacherUp('${item.id}','${item.teacherId}','${item.sort}','${item.courseTypeName}')"></i>
                            <i class="fa fa-arrow-circle-down" style="cursor: pointer;margin: 0 5px;" title="下移" onclick="editCourseTeacherDown('${item.id}','${item.teacherId}','${item.sort}','${item.courseTypeName}')"></i>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>


<%--    <div class="sch-course sch-big-div">--%>
<%--        <div class="row">--%>
<%--            <span class="sch-big-font">推荐课程</span>--%>
<%--            <span id="btn_add_course" class="btn btn-primary" style="margin-top: -8px"  onclick="addCourseGroup()">新增</span>--%>
<%--        </div>--%>
<%--        <c:forEach items="${schoolIndexConfigDto.schCourseDtoList}" var="courseItem" varStatus="courseStatus">--%>
<%--            <div class="panel panel-heading schCourseGroup" id="schCourse${courseStatus.index}">--%>
<%--                <input class="courseGroupSerialNumber" value="${courseStatus.index+1}" type="hidden">--%>
<%--                <div class="row ">--%>
<%--                    <span class="col-md-1 sch-sdiv">标题：</span>--%>
<%--                    <div class="col-md-11">--%>
<%--                        <input type="text" value="${courseItem.title}" title=" " onmouseover="this.title = this.value"--%>
<%--                               maxlength="128" class="courseTitle col-md-8" placeholder="请输入标题"--%>
<%--                               onchange="addCourseGroupDetail('title','${courseItem.id}',this)"/>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div class="row sch-row">--%>
<%--                    <span class="col-md-1 sch-sdiv">说明：</span>--%>
<%--                    <div class="col-md-11">--%>
<%--                        <textarea class="courseDescription  col-md-8"  autoHeight="true"--%>
<%--                               placeholder="请输入说明"--%>
<%--                                  onchange="addCourseGroupDetail('description','${courseItem.id}',this)">${courseItem.description}</textarea>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div class="row">--%>
<%--                    <div class="col-md-12 control-label text-right">--%>
<%--                        <span class="btn btn-primary" onclick="chooseCourse('${courseItem.id}',this)">选择课程</span>--%>
<%--                        <span class="btn btn-primary" onclick="moveCourseGroup('up','${courseItem.id}',this)">上移</span>--%>
<%--                        <span class="btn btn-primary"--%>
<%--                              onclick="moveCourseGroup('down','${courseItem.id}',this)">下移</span>--%>
<%--                        <span class="btn btn-primary" onclick="delCourseGroup('${courseItem.id}',this)">删除</span>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <table class="table table-bordered no-margin" id="schCourseTable${courseStatus.index}"--%>
<%--                       style="text-align: center;">--%>
<%--                    <thead>--%>
<%--                    <tr>--%>
<%--                        <td width="10%" class="sch-td">序号</td>--%>
<%--                        <td width="65%" class="sch-td">课程名称</td>--%>
<%--                        <td width="25%" class="sch-td">操作</td>--%>
<%--                    </tr>--%>
<%--                    </thead>--%>
<%--                    <tbody id="schCourseBody${courseStatus.index}" class="schCourseBody${courseStatus.index}">--%>
<%--                    <c:forEach items="${courseItem.courseDtoList}" var="couItem" varStatus="couStatus">--%>
<%--                        <tr data-id="${couItem.id}" data-index="${couStatus.index}">--%>
<%--                            <td class="serialNumber">--%>
<%--                                    ${couStatus.index+1}--%>
<%--                            </td>--%>
<%--                            <td class="courseName">--%>
<%--                                    ${couItem.courseName}--%>
<%--                            </td>--%>
<%--                            <td>--%>
<%--                                <span onclick="delCourseDetail('${couItem.id}',this)" class="sch-btn">删除</span>&nbsp;<span--%>
<%--                                    style="color: #0a67fb">|</span>--%>
<%--                                <span onclick="moveCourseDetail('up','${couItem.id}',this)" class="sch-btn">上移</span>&nbsp;<span--%>
<%--                                    style="color: #0a67fb">|</span>--%>
<%--                                <span onclick="moveCourseDetail('down','${couItem.id}',this)" class="sch-btn">下移</span>--%>
<%--                            </td>--%>
<%--                        </tr>--%>
<%--                    </c:forEach>--%>
<%--                    </tbody>--%>
<%--                </table>--%>
<%--            </div>--%>
<%--        </c:forEach>--%>
<%--    </div>--%>
<%--    <div class="sch-teacher sch-big-div">--%>
<%--        <div class="row">--%>
<%--            <span class="sch-big-font">专家讲师</span>--%>
<%--            <span id="btn_add_teacher" class="btn btn-primary" style="margin-top: -8px"  onclick="addTeacher()">新增</span>--%>
<%--        </div>--%>
<%--        <c:forEach items="${schoolIndexConfigDto.schTeacherDtoList}" var="teacherItem" varStatus="teacherStatus">--%>
<%--            <div class="panel panel-heading schTeacherGroup" id="schTeacher${teacherStatus.index}">--%>
<%--                <input class="teacherGroupSerialNumber" value="${teacherStatus.index+1}" type="hidden">--%>
<%--                <div class="row">--%>
<%--                    <span class="col-md-1 sch-sdiv">标题：</span>--%>
<%--                    <div class="col-md-11">--%>
<%--                        <input type="text" value="${teacherItem.title}" title=" " onmouseover="this.title = this.value"--%>
<%--                               maxlength="128" class="teacherTitle col-md-8" placeholder="请输入标题"--%>
<%--                               onchange="addCourseGroupDetail('title','${teacherItem.id}',this)"/>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div class="row sch-row">--%>
<%--                    <span class="col-md-1 sch-sdiv">说明：</span>--%>
<%--                    <div class="col-md-11">--%>
<%--                        <textarea   class="teacherDescription  col-md-8"  autoHeight="true"--%>
<%--                               placeholder="请输入说明"--%>
<%--                                   onchange="addCourseGroupDetail('description','${teacherItem.id}',this)">${teacherItem.description}</textarea>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div class="row" style="display: none">--%>
<%--                    <a href="javascript:void(0);" id="teacherUploadBtn"--%>
<%--                       class="file btn btn-warning btn-facebook btn-outline ">--%>
<%--                        <i class="fa fa-upload teacherFile"></i> 上传图片--%>
<%--                        <input id="teacherFile" type="file" class="teacherFile" name="files" multiple/>--%>
<%--                    </a>--%>
<%--                </div>--%>
<%--                <div class="row">--%>
<%--                    <div class="col-md-12 control-label text-right">--%>
<%--                        <span class="btn btn-primary" onclick="chooseTeacher('${teacherItem.id}',this)">选择讲师</span>--%>
<%--                        <span class="btn btn-primary"--%>
<%--                              onclick="moveTeacherGroup('up','${teacherItem.id}',this)">上移</span>--%>
<%--                        <span class="btn btn-primary"--%>
<%--                              onclick="moveTeacherGroup('down','${teacherItem.id}',this)">下移</span>--%>
<%--                        <span class="btn btn-primary" onclick="delTeacherGroup('${teacherItem.id}',this)">删除</span>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <table class="table table-bordered no-margin" id="schTeacherTable${teacherStatus.index}"--%>
<%--                       style="text-align: center;">--%>
<%--                    <thead>--%>
<%--                    <tr>--%>
<%--                        <td width="10%" class="sch-td">序号</td>--%>
<%--                        <td width="15%" class="sch-td">讲师姓名</td>--%>
<%--                        <td width="50%" class="sch-td">背景图片</td>--%>
<%--                        <td width="25%" class="sch-td">操作</td>--%>
<%--                    </tr>--%>
<%--                    </thead>--%>
<%--                    <tbody id="schTeacherBody${teacherStatus.index}" class="schTeacherBody${teacherStatus.index}">--%>
<%--                    <c:forEach items="${teacherItem.teacherDtoList}" var="teaItem" varStatus="teaStatus">--%>
<%--                        <tr data-id="${teaItem.id}" data-index="${teaStatus.index}">--%>
<%--                            <td class="serialNumber">--%>
<%--                                    ${teaStatus.index+1}--%>
<%--                            </td>--%>
<%--                            <td class="teacherName">--%>
<%--                                    ${teaItem.teacherName}--%>
<%--                            </td>--%>
<%--                            <td class="bgName">--%>
<%--                                <c:if test="${empty teaItem.bgName}">--%>
<%--                                    ----%>
<%--                                </c:if>--%>
<%--                                <c:if test="${not empty teaItem.bgName}">--%>
<%--                                    ${teaItem.bgName}--%>
<%--                                </c:if>--%>
<%--                            </td>--%>
<%--                            <td>--%>
<%--                                <span onclick="editPicture('${teaItem.teacherId}',this)" class="sch-btn">设置图片</span>&nbsp;<span--%>
<%--                                    style="color: #0a67fb">|</span>--%>
<%--                                <span onclick="delTeacherDetail('${teaItem.id}',this)" class="sch-btn">&nbsp;删除</span>&nbsp;<span--%>
<%--                                    style="color: #0a67fb">|</span>--%>
<%--                                <span onclick="moveTeacherDetail('up','${teaItem.id}',this)" class="sch-btn">&nbsp;上移</span>&nbsp;<span--%>
<%--                                    style="color: #0a67fb">|</span>--%>
<%--                                <span onclick="moveTeacherDetail('down','${teaItem.id}',this)" class="sch-btn">&nbsp;下移</span>--%>
<%--                            </td>--%>
<%--                        </tr>--%>
<%--                    </c:forEach>--%>
<%--                    </tbody>--%>
<%--                </table>--%>
<%--            </div>--%>
<%--        </c:forEach>--%>
<%--    </div>--%>
<%--底部banner--%>
    <div >
        <div class="panel-body">
            <div class="row"><span class="sch-big-font">底部banner</span>
                <c:if test="${getBannerList.size()==0}">
                   <input type="button" id="addBannerConfig" class="btn btn-primary" value="新增">
                </c:if>
            </div>
            <div class="row" style="margin-top: 10px ">
                <div id="bannerTable" class="col-md-12" style="">
                    <div class="table-primary">
                        <table class="table table-hover table-striped" style="width: 100%">
                            <thead>
                            <tr>
                                <th style="width: 5%; text-align: center;">序号</th>
                                <th style="width: 10%; text-align: center;">标题</th>
                                <th style="width: 10%; text-align: center;">关联直播/课程/链接</th>
                                <th style="width: 10%; text-align: center;">图片</th>
                                <th style="width: 10%; text-align: center;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="tbodyId1">
                            <c:forEach items="${getBannerList}" var="item" varStatus="index">
                                <tr>
                                    <td style="text-align: center;">${status.index+1}</td>
                                    <td style="text-align: center;">${item.bannerTitle}</td>
                                    <td style="text-align: center;">${item.bannerItemId}</td>
                                    <td style="text-align: center;">${item.bannerPicName}</td>
                                    <td style="text-align: center;"><i class="fa fa-edit iconStyle"  title="编辑" onclick="editBottomBanner('${item.id}')"></i></td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div >
        <div class="panel-body">
            <div class="row"><span class="sch-big-font">网络课程服务协议</span>

            </div>
            <div class="row" >
                新增/重新上传
                <input id="addServiceAgreement" type="file" name="files" multiple/>
            </div>
            <div class="table-primary">
                <table class="table table-hover table-striped" style="width: 100%">
                    <thead>
                    <tr>
                        <th style="width: 20%; text-align: center;">文件名</th>
                        <th style="width: 10%; text-align: center;">上传人</th>
                        <th style="width: 10%; text-align: center;">上传时间</th>
                    </tr>
                    </thead>
                    <tbody id="addServiceAgreement1">
                        <tr>
                            <td style="text-align: center;">${serviceAgreement.attName}</td>
                            <td style="text-align: center;">${serviceAgreement.userName}</td>
                            <td style="text-align: center;">${serviceAgreement.createDate}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>
</body>

</html>
