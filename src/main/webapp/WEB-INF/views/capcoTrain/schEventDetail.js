var saveFlag = true;
$(document).ready(function () {
    eventValidate();

    getEventDetailInfo();

    $("#btnSave").bind("click", function (data) {
        if($("#schEventForm").valid()) {
            ajaxSubmitForm('/schoolCourse/saveSchoolEvent', '', function (res) {
                popMsg("保存成功！");
                saveFlag = true;
                $("#eventId").val(res);
                //重新加载事件信息
                getEventDetailInfo();
            })
        }
    })
});

function eventValidate() {
    $("#schEventForm").validate({
        rules : {
            "eventTitle" :{
                required : true,
                maxlength: 100
            },
            "eventDate" :{
                required : true,
            },
        },
        messages : {
            "eventTitle" :{
                required : "请填写事件名称"
            },
        }
    });
}

function getEventDetailInfo() {
    var param = {
        eventId: $("#eventId").val()
    }
    ajaxData("/schoolCourse/getEventDetailInfo", param, function (result) {
        spliceTable(result);
    })
}

function showCourse() {
    if(saveFlag){
        var param = {
            courseId: getListCourseId(),
            relaType: '02',
        }
        parent.popWin("关联课程", "/schoolCourse/schColumnRelaCourse", param, "100%", "100%", function (result) {
            //按照课程id排序
            var compare = function (obj1, obj2) {
                var val1 = obj1.id;
                var val2 = obj2.id;
                if (val1 < val2) {
                    return -1;
                } else if (val1 > val2) {
                    return 1;
                } else {
                    return 0;
                }
            }
            var tableInfo = result.sort(compare);
            //再以之前专栏顺序排序
            var compareColumnSort = function (obj1, obj2) {
                //排序为空的，在下面
                var val1 = obj1.columnSort == null ? 10000 : obj1.columnSort;
                var val2 = obj2.columnSort == null ? 10000 : obj2.columnSort;
                if (val1 < val2) {
                    return -1;
                } else if (val1 > val2) {
                    return 1;
                } else {
                    return 0;
                }
            }
            tableInfo = result.sort(compareColumnSort);
            //清除重新加载表格
            spliceTable(tableInfo);
        });
    }else{
        parent.popMsg("请保存后，再进行操作！");
    }

}

function getListCourseId() {
    var courseId;
    var courseIds = [];
    $("#relaCourse").find(".courseId").each(function (i, o) {
        courseIds.push($(o).val());
    });

    if (courseIds.length > 0) {
        courseId = courseIds.join();
    }
    return courseId;
}

function spliceTable(result) {
    $("#relaCourse").html("");
    //拼接新表格
    var tr = "";
    var param = {};
    for (var n in result) {
        var num = parseInt(n) + 1
        var data = result[n] || {};
        param.n = n;
        tr += '<tr>' +
            '<td class="table-td indx">' +
            num +
            '</td>' +
            '<td class="table-td">' +
            data.id +
            '</td>' +
            '<td class="table-td">' +
            data.courseName +
            '</td>' +
            '<td class="table-td">' +
            data.releaseTime +
            '</td>' +
            '<td class="table-td">' +
            '<span title="上移" style="margin-right: 12px;" onclick="moveUp(this)"><i style="cursor:pointer" class="fa fa-chevron-circle-up"></i></span>' +
            '<span title="下移" style="margin-right: 12px;" onclick="moveDown(this)"><i style="cursor:pointer" class="fa fa-chevron-circle-down"></i></span>' +
            '<span title="删除" onclick="deleteRow(this)"><i style="cursor:pointer" class="fa fa-trash-o"></i></span>' +
            '<input type="hidden" class="courseId" id="schCourseDtoList[' + n + '].id" name="schCourseDtoList[' + n + '].id" value="' + data.id + '">' +
            '<input type="hidden" class="eventId" id="schCourseDtoList[' + n + '].eventId" name="schCourseDtoList[' + n + '].eventId" value="' + data.eventId + '">' +
            '<input type="hidden" class="versionType" id="schCourseDtoList[' + n + '].versionType" name="schCourseDtoList[' + n + '].versionType" value="1">' +
            '</td>' +
            '</tr>'
    }
    $("#relaCourse").html(tr);
}

function moveUp(obj) {
    var tr = $(obj).parents("tr");
    if (tr.index() != 0) {
        tr.prev().before(tr);
        saveFlag = false;
        sortTable();
    }else {
        popMsg("无法上移");
    }
}

function moveDown(obj) {
    var tr = $(obj).parents("tr");
    var trLength = $('#relaCourse').children('tr').length;
    if (tr.index() != trLength - 1) {
        tr.next().after(tr);
        saveFlag = false;
        sortTable();
    }else {
        popMsg("无法下移");
    }
}

function deleteRow(item) {
    var courseId = $(item).parents("tr").find(".courseId")[0].value;
    var eventId = $(item).parents("tr").find(".eventId")[0].value;
    if(eventId){
        popConfirm("确认删除", function() {
            //先删除表关联
            var param = {
                id: courseId,
                columnId: $("#eventId").val(),
                relaType: '01'
            }
            ajaxData("/schoolCourse/delAssociated", param, function (result) {
                parent.popMsg("删除成功！");
                $(item).parents("tr").remove();
                sortTable();
            })
        }, null);
    }else{
        $(item).parents("tr").remove();
        sortTable();
    }
}

function sortTable() {
    $('#relaCourse').children('tr').each(function (index,e) {
        $(e).find(".indx").text(parseInt(index) + 1);
        //更新name属性下标
        var inputElements = this.getElementsByTagName("input");
        if(inputElements){
            for(var i = 0; i < inputElements.length; i++){
                var attr = $(inputElements[i]).attr("name").split(".")[1];
                var newName = "schCourseDtoList["+ (index) +"]." + attr;
                $(inputElements[i]).attr("name", newName);
                $(inputElements[i]).attr("id", newName);
            }
        }
    })
}
