var teacherObj = "";
var teacherId = '';
$(document).ready(function () {
    $("#addBannerConfig").bind('click',function (){
        addBannerConfig();
    });

    jQuery.fn.extend({
        autoHeight: function(){
            return this.each(function(){
                var $this = jQuery(this);
                if( !$this.attr('_initAdjustHeight') ){
                    $this.attr('_initAdjustHeight', $this.outerHeight());
                }
                _adjustH(this).on('input', function(){
                    _adjustH(this);
                });
            });
            /**
             * 重置高度
             * @param {Object} elem
             */
            function _adjustH(elem){
                var $obj = jQuery(elem);
                return $obj.css({height: $obj.attr('_initAdjustHeight'), 'overflow-y': 'hidden'})
                    .height( elem.scrollHeight );
            }
        }
    });
    textareaAuto();


    var url = contextPath + '/filetempupload';
    $('#teacherFile').fileupload({
        url: url,
        dataType: 'json',
        autoUpload: true,
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            ;
            $.each(data.result, function (index, file) {

                $(teacherObj).find(".bgName").html(file.fileName);

                var param = {
                    teachId: teacherId,
                    bgImage: file.fileRelaId,
                    bgName: file.fileName,
                }
                ajaxData("/schoolIndexConfig/addTeacherBg", param, function () {

                })
            });
            layer.close(index);
        }
    });

    $('#addServiceAgreement').fileupload({
        url: url,
        dataType: 'json',
        autoUpload: true,
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                var param = {
                    fileContentType: file.fileContentType,
                    fileId: file.fileId,
                    fileName: file.fileName,
                    bgImage: file.fileRelaId,
                    fileSize: file.fileSize
                }
                ajaxData("/schoolIndexConfig/addServiceAgreement", param, function (data) {
                    popAlert("上传成功！");
                    var sss=$("#addServiceAgreement1")
                    $("#addServiceAgreement1")[0].rows[0].children[0].innerText= data.attName;
                    $("#addServiceAgreement1")[0].rows[0].children[1].innerText= data.userName;
                    $("#addServiceAgreement1")[0].rows[0].children[2].innerText= data.createDate;
                })
            });
            layer.close(index);
        }
    });

});

//轮播开始

//添加轮播信息
function addBanner() {
    parent.popWin("新增轮播", "/schoolIndexConfig/bannerAddInit", "", "100%", "100%", function (id) {
        //刷新table
        var param = {
            id: id
        }
        ajaxData("/schoolIndexConfig/getBanner", param, function (dto) {
            var id = dto.id;
            var title = dto.title;
            if (dto.relationItemName == null) {
                var relationItemName = '';
            } else {
                var relationItemName = dto.relationItemName;
            }
            var imageName = dto.imageName;
            var aliveName = dto.aliveName;
            var str = "<tr data-id=\"1107442362288104981\" data-index=\"6\">" +
                "                        <td class=\"serialNumber\">" +
                "                                7" +
                "                        </td>" +
                "                        <td class=\"tiele\">" +
                title +
                "                        </td>" +
                "                        <td class=\"relationItemName\">" +
                relationItemName +
                "                        </td>" +
                "                        <td class=\"imageName\">" +
                imageName +
                "                        </td>" +
                "                        <td class=\"aliveName\">" +
                aliveName +
                "                        </td>" +
                "                        <td>" +
                "                            <span onclick=\"editBanner('" + id + "',this)\" class=\"sch-btn\">编辑</span>&nbsp;<span style=\"color: #0a67fb\">|</span>" +
                "                            <span onclick=\"deleteBanener('" + id + "',this)\" class=\"sch-btn\">删除</span>&nbsp;<span style=\"color: #0a67fb\">|</span>" +
                "                            <span onclick=\"moveDetail('up','" + id + "',this)\" class=\"sch-btn\">上移</span>&nbsp;<span style=\"color: #0a67fb\">|</span>" +
                "                            <span onclick=\"moveDetail('down','" + id + "',this)\" class=\"sch-btn\">下移</span>" +
                "                        </td>" +
                "                    </tr>";
            $("#schBannerBody").append(str);
            refreshTableIndex();
        })
    });
}

//编辑轮播信息
function editBanner(id, obj) {
    var param = {
        id: id,
    };
    parent.popWin("编辑轮播", "/schoolIndexConfig/bannerAddInit", param, "100%", "100%", function (id) {
        //刷新table
        ajaxData("/schoolIndexConfig/getBanner", param, function (dto) {
            var id = dto.id;
            var title = dto.title;
            if (dto.relationItemName == null) {
                var relationItemName = '';
            } else {
                var relationItemName = dto.relationItemName;
            }
            var imageName = dto.imageName;
            var aliveName = dto.aliveName;
            $(obj).parent().parent().find(".tiele").html(title);
            $(obj).parent().parent().find(".relationItemName").html(relationItemName);
            $(obj).parent().parent().find(".imageName").html(imageName);
            $(obj).parent().parent().find(".aliveName").html(aliveName);
        });
    });
}

//删除轮播信息 ok
function deleteBanener(id, obj) {
    popConfirm("确定删除？",function () {
        var param = {
            id: id,
        };
        ajaxData("/schoolIndexConfig/deleteBanner", param, function () {
            //删除一行table
            $(obj).parent().parent().remove();
            //列表重排序号
            refreshTableIndex();
            popMsg("删除成功！")
        });
    })
}

//上下移动轮播信息 ok
function moveDetail(moveType, id, obj) {

    var seq = $(obj).parent().parent().find(".serialNumber").html();
    var tableLength = $("#schBannerTable tbody tr").length;
    if (moveType == "up" && parseInt(seq) == "1") {
        popMsg("第一个，不能上移了");
    } else if (moveType == "down" && parseInt(seq) == tableLength) {
        popMsg("最后一个，不能下移了");
    } else {
        ajaxData('/schoolIndexConfig/moveDetail', {moveType: moveType, id: id}, function () {
            popMsg('操作成功');
            //交换行
            if (moveType == "up") {
                $(obj).parent().parent().insertBefore($(obj).parent().parent().prev());
            } else if (moveType == "down") {
                $(obj).parent().parent().insertAfter($(obj).parent().parent().next());
            }
            refreshTableIndex();
        })
    }
}

//刷新轮播序号 ok
function refreshTableIndex() {
    //重新排序
    $("#schBannerTable tbody tr").each(function (n, obj) {
        $(obj).find(".serialNumber").html(n + 1);
    });
}

//轮播结束

//课程开始

//添加课程组 ok
function chooseCourse(id, obj) {
    var param = {
        groupId: id
    }
    parent.popWin("选择课程", "/schoolIndexConfig/chooseCourse", param, "70%", "70%", function (data) {
        //查并且插入所选课程
        var param = {
            id: id,
            ids: data
        }
        ajaxData("/schoolIndexConfig/addCourse", param, function (dto) {

            for (var nums in dto) {
                if (dto[nums]) {
                    var id = dto[nums].id;
                    var courseName = dto[nums].courseName;
                    var str = "<tr data-id='" + id + "' data-index=\"1\">"
                        + "<td class=\"serialNumber\">"
                        + courseName
                        + "</td>"
                        + "<td class=\"courseName\">"
                        + courseName
                        + "</td>"
                        + "<td>"
                        + "<span onclick=\"delCourseDetail('" + id + "',this)\" class=\"sch-btn\">删除&nbsp;</span>&nbsp;<span style=\"color: #0a67fb\">|</span>"
                        + "<span onclick=\"moveCourseDetail('up','" + id + "',this)\" class=\"sch-btn\">&nbsp;上移</span>&nbsp;<span style=\"color: #0a67fb\">|</span>"
                        + "<span onclick=\"moveCourseDetail('down','" + id + "',this)\" class=\"sch-btn\">&nbsp;下移</span>"
                        + "</td>"
                        + "</tr>";
                    $(obj).parent().parent().parent().find("tbody").append(str);
                    //重新排序
                    $(obj).parent().parent().parent().find("tr").each(function (n, obj) {
                        $(obj).find(".serialNumber").html(n);
                    });
                }
            }

        })

    })
}

function addCourseGroup() {
    var param = {
        type: "1"
    }
    ajaxData("/schoolIndexConfig/addGroup", param, function (id) {

        //添加一个课程组
        var str = "<div class=\"panel panel-heading schCourseGroup\" id=\"schCourse2\">" +
            "                <input class=\"courseGroupSerialNumber\" value=\"3\" type=\"hidden\">" +
            "                <div class=\"row \">" +
            "                    <span class=\"col-md-1 sch-sdiv\">标题：</span>" +
            "                    <div class=\"col-md-11\">" +
            "                        <input type=\"text\" value=\"\" class=\"courseTitle col-md-8\" title=\" \" onmouseover=\"this.title = this.value\" maxlength=\"128\"  placeholder=\"请输入标题\" onchange=\"addCourseGroupDetail('title','" + id + "',this)\">" +
            "                    </div>" +
            "                </div>" +
            "                <div class=\"row sch-row\">" +
            "                    <span class=\"col-md-1 sch-sdiv\">说明：</span>" +
            "                    <div class=\"col-md-11\">" +
            "<textarea   class=\"courseDescription  col-md-8\"  placeholder=\"请输入说明\" onchange=\"addCourseGroupDetail('description','" + id + "',this)\"></textarea>" +
            "                    </div>" +
            "                </div>" +
            "                <div class=\"row\">" +
            "                    <div class=\"col-md-12 control-label text-right\">" +
            "                        <span class=\"btn btn-primary\" onclick=\"chooseCourse('" + id + "',this)\">选择课程</span>" +
            "                        <span class=\"btn btn-primary\" onclick=\"moveCourseGroup('up','" + id + "',this)\">上移</span>" +
            "                        <span class=\"btn btn-primary\" onclick=\"moveCourseGroup('down','" + id + "',this)\">下移</span>" +
            "                        <span class=\"btn btn-primary\" onclick=\"delCourseGroup('" + id + "',this)\">删除</span>" +
            "                    </div>" +
            "                </div>" +
            "                <table class=\"table table-bordered no-margin\" id=\"schCourseTable2\" style=\"text-align: center;\">" +
            "                    <thead>" +
            "                    <tr>" +
            "                        <td width=\"10%\" class=\"sch-td\">序号</td>" +
            "                        <td width=\"65%\" class=\"sch-td\">课程名称</td>" +
            "                        <td width=\"25%\" class=\"sch-td\">操作</td>" +
            "                    </tr>" +
            "                    </thead>" +
            "                    <tbody id=\"schCourseBody2\" class=\"schCourseBody2\">" +
            "                    " +
            "                    </tbody>" +
            "                </table>" +
            "            </div>";
        $(".sch-course").append(str)
        refreshCourseGroupTableIndex();
        textareaAuto();
    })
}

//添加或者修改title和description ok
function addCourseGroupDetail(type, id, obj) {
    if (type == "description") {
        var param = {
            id: id,
            title: "",
            description: $(obj).val(),
        }
    } else {
        var param = {
            id: id,
            title: $(obj).val(),
            description: "",
        }
    }
    ajaxData("/schoolIndexConfig/addGroupDetail", param, function () {

    })
}

//删除课程分组 ok
function delCourseGroup(id, obj) {
    var param = {
        id: id
    }
    ajaxData("/schoolIndexConfig/delGroup", param, function () {
        $(obj).parent().parent().parent().remove();
        //刷新序号
        refreshCourseGroupTableIndex();
    })
}

//上下移动课程组 ok
function moveCourseGroup(moveType, id, obj) {
    var seq = $(obj).parent().parent().parent().find(".courseGroupSerialNumber").val();
    var length = $(".schCourseGroup").length;

    if (moveType == "up" && parseInt(seq) == "1") {
        popMsg("第一个，不能上移了");
    } else if (moveType == "down" && parseInt(seq) == length) {
        popMsg("最后一个，不能下移了");
    } else {
        var param = {
            type: "1",//课程
            moveType: moveType,
            id: id
        }
        ajaxData("/schoolIndexConfig/moveGroup", param, function () {
            popMsg('操作成功');
            //交换行
            if (moveType == "up") {
                $(obj).parent().parent().parent().insertBefore($(obj).parent().parent().parent().prev());
            } else if (moveType == "down") {
                $(obj).parent().parent().parent().insertAfter($(obj).parent().parent().parent().next());
            }
            refreshCourseGroupTableIndex();
        })
    }
}

//刷新课程组序号 ok
function refreshCourseGroupTableIndex() {

    $(".courseGroupSerialNumber").each(function (n, obj) {
        $(obj).val(n + 1);
    });
}

//上下移动一个课程组的课程 ok
function moveCourseDetail(moveType, id, obj) {
    var seq = $(obj).parent().parent().find(".serialNumber").html();
    var tableLength = $(obj).parent().parent().parent().find("tr").length;
    if (moveType == "up" && parseInt(seq) == "1") {
        popMsg("第一个，不能上移了");
    } else if (moveType == "down" && parseInt(seq) == tableLength) {
        popMsg("最后一个，不能下移了");
    } else {
        ajaxData('/schoolIndexConfig/moveCourseDetail', {type: "1", moveType: moveType, id: id}, function () {
            popMsg('操作成功');
            //交换行
            if (moveType == "up") {
                $(obj).parent().parent().insertBefore($(obj).parent().parent().prev());
            } else if (moveType == "down") {
                $(obj).parent().parent().insertAfter($(obj).parent().parent().next());
            }
            var index = $(obj).parent().parent().parent();
            refreshCourseTableIndex(index);
        })
    }
}

//刷新一个组里的课程序号 ok
function refreshCourseTableIndex(index) {

    //重新排序
    $(index).find("tr").each(function (n, obj) {
        $(obj).find(".serialNumber").html(n + 1);
    });
}

//删除一个组里的课程信息 ok
function delCourseDetail(id, obj) {
    var param = {
        type: "1",
        id: id
    }
    ajaxData("/schoolIndexConfig/delCourseDetail", param, function () {
        //删除一行table
        var obj1 = $(obj).parent().parent().parent()

        $(obj).parent().parent().remove();
        //刷新序号

        refreshCourseTableIndex(obj1);
    })
}

//课程结束


//教师开始

// 添加教师分组 0.5
function addTeacher() {
    var param = {
        type: "0"
    }
    ajaxData("/schoolIndexConfig/addGroup", param, function (id) {

        //添加一个课程组
        var str = "<div class=\"panel panel-heading schTeacherGroup\" id=\"schTeacher1\">" +
            "                <input class=\"teacherGroupSerialNumber\" value=\"2\" type=\"hidden\">" +
            "                <div class=\"row\">" +
            "                    <span class=\"col-md-1 sch-sdiv\">标题：</span>" +
            "                    <div class=\"col-md-11\">" +
            "<input type=\"text\" value=\"\" class=\"teacherTitle col-md-8\" maxlength=\"128\" title=\" \" onmouseover=\"this.title = this.value\" placeholder=\"请输入标题\" onchange=\"addCourseGroupDetail('title','" + id + "',this)\">" +
            "                    </div>" +
            "                </div>" +
            "                <div class=\"row sch-row\">" +
            "                    <span class=\"col-md-1 sch-sdiv\">说明：</span>" +
            "                    <div class=\"col-md-11\">" +
            "<textarea   class=\"teacherDescription  col-md-8\"  placeholder=\"请输入说明\" onchange=\"addCourseGroupDetail('description','" + id + "',this)\"></textarea>" +
            "                    </div>" +
            "                </div>" +
            "                <div class=\"row\" style=\"display: none\">" +
            "                    <a href=\"javascript:void(0);\" id=\"teacherUploadBtn\" class=\"file btn btn-warning btn-facebook btn-outline \">" +
            "                        <i class=\"fa fa-upload teacherFile\"></i> 上传图片" +
            "                        <input id=\"teacherFile\" type=\"file\" class=\"teacherFile\" name=\"files\" multiple=\"\">" +
            "                    </a>" +
            "                </div>" +
            "                <div class=\"row\">" +
            "                    <div class=\"col-md-12 control-label text-right\">" +
            "                        <span class=\"btn btn-primary\" onclick=\"chooseTeacher('" + id + "',this)\">选择讲师</span>" +
            "                        <span class=\"btn btn-primary\" onclick=\"moveTeacherGroup('up','" + id + "',this)\">上移</span>" +
            "                        <span class=\"btn btn-primary\" onclick=\"moveTeacherGroup('down','" + id + "',this)\">下移</span>" +
            "                        <span class=\"btn btn-primary\" onclick=\"delTeacherGroup('" + id + "',this)\">删除</span>" +
            "                    </div>" +
            "                </div>" +
            "                <table class=\"table table-bordered no-margin\" id=\"schTeacherTable1\" style=\"text-align: center;\">" +
            "                    <thead>" +
            "                    <tr>" +
            "                        <td width=\"10%\" class=\"sch-td\">序号</td>" +
            "                        <td width=\"15%\" class=\"sch-td\">讲师姓名</td>" +
            "                        <td width=\"50%\" class=\"sch-td\">背景图片</td>" +
            "                        <td width=\"25%\" class=\"sch-td\">操作</td>" +
            "                    </tr>" +
            "                    </thead>" +
            "                    <tbody id=\"schTeacherBody1\" class=\"schTeacherBody1\">" +
            "                    " +
            "                    </tbody>" +
            "                </table>" +
            "            </div>";
        $(".sch-teacher").append(str)
        refreshTeacherGroupTableIndex();
        textareaAuto();
    })
}

//移动教师分组 ok
function moveTeacherGroup(moveType, id, obj) {
    var seq = $(obj).parent().parent().parent().find(".teacherGroupSerialNumber").val();
    var length = $(".schTeacherGroup").length;

    if (moveType == "up" && parseInt(seq) == "1") {
        popMsg("第一个，不能上移了");
    } else if (moveType == "down" && parseInt(seq) == length) {
        popMsg("最后一个，不能下移了");
    } else {
        var param = {
            type: "0",//讲师
            moveType: moveType,
            id: id
        }
        ajaxData("/schoolIndexConfig/moveGroup", param, function () {
            popMsg('操作成功');
            //交换行
            if (moveType == "up") {
                $(obj).parent().parent().parent().insertBefore($(obj).parent().parent().parent().prev());
            } else if (moveType == "down") {
                $(obj).parent().parent().parent().insertAfter($(obj).parent().parent().parent().next());
            }
            refreshTeacherGroupTableIndex();
        })
    }
}

//删除教师组 ok
function delTeacherGroup(id, obj) {
    var param = {
        id: id
    }
    ajaxData("/schoolIndexConfig/delGroup", param, function () {
        $(obj).parent().parent().parent().remove();
        //刷新序号
        refreshTeacherGroupTableIndex();
    })
}


//刷新教师组序号 ok
function refreshTeacherGroupTableIndex() {
    $(".teacherGroupSerialNumber").each(function (n, obj) {
        $(obj).val(n + 1);
    });
}

//上下移动一个教师组的课程 ok
function moveTeacherDetail(moveType, id, obj) {
    var seq = $(obj).parent().parent().find(".serialNumber").html();
    var tableLength = $(obj).parent().parent().parent().find("tr").length;
    if (moveType == "up" && parseInt(seq) == "1") {
        popMsg("第一个，不能上移了");
    } else if (moveType == "down" && parseInt(seq) == tableLength) {
        popMsg("最后一个，不能下移了");
    } else {
        ajaxData('/schoolIndexConfig/moveCourseDetail', {type: "2", moveType: moveType, id: id}, function () {
            popMsg('操作成功');
            //交换行
            if (moveType == "up") {
                $(obj).parent().parent().insertBefore($(obj).parent().parent().prev());
            } else if (moveType == "down") {
                $(obj).parent().parent().insertAfter($(obj).parent().parent().next());
            }
            $(obj).parent().parent().parent().find("tr").each(function (n, obj1) {
                $(obj1).find(".serialNumber").html(n + 1)
            })

        })
    }
}

function delTeacherDetail(id, obj) {
    var param = {
        type: "0",
        id: id
    }
    ajaxData("/schoolIndexConfig/delCourseDetail", param, function () {
        //删除一行table
        var obj1 = $(obj).parent().parent().parent()

        $(obj).parent().parent().remove();
        //刷新序号

        refreshCourseTableIndex(obj1);
    })
}

//设置教师背景图 0
function editPicture(id, obj) {
    $("#teacherFile").click();
    teacherId = id;
    teacherObj = $(obj).parent().parent();
}

//选择讲师
function chooseTeacher(id, obj) {
    var param = {
        groupId: id
    }
    popWin("选择讲师", "/schoolIndexConfig/chooseTeacher", param, "70%", "70%", function (data) {
        //查并且插入所选课程
        var param = {
            id: id,
            ids: data
        }
        ajaxData("/schoolIndexConfig/addTeacher", param, function (dto) {

            for (var nums in dto) {
                if (dto[nums]) {
                    var id = dto[nums].id;
                    var teacherName = dto[nums].teacherName;
                    var teacherId = dto[nums].teacherId;
                    var bgName = dto[nums].bgName;
                    var str = "<tr data-id= '" + id + "' data-index=\"2\">"
                        + "<td class=\"serialNumber\">"
                        + "3"
                        + "</td>"
                        + "<td class=\"teacherName\">"
                        + teacherName
                        + "</td>"
                        + "<td class=\"bgName\">"
                        + bgName
                        + "</td>"
                        + "<td>"
                        + "<span onclick=\"editPicture('" + teacherId + "',this)\" class=\"sch-btn\">设置图片</span>&nbsp;<span style=\"color: #0a67fb\">|</span>" +
                        "<span onclick=\"delTeacherDetail('" + id + "',this)\" class=\"sch-btn\">&nbsp;&nbsp;删除</span>&nbsp;<span style=\"color: #0a67fb\">|</span>" +
                        "<span onclick=\"moveTeacherDetail('up','" + id + "',this)\" class=\"sch-btn\">&nbsp;&nbsp;上移</span>&nbsp;<span style=\"color: #0a67fb\">|</span>" +
                        "<span onclick=\"moveTeacherDetail('down','" + id + "',this)\" class=\"sch-btn\">&nbsp;&nbsp;下移</span>" +
                        "</td>" +
                        "</tr>";
                    $(obj).parent().parent().parent().find("tbody").append(str);
                    //重新排序
                    $(obj).parent().parent().parent().find("tr").each(function (n, obj) {
                        $(obj).find(".serialNumber").html(n);
                    });
                }
            }
        })
    })

}

//教师结束
function textareaAuto() {
    $(function(){
        $('textarea').autoHeight();
    });
}


function editBottomBanner(id){
    addBannerConfig(id)
}

function addBannerConfig(id){
    var str="";
    if (id==""){
        str="新增banner"
    }else {
        str="修改banner"
    }
    var param={
        id:id,
        bannerKind:'0'
    }
    popWin(str, "/videomessage/bannerConfig", param, "1200px", "600px",
        bannerCallback, bannerCallback);
}


function bannerCallback(){
    $(".addBtnClass").hide()
    $("#tbodyId1").empty();
    var param={
        bannerKind:"0"
    }
    ajaxData("/videomessage/getBannerList", param, function (res) {
        var tbody=""
        for (var i=0;i<res.length;i++) {
            tbody+="<tr>\n" +
                " <td style=\"text-align: center;\">"+(i+1)+"</td>\n" +
                " <td style=\"text-align: center;\">"+res[i].bannerTitle+"</td>\n" +
                " <td style=\"text-align: center;\">"+res[i].bannerItemId+"</td>\n" +
                " <td style=\"text-align: center;\">"+res[i].bannerPicName+"</td>\n" +
                " <td style=\"text-align: center;\"><i class=\"fa fa-edit iconStyle\" id=\"editTable\" title=\"编辑\" onclick=\"editBottomBanner('"+res[i].id+"')\"></i>" +
                "</td>\n" +
                " </tr>"
        }
        $("#tbodyId1").append(tbody);
    })
}
//热门分类
function editCourseType(type,id,courseId,sort,courseTypeName) {
    var param = {
        type : type,
        id : id,
        courseId : courseId,
        sort : sort,
        courseTypeName:courseTypeName
    }
    popWin("编辑分类", "/schoolIndexConfig/editCourseType", param, "500px", "500px", editCourseTypeCallBack,"")
}
function editCourseTypeCallBack(data){
    $("#courseTableId").empty();
    var html = ""
    for(var i=0;i<data.length;i++){
        var type = "自动变更"
        var courseTypeName = "-"
        var cancelType = ""
        if (data[i].courseId != null && data[i].courseId != ''){
            courseTypeName = data[i].courseTypeName
            type = "固定"
            cancelType = "<i class=\"icon-link\" title=\"取消固定\" onclick=\"cancelCourseType('"+data[i].type+"','"+data[i].id+"')\"></i>"
        }
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+courseTypeName+"</td>"+
            "<td>"+type+"</td>"+
            "<td><i class=\"fa fa-edit\" style=\"cursor: pointer;margin: 0 5px;\" title=\"编辑\" onclick=\"editCourseType('"+data[i].type+"','"+data[i].id+"','"+data[i].courseId+"','"+data[i].sort+"')\"></i>"+cancelType+"</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseTableId").append(html)
}

//热门专题
function editcourseSpecia(type,id,courseId,sort,courseTypeName) {
    var param = {
        type : type,
        id : id,
        courseId : courseId,
        sort : sort,
        courseTypeName:courseTypeName
    }
    popWin("编辑专题", "/schoolIndexConfig/editCourseType", param, "680px", "600px", editCourseSpeciaCallBack,"")
}
function editCourseSpeciaCallBack(data) {
    $("#courseSpeciaTableId").empty();
    var html = ""
    for(var i=0;i<data.length;i++){
        var type = "自动变更"
        var courseTypeName = "-"
        var cancelType = ""
        if (data[i].courseId != null && data[i].courseId != ''){
            courseTypeName = data[i].courseTypeName
            type = "固定"
            cancelType = "<i class=\"icon-link\" title=\"取消固定\" onclick=\"cancelCourseType('"+data[i].type+"','"+data[i].id+"')\"></i>"
        }
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+courseTypeName+"</td>"+
            "<td>"+type+"</td>"+
            "<td><i class=\"fa fa-edit\" style=\"cursor: pointer;margin: 0 5px;\" title=\"编辑\" onclick=\"editcourseSpecia('"+data[i].type+"','"+data[i].id+"','"+data[i].courseId+"','"+data[i].sort+"')\"></i>"+cancelType+"</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseSpeciaTableId").append(html)
}

//热门课程
function editCourseInfo(type,id,courseId,sort,courseTypeName) {
    var param = {
        type : type,
        id : id,
        courseId : courseId,
        sort : sort,
        courseTypeName:courseTypeName
    }
    popWin("选择课程", "/schoolIndexConfig/editCourseInfo", param, "80%", "100%", editCourseInfoCallBack,"")
}
function editCourseInfoCallBack(data) {
    $("#courseInfoTable").empty()
    var html = ""
    for(var i=0;i<data.length;i++){
        var type = "自动变更"
        var courseTypeName = "-"
        var cancelType = ""
        var amount = "-"
        if (data[i].courseId != null && data[i].courseId != ''){
            courseTypeName = data[i].courseTypeName
            type = "固定"
            cancelType = "<i class=\"icon-link\" title=\"取消固定\" onclick=\"cancelCourseType('"+data[i].type+"','"+data[i].id+"')\"></i>"
            amount = data[i].amount
        }
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+courseTypeName+"</td>"+
            "<td>"+amount+"</td>"+
            "<td>"+type+"</td>"+
            "<td><i class=\"fa fa-edit\" style=\"cursor: pointer;margin: 0 5px;\" title=\"编辑\" onclick=\"editCourseInfo('"+data[i].type+"','"+data[i].id+"','"+data[i].courseId+"','"+data[i].sort+"')\"></i>"+cancelType+"</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseInfoTable").append(html)
}
//取消固定
function cancelCourseType(type,id) {
    popConfirm("取消固定？",function () {
        var param = {
            type :type,
            id : id,
            courseId : "",
        }
        ajaxData("/schoolIndexConfig/saveConfigCourseType",param,function (data) {
            if (type=='0'){
                editCourseTypeCallBack(data)
            }else if (type == '2') {
                editCourseSpeciaCallBack(data)
            }else if (type=='1'){
                editCourseInfoCallBack(data)
            }

        })
    })
}
//选择讲师
function addCourseTeacher() {
    var sort = $("#courseTeacherTableId").find("tr").length+1;
    var param = {
        sort : sort
    }
    popWin("选择讲师", "/schoolIndexConfig/editCourseTeacher", param, "80%", "100%", editCourseTeacherCallBack,"")
}
function editCourseTeacherCallBack(data) {
    $("#courseTeacherTableId").empty();
    var html = ""
    for(var i=0;i<data.length;i++){
        var appendHtml = "<tr>"+
            "<td>"+Number(i+1)+"</td>"+
            "<td>"+data[i].courseTypeName+"</td>"+
            "<td>"+data[i].teachOrg+"</td>"+
            "<td>" +
            "<i class=\"fa fa-edit\" style=\"cursor: pointer;margin: 0 5px;\" title=\"编辑\" onclick=\"editCourseTeacher('"+data[i].id+"','"+data[i].teacherId+"','"+data[i].sort+"','"+data[i].courseTypeName+"')\"></i>\n" +
            "<i class=\"fa fa-trash\" style=\"cursor: pointer;margin: 0 5px;\" title=\"删除\" onclick=\"deleteCourseTeacher('"+data[i].id+"','"+data[i].teacherId+"','"+data[i].sort+"','"+data[i].courseTypeName+"')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-up\" style=\"cursor: pointer;margin: 0 5px;\" title=\"上移\" onclick=\"editCourseTeacherUp('"+data[i].id+"','"+data[i].teacherId+"','"+data[i].sort+"','"+data[i].courseTypeName+"')\"></i>\n" +
            "<i class=\"fa fa-arrow-circle-down\" style=\"cursor: pointer;margin: 0 5px;\" title=\"下移\" onclick=\"editCourseTeacherDown('"+data[i].id+"','"+data[i].teacherId+"','"+data[i].sort+"','"+data[i].courseTypeName+"')\"></i>"+
            "</td>"+
            "</tr>"
        html = html+appendHtml
    }
    $("#courseTeacherTableId").append(html)
    $("#sort").val(data.length)
}
function editCourseTeacher(id,teacherId,sort,courseTypeName) {
    var param = {
        id : teacherId
    }
    popWin("讲师明细", "/ebSchoolLecturerInfo/teacherInit", param, "900px", "600px",editCourseTeacherCallBack,"");
}

function deleteCourseTeacher(id,teacherId,sort,courseTypeName) {
    popConfirm("确定删除？",function () {
        var param = {
            id: id,
            teacherId: teacherId,
            sort: sort,
            courseTypeName: courseTypeName
        }
        ajaxData("/schoolIndexConfig/deleteCourseTeacher", param, function (data) {
            popMsg("删除成功")
            editCourseTeacherCallBack(data)
        })
    })
}
function editCourseTeacherUp(id,teacherId,sort,courseTypeName) {
    if (sort=='1'){
        popMsg("当前排序已是最前")
    }else {
        var param = {
            id: id,
            teacherId: teacherId,
            sort: sort,
            courseTypeName: courseTypeName,
            move : '1'
        }
        ajaxData("/schoolIndexConfig/editCourseTeacherSort",param,function (data) {
            popMsg("上移成功")
            editCourseTeacherCallBack(data)
        })
    }
}
function editCourseTeacherDown(id,teacherId,sort,courseTypeName) {
    if (sort==$("#sort").val()){
        popMsg("当前排序已是最后")
    }else {
        var param = {
            id: id,
            teacherId: teacherId,
            sort: sort,
            courseTypeName: courseTypeName,
            move : '2'
        }
        ajaxData("/schoolIndexConfig/editCourseTeacherSort",param,function (data) {
            popMsg("下移成功")
            editCourseTeacherCallBack(data)
        })
    }
}