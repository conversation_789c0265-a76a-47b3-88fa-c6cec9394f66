$(document).ready(function () {
    userTableDataInit();
    btnInit();

});

function userTableDataInit() {
    var contractId = $("#contractId").val();
    ajaxData("/schUserManage/queryContractUserInfoDetail", {contractId: contractId}, function (res) {
        var count = 0;
        for (var i = 0; i < res.length; i++) {
            if (res[i].isAccredit == '1') {
                count++;
            }
        }
        $(".useNum").html(count);
        $(".useTotal").html(res.length)
    });
}

function userTableInit(flag) {
    ajaxTableReload('tableAll', false);
    if (!flag) {
        userTableDataInit();
    }
}

function editUserInfo(userId, contractId) {
    parent.popWin('编辑用户信息', '/schUserManage/editOrgUserInit', {
        userId,
        contractId,
        startTime:$("#startTime").html(),
        endTime:$("#endTime").html()
    }, '98%', '98%', userTableInit, '', userTableInit);
}

function changeIsAccredit(item) {
    var id = $(item).parents("tr").find(".userContractId").val();
    var contractId = $("#contractId").val();
    var isAccredit = $(item).parents("tr").find(".isAccreditData").val() != 'null' ? $(item).parents("tr").find(".isAccreditData").val() : '0';
    isAccredit = isAccredit == '1' ? '0' : '1';
    ajaxData("/schUserManage/editOrgUserSave", {id, isAccredit, contractId}, function (res) {
        if (res == '1') {
            if (isAccredit == '1') {
                popMsg("授权成功");
            } else {
                popMsg("取消授权成功");
            }
            $(item).parents("tr").find(".isAccreditData").val(isAccredit)
            //刷新页面授权数量
        } else {
            if (isAccredit == '1') {
                popMsg("授权失败");
                $(item).prop("checked",false)
            } else {
                popMsg("取消授权失败");
            }
        }
        userTableInit();
    });
}

function btnInit() {
    $("#btnAddUser").bind("click", function () {
        editUserInfo("", $("#contractId").val())
    });

    $('#windowFileupload').fileupload({
        url: contextPath + '/schUserManage/importUserInfo',
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            //校验必填
            if (checkFileType(data.files[0].name)) {
                addLoading();
                data.submit();
            } else {
                parent.popMsg("请选择Excel文件上传");
            }
        },
        done: function (e, data) {
            removeLoading();
            var failList = data.result.result.failList;
            var successList = data.result.result.successList;

            if(failList.length == 0 && successList == 0){
                parent.popMsg("您当前的授权数量已超过合同的总授权量，请重新操作！");
            }else{
               var failAccount = "";
                for(var n in failList){
                    failAccount += "<span>" + failList[n].nickName + "</span>&nbsp;&nbsp;<span>" + failList[n].phone + "</span><br/>"
                }
                parent.popAlert("<span>成功匹配" + successList.length +  "个，失败" + failList.length + "个，失败账号如下：</span>"
                + "<br/>" + failAccount);
            }
            userTableInit();
        }
    });
}

function queryIsAccredit(item) {
    var isAccredit = $(item).prop("checked");
    var contractId = $("#contractId").val();
    isAccredit = isAccredit ? '1' : '';
    $("#isAccredit").val(isAccredit);

    ajaxTableQuery("tableAll", "/schUserManage/queryContractUserInfo",
        $("#schContractForm").formSerialize());

    userTableDataInit(true);
}

function delUserInfo(userId, contractId) {
    popConfirm("确认删除", function () {
        ajaxData("/schUserManage/delOrgUserInfo", {userId, contractId}, function (res) {
            //删除成功
            parent.popMsg("删除成功");
            userTableInit()
        });
    })
}

function updateUserTime() {
    var contractId = $("#contractId").val();
    parent.popConfirm("确认是否更新", function () {
        ajaxData("/schUserManage/updateUserTime", {contractId}, function (res) {
            parent.popMsg("更新成功");
            userTableInit()
        });
    });
}

function downloadInfo() {
    window.open(contextPath + "/schUserManage/downloadInfo");
}

function checkFileType(name) {
    var checkFile = name.substring(name.lastIndexOf(".") + 1, name.length);
    var resumeType = ["xlsx", "xls"];
    for (n in resumeType) {
        if (checkFile) {
            if (checkFile.toLowerCase() == resumeType[n]) {
                return true;
            }
        }
    }
    return false;
}

function renderColumnOption(data, type, row, meta) {
    var str = '<span><input class="hidden userContractId" value="' + data.id + '"/>'
        + '<input class="hidden isAccreditData" value="' + data.isAccredit + '"/>'
        + '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑用户信息" onclick="editUserInfo(\'' + data.userId + '\',\'' + data.contractId + '\')"></i>'
        + '<i class="fa fa-trash" style="cursor: pointer;margin: 0 5px;" title="删除用户信息" onclick="delUserInfo(\'' + data.userId + '\',\'' + data.contractId + '\')"></i></span>';
    return str;
}

function rcIndex(data, type, row, meta) {
    return meta.row + 1;
}

function renderColumnIsAccredit(data, type, row, meta) {
    return data.isAccredit == '1' ? '<input type="checkbox" checked class="isAccredit" onchange="changeIsAccredit(this)"/>' : '<input type="checkbox" class="isAccredit" onchange="changeIsAccredit(this)"/>';
}
