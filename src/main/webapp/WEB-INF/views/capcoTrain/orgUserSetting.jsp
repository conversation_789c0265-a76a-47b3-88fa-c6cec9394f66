<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        .labelName{
            width:111px;
            text-align: left;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <div class="col-md-12">
            <div style="width:1100px;margin: auto;min-height:700px;padding-top:50px">
                <form:form id="schContractForm" modelAttribute="schContractDto" cssClass="form-horizontal">
                    <form:hidden path="contractId"/>
                    <form:hidden path="isAccredit"/>
                    <form:hidden path="orgId"/>

                    <div class="row">
                        <label class="col-md-2 control-label labelName">内部合同编码：</label>
                        <div class="col-md-2">
                            <label id="contractNo" placeholder="请输入内部合同编码" class="labelName">${schContractDto.contractNo}</label>
                        </div>
                        <label class="col-md-2 control-label labelName">授权起始日：</label>
                        <div class="col-md-2">
                            <label id="startTime" placeholder="请输入授权起始日" class="labelName"
                                   autocomplete="off">${schContractDto.startTime}</label>
                        </div>
                        <label class="col-md-2 control-label labelName">授权截止日：</label>
                        <div class="col-md-2">
                            <label id="endTime" placeholder="请输入授权截止日" class="labelName"
                                   autocomplete="off">${schContractDto.endTime}</label>
                        </div>
                    </div>

                    <div class="row">
                        <label class="col-md-2 control-label labelName">购买数量：</label>
                        <div class="col-md-2">
                            <label id="buyNum" placeholder="请输入购买数量" class="labelName"
                                   autocomplete="off">${schContractDto.buyNum}</label>
                        </div>
                        <label class="col-md-2 control-label labelName">赠送数量：</label>
                        <div class="col-md-2">
                            <label id="giveNum" placeholder="请输入赠送数量" class="labelName"
                                   autocomplete="off">${schContractDto.giveNum}</label>
                        </div>
                        <label class="col-md-2 control-label labelName">授权数量总计：</label>
                        <div class="col-md-2">
                            <label id="totalNum" class="labelName"
                                   autocomplete="off">${schContractDto.totalNum}</label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <label style="font-size: 16px;font-weight: bold;padding: 20px 0px;">用户信息</label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <span>用户总计<span class="useTotal"></span>，已授权<span class="useNum"></span></span>
                            <input type="checkbox" style="margin-left:15px" id="isAccredit" onchange="queryIsAccredit(this)"><span>只显示已授权</span>
                            <div style="display: inline-block;float:right">
                                <span style="cursor:pointer;color:#0356E0;padding-right: 10px;" onclick="downloadInfo()">下载Excel模板</span>
                                <span style="cursor:pointer;color:#0356E0" onclick="updateUserTime()">更新所有授权时间</span>
                                <span class="btn btn-primary fileinput-button"><span class="">Excel导入用户</span><input id="windowFileupload" type="file" name="files" class="btn btn-primary"/></span>
                                <span class="btn btn-primary" id="btnAddUser">添加用户</span>
                            </div>
                        </div>
                    </div>
                </form:form>

                <div class="row">
                    <e:grid id="tableAll" action="/schUserManage/queryContractUserInfo?contractId=${schContractDto.contractId}" cssClass="table table-striped table-hover">
                        <e:gridColumn label="序号" renderColumn="rcIndex" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        <e:gridColumn label="姓名" displayColumn="nickName" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        <e:gridColumn label="手机号" displayColumn="phone" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        <e:gridColumn label="授权起始日期" displayColumn="startTime" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        <e:gridColumn label="授权截止日期" displayColumn="endTime" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        <e:gridColumn label="是否授权" renderColumn="renderColumnIsAccredit" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                        <e:gridColumn label="操作" renderColumn="renderColumnOption" orderable="false" cssClass="text-center" cssStyle="width:8%;text-align:center;"/>
                    </e:grid>

                    <%----%>
                    <%--<table id="userTable" class="table table-bordered no-margin"--%>
                           <%--style="text-align: center;border-color: #D7D7D7;margin-top: 10px !important;width:100%">--%>
                        <%--<thead>--%>
                        <%--<th class="text-center table-th" width="10%">序号</th>--%>
                        <%--<th class="text-center table-th" width="10%">姓名</th>--%>
                        <%--<th class="text-center table-th" width="10%">手机号</th>--%>
                        <%--<th class="text-center table-th" width="10%">授权起始日期</th>--%>
                        <%--<th class="text-center table-th" width="10%">授权截止日</th>--%>
                        <%--<th class="text-center table-th" width="10%">是否授权</th>--%>
                        <%--<th class="text-center table-th" width="10%">操作</th>--%>
                        <%--</thead>--%>
                        <%--<tbody id="userBody">--%>
                        <%--</tbody>--%>
                    <%--</table>--%>
                </div>
            </div>

        </div>

    </div>
</div>
</body>
</html>
