<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style type="text/css">
        .control-label {
            text-align: right !important;
            text-align: center;
            margin-top: 6px;
        }
        .ellipsis {
            max-width: 850px;
            overflow: hidden; /*自动隐藏文字*/
            text-overflow: ellipsis;/*文字隐藏后添加省略号*/
            white-space: nowrap;/*强制不换行*/
            width: 16em;/*不允许出现半汉字截断*/
        }
        input {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  讲师列表</i>
    </div>
    <div class="panel-body">
        <form:form modelAttribute="ebSchoolLecturerInfoDto" id="teachInfoForm">
            <div class="row">
                <label class=" col-md-1 control-label">讲师姓名：</label>
                <div class="col-md-3">
                    <form:input path="teachName" placeholder="请输入讲师姓名" cssClass="form-control" autocomplete="off"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
                </div>
            </div>
            <div class="row" style="margin-bottom: 8px;float: right;">
<%--                <sec:authorize access="hasAuthority('RES_EB_SCHOOL_LECTURER_EDIT_AUTHORITY_3')" >--%>
                     <span id="btnClear" class="btn btn-default">清空条件</span>
                    <span id="btnAdd" class="btn btn-primary">新增讲师</span>
<%--                </sec:authorize>--%>

<%--                <sec:authorize access="hasAuthority('RES_EB_SCHOOL_LECTURER_QUERY_AUTHORITY_3')" >--%>
                    <span id="btnQuery" class="btn btn-primary">查询</span>
<%--                </sec:authorize>--%>
            </div>
        </form:form>
        <div class="row">
            <e:grid id="table1" action="/ebSchoolLecturerInfo/queryLecturerInfoList"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%"/>
                <e:gridColumn label="讲师名称" displayColumn="teachName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="讲师性别" displayColumn="teacherSex" orderable="false"
                              cssClass="text-center" cssStyle="width:10%"/>
                <e:gridColumn label="讲师简介" displayColumn="teachIntro" orderable="false"
                              cssClass="text-center ellipsis" cssStyle="width:55%;"/>
                <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false"
                              cssClass="text-center" cssStyle="width:20%;"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>

