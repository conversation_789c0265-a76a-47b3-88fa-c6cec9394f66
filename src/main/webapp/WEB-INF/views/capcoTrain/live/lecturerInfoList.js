// let teachOrg = ''
var param ={}
$(document).ready(function() {

    //查询
    $("#btnQuery").bind("click",function() {
        var teachName = $("#teachName").val().replace(/(^\s*)|(\s*$)/g, "");
        param = {
            teachName:teachName
        }
        ajaxTableQuery("table1", "/ebSchoolLecturerInfo/queryLecturerInfoList",param);
        // ajaxTableQuery("table1", "/ebSchoolLecturerInfo/queryLecturerInfoList", $("#teachInfoForm").formSerialize());
    });
    //清空
    $("#btnClear").bind("click", function () {
        $('input[name="teachName"]').val("");
        $('#teachName').val("")
        param = {
            teachName: ""
        }
        document.getElementById("teachInfoForm").reset();
        ajaxTableQuery("table1", "/ebSchoolLecturerInfo/queryLecturerInfoList",param);
    });
    //同步
    $("#btnSyn").bind("click", function () {
        ajaxTableQuery("table1", "/ebSchoolLecturerInfo/synLecturerInfo","",function (){
            popMsg("同步成功");
        });
    });
    //新增
    $("#btnAdd").bind("click", function() {
        var param = {
            type:$("#type").val()
        }
        parent.popWin("基本信息", "/ebSchoolLecturerInfo/teacherInit", param, "900px", "600px", winCallback, "");
    });
});


function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
    teachOrg = d.value
}
//序号
function renderColumnIndex(data, type, row, meta){
    return meta.row + 1;
}

// 操作
function renderColumnOperation(data, type, row, meta){

    var str = '';
        str += '<a href="#" onClick="teachInfoEdit(\''+data.id+'\')" style="margin-right:4px;">编辑</a>';
        str += ' | <a href="#" onClick="teachInfoDel(\''+data.id+'\')" style="margin-right:4px;">删除</a>';
    return str;
}

function teachInfoEdit(id){
    var param = {
        id : id
    }
    parent.popWin("讲师明细", "/ebSchoolLecturerInfo/teacherInit", param, "900px", "600px", winCallback, "");
}


//删除
function teachInfoDel(id){
    popConfirm("确认删除该讲师吗？",function(){
        var param = {
            id:id
        };
        ajaxData("/ebSchoolLecturerInfo/teachInfoDel",param,function(data) {
            if(data > 0) {
                ajaxTableReload("table1",false);
            } else {
                popMsg("操作失败，请稍后再试");
            }
        });
    })
}

function winCallback(paraWin, paraCallBack) {
    ajaxTableReload("table1", false);
}
