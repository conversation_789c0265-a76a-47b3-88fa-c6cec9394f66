<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>

    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/viewer.js"></script>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/viewer.css">
    <e:base />
    <e:js />
</head>
<style>
    .row{
        margin: 0;
    }
</style>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="teacherInfoDto" modelAttribute="teacherInfoDto" cssClass="form-horizontal">
            <form:hidden path="id"/>
            <form:hidden path="url" value="${teacherInfoDto.teachPic}"/>
            <form:hidden path="newUrl" value="${teacherInfoDto.newTeachPic}"/>
            <form:hidden path="oldUrl"/>
            <div class="row" style="padding-top: 15px;padding-bottom: 10px">
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">姓名:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.teachName}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">性别:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.teacherSex}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">讲师电话:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.phone}</label>
                </div>
            </div>
            <div class="row" style="padding-top: 15px;padding-bottom: 10px">
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;" id="companyNameLabel">邮箱:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.email}</label>
                </div>

                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">登记日期:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.registrationTime}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">讲师职位:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.title}</label>
                </div>
            </div>
            <div class="row" style="padding-top: 15px;padding-bottom: 10px">
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" id="orgNameLabel" style="width: 90px;">所属机构:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.teachOrg}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">业务方向:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.businessDirection}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;" id="jobNameLabel">邮寄地址:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.address}</label>
                </div>
            </div>
            <div class="row" style="padding-top: 15px;padding-bottom: 10px">
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">曾在协会授课:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.ifOnce}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">关联课程:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.courseName}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">讲师简介:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.teachBriefIntro}</label>
                </div>
            </div>
            <div class="row" style="padding-top: 15px;padding-bottom: 10px">
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">行业方向:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.industryDirection}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">讲师介绍:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.teachIntro}</label>
                </div>
                <div class="col-md-4">
                    <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">推荐理由:</label>
                    <label class="col-md-9 control-label">${teacherInfoDto.reason}</label>
                </div>
            </div>
            <div class="col-md-12" style="padding-top: 15px;padding-bottom: 10px">
                <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">导师照片:</label>
                <div class="col-md-12">
                    <div id="imgId" class="col-md-5">
                        <img id="ImgPr" src="${teacherInfoDto.teachPic}" style="width: 100px;height: 150px;"/>
                    </div>
                    <div id="newImgId" class="col-md-5">
                        <img id="newImgPr" src="${teacherInfoDto.newTeachPic}" style="width: 100px;height: 100px;"/>
                    </div>
                </div>
            </div>
            <div class="col-md-12" style="padding-top: 15px;padding-bottom: 10px">
                <label class="col-md-2 no-padding control-label text-right" style="width: 90px;">推荐理由附件</label>
                <div class="col-xs-6 controls">
                    <div class="col-xs-12 no-padding" id="fileDiv" style="color: #145ccd;cursor: pointer;">
                        <c:forEach items="${teacherInfoDto.fileList}" var="item" varStatus="status">
                            <div class="col-xs-12 file-content">
                                <span onclick="downloadFile('${item.attId}', 1)" class="file-span" title="下载"
                                      data-file-type="1" data-atta-id="${item.attId}"
                                      data-file-id="${item.attUrl}">${item.fileName}</span>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
</div>
</body>
</html>
