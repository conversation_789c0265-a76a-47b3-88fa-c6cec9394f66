//@ sourceURL=orgInit.js
var cont = 0;
$(document).ready(function() {
    $("#btnAdd").bind("click", function() {
        addTagRow();
    });

    $("#btnSave").bind("click", function() {
        tableSave();
    });
})

function addTagRow(){
    var param ={
        type:$("#type").val()
    }
    ajaxData('/ebSchoolLecturerInfo/getValueInfo', param, function (data){
        var len=$('#courseDataBody').find("tr").length
         cont +=1;
        var  va =parseInt(data)+cont;
        var row=""
        row+="<tr>\n" +
            "<td style=\"text-align: center;\">"+(len+1)+"</td>\n" +
            "<td style=\"padding-right: 0\"><input type=\"hidden\" name=\"orgList["+len+"].typeValue\" value=\""+va+"\">" +
            "<input style=\"width: 100%;\" name=\"orgList["+len+"].typeName\"></td>\n" +
            '<td style="border-left-style:hidden;"><i class="icon-trash" title="删除" onclick="deleteOrg(null,this)"></i></td>'+
            "</tr>"
        $('#courseDataBody').append(row)
    });
}

function tableSave(){
    var flag = true;
    $('input').each(function (){
        if ($(this).val().replace(/(^\s*)|(\s*$)/g, "")==""){
            popMsg("不能为空")
            flag = false
        }
    })
    if(flag){
        ajaxSubmitForm("/ebSchoolLecturerInfo/saveOrgName", "",function(data){
            cont = 0;
            $('#courseDataBody').empty(tbody);
            for (var i=0;i<data.length;i++){
                var tbody = "";
                tbody='<tr><td style="text-align: center;">'+(i+1)+'</td>' +
                    '<td style="padding-right: 0"><input type="hidden" name="orgList['+i+'].id" value="'+data[i].id+'">' +
                    '<input type="hidden" name="orgList['+i+'].typeValue" value="'+data[i].typeValue+'">' +
                    '<input style="width: 100%;" name="orgList['+i+'].typeName" value="'+data[i].typeName+'"></td>' +
                    "<td style=\"border-left-style:hidden;\"><i class=\"icon-trash\" title=\"删除\" onclick=\"deleteOrg('"+data[i].id+"',this)\"></i></td></tr>"
                $('#courseDataBody').append(tbody)
            }
            popMsg("保存成功")
        })
    }
}
function updateNumber(){
        ajaxSubmitForm("/ebSchoolLecturerInfo/saveOrgName", "",function(data){
            cont = 0;
            $('#courseDataBody').empty(tbody);
            for (var i=0;i<data.length;i++){
                var tbody = "";
                tbody='<tr><td style="text-align: center;">'+(i+1)+'</td>' +
                    '<td style="padding-right: 0"><input type="hidden" name="orgList['+i+'].id" value="'+data[i].id+'">' +
                    '<input type="hidden" name="orgList['+i+'].typeValue" value="'+data[i].typeValue+'">' +
                    '<input style="width: 100%;" name="orgList['+i+'].typeName" value="'+data[i].typeName+'"></td>' +
                    "<td style=\"border-left-style:hidden;\"><i class=\"icon-trash\" title=\"删除\" onclick=\"deleteOrg('"+data[i].id+"',this)\"></i></td></tr>"
                $('#courseDataBody').append(tbody)
            }
        })
}

function deleteOrg(id,obj) {
    if(id == null){
        $(obj).parent().parent().remove();
        popMsg("删除成功")
        updateNumber();
    }else {
        parent.popConfirm("确认删除该机构?", function () {
            var param ={
                id:id
            };
            ajaxData('/ebSchoolLecturerInfo/deleteOrg', param, function (){
                $(obj).parent().parent().remove();
                popMsg("删除成功")
                updateNumber();
            });
        });
    }
}