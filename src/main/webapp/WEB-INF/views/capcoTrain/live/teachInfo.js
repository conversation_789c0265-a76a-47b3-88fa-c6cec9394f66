var _Openfileupload = contextPath + '/ebSchoolLecturerInfo/uploadCourseFile';
let _fileUploadUrl = contextPath + '/filetempupload';
$(document).ready(function() {
    // 取消
    $('#btnCancel').bind('click', function() {
        closeWin();
    });

    // 保存
    $("#btnSave").bind("click", function() {
        if( getValue($('#teachName').val())  == ""  ){
            popMsg("请输入讲师姓名");
            return;
        }
        if(getValue($("#teacherSex").val()) == "") {
            popMsg("请选择讲师性别");
            return;
        }
        if(getValue($("#fileId").val()) == "") {
            $("#fileId").val(getValue($("#teachPic").val()))
        }
        if(getValue($("#newFileId").val()) == "") {
            $("#newFileId").val(getValue($("#newTeachPic").val()))
        }
        if(getValue($("#teachIntro").val()) == "") {
            popMsg("请输入讲师简介");
            return;
        }
        if(getValue($("#fileId").val()) == "") {
            popMsg("请上传讲师照片一");
            return;
        }
        if(getValue($("#newFileId").val()) == "") {
            popMsg("请上传导师照片二");
            return;
        }
        if ($("#winningRecordForm").valid()) {
            var param = {
                url:$("#url").val(),
                newUrl:$("#newUrl").val(),
                id:$("#id").val(),
                teachName:$("#teachName").val(),
                teachPic :$("#fileId").val(),
                newTeachPic :$("#newFileId").val(),
                teachIntro:$("#teachIntro").val(),
                teacherSex : $("#teacherSex").val(),
            };
            $.ajax({
                url: contextPath + "/ebSchoolLecturerInfo/saveTeacherInfo",
                data: JSON.stringify(param),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (req) {
                    if (req.success) {
                        closeWinCallBack(req);
                        popMsg("保存成功");
                    } else {
                        let errorMsg = ( req.error ? req.error : req.errorMsg );
                        popMsg(errorMsg);
                    }
                },
                error: function () {
                    popMsg("保存失败");
                }
            });
        }
    })

    //上传2:3图片
    uploadFile();
    //上传1:1new图片
    uploadNewFile();
    //初始化照片
    imgLoad();
})

function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
    teachOrg = d.value
}/**
 * 删除图片
 * @param obi
 * @param fieldId
 */
function clearImg(obi, fieldId) {
    popConfirm("确认删除图片?", function() {
        $("#fileId").val("");
        $("#imgId").css("display", "none");
        $("#up").val("");
        if ($("#teachPic").val() != "") {
            $("delFileId").val($("#teachPic").val());
            $("#teachPic").val("");
        }
    });
}
//删除1:1图片
function newClearImg() {
    popConfirm("确认删除头像图片", function() {
        $("#newFileId").val("");
        $("#newImgId").css("display", "none");
        $("#newUp").val("");
        if ($("#newTeachPic").val() != "") {
            $("newDelFileId").val($("#newTeachPic").val());
            $("#newTeachPic").val("");
        }
    });
}

$.fn
    .extend({
        uploadPreview : function(opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img : "ImgPr",
                Width : 100,
                Height : 100,
                ImgType : [ "gif", "jpeg", "jpg", "bmp", "png" ],
                Callback : function() {
                }
            }, opts || {});
            _self.getObjectURL = function(file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }
                if ($("#teachPic").val() != "") {
                    $(
                        '<input type="hidden" name="delFileId" value="'
                        + $("#teachPic").val() + '"/>').appendTo(
                        '#delFileIdList');
                    $("#teachPic").val("");
                }
                $("#imgId").css("display", "block");
                return url
            };

            _this.change(function() {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter' : 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width' : opts.Width
                                            + 'px',
                                        'height' : opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })

function uploadFile() {
    var url = contextPath + '/filetempupload';
    $('#up').fileupload({
        url : url,
        dataType : 'json',
        autoUpload : true,
        submit : function(e, data) {
            index = layer.load(1, {
                shade : [ 0.1, '#fff' ]
                // 0.1透明度的白色背景
            });
        },
        done : function(e, data) {
            $.each(data.result, function(index, file) {
                $("#fileId").val(file.filePath);
                $("#teachPic").val(file.filePath);
            });
            layer.close(index);
            if(getValue($('#teachPic').val())  == ""){
                document.getElementById("imgId").style.display = "none";
            }
            else{
                document.getElementById("imgId").style.display = "block";
            }
        }
    });

    $("#up").uploadPreview({
        Img : "ImgPr",
        Width : 50,
        Height : 75
    });
}
function uploadNewFile(){
    var url = contextPath + '/filetempupload';
    $('#newUp').fileupload({
        url : url,
        dataType : 'json',
        autoUpload : true,
        submit : function(e, data) {
            index = layer.load(1, {
                shade : [ 0.1, '#fff' ]
                // 0.1透明度的白色背景
            });
        },
        done : function(e, data) {
            $.each(data.result, function(index, file) {
                $("#newFileId").val(file.filePath);
                $("#newTeachPic").val(file.filePath);
            });
            layer.close(index);
            if(getValue($('#newTeachPic').val())  == ""){
                document.getElementById("newImgId").style.display = "none";
            }
            else{
                document.getElementById("newImgId").style.display = "block";
            }
        }
    });

    $("#newUp").uploadPreview({
        Img : "newImgPr",
        Width : 50,
        Height : 50
    });
    if(getValue($('#newTeachPic').val())  == ""){
        document.getElementById("newImgId").style.display = "none";
    }
    else{
        document.getElementById("newImgId").style.display = "block";
    }
}
//判断图片是否显示
function imgLoad(){
    debugger
    if(getValue($('#teachPic').val())  == ""){
        document.getElementById("imgId").style.display = "none";
    }
    else{
        document.getElementById("imgId").style.display = "block";
    }
    if(getValue($('#newTeachPic').val())  == ""){
        document.getElementById("newImgId").style.display = "none";
    }
    else{
        document.getElementById("newImgId").style.display = "block";
    }
}



