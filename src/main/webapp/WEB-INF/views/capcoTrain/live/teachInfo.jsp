<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base />
    <e:js />
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/video/js-vod-sdk-1.0.1.min.js">
        var id = '${id}';
    </script>

    <style>
        label {
            padding-top: 7px;
        }

        #liveFile {
            margin: -20px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }

        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }

        #livePicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        #backImgFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }

        #dataFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            height: 32px;
        }

        #livePicImg {
            height: 200px;
            border: 0;
            margin: 10px 0;
        }
        #backImg {
            height: 200px;
            border: 0;
            margin: 10px 0;
        }


        .ztree * {
            font-size: 14px !important;
        }

        .tree-div {
            /*display: none;*/
        }

        .table-th {
            background-color: #37C8F4 !important;
            color: white;
        }

        .table-del {
            font-style: inherit !important;
        }
        .isNumber{
            border: 1px solid #ccc;
            height: 30px;
            width: 50%;
            background: #F5F5F5;
        }

    </style>
</head>
<body>
<div class="panel">
    <div class="panel-body">
        <form:form id="winningRecordForm" modelAttribute="teacherInfoDto" cssClass="form-horizontal">
            <form:hidden path="id"/>
            <form:hidden path="url" value="${teacherInfoDto.teachPic}"/>
            <form:hidden path="newUrl" value="${teacherInfoDto.newTeachPic}"/>
            <form:hidden path="oldUrl"/>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>讲师姓名：</label>
                <div class="col-md-4 no-padding" style="width: 220px">
                    <c:if test="${teacherInfoDto.mark!='0'}">
                    <form:input path="teachName" cssClass="form-control"  placeholder="请输入讲师名称" maxlength="45"
                                autocomplete="off"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"/>
                    </c:if>
                    <c:if test="${teacherInfoDto.mark=='0'}">
                        <span style="margin-top: 5px;display: inline-block">${teacherInfoDto.teachName}</span>
                    </c:if>
                </div>
            </div>
            <div class="col-md-12"  style="display: flex">
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>讲师性别：</label>
                <div class="col-md-4 no-padding" style="width: 220px">
                    <c:if test="${teacherInfoDto.mark!='0'}">
                    <select id="teacherSex" name="teacherSex" class="form-control">
                        <option value="">请选择</option>
                        <option value="1"
                                <c:if test="${teacherInfoDto.teacherSex=='1'}">
                                    selected
                                </c:if>
                        >先生</option>
                        <option value="2"
                                <c:if test="${teacherInfoDto.teacherSex=='2'}">
                                    selected
                                </c:if>
                        >女士</option>
                    </select>
                    </c:if>
                    <c:if test="${teacherInfoDto.mark=='0'}">
                        <span style="margin-top: 5px;display: inline-block">
                        <c:if test="${teacherInfoDto.teacherSex=='1'}">先生</c:if>
                        <c:if test="${teacherInfoDto.teacherSex=='2'}">女士</c:if>
                        </span>
                    </c:if>
                </div>
            </div>
            <div class="col-md-12" >
                <label class="col-md-2 no-padding control-label" style="margin-top: 5px;width: 100px;"><font color="#FF0000">*</font>讲师简介：</label>
                <div class="col-md-10 no-padding" >
                    <c:if test="${teacherInfoDto.mark!='0'}">
                    <form:textarea path="teachIntro"  autocomplete="off" cssClass="form-control" rows="4" maxlength="500" />
                    </c:if>
                    <c:if test="${teacherInfoDto.mark=='0'}">
                        <span style="margin-top: 5px;margin-left:10px;display: inline-block">  ${teacherInfoDto.teachIntro}</span>
                    </c:if>
                </div>
            </div>
            <div class="col-md-12" style="max-height: 170px;height: 170px;display: flex;" >
                <label class="col-md-3 no-padding control-label" style="margin-top: 5px;"><font color="#FF0000">*</font>讲师照片：
                        <%--                        <div style="font-size: 12px;color: red;">（宽高比为2: 3）</div>--%>
                </label>
                <div class="col-md-5" style="width: 40%">
                    <label class="col-md-3 no-padding control-label" style="margin-top: 5px;">上传导师照片一
                        <div style="font-size: 12px;color: red;">（宽高比为2: 3）</div>
                    </label>
                    <div class="col-md-4" style="display: inline-block;position: relative;top: -16px ">
                        <c:if test="${teacherInfoDto.mark!='0'}">
                        <span class="btn btn-sm btn-success fileinput-button">
                            <span>上传</span>
                            <input id="up" type="file" name="files">
                        </span>
                        <span onclick="clearImg()" class="btn btn-sm" style="margin-left: 20px;">删除</span>
                        </c:if>
                    </div>
                    <div id="imgId" class="col-md-5" >
                        <img id="ImgPr" src="${teacherInfoDto.teachPic}" style="width: 100px;height: 150px;"/>
                        <input type="hidden" name="fileId" id="fileId" value="${teacherInfoDto.teachPic}"/>
                        <input type="hidden" name="delFileId" id="delFileId" value="${teacherInfoDto.teachPic}"/>
                        <form:hidden path="teachPic" />
                    </div>
                </div>
                <div class="col-md-5" style="width: 40%">
                    <label class="col-md-3 no-padding control-label" style="margin-top: 5px;">上传导师照片二
                        <div style="font-size: 12px;color: red;">（宽高比为1: 1）</div>
                    </label>
                    <div class="col-md-4" style="display: inline-block;position: relative; top: -16px">
                        <span class="btn btn-sm btn-success fileinput-button">
                            <span>上传</span>
                            <input id="newUp" type="file" name="files">
                        </span>
                        <span onclick="newClearImg()" class="btn btn-sm" style="margin-left: 20px;">删除</span>
                    </div>
                    <div id="newImgId" class="col-md-5" style="display: none">
                        <img id="newImgPr" src="${teacherInfoDto.newTeachPic}" style="width: 100px;height: 100px;"/>
                        <input type="hidden" name="newFileId" id="newFileId" value="${teacherInfoDto.newTeachPic}"/>
                        <input type="hidden" name="newDelFileId" id="newDelFileId" value="${teacherInfoDto.newTeachPic}"/>
                        <form:hidden path="newTeachPic" />
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top: 20px;text-align: center;">
                <c:if test="${teacherInfoDto.mark!='0'}">
                    <input type="button" id="btnSave" style="margin-right: 15px;" class="btn btn-primary" value="保存" />
                    <input type="button" id="btnCancel" class="btn btn-default" value="取消" />
                </c:if>
            </div>

        </form:form>
    </div>
</div>
</body>
</html>
