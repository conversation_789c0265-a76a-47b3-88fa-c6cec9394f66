var saveFlag = true;
$(document).ready(function () {

    getColumnDetailVersionInfo();
    
    $("#btnClose").bind("click", function (data) {
        closeWinCallBack();
    });

    $("#btnSave").bind("click", function (data) {
        if(ifSameVersionType()){
            parent.popMsg("课程版本号重复，无法保存！");
        }else{
            ajaxSubmitForm('/schoolCourse/saveSchoolColumnDetailVersion', '', function (res) {
                parent.popMsg("保存成功！");
                saveFlag = true;
            })
        }
    });
});

//判断  版本号唯一
function ifSameVersionType() {
    var sameFlag = false;
    var versionTypeArr = [];
    $("#versionBody").find(".versionType").each(function (i, o) {
        versionTypeArr.push($(o).val());
    });
    let s = versionTypeArr.join(",")+",";
    for(let i=0;i<versionTypeArr.length;i++) {
        if(s.replace(versionTypeArr[i]+",","").indexOf(versionTypeArr[i]+",")>-1) {
            sameFlag = true;
            break;
        }
    }
    return sameFlag;
}

function getColumnDetailVersionInfo() {
    var param = {
        columnId: $("#columnId").val(),
        columnSort: $("#columnSort").val()
    }
    ajaxData("/schoolCourse/getColumnDetailVersionInfo", param, function (result) {
        spliceTable(result);
    })
}

function showCourse() {
    if(saveFlag){
        var courseId = getListCourseId();
        var param = {
            courseId: courseId,
            relaType: '01',
        }
        parent.popWin("关联课程", "/schoolCourse/schColumnRelaCourse", param, "100%", "100%", function (result) {
            //按照课程id排序版本号
            var compare = function (obj1, obj2) {
                var val1 = obj1.id;
                var val2 = obj2.id;
                if (val1 < val2) {
                    return -1;
                } else if (val1 > val2) {
                    return 1;
                } else {
                    return 0;
                }
            }
            var tableInfo = result.sort(compare);
            //再以版本号排序
            var compareVersionType = function (obj1, obj2) {
                var val1 = obj1.versionType == null ? 10000 : obj1.versionType;
                var val2 = obj2.versionType == null ? 10000 : obj2.versionType;
                if (val1 < val2) {
                    return -1;
                } else if (val1 > val2) {
                    return 1;
                } else {
                    return 0;
                }
            }
            tableInfo = result.sort(compareVersionType);


            var versionType = 0;
            for(var i=0;i<tableInfo.length;i++){
                if(tableInfo[i].versionType){
                    versionType = tableInfo[i].versionType;
                }else{
                    versionType = Number(versionType) + 1;
                    tableInfo[i].versionType = versionType;
                }
            }

            //清除重新加载表格
            spliceTable(tableInfo);
        });
    }else{
        parent.popMsg("请保存后，再进行操作！");
    }
}

function getListCourseId() {
    var courseId;
    var courseIds = [];
    $("#versionBody").find(".courseId").each(function (i, o) {
        courseIds.push($(o).val());
    });

    if (courseIds.length > 0) {
        courseId = courseIds.join();
    }
    return courseId;
}

function spliceTable(result) {
    $("#versionBody").html("");
    //拼接新表格
    var tr = "";
    var versionTypeSelectTpl = "";
    var param = {};
    for (var n = result.length - 1;n>=0;n--) {
        var num = result.length - parseInt(n);
        var data = result[n] || {};
        param.n = n;
        param.versionType = data.versionType;
        versionTypeSelectTpl = template('versionTypeSelectTpl', param);
        tr += '<tr>' +
            '<td class="table-td indx">' +
            num +
            '</td>' +
            '<td class="table-td">' +
            data.id +
            '</td>' +
            '<td class="table-td">' +
            data.courseName +
            '</td>' +
            '<td class="table-td">' +
            versionTypeSelectTpl +
            '</td>' +
            '<td class="table-td">' +
            data.videoDuration +
            '</td>' +
            '<td class="table-td">' +
            data.releaseTime +
            '</td>' +
            '<td class="table-td">' +
            '<span title="上移" style="margin-right: 12px;" onclick="moveUp(this)"><i style="cursor:pointer" class="fa fa-chevron-circle-up"></i></span>' +
            '<span title="下移" style="margin-right: 12px;" onclick="moveDown(this)"><i style="cursor:pointer" class="fa fa-chevron-circle-down"></i></span>' +
            '<span title="删除" onclick="deleteRow(this)"><i style="cursor:pointer" class="fa fa-trash-o"></i></span>' +
            '<input type="hidden" class="courseId" id="schCourseDtoList[' + n + '].id" name="schCourseDtoList[' + n + '].id" value="' + data.id + '">' +
            '<input type="hidden" class="columnId" id="schCourseDtoList[' + n + '].columnId" name="schCourseDtoList[' + n + '].columnId" value="' + data.columnId + '">' +
            '</td>' +
            '</tr>'
    }
    $("#versionBody").html(tr);
}

function moveUp(obj) {
    var trLength = $('#versionBody').children('tr').length;
    var tr = $(obj).parents("tr");
    if (tr.index() != 0) {
        var versionTypeVal1 = $("select[name='schCourseDtoList["+(trLength - tr.index())+"].versionType']").val();
        var versionTypeVal2 = $("select[name='schCourseDtoList["+(trLength - tr.index() - 1)+"].versionType']").val();
        //交换版本号
        $("select[name='schCourseDtoList["+(trLength - tr.index())+"].versionType']").val(versionTypeVal2);
        $("select[name='schCourseDtoList["+(trLength - tr.index() - 1)+"].versionType']").val(versionTypeVal1);
        tr.prev().before(tr);
        saveFlag = false;
        sortTable();
    }else {
        popMsg("无法上移");
    }
}

function moveDown(obj) {
    var tr = $(obj).parents("tr");
    var trLength = $('#versionBody').children('tr').length;
    if (tr.index() != trLength - 1) {
        var versionTypeVal1 = $("select[name='schCourseDtoList["+(trLength - tr.index() - 1)+"].versionType']").val();
        var versionTypeVal2 = $("select[name='schCourseDtoList["+(trLength - tr.index() - 2)+"].versionType']").val();
        //交换版本号
        $("select[name='schCourseDtoList["+(trLength - tr.index() - 1)+"].versionType']").val(versionTypeVal2);
        $("select[name='schCourseDtoList["+(trLength - tr.index() - 2)+"].versionType']").val(versionTypeVal1);
        tr.next().after(tr);
        saveFlag = false;
        sortTable()
    }else {
        popMsg("无法下移");
    }
}

function sortTable() {
    var trLength = $('#versionBody').children('tr').length;
    $('#versionBody').children('tr').each(function (index,e) {
        $(e).find(".indx").text(parseInt(index) + 1);
        //更新name属性下标
        var inputElements = this.getElementsByTagName("input");
        if(inputElements){
            for(var i = 0; i < inputElements.length; i++){
                var attr = $(inputElements[i]).attr("name").split(".")[1];
                var newName = "schCourseDtoList["+ (trLength - 1 - index) +"]." + attr;
                $(inputElements[i]).attr("name", newName);
                $(inputElements[i]).attr("id", newName);
            }
        }

        var inputElementsSelect = this.getElementsByTagName("select");
        if(inputElementsSelect){
            for(var i = 0; i < inputElementsSelect.length; i++){
                var attr = $(inputElementsSelect[i]).attr("name").split(".")[1];
                var newName = "schCourseDtoList["+ (trLength - 1 - index) +"]." + attr;
                $(inputElementsSelect[i]).attr("name", newName);
            }
        }
    })
}

function deleteRow(item) {
    var courseId = $(item).parents("tr").find(".courseId")[0].value;
    var columnId = $(item).parents("tr").find(".columnId")[0].value;
    if(columnId){
        popConfirm("确认删除", function() {
            //先删除表关联
            var param = {
                id: courseId,
                columnId: $("#columnId").val(),
                relaType: '02'
            }
            ajaxData("/schoolCourse/delAssociated", param, function (result) {
                parent.popMsg("删除成功！");
                $(item).parents("tr").remove();
                sortTable();
            })
        }, null);
    }else{
        $(item).parents("tr").remove();
        sortTable();
    }
}

