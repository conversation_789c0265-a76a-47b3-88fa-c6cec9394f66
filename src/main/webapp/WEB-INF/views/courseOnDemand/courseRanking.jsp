<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="tab-content">
    <div class="panel-heading">
        <form:form modelAttribute="courseDto" id="queryForm" onkeydown="bindEnter(event)">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">业务专题</label>
                    <div class="col-md-3">
                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' placeholder="请选择业务专题" />
                        <input name="courseType" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">讲师</label>
                    <div class="col-md-3">
                        <input id="teacher" type="text" class="t-select" json-data='${teacherSelect}' placeholder="请输入讲师" />
                        <input name="teacher" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">是否开放</label>
                    <div class="col-md-3">
                        <form:select path="ifOpen" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未开放</form:option>
                            <form:option value="1">已开放</form:option>
                        </form:select>
                    </div>
                </div>
                <div class="col-md-12">
                    <label class="col-md-1 control-label">是否发布</label>
                    <div class="col-md-3">
                        <form:select path="releaseFlag" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未发布</form:option>
                            <form:option value="1">已发布</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">是否失效</label>
                    <div class="col-md-3">
                        <form:select path="status" cssClass="form-control" id="status">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">失效</form:option>
                            <form:option value="1">有效</form:option>
                        </form:select>
                    </div>
                    <div class="col-md-4 text-right">
                        <sec:authorize access="hasAuthority('RES_GET_COURSE_LIST_AUTHORITY_3')" >
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空全部</span>
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_SAVE_SORT_AUTHORITY_3')" >
            <%--上移/下移权限--%>
            <input type="hidden" id="moveSortAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_EDIT_SORT_PAGE_AUTHORITY_3')" >
            <%--顺序调整权限--%>
            <input type="hidden" id="editSortAuth" value="true">
        </sec:authorize>
    </div>
    <div id="tab1" class="tab-pane fade in active">
        <div class="table-primary">
            <e:grid id="tableSort" action="/courseRanking/getSortList" paging="false"
                    cssClass="table table-striped table-hover">
                <e:gridColumn label="排序" renderColumn="sortColumn" orderable="false" cssClass="text-center"
                              cssStyle="width:10%"/>
                <e:gridColumn label="课程名称" displayColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:20%"/>
                <e:gridColumn label="发布时间" displayColumn="releaseTime" orderable="false"
                              cssClass="text-center" cssStyle="width:20%"/>
                <e:gridColumn label="是否发布" displayColumn="releaseFlag" orderable="false" cssClass="text-center"
                              cssStyle="width:20%"/>
                <e:gridColumn label="是否开放" displayColumn="ifOpen" orderable="false" cssClass="text-center"
                              cssStyle="width:10%"/>
                <e:gridColumn label="是否失效" displayColumn="status" orderable="false" cssClass="text-center"
                              cssStyle="width:10%"/>
                <e:gridColumn label="操作" renderColumn="modelOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:40%"/>
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
