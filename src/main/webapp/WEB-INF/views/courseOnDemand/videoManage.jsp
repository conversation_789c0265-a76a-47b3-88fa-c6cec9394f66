<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/video/js-vod-sdk-1.0.1.min.js"></script>
    <script>
    </script>
    <style>
        label{
            padding-top: 7px;
        }
    </style>
</head>
<body>
<div class="panel" >
    <div class="panel-heading" style="height: 45px;">
        <i class="icon icon-list">  视频管理</i>
    </div>
    <div class="panel-body" >
        <div class="row">
            <form:form  modelAttribute="videoInfoDto" id="videoForm">
                <div class="col-md-12">
                    <label class="col-md-1  control-label" style="text-align:center">文件名称：</label>
                    <div class="col-md-3">
                        <form:input path="fileName" placeholder="请输入文件名称" id="titleId" cssClass="form-control" />
                    </div>
                    <label class="col-md-1  control-label" style="text-align:center">是否关联：</label>
                    <div class="col-md-3">
                        <form:select path="relation" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="1">是</form:option>
                            <form:option value="2">否</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1  control-label" style="text-align:center">上传人员名称：</label>
                    <div class="col-md-3">
                        <form:input path="createUser" placeholder="请输入上传人员名称" id="titleId" cssClass="form-control" />
                    </div>

                </div>

                <%--新增学时/删除/编辑权限--%>
                <input type="hidden" id="editAuth" value="true">
                <%--视频切片权限--%>
                <input type="hidden" id="cutVideoAuth" value="true">
                <%--视频切片链接更新权限--%>
                <input type="hidden" id="updateVideoCutUrlAuth" value="true">
        </div>

        <div class="row">
            <div class="col-md-12" >
                <label class="col-md-1 control-label" style="text-align:center">上传时间：</label>
                <div class="col-md-3 daterange">
                    <form:input path="uploadTime" placeholder="请选择上传时间" cssClass="form-control"/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-right">
                <input type="button" id="btnClear" class="btn btn-default" value="清空条件">
                <input type="button" id="querySendMessageBtn" class="btn btn-primary" value="查询">
                <label for="newUploadFile" class="btn btn-primary">点击上传</label>
                <input id="newUploadFile" type="file" onchange ="uploadFileBefore()"  placeholder="请选择文件" style="display: none;">
                <input type="button" id="getVideoDuration" class="btn btn-primary" value="刷新视频时长">
            </div>
        </div>
        </form:form>
    </div>
    <div class="panel-body" >
        <e:grid id="sendTable" action="/video/getCourseVideoInfoList" cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false"
                          cssStyle="text-align:left;width:3%"/>
            <e:gridColumn label="华为云id" displayColumn="assetId" orderable="false" cssClass="text-left" cssStyle="width:100px"/>
            <e:gridColumn label="文件名" displayColumn="fileName" orderable="false" cssClass="text-left" cssStyle="width:150px"/>
            <e:gridColumn label="视频时长" displayColumn="videoDuration" orderable="false" cssClass="text-left" cssStyle="width:3%"/>
            <e:gridColumn label="学时" displayColumn="period" orderable="false" cssClass="text-center" cssStyle="width:3%"/>
            <e:gridColumn label="切片地址" renderColumn="renderColumnCutUrl" orderable="false" cssClass="text-center" cssStyle="width:3%"/>
            <e:gridColumn label="当前进度" displayColumn="processTypeName" orderable="false" cssClass="text-center" cssStyle="width:3%"/>
            <e:gridColumn label="上传时间" displayColumn="createTime" orderable="false" cssClass="text-center" cssStyle="width:6%"/>
            <e:gridColumn label="上传人员" displayColumn="createUser" orderable="false" cssClass="text-center" cssStyle="width:5%"/>
            <e:gridColumn label="是否关联" renderColumn="renderColumnNum" orderable="false" cssClass="text-center" cssStyle="width:3%" />
            <e:gridColumn label="操作" renderColumn="renderColumnOperation" orderable="false" cssClass="text-center" cssStyle="width:10%" />
        </e:grid>
        <div style="border:1px solid #a0d7e9;background-color: #E6ECEE;border-radius: 350px;position: absolute;
                        top:50%;left:50%;margin-left:-90px;margin-top:-10px;width:300px;height:20px;display:none; " id="rateprogress">
            <span id="progress_num" style="font-size: 16px;color:#0086A7;width: 33px;max-width:33px;text-align:center;position: absolute;left: 302px;top: -2px;"></span>
            <div id="progress" class="progress-bar" style="background-color:#0086A7; border-radius: 350px;"></div>
        </div>
        <input type="hidden" id="approveFilesSuffix1" value="mp4;ts;mov;mxf;mpg;flv;wmv"/>
        <input type="hidden" id="uploadVideoType" value="1"/>
    </div>
</div>
</body>
</html>
