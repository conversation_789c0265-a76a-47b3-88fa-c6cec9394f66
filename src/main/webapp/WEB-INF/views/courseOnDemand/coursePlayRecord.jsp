<%--
  Created by IntelliJ IDEA.
  User: Zhaol
  Date: 2022/12/12
  Time: 11:18
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="playRecordDTO" id="queryForm">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">课程名称</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">业务专题</label>
                    <div class="col-md-3">
                        <label for="courseType"></label><input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' placeholder="请选择业务专题" />
                        <input name="courseType" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">开放范围</label>
                    <div class="col-md-3">
                        <form:select path="usefulness" cssClass="form-control">
                            <form:option value="">全部</form:option>
                            <form:options items="${usefulnessList}" itemLabel="label" itemValue="value" />
                        </form:select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">发布时间</label>
                    <div class="col-md-3 daterange">
                        <form:input path="releaseTime" placeholder="请输发布时间" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">是否发布</label>
                    <div class="col-md-3">
                        <form:select path="releaseFlag" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未发布</form:option>
                            <form:option value="1">已发布</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">是否开放</label>
                    <div class="col-md-3">
                        <form:select path="ifOpen" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未开放</form:option>
                            <form:option value="1">已开放</form:option>
                        </form:select>
                    </div>

                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">讲师</label>
                    <div class="col-md-3">
                        <label for="teacher"></label><input id="teacher" type="text" class="t-select" json-data='${teacherSelect}' placeholder="请输入讲师" />
                        <input name="teacher" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">课程类型</label>
                    <div class="col-md-3">
                        <label for="applyMechanism"></label><input id="applyMechanism" type="text" class="t-select" json-data='${courseTypeSelect004}' placeholder="请选择课程类型"/>
                        <input name="applyMechanism" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">所在辖区</label>
                    <div class="col-md-3">
                        <label for="belongCommission"></label><input id="belongCommission" type="text" class="t-select" json-data='${belongCommissionList}' placeholder="请选择辖区"/>
                        <input name="belongCommission" type="hidden"  />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-12 text-right"  style="padding-top: 5px">
                        <span id="export" class="btn btn-primary">导出</span>
                        <span id="btnQuery" class="btn btn-primary">查询</span>
                        <span id="btnClear" class="btn btn-default">清空全部</span>
                    </div>
                </div>
            </div>
        </form:form>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/coursePlayRecord/queryCoursePlayRecordList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="学时" renderColumn="period" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="业务专题" renderColumn="courseType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程类型" renderColumn="applyMechanism" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程讲师" renderColumn="teacher" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否发布" renderColumn="columnRelease"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="是否开放" renderColumn="columnOpen"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="观看人数" renderColumn="viewNum"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
