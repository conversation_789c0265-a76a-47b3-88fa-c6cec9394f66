//@sourceURL=videoManagement.js

$(document).ready(function() {
    if (!window._babelPolyfill) {
        var oHead = document.getElementsByTagName('HEAD').item(0);
        var oScript = document.createElement("script");
        oScript.type = "text/javascript";
        oScript.src = contextPath + "/static/video/esdk-obs-browserjs-2.1.4.min.js";
        oHead.appendChild(oScript);
    }
    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#videoForm")[0].reset();
        $("#uploadTime").val("");
        dataRangePickerInit($('#uploadTime'), null, null, function () {
        }, function () {
        });
        ajaxTableQuery("sendTable", "/video/getCourseVideoInfoList", $("#videoForm").formSerialize());
    });

    $("#querySendMessageBtn").bind("click", function() {
        ajaxTableQuery("sendTable", "/video/getCourseVideoInfoList", $("#videoForm").formSerialize());
    });

    $("#uploadVideo").bind("click", function() {
        fileUpload();
    });


    $("#checkVideoType").bind("click", function() {
        checkVideoType();
    });

    $("#getVideoDuration").bind("click", function() {
        getVideoDuration();
    });

    dataRangePickerInit($('#uploadTime'), null, null, function () {

    }, function () {

    });

});

function uploadFileBefore(){
    var file = document.getElementById('newUploadFile').files[0];
    var checkFlag = checkFileSuffix(file.name);
    if (!checkFlag) {
        popMsg("上传失败，含有不可上传的文件格式");
        return;
    }
    hwCloudStartUpload(file,"course", callback);
}

function callback(videoUrl){
    ajaxTableReload("sendTable",false);
}

function getVideoDuration(){
    ajaxData("/video/getVideoDuration", {videoType:'1'}, editVideoTypeCallback);
}

function renderColumnOperation(data, type, row, meta) {
    var num = parseInt(data.num);

    var edit = '';
    var tempRead = '';
    var updateName = '';
    var cutVideo = '';
    var updateVideoCutUrl = '';

    if (document.getElementById("editAuth")) {
        // edit = '<span style="cursor:pointer;margin-right: 10px" title="新增学时" onclick="editTimeFile(\'' + data.id + '\',\'' + data.period + '\',this)"><i class="icon icon-bars"></i></i></span>';
        tempRead = '<span style="cursor:pointer;" title="删除" onclick="deleteFile(\'' + data.id + '\',\'' + num + '\')"><i class="icon icon-trash"></i></span>';
        updateName = '<span style="cursor:pointer;margin-right: 10px" title="修改" onclick="updateVideoName(\'' + data.id + '\',\'' + data.fileName + '\',this)"><i class="icon icon-edit"></i></i></span>';
    }

    if (document.getElementById("cutVideoAuth")) {
        cutVideo = '<span style="cursor:pointer;margin-right: 10px;" title="视频切片" onclick="cutVideo(\'' + data.assetId + '\')"><i class="icon icon-cut"></i></i></span>';
    }

    if (document.getElementById("updateVideoCutUrlAuth")) {
        updateVideoCutUrl = '<span style="cursor:pointer;margin-right: 10px;display: none" title="视频切片链接更新" onclick="updateVideoCutUrl(\'' + data.assetId + '\')"><i class="icon icon-refresh"></i></i></span>';
    }


    if (edit || cutVideo || updateVideoCutUrl) {
        updateName = '<span style="cursor:pointer;margin-right: 10px" title="修改" onclick="updateVideoName(\'' + data.id + '\',\'' + data.fileName + '\',this)"><i class="icon icon-edit"></i></i></span>';
    }

    var str = '';
    str = edit+updateName+cutVideo+updateVideoCutUrl+tempRead;

    if (!str) {
        str = '-';
    }
    return str;
}

function cutVideo(assetId) {
    var param = {
        assetId:assetId
    }
    ajaxData("/video/cutVideo",param,function (res) {
        if(res){
            popMsg("视频已经执行过切片，请勿重复操作");
            ajaxTableReload("sendTable",false);
        }else{
            popMsg("切片流程已发起，请稍后进行查询");
        }
    })
}

function updateVideoCutUrl(assetId) {
    var param = {
        assetId:assetId
    }
    ajaxData("/video/updateVideoCutUrl",param,function (res) {
        if(res){
            popMsg("视频切片地址更新成功");
            ajaxTableReload("sendTable",false);
        }else{
            popMsg("视频切片地址更新失败，请联系相关人员");
        }
    })
}

function editVideoTypeCallback(){
    ajaxTableReload("sendTable",true);
}

function deleteFile(id,num) {
    var param = {
        id : id,
        status : "0"
    };
    if (num < 1) {
        parent.popConfirm("确定删除？",function () {
            ajaxData("/video/updateVideo", param, function (data) {
                popMsg("删除成功!");
                ajaxTableReload("sendTable",false);
            })
        })
    }else{
        popMsg("只能删除没有关联的视频!");
    }
}

document.onkeydown = function () {
    if (window.event && window.event.keyCode == 13) {
        window.event.returnValue = false;
    }
}

function checkFileSuffix(fileName) {
    var checkFile = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length);
    var approveFiles = $("#approveFilesSuffix1").val();
    if (approveFiles == "") {
        return false;
    }
    var arrayFiles = approveFiles.split(";");
    for (var i = 0; i < arrayFiles.length; i++) {
        if (checkFile.toLowerCase() == arrayFiles[i].toLowerCase()) {
            return true;
        }
    }
    return false;
}
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function renderColumnNum(data, type, row, meta) {
    if(parseInt(data.num)>0){
        return '是'
    }else{
        return '否'
    }
}

function renderColumnCutUrl(data, type, row, meta) {
    if(data.videoCutUrl){
        return '是'
    }else{
        return '否'
    }
}

function renderColumnVideoType(data, type, row, meta) {
    if(data.videoType == '1'){
        return '操作演示'
    }else{
        return '课程视频'
    }
}

function updateVideoName(id,fileName) {
    var content = "<div>"+
        "<textarea type='text' id='fileName' name='fileName' style='width: 300px;height: 72px;line-height: 30px;border: 1px solid #e6e6e6;' maxlength='256'>"+fileName+"</textarea>"+
        "</div>"
    var layerIndex = layer.open({
        title:'修改视频名称',
        content: content,
        btn:['确定','取消']
        ,yes:function (index, layero) {
            var param = {
                id:id,
                videoName:$("#fileName").val()
            }
            ajaxData("/video/updateVideo",param,function (res) {
                layer.close(layerIndex);
                ajaxTableReload("sendTable",false);
                popMsg("修改成功")
            })
        }
        ,btn2:function (index, layero) {
            layer.close(layerIndex);
        }
    })
}

function editTimeFile(id,period) {
    if(period == "null"){
        period = ""
    }
    var content = "<div>"+
        "<input type=\"text\" id='period' onkeyup=\"value=value.replace(/[^\\-?\\d.]/g,'')\"  placeholder=\"只能输入数字\"    value='" + period + "' name='period' style='width: 300px;height: 72px;line-height: 30px;border: 1px solid #e6e6e6;' maxlength='256'></input>"+
        "</div>"
    var layerIndex = layer.open({
        title:'增加学时',
        content: content,
        btn:['确定','取消']
        ,
        yes:function (index, layero) {
            if($("#period").val() != "" && $("#period").val().trim() != ""){
                var param = {
                    id:id,
                    period:$("#period").val()
                }
                ajaxData("/video/updateVideo",param,function (res) {
                    layer.close(layerIndex);
                    ajaxTableReload("sendTable",false);
                    popMsg("修改成功")
                })
            }else{
                popMsg("不能提交空白")
            }
        }
        ,btn2:function (index, layero) {
            layer.close(layerIndex);
        }
    })
}

