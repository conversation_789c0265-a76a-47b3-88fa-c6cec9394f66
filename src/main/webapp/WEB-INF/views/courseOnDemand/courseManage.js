//@ sourceURL=courseManage.js
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    dateInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        tSelectInit();
        dateInit();
        search();
    });


    $('#addCourse').click(function () {
        addCourse("", "", "", "01", "0");
    });

    $("#exportCourse").click(function () {
        exportCourse();
    })

    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/basicInformation/courseExport?" + $("#queryForm").serialize());
    });
});

function search() {
    ajaxTableQuery("tableAll", "/basicInformation/queryRepCaseInfoList",
        $("#queryForm").formSerialize());
}

function addCourse(id, recordUserId, reviewUserId, editStatus, releaseFlag, modifyType) {
    var param = {
        id: id,
        recordUserId: recordUserId,
        reviewUserId: reviewUserId,
        editStatus: editStatus,
        modifyType: modifyType,
        releaseFlag: releaseFlag
    };
    var userId = $('#currUserId').val();

    if(id){
        if ($('#isAdmin').val() == 'true') { //管理员账号登录
            param.modifyType = '2';
        } else if (userId == recordUserId && userId == reviewUserId) { //录入人和复核人为同一个人
            param.modifyType = '4';
        } else if (userId == recordUserId) { //已认领录入人员再次进入页面
            param.modifyType = '0';
        } else if (userId == reviewUserId) { //已认领复核人员再次进入页面
            param.modifyType = '1';
        } else {//访客进入
            parent.popConfirm("您只能查看，无法修改", function () {
                param.modifyType = '3';
                parent.popWin('新增课程信息', '/basicInformation/addCourseManageInit', param, '98%', '98%', callBackAddCourse, '', callBackAddCourse);
            });
            return false;
        }
    }else{
        parent.popWin('新增课程信息', '/basicInformation/addCourseManageInit', param, '98%', '98%', callBackAddCourse, '', callBackAddCourse);
    }
}

function exportCourse() {
    window.open(contextPath + "/schoolCourse/exportCourse?" + $("#queryForm").serialize());
}

function editCourse(id, releaseFlag) {
    var param = {
        id: id,
        releaseFlag: releaseFlag == '是' ? '1' : '0'
    };
    var userId = $('#currUserId').val();
    parent.popWin('编辑课程信息', '/basicInformation/addCourseManageInit', param, '98%', '98%', callBackAddCourse, '', callBackAddCourse);
}

function callBackAddCourse() {
    ajaxTableReload("tableAll", false);
}

function dateInit() {
    dataRangePickerInit($('#releaseTime'), null, null, function () {

    }, function () {

    });
    dataRangePickerInit($('#amountTime'), null, null, function () {

    }, function () {

    });

}

//下拉初始化
function tSelectInit() {
    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    // $('#courseType').tselectInit(null, tSelectOptions);
    $('#applyPlate').tselectInit(null, tSelectOptions);
    $('#applyPerson').tselectInit(null, tSelectOptions);
    $('#applyProficiency').tselectInit(null, tSelectOptions);
    $('#applyMechanism').tselectInit(null, tSelectOptions);
    $('#subject').tselectInit(null, tSelectOptions);

    var teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);

    var courseTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, courseTypeSelectOptions);

    var tSelectOption = {
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack,
        id: 'id',
        pid: 'pId',
        name: 'labelName',
        value: 'labelValue',
        grade: 2,
        resultType: 'all',
        style: {}
    };

    $('#typeRole').tselectInit(null, $.extend({}, tSelectOption));// 角色分类
    $('#editStatus').tselectInit(null, $.extend({}, tSelectOption));// 编辑状态

    var tUserSelectOption = {
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack,
        id: 'id',
        name: 'realName',
        value: 'id',
        grade: 1,
        resultType: 'all',
        style: {},
        inputSearch: true
    };
    $('#userId').tselectInit(null, $.extend({}, tUserSelectOption));// 角色分类


    var tuseSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#superviseType').tselectInit(null, tuseSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function columnRelease(data, type, row, meta) {
    if (data.releaseFlag=='0'){
        return '否'
    }else if (data.releaseFlag=='1'){
        return '是'
    }else {
        return ''
    }
}
function columnOpen(data, type, row, meta) {
    if (data.ifOpen=='0'){
        return '否'
    }else if (data.ifOpen=='1'){
        return '是'
    }else {
        return ''
    }
}

function columnOperation(data, type, row, meta) {
    var str = '';
    let editCourseAuth = '';
    let delCourseAuth = '';

    if (document.getElementById("editCourseAuth")) {
        editCourseAuth = true
    }

    if (document.getElementById("delCourseAuth")) {
        delCourseAuth = true
    }
    if (editCourseAuth) {
        str = '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editCourse(\'' + data.id + '\',\'' + data.releaseFlag + '\')"></i>';
    }
    if (data.releaseFlag=='0') {
        if (delCourseAuth) {
            str += '<i class="fa fa-trash-o" style="cursor: pointer;margin: 0 5px;" title="删除" onclick="delCourse(\'' + data.id + '\')"></i>';
        }
    }else {
        str += '<i class="fa fa-copy" style="cursor: pointer;margin: 0 5px;" title="复制视频播放链接" onclick="copyCourse(\'' + data.id + '\')"></i>';
    }

    if(!str) {
        str = '-';
    }
    return str;
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function delCourse(id) {
    parent.popConfirm("确认删除?", function () {
        var param = {
            id: id,
            status: '0'
        };
        ajaxData("/basicInformation/deleteCourse", param, callBackAddCourse);
    });
}

function courseName(data, type, row, meta) {
    var str = data.courseName;
    // if (str!=null && str !=''){
    // 	if (str.length>12){
    // 		str = str.substring(0,12) + "...";
    // 	}
    // }
    return str;
}


//学时
function period(data, type, row, meta) {
    return data.period;
}

function courseType(data, type, row, meta) {
    var str = data.courseType;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return '<span title="' + data.courseType + '">' + str + '</span>';
}

function applyPlate(data, type, row, meta) {
    var str = data.applyPlate;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPlate + '">' + str + '</span>';
}

function applyPerson(data, type, row, meta) {
    var str = data.applyPerson;
    if (str != null && str != '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPerson + '">' + str + '</span>';
}

function applyMechanism(data, type, row, meta) {
    var str = data.applyMechanism;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyMechanism + '">' + str + '</span>';
}

function teacher(data, type, row, meta) {
    var str = data.teacher;
    if (str!=null && str !=''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.teacher + '">' + str + '</span>';
}


//录入人
function renderColumnrecordUser(data, type, row, meta) {
    if ($('#isAdmin').val() == 'true') {
        var str = data.recordUserName || "待认领";
        return '<a href="javascript:void(0)" onclick="changeUser(\'' + data.id + '\',0,\'' + data.recordUserId + '\')">' + str + '</a>';
    } else {
        var str = data.recordUserName || "待认领";
        if (!data.recordUserName) {//如果没有录入人
            str = '<a href="javascript:void(0)" onclick="claimCase(\'' + data.id + '\',\'' + data.createTime + '\',\'' + data.recordUserId + '\',\'' + data.editStatus + '\',0)">待认领</a>'
        }
        return str;
    }
}

//复核人
function renderColumnreviewUser(data, type, row, meta) {
    if ($('#isAdmin').val() == 'true') {
        var str = "-";
        if (data.editStatus != '01' && data.editStatus != '02' && getValue(data.recordUserId) != '') {
            str = data.reviewUserName || "待认领";
            str = '<a href="javascript:void(0)" onclick="changeUser(\'' + data.id + '\',1,\'' + data.reviewUserId + '\')">' + str + '</a>';
        }
        return str;
    } else {
        var str = "-";
        if (data.reviewUserName) {
            str = data.reviewUserName;
        } else if (data.editStatus == '03' && getValue(data.recordUserId) != '') {
            str = '待认领';
            //状态为待复核，录入人不为空、当前用户不是录入人、此人是模块用户或者模块管理员
            str = '<a href="javascript:void(0)" onclick="claimCase(\'' + data.id + '\',\'' + data.createTime + '\',\'' + data.recordUserId + '\',\'' + data.editStatus + '\',1)">' + str + '</a>'
        }
        return str;
    }
}

function claimCase(id, createTime, recordUserId, editStatus, modifyType) {
    var param = {
        id: id,
        createTimeStr: createTime,
        recordUserId: recordUserId,
        modifyType: modifyType,
        editStatus: editStatus,
        caseStatus: '23'
    };
    if ($('#isOrdinaryUser').val() != 'false') {
        if ('0' == modifyType) { //录入人认领
            popConfirm("是否认领该录入任务", function () {
                ajaxData('/repCase/claimCase', param, function (data) {
                    if (data == '0') {
                        parent.popMsg('此任务已经被领取，请领取其它任务');
                    } else {
                        parent.popMsg('领取录入任务成功');
                    }
                    ajaxTableReload('tableAll', false);
                })
            });
        } else if ('1' == modifyType) {//复核人认领
            if (recordUserId != $("#currUserId").val()) {
                popConfirm("是否认领该复核任务", function () {
                    ajaxData('/repCase/claimCase', param, function (data) {
                        if (data == '0') {
                            parent.popMsg('此任务已经被领取，请领取其它任务');
                        } else {
                            parent.popMsg('领取复核任务成功');
                        }
                        ajaxTableReload('tableAll', false);
                    })
                });
            } else {
                parent.popAlert("录入人和复核人不可以由同一人兼认，管理员手动分配除外")
            }
        }
    } else {
        parent.popAlert("非项目组成员，不可以认领任务。请联系项目负责人进行权限管理")
    }
}

//编辑状态
function renderColumnEditStatus(data, type, row, meta) {
    return data.editStatusLabel || '';
}

function changeUser(id, modifyType, userId) {
    var titleNmae = '';
    if ('0' == modifyType) {
        titleNmae = '录入人';
    } else {
        titleNmae = '复核人';
    }
    var param = {
        id: id,
        modifyType: modifyType,
        userId: userId,
        caseStatus: '23'
    };
    parent.popWin("修改" + titleNmae, "/repCase/changeUserInit", param, "50%", "600px", changeUserCallBack, "");
}

function changeUserCallBack() {
    parent.popMsg('修改成功');
    ajaxTableReload('tableAll', false);
}

function changeStatus() {
    var status = $("#status").val();
    if (status == '1') {
        $("#status").val('0');
    } else {
        $("#status").val('1');
    }
    search();
}


function claimCase(id, createTime, recordUserId, editStatus, modifyType) {
    var param = {
        id: id,
        createTimeStr: createTime,
        recordUserId: recordUserId,
        modifyType: modifyType,
        editStatus: editStatus,
        caseStatus: '23'
    };
    if ($('#isOrdinaryUser').val() != 'false') {
        if ('0' == modifyType) { //录入人认领
            popConfirm("是否认领该录入任务", function () {
                ajaxData('/repCase/claimCase', param, function (data) {
                    if (data == '0') {
                        parent.popMsg('此任务已经被领取，请领取其它任务');
                    } else {
                        parent.popMsg('领取录入任务成功');
                    }
                    ajaxTableReload('tableAll', false);
                })
            });
        } else if ('1' == modifyType) {//复核人认领
            if (recordUserId != $("#currUserId").val()) {
                popConfirm("是否认领该复核任务", function () {
                    ajaxData('/repCase/claimCase', param, function (data) {
                        if (data == '0') {
                            parent.popMsg('此任务已经被领取，请领取其它任务');
                        } else {
                            parent.popMsg('领取复核任务成功');
                        }
                        ajaxTableReload('tableAll', false);
                    })
                });
            } else {
                parent.popAlert("录入人和复核人不可以由同一人兼认，管理员手动分配除外")
            }
        }
    } else {
        parent.popAlert("非项目组成员，不可以认领任务。请联系项目负责人进行权限管理")
    }
}
function renderColumnAverage(data, type, row, meta) {
    return data.average || '--';
}
function renderColumnOriginalPrice(data, type, row, meta) {
    return data.originalPrice || '--';
}

function rendOpenUrl(data, type, row, meta) {
    var str = '';
    if (data.releaseFlag  == '1') {
        var url = $("#baseUrl").val() + "/trainingCenter/courseOnDemand/playDetail?active=courseOnDemand&id="+data.id
        str = '<input id="' + data.id + '" value="'+ url +'" >'
        return str
    } else {
        return str;
    }
}
function copyCourse(id) {
    var input = document.getElementById(id);
    input.select(); // 选中文本
    document.execCommand("copy"); // 执行浏览器复制命令
    alert("复制成功");
}
