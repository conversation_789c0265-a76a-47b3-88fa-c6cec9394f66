$(document).ready(function () {

    $('#btnAdd').click(function () {
        btnAdd();
    });

    $("#btnQuery").bind("click", function() {
        search()
    });

    $('#btnClear').click(function () {
        //document.getElementById("queryForm").reset();
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });
        search()
    });

    }
)
function btnAdd(){
    parent.popWin('新增限免设置', '/limitedExemption/addLimitedExemption', "", '98%', '98%', "", '', "");

}

function search() {
    ajaxTableQuery("limitTable", "/limitedExemption/queryLimitedExemptionList", $("#queryForm").formSerialize());
}

// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}

function ifRelease(data, type, row, meta) {
    if (data.ifRelease === '1'){
        return '是';
    } else {
        return '否';
    }
}

function columnOperation(data, type, row, meta) {
    var str = '';
    let editCourseAuth = '';
    let delCourseAuth = '';
    editCourseAuth = true
    delCourseAuth = true

    // if (document.getElementById("editCourseAuth")) {
    //     editCourseAuth = true
    // }
    //
    // if (document.getElementById("delCourseAuth")) {
    //     delCourseAuth = true
    // }
    if (editCourseAuth) {
        str = '<i class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="编辑" onclick="editCourse(\'' + data.id + '\',\'' + data.ifRelease + '\')"></i>';
    }
    if (delCourseAuth) {
        str += '<i class="fa fa-trash-o" style="cursor: pointer;margin: 0 5px;" title="删除" onclick="delCourse(\'' + data.id + '\')"></i>';
    }

    if(!str) {
        str = '-';
    }
    return str;
}

function editCourse(id, ifRelease) {
    
    var param = {
        id: id,
        ifRelease:ifRelease
    };
    var userId = $('#currUserId').val();
    parent.popWin('限免管理基本信息', '/limitedExemption/addLimitedExemption', param, '98%', '98%', callBackAddCourse, '', callBackAddCourse);
}

function delCourse(id) {
    parent.popConfirm("确认删除?", function () {
        var param = {
            id: id
        };
        ajaxData("/limitedExemption/deleteLimitedExemption", param, callBackAddCourse);
    });
}

function callBackAddCourse() {
    ajaxTableReload("limitTable", false);
}

