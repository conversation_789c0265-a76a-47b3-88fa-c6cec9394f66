$(document).ready(function () {
    //保存
    $("#btnSave").click(function () {
        saveCourse()
    })
    //发布
    $("#btnRelease").click(function () {
        ReleaseCourse()
    })
    //取消发布
    $("#btnCancel").click(function () {
        ReleaseCancel()
    })
    //关闭
    $("#btnClose").click(function () {
        closeWin();
    })
    //下拉初始化
    tSelectInit();

    $('#btnSendUser').bind("click", function () {
        
        let id = $("#id").val();
        if(getValue(id) == ""){
            popMsg("请先保存数据")
            return ;
        }else {
            let params = {
                userIds:$("#personId").val(),
                id: $("#id").val()
            }
            parent.popWin('选择用户','/limitedExemption/selectSendUser', params,'100%', '100%', function (res){
                userIds = res
            })
        }

    })

})

//下拉初始化
function tSelectInit() {
    var pagSelectOptions = {
        id: 'packageId',
        name: 'packageName',
        value: 'packageId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#packageId').tselectInit(null, pagSelectOptions);

    var liveSelectOptions = {
        id: 'liveId',
        name: 'liveName',
        value: 'liveId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#liveId').tselectInit(null, liveSelectOptions);

}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}

function saveCourse(){
    if(getValue($("#moduleName").val()) == "") {
        popMsg("请输入模块名称");
        return;
    }else {
        popConfirm("保存?", function () {
            btnSave();
        });
    }
}

function ReleaseCourse(){
    if(getValue($("#moduleName").val()) == "") {
        popMsg("请输入模块名称");
        return;
    }else {
        popConfirm("发布?", function () {
            btnRelease();
        });
    }
}

function ReleaseCancel(){
    popConfirm("取消发布?", function () {
        btnCancel();
    });
}

function btnSave() {
    ajaxData("/limitedExemption/saveLimitedPackage?", $("#queryForm").formSerialize(), function (res){
        popMsg("保存成功");
    })
}

function btnRelease() {
    var formArray = $("#queryForm").serializeArray();
    var ifReleaseExist = false;
    $.each(formArray, function(index, item) {
        if (item.name === "ifRelease") {
            item.value = "1";
            ifReleaseExist = true;
            return false;  // 终止each循环
        }
    });
    if (!ifReleaseExist) {
        formArray.push({name: "ifRelease", value: "1"});
    }
    var formData = $.param(formArray);
    ajaxData("/limitedExemption/saveLimitedPackage?", formData, function(res) {
        popMsg("发布成功");
    });
}


function btnCancel() {
    var formArray = $("#queryForm").serializeArray();
    var ifReleaseExist = false;
    $.each(formArray, function(index, item) {
        if (item.name === "ifRelease") {
            item.value = "0";
            ifReleaseExist = true;
            return false;  // 终止each循环
        }
    });
    if (!ifReleaseExist) {
        formArray.push({name: "ifRelease", value: "0"});
    }
    var formData = $.param(formArray);
    ajaxData("/limitedExemption/saveLimitedPackage?", formData, function(res) {
        popMsg("取消发布成功");
    });
}


