//@ sourceURL=addCourse.js
var _ImgUploadUrl = contextPath + '/filetempupload';
var _Openfileupload = contextPath + '/basicInformation/uploadCourseFile';
var log, className = "dark", curDragNodes, autoExpandNode;
var _scrollTop = 0;

var videoSelectFlag = true;


//tree setting
var setting = {
    view: {
        addHoverDom: addHoverDom,
        removeHoverDom: removeHoverDom,
        showIcon: true,
        expandSpeed: "fast",
    },
    callback: {
        onClick: treeOnClick,
        beforeEditName: beforeEditName,
        beforeRemove: beforeRemove,
        beforeRename: beforeRename,
        onRemove: onRemove,
        onRename: onRename,
        beforeDrag: beforeDrag,
        beforeDrop: beforeDrop,
        beforeDragOpen: beforeDragOpen,
        onDrag: onDrag,
        onDrop: onDrop
    },
    data: {
        simpleData: {
            enable: true,
            idKey: "id",
            pIdKey: "pId",
            rootPId: null
        },
        key: {
            name: "itemName"
        }
    },
    edit: {
        drag: {
            autoExpandTrigger: true,
            isCopy: false,
            isMove: true,
            prev: true,
            next: true,
            inner: true,
            borderMax: 10,
            borderMin: -5,
            minMoveSize: 5,
            maxShowNodeNum: 5,
            autoOpenTime: 500
        },
        editNameSelectAll: false,
        enable: true,
        removeTitle: "",
        renameTitle: "",
        showRemoveBtn: true,
        showRenameBtn: true
    }
};
var map = {};
//var statusTree = $.parseJSON($("#treeValue").text());
//当前选择节点
var selectNode;
//按钮提交方式(1:添加同级节点；2：添加下级节点；3：修改节点)
var modifyMethod;
//当前节点路径
var curPath;
var maxSort;
var showFlag = "1";
$(document).ready(function () {
    $("#edit").hide();
    //保存基本信息

    $("#btnSave").click(function () {
        saveCourse()
    })
    $("#btnSaveCopy").click(function () {
        saveCourse()
    })
    //关联视频
    $("#selectBtn").click(function () {
        if ($("#releaseFlag").val()=='1'){
            popMsg("当前课程已在发布中，修改请取消发布")
        }else {
            parent.popWin("选择视频", "/basicInformation/videoManageInit", null, "80%", "100%", myWinCallback);
        }
    });

    $("#cancelBtn").click(function () {
        //如果是发布 则不让取消关联
        if($("#releaseFlag").val() != '1'){
            var param = {
                id: $("#nodeId").val()
            };
            ajaxData("/schoolCourse/deleteSchCourseVideoMap", param, deleteSchCourseVideoMapCallback);
        }else{
            parent.popMsg("发布案例不可取消关联！")
        }
    });
    switchInit();
    compulsorySwitchInit();
    // selection plugin init
    tSelectInit();
    //date selection plugin init
    // dateInit();
    //pic upload init
    livePicInit();
    backImgInit();
    //课程资料显示init
    dataFileInit();
    ueInit();
    //操作视频章节
    treeInit();

    //发布
    $("#btnRelease").click(function () {
        if ($("#queryForm").valid()) {
            // var releaseTime = "";
            // if ($("#releaseTime").val() != "" && $("#releaseTime").val() != null) {
            //     releaseTime = new Date(Date.parse($("#releaseTime").val().replace(/-/g, "/")));
            // }
            // var nowDate = new Date();
            // if (releaseTime > nowDate) {
            //     popMsg("发布时间大于当前时间，请修改发布时间")
            // } else {
                popConfirm("确认发布?", function () {
                    btnRelease();
                });
            // }
        }

    });
    //发布
    $("#btnReleaseCopy").click(function () {
        if ($("#queryForm").valid()) {
            // var releaseTime = "";
            // if ($("#releaseTime").val() != "" && $("#releaseTime").val() != null) {
            //     releaseTime = new Date(Date.parse($("#releaseTime").val().replace(/-/g, "/")));
            // }
            // var nowDate = new Date();
            // if (releaseTime > nowDate) {
            //     popMsg("发布时间大于当前时间，请修改发布时间")
            // } else {
                popConfirm("确认发布?", function () {
                    btnRelease();
                });
            // }
        }

    });

    $("#btnCancel").click(function () {
        popConfirm("取消发布?", function () {
            btnCancel();
        });
    });
    $("#btnCancelCopy").click(function () {
        popConfirm("取消发布?", function () {
            btnCancel();
        });
    });

    $("#btnClose").click(function () {
        popConfirm("确认关闭?", function () {
            btnClose();
        });
    });
    if ($("#id").val()) {
        $(".tree-div").show();
        if ($("#releaseFlag").val() == '1') {
            //取消发布
            $("#btnCancel").show();
            $("#btnRelease").hide();
        } else {
            //发布
            $("#btnCancel").hide();
            $("#btnRelease").show();
        }
    }


    $.validator.addMethod("stringMaxLength", function (value, element, params) {
        var length = 0;
        for (var i = 0; i < value.length; i++) {
            if (value.charCodeAt(i) > 19967) {
                length++;
            }
        }
        return length > params[0] ? false : true;
    }, "最大长度不能超过{0}个字");

    $.validator.addMethod("numberVaild", function (value, element) {
        if (value.replace(/,/g, '').trim() != "") {
            var re = /^(-?[0-9]\d{0,9})(,\d{3})*(\.\d{0,2})?$/;
            var isValid = this.optional(element) || re.test(value.replace(/,/g, '').trim());
            //formatter(element);
            return isValid;
        } else {
            return true;
        }
    }, "请输入正确的数值(整数或小数,保留小数点后2位)");

    $("#queryForm").validate({
        ignore:"",
        rules: {
            "courseName": {
                required: true,
                stringMaxLength: [60]
            },
            "commodityPrice": {
                numberVaild: true,
                required: true,
            },
            "markingPrice": {
                numberVaild: true
            },
           /* "releaseTime": {
                required: true,
            },*/
            "mainTextTag": {
                required: true,
                stringMaxLength: [200]
            },
            "introduce": {
                required: true,
                stringMaxLength: [1000]
            },
        },
        messages: {
            "courseName": {
                required: "请填写课程名称"
            },
           /* "releaseTime": {
                required: "请填写发布时间",
            },*/
        }
    });

    $("#btnSelectCost").bind("click", function() {
        var relationId = $("#id").val()
        if(getValue(relationId) == ""){
            popMsg("请先保存数据")
            return ;
        }
        var param = {
            existPersonType:true,
            existIfFree:true,
            existIfDiscount:true,
            existDiscount:true,
            existPrice:true,
            relationId:relationId
        }
        popWin("选择用户类型", "/selectTrainTypeCost", param, "80%", "80%");
    });

});
function switchInit() {
    $('#ifOpenSwitch').bootstrapSwitch('size', 'small');
    let ifOpen = $('input[name="ifOpen"]').val();
    if (ifOpen == '1') {
        $('input[name="ifOpen"]').val(true);
    } else {
        $('input[name="ifOpen"]').val(false);
    }
    let ifOpenOld = eval($('input[name="ifOpen"]').val());
    let ifOpenNow = $('#ifOpenSwitch').prop("checked");
    if (ifOpenOld !== ifOpenNow) {
        $('#ifOpenSwitch').bootstrapSwitch('toggleState');
    }
    $('#ifOpenSwitch').on('switchChange.bootstrapSwitch',
        function (event, state) {
            if (state == true) {
                $('input[name="ifOpen"]').val(true);
            } else {
                $('input[name="ifOpen"]').val(false);
            }
        });
}

function compulsorySwitchInit() {
    $('#compulsorySwitch').bootstrapSwitch('size', 'small');
    let compulsory = $('input[name="compulsory"]').val();
    if (compulsory == '1') {
        $('input[name="compulsory"]').val(true);
    } else {
        $('input[name="compulsory"]').val(false);
    }
    let compulsoryOld = eval($('input[name="compulsory"]').val());
    let compulsoryNow = $('#compulsorySwitch').prop("checked");
    if (compulsoryOld !== compulsoryNow) {
        $('#compulsorySwitch').bootstrapSwitch('toggleState');
    }
    $('#compulsorySwitch').on('switchChange.bootstrapSwitch',
        function (event, state) {
            if (state == true) {
                $('input[name="compulsory"]').val(true);
            } else {
                $('input[name="compulsory"]').val(false);
            }
        });
}

function saveCourse(){

        var priceNum = 0;
        $("#freeTableId").find("tr").each(function (n,obj){
            var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
            if (checkFlag){
                var price = $(obj).find('td').eq(2).find('input').val()
                if (price == ''){
                    priceNum++
                }
            }
        })
        if (priceNum>0){
            popMsg("当前收费价格有为空。");
            return;
        }
        if(getValue($("#livePicImg").val()) == "") {
            popMsg("请上传课程图片");
            return;
        }
        if(getValue($("#backImg").val()) == "") {
            popMsg("请上传课程背景图片");
            return;
        }
        var total = $(document).find("input[name='courseType']").val().split(",").length;
        if ($("#queryForm").valid()) {
            if(!$("input[name='teacher']").val()){
                popMsg("请选择讲师")
            }else if(!$("input[name='courseType']").val()){
                popMsg("请选择业务专题")
            }else {
                popConfirm("保存?", function () {
                    btnSave();
                });
            }
        }
}
//修改学时
function editCoursePeriod(obj) {
    if ($("#releaseFlag").val()=='1'){
        popMsg("当前课程已在发布中，修改请取消发布")
    }else {
        var period = "";
        var content = "<div>"+
            "<input type=\"text\" id='periodName' placeholder=\"请输入修改的学时（只能输入数字）\"  onkeyup=\"value=value.replace(/[^\\-?\\d.]/g,'')\"   value='" + period + "' name='periodName' style='width: 300px;height: 72px;line-height: 30px;border: 1px solid #e6e6e6;' maxlength='256'></input>"+
            "</div>"
        var layerIndex = layer.open({
            title:'修改学时',
            content: content,
            btn:['确定','取消']
            ,
            yes:function (index, layero) {
                var fname =   $('#periodName').val();
                if(fname == '' ||  fname.indexOf(" ") >= 0){
                    popMsg("学时不能为空")
                }else{
                    $(obj).parent().children("div:eq(1)").find("#period").text(fname)
                    var data = {
                        period :fname,
                        type:1
                    }
                    myWinCallback(data);
                    layer.close(layerIndex);
                }
            }
            ,btn2:function (index, layero) {
                layer.close(layerIndex);
            }
        })
    }

}

function ueInit() {

    UE.delEditor('editorPicture');
    um2 = UE.getEditor('editorPicture',
        {
            initialFrameHeight: 250,
            initialFrameWidth: "100%",
            textarea: "picture",
            elementPathEnabled: false,
            autoHeightEnabled: false
        });


}


function btnSave() {
    var personTypeList = []
    $("#freeTableId").find("tr").each(function (n,obj){
        var checkFlag = $(obj).find('td').eq(0).find('input').is(':checked')
        if (checkFlag){
            var relationId = $("#id").val()
            var personType = $(obj).find('td').eq(0).find('input').val()
            var ifFree = $(obj).find('td').eq(1).find('input:radio:checked').val();
            var price = ''
            if (ifFree === '1'){
                price = $(obj).find('td').eq(2).find('input').val()
            }
            var personTypeDto = {
                relationId : relationId,
                personType : personType,
                ifFree : ifFree,
                price : price
            }
            personTypeList.push(personTypeDto)
        }
    })
    $("#personTypes").val(JSON.stringify(personTypeList))
    console.log(personTypeList)
    ajaxData("/basicInformation/courseSave?", $("#queryForm").formSerialize(), function (res) {
        popMsg("保存成功");
        $("#id").val(res.id);
        $("#releaseFlag").val(res.releaseFlag);
        if (res.id) {
            $(".tree-div").show();
            if ($("#releaseFlag").val() == '1') {
                //取消发布
                $("#btnCancel").show();
                $("#btnRelease").hide();
            } else {
                //发布
                $("#btnCancel").hide();
                $("#btnRelease").show();
            }
        }
    });
}

function dateInit() {
    var limitTime = $('#limitTime').val();
    if (limitTime.trim() != '') {
        var limitTimes = limitTime.split(' 至 ');
        dataRangePickerInit($('#limitTime'), limitTimes[0], limitTimes[1], function () {
        }, function () {
        });
    } else {
        dataRangePickerInit($('#limitTime'), null, null, function () {
        }, function () {
        });
    }
}

//下拉初始化
function tSelectInit() {
    var tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'all',
        inputSearch: true,
        openDown: false,//是否初始展开tree
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };

    var tSelectOptionType = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        useFooter: true,
        hiddenInput: true,
        grade: 1,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };

    $('#courseType').tselectInit(null, tSelectOptionType);
    $('#applyPlate').tselectInit(null, tSelectOptions);
    $('#applyPerson').tselectInit(null, tSelectOptions);
    $('#applyProficiency').tselectInit(null, tSelectOptions);
    $('#applyMechanism').tselectInit(null, tSelectOptions);
    $('#subject').tselectInit(null, tSelectOptions);

    var teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);

    var tuseSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#usefulness').tselectInit(null, tuseSelectOptions);
    $('#superviseType').tselectInit(null, tuseSelectOptions);
}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}


function livePicInit() {
    $('#livePicRemoveBtn').bind('click', function () {
        clearLivePic();
    });
    $('#livePicFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#livePicFileId").val(file.fileRelaId);
                $("#livePicImg").val(file.fileRelaId);
                $("#picAttId").val("");
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });
    $("#livePicFile").livePicUploadPreview({
        Img: "livePicImgSrc",
        Width: 50,
        Height: 50
    });

    $("#dataUploadBtn").bind('click',function () {
        if ($("#id").val()!=''){
            $('#dataFile').fileupload({
                url: _Openfileupload + "?bizId=" + $("#id").val(),
                dataType: 'json',
                autoUpload: true,
                add: function (e, data) {
                    var fileName = data.files[0].name;
                    data.submit();
                },
                submit: function (e, data) {
                    index = layer.load(1, {
                        shade: [0.1, '#fff']
                        // 0.1透明度的白色背景
                    });
                },
                done: function (e, data) {
                    if ($("#dataTmpl").length > 0) {
                        var html = $("#dataTmpl").render(data.result.result);
                        $("#courseDataBody").append(html);
                    }
                    refreshLetterIndex('courseDataBody');
                    layer.close(index);
                },
                fail: function (e, data) {
                    layer.close(index);
                    popAlert("上传失败,请检查文件");
                }
            });
        }else {
            popMsg("请先保存课程")
        }
    })

}
function backImgInit() {
    $('#backImgRemoveBtn').bind('click', function () {
        clearBackImg();
    });
    $('#backImgFile').fileupload({
        url: _ImgUploadUrl,
        dataType: 'json',
        autoUpload: true,
        add: function (e, data) {
            var fileName = data.files[0].name;
            var imgType = ["gif", "jpeg", "jpg", "bmp", "png"];
            if (!RegExp(
                "\.(" + imgType.join("|") + ")$",
                "i").test(fileName.toLowerCase())) {
                popMsg("选择文件错误,图片类型必须是"
                    + imgType.join("，") + "中的一种");
                return;
            }
            data.submit();
        },
        submit: function (e, data) {
            index = layer.load(1, {
                shade: [0.1, '#fff']
                // 0.1透明度的白色背景
            });
        },
        done: function (e, data) {
            $.each(data.result, function (index, file) {
                $("#backImgFileId").val(file.fileRelaId);
                $("#backImg").val(file.fileRelaId);
                $("#picAttId").val("");
            });
            layer.close(index);
        },
        fail: function (e, data) {
            layer.close(index);
            popAlert("上传失败,请检查文件");
        }
    });
    $("#backImgFile").backImgUploadPreview({
        Img: "backImgSrc",
        Width: 50,
        Height: 50
    });
}

function clearLivePic() {
    popConfirm("确认删除图片吗？", function () {
        $("#livePicFileId").val("");
        $("#livePicDiv").css("display", "none");
        $("#livePicFile").val("");
        $("#livePicImg").val("");
        $("#picAttId").val("");
    });
}
function clearBackImg() {
    popConfirm("确认删除图片吗？", function () {
        $("#backImgFileId").val("");
        $("#backImgDiv").css("display", "none");
        $("#backImgFile").val("");
        $("#backImg").val("");
        $("#picAttId").val("");
    });
    // //后台删除图片
    // ajaxData("/basicInformation/uploadBackImg", $("#queryForm").formSerialize());
}

$.fn.extend({
    livePicUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "livePicImgSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#livePicImg").val("");
            $("#livePicDiv").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});
$.fn.extend({
    backImgUploadPreview: function (opts) {
        var _self = this, _this = $(this);
        opts = jQuery.extend({
            Img: "backImgSrc",
            Width: 100,
            Height: 100,
            ImgType: ["gif", "jpeg", "jpg", "bmp", "png"],
            Callback: function () {
            }
        }, opts || {});
        _self.getObjectURL = function (file) {
            var url = null;
            if (window.createObjectURL != undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL != undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL != undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            $("#backImg").val("");
            $("#backImgDiv").css("display", "block");
            return url
        };
        _this.change(function () {
            if (this.value) {
                if (!RegExp(
                    "\.(" + opts.ImgType.join("|") + ")$",
                    "i").test(this.value.toLowerCase())) {
                    alert("选择文件错误,图片类型必须是"
                        + opts.ImgType.join("，") + "中的一种");
                    this.value = "";
                    return false
                }
                $.browser = {};
                $.browser.msie = /msie/
                    .test(navigator.userAgent.toLowerCase());
                if ($.browser.msie) {
                    try {
                        $("#" + opts.Img)
                            .attr(
                                'src',
                                _self
                                    .getObjectURL(this.files[0]))
                    } catch (e) {
                        var src = "";
                        var obj = $("#" + opts.Img);
                        var div = obj.parent("div")[0];
                        _self.select();
                        if (top != self) {
                            window.parent.document.body.focus()
                        } else {
                            _self.blur()
                        }
                        src = document.selection.createRange().text;
                        document.selection.empty();
                        obj.hide();
                        obj
                            .parent("div")
                            .css(
                                {
                                    'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                    'width': opts.Width
                                        + 'px',
                                    'height': opts.Height
                                        + 'px'
                                });
                        div.filters
                            .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                    }
                } else {
                    $("#" + opts.Img).attr('src',
                        _self.getObjectURL(this.files[0]))
                }
                opts.Callback()
            }
        })
    }
});


/**
 * tree init
 */
function treeInit() {
    $("#nodeCreate").click(function () {
        nodeCreate();
    });

    $("#refresh").click(function () {
        refresh();
    });

    $("#getConsole").click(function () {
        getConsole();
    });


    $("#nodeSave").click(function () {
        nodeSave();
    });


    selectNode = undefined;
    var zNodes = $.parseJSON($("#treeValue").text());
    map["treeObj"] = $.fn.zTree.init($("#tree"), setting, zNodes);
    var treeObj = map["treeObj"];
    //var fNodes = treeObj.getNodesByParamFuzzy("status", "0");
    treeObj.expandAll(true);
    //treeObj.hideNodes(fNodes);
    // 设定表单校验规则
    // $("#treeForm").validate({
    //     rules: {
    //         "itemName": {
    //             required: true,
    //             maxlength: 128
    //         }
    //     }
    // });
    $("#nodeSave").hide();
    $("#refresh").hide();
    $("#selectBtn").hide();
    $("#cancelBtn").hide();
}

/**
 * create the root node
 */
function nodeCreate() {
    if($("#id").val()==''){
        popMsg("请先保存课程")
    }else {
        if($("#releaseFlag").val()=='1'){
            popMsg("当前课程已在发布中，修改请取消发布")
        }else {
            var zTree = $.fn.zTree.getZTreeObj("tree");
            var newNode = zTree.addNodes(null,
                {
                    id: undefined,
                    pId: null,
                    bizId: $("#id").val(),
                    iconSkin: 'TITLE',
                    level: '0',
                    itemName: '',
                    status: '1'
                }
            );
            zTree.editName(newNode[0]);
        }
    }
}

/**
 * tree update callBack
 * @param data
 */
function saveTreeCallback(data) {
    $("#itemName").text("");
    $("#iconSkin").text("");
    $("#videoName").text("");
    $("#period").text("");
    $("#videoDuration").text("");
    $("#nodeId").val("");

    popMsg("保存成功");
    if (data) {
        curPath = "";
        var treeObj = $.fn.zTree.init($("#tree"), setting, data);
        treeObj.expandAll(true);

        treeOperateHide();
        videoSelectFlag = true;
    }
}


/**
 * tree update callBack
 * @param data
 */
function refreshCallback(data) {
    $("#itemName").text("");
    $("#iconSkin").text("");
    $("#videoName").text("");
    $("#period").text("");
    $("#videoDuration").text("");
    $("#nodeId").val("");
    popMsg("撤销成功");
    if (data) {
        curPath = "";
        var treeObj = $.fn.zTree.init($("#tree"), setting, data);
        treeObj.expandAll(true);
        treeOperateHide();
        videoSelectFlag = true;
    }
}

/**
 * tree statusChange, not use
 */
function statusChange() {
    var treeObj = map['treeObj'];
    var nodes = treeObj.transformToArray(treeObj.getNodes());
    var HidNodes = treeObj.getNodesByParamFuzzy("status", "0");
    if ($("#statusForbidden").prop("checked")) {
        treeObj.showNodes(nodes);
    } else {
        treeObj.hideNodes(HidNodes);
    }
}

/**
 * click the node method
 * @param event
 * @param treeId
 * @param zNodes
 */
function treeOnClick(event, treeId, zNodes) {

    selectNode = zNodes;

    var iconSkin = "";
    if (zNodes.iconSkin == 'TITLE') {
        iconSkin = "标题"
    } else if (zNodes.iconSkin == 'VIDEO') {
        iconSkin = "视频"
    } else {
    }
    $("#itemName").text(zNodes.itemName);
    $("#iconSkin").text(iconSkin);
    $("#videoName").text(zNodes.videoName == null ? "" : zNodes.videoName);
    $("#period").text(zNodes.period == null ? "" : zNodes.period);
    $("#videoDuration").text(zNodes.videoDuration == null ? "" : zNodes.videoDuration);
    $("#edit").show();
    $("#nodeId").val(zNodes.id);

    if (videoSelectFlag) {
        $("#selectBtn").show();
        if (zNodes.iconSkin == 'VIDEO') {
            $("#cancelBtn").show();
        } else {
            $("#cancelBtn").hide();
        }

    }

}


function addHoverDom(treeId, treeNode) {
    var sObj = $("#" + treeNode.tId + "_span");
    if (treeNode.level != 0 || treeNode.editNameFlag || $("#addBtn_" + treeNode.tId).length > 0) return;
    var addStr = "<span class='button add' id='addBtn_" + treeNode.tId
        + "' title='' onfocus='this.blur();'></span>";
    sObj.after(addStr);
    var btn = $("#addBtn_" + treeNode.tId);
    if (btn) btn.bind("click", function () {
        var zTree = $.fn.zTree.getZTreeObj("tree");
        var newNode = zTree.addNodes(treeNode,
            {
                id: undefined,
                pId: treeNode.id,
                bizId: $("#id").val(),
                iconSkin: 'TITLE',
                level: '1',
                itemName: '',
                status: '1'
            }
        );
        zTree.editName(newNode[0]);
        console.log(newNode[0]);
        return false;
    });
}

function removeHoverDom(treeId, treeNode) {
    $("#addBtn_" + treeNode.tId).unbind().remove();
}

function beforeEditName(treeId, treeNode) {
    var zTree = $.fn.zTree.getZTreeObj("tree");
    zTree.selectNode(treeNode);
    zTree.editName(treeNode);
    return false;
}

//改名前的钩子函数,name为空返回false, 不执行noRename
var renameFlag = true;

function beforeRename(treeId, treeNode, newName, isCancel) {
    if (treeNode.itemName != "" && newName != "" && treeNode.itemName == newName) {
        renameFlag = false;
    }
    
    if (newName.length == 0) {
        console.log("222222222222222222222222222222222")
        var zTree = $.fn.zTree.getZTreeObj("tree");
        //取消edit,tree恢复改之前的名字
        //zTree.cancelEditName();
        popConfirm("名称不能为空, 确定:继续编辑, 取消:放弃编辑",
            function () {
                zTree.editName(treeNode);
            },
            null,
            function () {
                if (treeNode.id) {
                    zTree.cancelEditName();
                } else {
                    zTree.removeNode(treeNode);
                }
            },
            null,
            "提示");
        return false;
    }
    return true;
}


function onRename(e, treeId, treeNode, isCancel) {
    if (renameFlag) {
        treeOperateShow();
        videoSelectFlag = false;
    } else {
        renameFlag = true;
    }
}

function beforeRemove(treeId, treeNode) {
    popConfirm("确认删除节点?",
        function () {
            //确认删除
            treeNode.status = '0';
            var zTree = $.fn.zTree.getZTreeObj("tree");
            zTree.hideNode(treeNode);
            if (treeNode.children != null && treeNode.children.length > 0) {
                for (var i = 0; i < treeNode.children.length; i++) {
                    treeNode.children[i].status = '0';
                }
            }
            treeOperateShow();
            videoSelectFlag = false;
        },
        null,
        null,
        null,
        "提示");
    return false;
}

function onRemove(e, treeId, treeNode) {
    //onRemove暂时用不到, beforeRemove=false, 永远也不触发onRemove
}

//开始拖的钩子函数
function beforeDrag(treeId, treeNodes) {
    curDragNodes = treeNodes;
    return true;
}

function beforeDragOpen(treeId, treeNode) {
    autoExpandNode = treeNode;
    return true;
}

function onDrag(event, treeId, treeNodes) {
}


//开始drop的钩子函数
//如果为true后, 方可执行onDrop
function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
    console.log(treeId, treeNodes, targetNode, moveType, isCopy);
    //true:node, false:leaf
    var treeFlag = treeNodes[0].level == '0' ? true : false;
    var targetFlag = targetNode.level == '0' ? true : false;
    //leaf->node添加可以, 反之拒绝操作
    if (moveType == "inner") {
        if (treeFlag == false && targetFlag == true) {
            return true;
        }
        return false;
    } else if (moveType == "prev" || moveType == "next") {
        //相同类型的可以移动
        if (treeFlag == targetFlag) {
            return true;
        } else {
            return false;
        }
    }
    return true;
}

//前端放行, 可以执行放的动作, 请求后台
function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
    treeOperateShow();
    videoSelectFlag = false;
}

function nodeSave() {
    if($("#releaseFlag").val()=='1'){
        popMsg("当前课程已在发布中，修改请取消发布")
    }else {
        var treeObj = $.fn.zTree.getZTreeObj("tree");
        var nodes = treeObj.getNodes();
        $.ajax({
            type: "post",
            url: contextPath + "/basicInformation/treeUpdate",
            data: JSON.stringify(nodes),
            dataType: "json",
            contentType: "application/json",
            beforeSend: globalAjaxBeforeSend,
            success: function (data) {
                if (data && data.result) {
                    saveTreeCallback(data.result);
                }
            },
            error: globalAjaxError,
            complete: globalAjaxComplete
        });
    }
}


function refresh() {
    var param = {
        id: $("#id").val()
    };
    ajaxData("/basicInformation/refresh", param, refreshCallback);
}

/**
 * debug console
 */
function getConsole() {
    treeObj = $.fn.zTree.getZTreeObj("tree");
    var nodes = treeObj.getNodes();
    console.log(nodes);
    console.table(treeObj.transformToArray(treeObj.getNodes()));
}

function globalAjaxBeforeSend(xhr) {
    // 加遮罩
    addLoading();
}

function globalAjaxError(xhr, textStatus, errorThrown) {
    console.log(xhr.url + "|" + textStatus + "|" + errorThrown);
    popAlert("请求出错");
}

function globalAjaxComplete(xhr, textStatus) {
    removeLoading();
}

function addLoading() {
    layer.load(2);
}

function removeLoading() {
    layer.closeAll('loading');
}

function treeOperateHide() {
    $("#nodeSave").hide();
    $("#refresh").hide();
    $("#selectBtn").hide();
    $("#cancelBtn").hide();
}

function treeOperateShow() {
    $("#nodeSave").show();
    $("#refresh").show();
    $("#selectBtn").hide();
    $("#cancelBtn").hide();
}

function myWinCallback(data) {
    if(data.period == "null"){
        if (data) {
            var param = {
                id: $("#nodeId").val(),
                videoId: data.videoId,
                iconSkin: 'VIDEO',
                videoName: data.videoName,
                bizId: $("#id").val()
            };
            ajaxData("/basicInformation/nodeUpdate", param, selectVideoCallback);
        }
    }else{
        if (data) {
            var param = {
                id: $("#nodeId").val(),
                videoId: data.videoId,
                iconSkin: 'VIDEO',
                videoName: data.videoName,
                period: data.period,
                bizId: $("#id").val()
            };
            ajaxData("/basicInformation/nodeUpdate", param, selectVideoCallback);
        }
    }

}


function selectVideoCallback(result) {
    popMsg("操作成功。课程原价已根据视频时长生成！");
    //回显
    $("#treeValue").text(result.treeValue)
    //附上视频名称，id啥的
    $("#videoName").text(result.data.videoName);
    $("#period").text(result.data.period);
    $("#videoDuration").text(result.data.videoDuration);
    $("#nodeId").val(result.data.id)
    //根据视频时长计算后的课程原价
    $("#originalPrice").val(result.originalPrice)
    selectNode = undefined;
    var zNodes = $.parseJSON($("#treeValue").text());
    map["treeObj"] = $.fn.zTree.init($("#tree"), setting, zNodes);
    var treeObj = map["treeObj"];
    //var fNodes = treeObj.getNodesByParamFuzzy("status", "0");
    treeObj.expandAll(true);
}


function deleteSchCourseVideoMapCallback(result) {
    popMsg("操作成功！");
    $("#videoName").text("");
    $("#period").text("");
    $("#nodeId").val("");
    $("#videoDuration").text("");
}



function refreshLetterIndex(tableId) {
    var rowIndex = 0;
    $("#" + tableId).find("." + tableId + "Td").each(
        function (index, item) {
            console.log($(item));
            $(item).text(index + 1);
            rowIndex++;
        });
    // 返回最大的index
    return rowIndex;
}


function btnCancel() {
    var param = {
        id: $("#id").val(),
        releaseFlag: '0'
    };
    ajaxData("/basicInformation/courseRelease", param, function (data) {
        popMsg("已取消");
        $("#releaseFlag").val('0');
        $("#btnCancel").hide();
        $("#btnRelease").show();
        $("#dataUploadBtn").show();
        $(".deleteFile").each(function (n,obj) {
            $(obj).show();
        })
    });
}

function btnRelease() {
    var treeObj = $.fn.zTree.getZTreeObj("tree");
    var nodes = treeObj.getNodes();
    var param = {
        id: $("#id").val(),
        releaseFlag: '1',
        // releaseTime: $("#releaseTime").val()
    };
    if (nodes==''){
        popMsg("请选择关联视频")
    }else {
        ajaxData("/basicInformation/courseRelease", param, function (data) {
            popMsg("已发布");
            $("#releaseFlag").val('1');
            $("#btnCancel").show();
            $("#btnRelease").hide();
            // $("#dataUploadBtn").hide();
            // $(".deleteFile").each(function (n,obj) {
            //     $(obj).hide();
            // })
        });
    }
}

function btnClose() {
    closeWinCallBack();
}

//金融显示千位符,保存至后台是带","的字符串
function formatter(o) {
    var arr = o.value.split('.');
    var tmp = arr[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
    var ss = o.selectionStart + tmp.length - arr[0].length;// 控制光标的位置-保持不变
    if (arr[1] != undefined) {
        o.value = tmp + '.' + arr[1];
    } else {
        o.value = tmp
    }
    o.selectionStart = o.selectionEnd = ss
}

function dataFileInit() {
    var dataValue = $.parseJSON($("#dataValue").text());
    if ($("#dataTmpl").length > 0) {
        var html = $("#dataTmpl").render(dataValue);
        $("#courseDataBody").empty();
        $("#courseDataBody").append(html);
    }
}
function checkbox(fieldId,obj) {
    var x = $(obj).is(':checked');
    var openFlag;
    if (x == true) {
        openFlag = "1";
    }else {
        openFlag = "0";
    }
    var param = {
        attachmentId: fieldId,
        openFlag: openFlag
    };
    ajaxData("/schoolCourse/openFile", param, function (data) {
        popMsg("操作成功")
    })
}
function delData(fieldId) {
    layer.confirm("确认删除", {icon: 3, title: "系统确认"}, function (index) {
        layer.close(index);
        var param = {
            bizId: $("#id").val(),
            fileId: fieldId
        };
        ajaxData("/schoolCourse/delFile", param, function (data) {
            if ($("#dataTmpl").length > 0) {
                var html = $("#dataTmpl").render(data);
                $("#courseDataBody").empty();
                $("#courseDataBody").append(html);
            }
            //refreshLetterIndex('courseDataBody');
            popMsg("删除成功");
        });
    });
}
function relationLive(){
    let param = {
        id: $("#id").val(),
    };
    popWin("关联直播", "/basicInformation/relationLiveInit",param,"40%", "70%",relationCallBack);
}

function relationCallBack(){
    popMsg("关联成功")
}