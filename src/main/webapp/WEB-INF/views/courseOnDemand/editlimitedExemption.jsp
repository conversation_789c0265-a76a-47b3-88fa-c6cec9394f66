<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <script type="text/javascript">
        var id = '${id}';
        var personId = '${limitedExemptionDto.personId}';
    </script>
</head>
<body style="background-color: #fff">
<div class="panel">
    <div class="panel-heading">
        <div class="row" style="padding-top: 5px;padding-bottom: 10px">
            <div class="col-md-12">
                <div class="col-md-6 text-left">
                    <div style="font-size: 20px;font-weight: bold">限免管理基本信息</div>
                </div>
                <div class="col-md-6 text-right">
                    <span id="btnRelease" class="identity-limit btn btn-primary"
                            <c:if test="${ifRelease == '1' || id==''}">
                                style="display: none"
                            </c:if>
                    >发布</span>
                    <span id="btnCancel" class="identity-limit btn btn-primary"
                            <c:if test="${ifRelease == '0' || id==''}">
                                style="display: none"
                            </c:if>
                    >取消发布</span>
                    <span id="btnSave" class="identity-limit btn btn-primary"
                    >保存基本信息</span>
                    <span id="btnClose" class="btn">关闭</span>
                </div>
            </div>
        </div>
        <form:form modelAttribute="limitedExemptionDto" id="queryForm" autocomplete="off">
            <form:hidden path="id"/>
            <form:hidden path="personId"/>
            <form:hidden path="ifRelease"/>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>模块名称</label>
                    <div class="col-md-7">
                        <form:input path="moduleName" placeholder="请输入模块名称" cssClass="form-control"/>
                    </div>
                </div>
            </div>
            <div class="row" >
                <div class="col-md-12">
                    <label class="col-md-1 control-label">模块描述</label>
                    <div class="col-md-10" style="padding-bottom: 8px;min-height: 100px">
                        <form:textarea cols="60" rows="5" path="moduleNote" placeholder="请输入模块描述"></form:textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12" style="padding-bottom: 10px" >
                    <label class="col-md-1 control-label">关联课程包</label>
                    <div class="col-md-7">
                        <input id="packageId" type="text" class="t-select" json-data='${coursePackageList}'
                               selected-ids="${limitedExemptionDto.packageId}"/>
                        <input name="packageId" type="hidden" placeholder="请选择课程包" value='${limitedExemptionDto.packageId}'/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12" style="padding-bottom: 10px">
                    <label class="col-md-1 control-label">关联直播课程</label>
                    <div class="col-md-7">
                        <input id="liveId" type="text" class="t-select" json-data='${liveInfoList}'
                               selected-ids="${limitedExemptionDto.liveId}"/>
                        <input name="liveId" type="hidden" placeholder="请选择直播课程" value='${limitedExemptionDto.liveId}'/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">关联人员</label>
                    <div class="col-md-10 no-padding" style="margin-left: 10px;">
                        <input type="button" style="margin-right: 15px;" class="btn"  value="选择用户" id="btnSendUser"/>
                    </div>
                </div>
            </div>
        </form:form>

    </div>
</script>
</div>
</body>
</html>
