<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>中上协培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
        .SortId:before {
            content: counter(sectioncounter);
            counter-increment: sectioncounter;
        }
        tbody {
            counter-reset:sectioncounter;
        }
        .iconStyle{
            padding: 5px;
            cursor:pointer;
        }
        .fa-caret-down{
            top:13px!important;
        }
    </style>
    <script>
    </script>
</head>
<body style="background-color: #FFFFFF;min-height:550px">
<%--<div class="row" style="margin: 10px 0 10px 20px;">--%>
<%--<button type="button" class="btn btn-primary" id="expandAll" onclick="expandAll();">全部展开</button>--%>
<%--<button type="button" class="btn btn-primary" id="closeAll" onclick="closeAll();">全部收起</button>--%>
<%--</div>--%>
<div class="col-md-4">
    <div class="panel">
        <div class="panel-heading" >
            <i class="icon icon-list">&nbsp;事项类别</i>
            <input type="checkbox" id="statusForbidden" onclick="statusFun(this)"><label for="statusForbidden">显示禁止分类</label>
            <%--            <i class="icon icon-angle-down violateIcon" style="margin-left: 180px;" onclick="showOrHide('violateType',this);"></i>--%>
        </div>
        <div class="panel-body expandArea violateType" style="height:495px;overflow:auto;background:#EEEEEE;">
            <ul id="schoolCourseNodeTree" class="ztree"></ul>
        </div>
    </div>
</div>
<div class="col-md-8">
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-list-alt"><label>&nbsp;业务类别</label></i>
        </div>
        <div class="panel-body" style="height: 500px; overflow: auto; background: #EEEEEE;">
            <form:form id="ipoClassForm"  modelAttribute="schoolCourseTypeDto">
                <div class="row form-group">
                    <div class="col-md-mine"><label><strong>操作模式</strong></label></div>
                    <div class="col-md-10">
<%--                        <input type="button" class="btn modifyBtn" id="modifyBtn" style="margin-right: 3px" value="添加主分类" onclick="changeFunction(0,this)" />--%>
                        <input type="button" class="btn modifyBtn" id="modifyBtn1" style="margin-right: 3px" value="添加子分类" onclick="changeFunction(1,this)" />
                        <input type="button" class="btn modifyBtn" id="modifyBtn2" style="margin-right: 3px" value="修改分类" onclick="changeFunction(2,this)" />
                        <input type="button" class="btn modifyBtn" id="modifyBtn3" style="margin-right: 3px" value="上移分类" onclick="changeFunction(3,this)" />
                        <input type="button" class="btn modifyBtn" id="modifyBtn4" style="margin-right: 3px" value="下移分类" onclick="changeFunction(4,this)" />
                            <%--                            <input type="button" class="btn modifyBtn" id="modifyBtn5" style="margin-right: 3px" value="调整分类" onclick="changeFunction(5,this)" />--%>
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-md-mine"><label><strong>选中分类</strong></label></div>
                    <div class="col-md-4"><label id="currentClass"></label></div>
                </div>
                <div class="row form-group">
                    <div class="col-md-mine"><label><strong>分类ID</strong></label></div>
                    <div class="col-md-4"><label id="currentClassId"></label></div>
                </div>
                <div class="row form-group" id="show">
                    <div class="col-md-mine"><label><strong>分类名称</strong></label></div>
                    <div class="col-md-4"><form:input path="itemName" cssClass=" itemNameClass form-control"/></div>
                    <input style="display:none">
                </div>

                <div class="row form-group" id="show">
                    <div class="col-md-mine"><label><strong>分类简介</strong></label></div>
                    <div class="col-md-4">
                        <form:textarea path="courseTypeBrief"  cssClass="courseTypeBriefClass form-control" cssStyle="height: 34px" autocomplete="off" />
                    </div>
                    <input style="display:none">
                </div>

<%--                <div class="row form-group">--%>
<%--                    <div class="col-md-mine"><label><strong>分类图片</strong></label></div>--%>
<%--                    <div class="col-md-4" style="padding: 6px">--%>
<%--                        <span class="btn btn-sm btn-primary fileinput-button">--%>
<%--                            <span>上传图片</span>--%>
<%--                            <input id="uploadPng" type="file" name="files">--%>
<%--                        </span>--%>
<%--                        <span onclick="clearImg()" class="btn btn-sm" style="margin-left: 20px;">删除</span>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div class="row form-group">--%>
<%--                    <div style="font-size: 12px;color: red;position: relative;top: -18px;">（请上传1:1尺寸的图片）</div>--%>
<%--                    <div id="imgId" class="col-md-5" style="display: none">--%>
<%--                        <img id="ImgPr" src="" style="width: 110px;height: 110px; "  />--%>
<%--                        <input type="hidden" name="fileId" id="fileId" value=""/>--%>
<%--                    </div>--%>
<%--                </div>--%>


                <div class="row">
                    <div class="col-md-6 col-md-offset-3">
                            <input type="button" class="btn btn-primary" id="saveBtn" style="margin-right: 20px" value="保存"/>
                            <input type="button" class="btn" id="forbiddenBtn" style="margin-right: 20px" value="禁用选中节点"/>
                            <input type="button" class="btn" id="forbiddenBtn1" style="margin-right: 20px" value="解禁选中节点"/>
                    </div>
                </div>
                <form:hidden path="parentItemNo"/>
                <form:hidden path="id"/>
                <div id="schoolCourseNodeTreeValue" style="display:none">${schoolCourseNodeTree}</div>
            </form:form>
        </div>
    </div>

</div>

</body>
</html>
