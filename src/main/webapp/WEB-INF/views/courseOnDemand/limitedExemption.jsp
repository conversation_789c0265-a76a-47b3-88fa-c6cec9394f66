<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
</head>
<body>
<div class="tab-content">
    <div class="panel-heading">
        <form:form modelAttribute="limitedExemptionDto" id="queryForm" onkeydown="bindEnter(event)">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label" style="width: 100px">模块名称：</label>
                    <div class="col-md-3">
                        <form:input path="moduleName" placeholder="请输入模块名称" cssClass="form-control"/>
                    </div>
                </div>
            </div>
        </form:form>
        <div class="row">
            <div class="col-md-12">
                <div class="col-md-12 text-right"  style="padding-top: 5px">
                    <span id="btnAdd" class="btn btn-primary">新增</span>
                    <span id="btnQuery" class="btn btn-primary">查询</span>
                    <span id="btnClear" class="btn btn-default">清空全部</span>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-pane fade in active">
        <e:grid id="limitTable" action="/limitedExemption/queryLimitedExemptionList" cssClass="table table-striped table-hover">
            <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                          cssStyle="width:5%" />
            <e:gridColumn label="模块名称" displayColumn="moduleName" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
            <e:gridColumn label="课程包名称" displayColumn="packageName" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
            <e:gridColumn label="发布状态" renderColumn="ifRelease" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
            <e:gridColumn label="最后编辑时间" displayColumn="updateTime" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
            <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false"
                          cssClass="text-center" cssStyle="width:10%" />
        </e:grid>

    </div>
</div>
</body>
</html>
