<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <script type="text/javascript">
        var userId = "${userId}";
        var personName = "${personName}";
        var examBaseUrl = "${examBaseUrl}";
    </script>
    <e:js />
</head>
<body>
<div class="panel">
    <ul id="myTab" class="nav nav-tabs">
        <li class="active"><a href="#tab1" style="font-weight: 600" data-toggle="tab" onclick="toPage('courseTypeManage')">分类管理</a></li>
        <li ><a href="#tab2" data-toggle="tab" style="font-weight: 600" id="ipoFdFileParent" onclick="toPage('courseVideoManage')">视频管理</a></li>
        <li ><a href="#tab3" data-toggle="tab" style="font-weight: 600" id="ipoFdXsbFileParent" onclick="toPage('essentialInformationManage')">基本信息管理</a></li>
        <li ><a href="#tab7" data-toggle="tab" style="font-weight: 600" id="ipoFd" onclick="toPage('courseRanking')">课程排序</a></li>
        <c:if test="${isShowExam == true}">
            <li><a href="#tab6" data-toggle="tab" style="font-weight: 600" onclick="openExam()">课程试题管理</a></li>
        </c:if>
    </ul>
    <div class="panel-body">
        <div class="tab-content">
            <div class="tab-pane fade in active" id="tab1">
                <iframe id="courseTypeManage" scrolling="no" style="width:100%;border:0px;"></iframe>
            </div>

            <div class="tab-pane fade in" id="tab2">
                <iframe id="courseVideoManage" scrolling="no" style="width:100%;border:0px;"></iframe>
            </div>

            <div class="tab-pane fade in" id="tab3">
                <iframe id="essentialInformationManage" scrolling="no" style="width:100%;border:0px;"></iframe>
            </div>

            <div class="tab-pane fade in" id="tab4">
                <iframe id="columnManage" scrolling="no" style="width:100%;border:0px;"></iframe>
            </div>

            <div class="tab-pane fade in" id="tab5">
                <iframe id="eventManage" scrolling="no" style="width:100%;border:0px;"></iframe>
            </div>

            <div class="tab-pane fade in" id="tab7">
                <iframe id="courseRanking" scrolling="no" style="width:100%;border:0px;"></iframe>
            </div>
            <c:if test="${isShowExam == true}">
                <div class="tab-pane fade in" id="tab6">
                </div>
            </c:if>
        </div>
    </div>
</div>

</body>
</html>
