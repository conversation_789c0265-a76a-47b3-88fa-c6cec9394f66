<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label{
            padding-top: 7px;
        }
        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <form:form modelAttribute="courseDto" id="queryForm" onkeydown="bindEnter(event)">
            <input id="currUserId" type="hidden" value="${currentUser}">
            <input id="isAdmin" type="hidden" value="${isAdmin}">
            <input id="isOrdinaryUser" type="hidden" value="${isOrdinaryUser}">
            <input id="baseUrl" type="hidden" value="${baseUrl}">

            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">课程名称</label>
                    <div class="col-md-3">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">业务专题</label>
                    <div class="col-md-3">
                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}' placeholder="请选择业务专题" />
                        <input name="courseType" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">职务分类</label>
                    <div class="col-md-3">
                        <input id="subject" type="text" class="t-select" json-data='${courseTypeSelect005}' placeholder="请选择职务分类" />
                        <input name="subject" type="hidden" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">发布时间</label>
                    <div class="col-md-3 daterange">
                        <form:input path="releaseTime" placeholder="请输发布时间" cssClass="form-control" />
                    </div>
                    <label class="col-md-1 control-label">是否发布</label>
                    <div class="col-md-3">
                        <form:select path="releaseFlag" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未发布</form:option>
                            <form:option value="1">已发布</form:option>
                        </form:select>
                    </div>
                    <label class="col-md-1 control-label">是否开放</label>
                    <div class="col-md-3">
                        <form:select path="ifOpen" cssClass="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="0">未开放</form:option>
                            <form:option value="1">已开放</form:option>
                        </form:select>
                    </div>

                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">讲师</label>
                    <div class="col-md-3">
                        <input id="teacher" type="text" class="t-select" json-data='${teacherSelect}' placeholder="请输入讲师" />
                        <input name="teacher" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">课程类型</label>
                    <div class="col-md-3">
                        <input id="applyMechanism" type="text" class="t-select" json-data='${courseTypeSelect004}'  placeholder="请选择课程类型"/>
                        <input name="applyMechanism" type="hidden" />
                    </div>
                    <label class="col-md-1 control-label">适用板块</label>
                    <div class="col-md-3">
                        <input id="applyPlate" type="text" class="t-select" json-data='${courseTypeSelect003}' placeholder="请选择板块"/>
                        <input name="applyPlate" type="hidden"  />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">监管课程</label>
                    <div class="col-md-3">
                        <input id="superviseType" type="text" class="t-select" json-data='${superviseTypeList}' placeholder="请选择监管课程"/>
                        <input name="superviseType" type="hidden"  />
                    </div>
                    <div class="col-md-8 text-right"  style="padding-top: 5px">
                        <sec:authorize access="hasAuthority('RES_EXPORT_COURSE_AUTHORITY_3')" >
                            <span id="export" class="btn btn-primary">导出</span>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('RES_COURSE_QUERY_INFO_AUTHORITY_3')" >
                            <span id="btnQuery" class="btn btn-primary">查询</span>
                        </sec:authorize>
                        <span id="btnClear" class="btn btn-default">清空全部</span>
                        <sec:authorize access="hasAuthority('RES_ADD_COURSE_INFO_AUTHORITY_3')" >
                            <span id="addCourse" class="btn btn-primary">新增课程</span>
                        </sec:authorize>
                            <%--                        <span id="exportCourse" class="btn btn-primary">导出excel</span>--%>
                    </div>
                </div>
            </div>
        </form:form>
        <sec:authorize access="hasAuthority('RES_ADD_COURSE_INFO_AUTHORITY_3')" >
            <%--编辑权限--%>
            <input type="hidden" id="editCourseAuth" value="true">
        </sec:authorize>
        <sec:authorize access="hasAuthority('RES_DELETE_COURSE_AUTHORITY_3')" >
            <%--删除权限--%>
            <input type="hidden" id="delCourseAuth" value="true">
        </sec:authorize>
    </div>
    <div class="panel-body">
        <div class="row">
            <e:grid id="tableAll" action="/basicInformation/queryRepCaseInfoList" cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderColumnIndex" orderable="false" cssClass="text-center"
                              cssStyle="width:5%" />
                <e:gridColumn label="课程名称" renderColumn="courseName" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="学时" renderColumn="period" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="原价" renderColumn="renderColumnOriginalPrice" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="业务专题" renderColumn="courseType" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程类型" renderColumn="applyMechanism" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="课程讲师" renderColumn="teacher" orderable="false"
                              cssClass="text-center" cssStyle="width:10%" />
                <e:gridColumn label="是否发布" renderColumn="columnRelease"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="是否开放" renderColumn="columnOpen"  orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
                <e:gridColumn label="发布时间" displayColumn="releaseTime" orderColumn="release_time" cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="播放链接" renderColumn="rendOpenUrl"  cssClass="text-center"
                              cssStyle="width:10%" />
                <e:gridColumn label="操作" renderColumn="columnOperation" orderable="false" cssClass="text-center"
                              cssStyle="width:8%" />
            </e:grid>
        </div>
    </div>
</div>
</body>
</html>
