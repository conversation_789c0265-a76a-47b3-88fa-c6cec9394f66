<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>易董 云端管理平台</title>
    <e:base />
    <e:js />
</head>
<body>

<div class="panel" style="padding: 15px 15px 40px">
    <div class="row">
        <form:form action="" modelAttribute="videoInfoDto" id="videoForm">
            <div class="form-group">
                <label class="col-xs-1  control-label"
                       style="padding-top: 10px;  text-align:left; width: 60px;">文件名</label>
                <div class="col-xs-2">
                    <form:input path="videoName" cssClass="form-control" />
                </div>
            </div>
            <div class=" col-xs-1" align="right">
                <input type="button" id="querySendMessageBtn" class="btn btn-primary" value="查询">
            </div>
        </form:form>
    </div>
    <e:grid id="sendTable" action="/basicInformation/videoInfoListInit" cssClass="table table-striped table-hover">
        <e:gridColumn label="选择" renderColumn="renderColumnSelect" orderable="false" cssClass="text-center"
                      cssStyle="text-align:center;width:10%;"/>
        <e:gridColumn label="文件名" displayColumn="videoName" orderable="false" cssClass="text-center"/>
        <e:gridColumn label="学时" displayColumn="period" orderable="false" cssClass="text-center"/>
        <e:gridColumn label="视频类型" displayColumn="videoTypeName" orderable="false" cssClass="text-center"/>
        <e:gridColumn label="视频时长" displayColumn="videoDuration" orderable="false" cssClass="text-center"/>
    </e:grid>
    <div style="border:1px solid #a0d7e9;background-color: #E6ECEE;border-radius: 350px;position: absolute;
                    top:50%;left:50%;margin-left:-90px;margin-top:-10px;width:300px;height:20px;display:none; " id="rateprogress">
        <span id="progress_num" style="font-size: 16px;color:#0086A7;width: 33px;max-width:33px;text-align:center;display:inline-block"></span>
        <div id="progress" class="progress-bar" style="background-color:#0086A7; border-radius: 350px;"></div>
    </div>
    <input type="hidden" id="approveFilesSuffix1" value="mp4;ts;mov;mxf;mpg;flv;wmv"/>
    <input type="hidden" id="uploadVideoType" value=""/>
</div>
</body>
</html>