$(document).ready(function () {
        $("#btnSave").bind('click', function () {
            if ($("#sortEnd").val() == "") {
                popMsg("调整顺序必须填写！")
            } else if (parseInt($("#sortEnd").val()) > parseInt($("#maxSort").val())) {
                popMsg("最大序号为" + (parseInt($("#maxSort").val())))
            } else if (parseInt($("#sortEnd").val()) < 1) {
                popMsg("最小序号为1")
            } else if (parseInt($("#sortEnd").val()) == parseInt($("#sortColumn").text())) {
                popMsg("顺序无需调整")
            } else {
                var param = {
                    id: $("#id").val(),
                    sort: $("#sort").val(),
                    exchangeSort: $("#sortEnd").val(),
                }
                ajaxData("/courseRanking/moveInfo", param, function (res) {
                    closeWinCallBack();
                })
            }
        });
        $("#btnClose").bind('click', function () {
            closeWinCallBack();
        });
    }
)