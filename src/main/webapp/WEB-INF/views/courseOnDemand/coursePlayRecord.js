let isSearchFlag = false;
$(document).ready(function () {
    // 下拉初始化
    tSelectInit();
    dateInit();

    $('#btnQuery').click(function () {
        search();
    });

    $('#btnClear').click(function () {
        $("#queryForm")[0].reset();
        $("input[type='hidden']").each(function () {
            $(this).val("");
        });

        tSelectInit();
        dateInit();
        search();
    });

    //导出
    $("#export").bind("click",function () {
        window.open(contextPath + "/coursePlayRecord/coursePlayRecordExport?" + $("#queryForm").serialize());
    });
});


function search() {
    ajaxTableQuery("tableAll", "/coursePlayRecord/queryCoursePlayRecordList",
        $("#queryForm").formSerialize());
    isSearchFlag = true;
}
function dateInit() {
    dataRangePickerInit($('#releaseTime'), null, null, function () {
    }, function () {
    });
    dataRangePickerInit($('#amountTime'), null, null, function () {
    }, function () {
    });
}
//下拉初始化
function tSelectInit() {
    const tSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    const bSelectOptions = {
        id: 'codeValue',
        name: 'codeName',
        value: 'codeValue',
        grade: 1,
        resultType: 'all',
        style: {},
        inputSearch: true,
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    const teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    const courseTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#belongCommission').tselectInit(null, bSelectOptions);//辖区
    $('#applyMechanism').tselectInit(null, tSelectOptions);//课程类型
    $('#teacher').tselectInit(null, teaSelectOptions);//教师
    $('#courseType').tselectInit(null, courseTypeSelectOptions);//业务专题
}
//下拉框自定义方法
function tSelectCustomCallBack(t) {
}
//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
function columnRelease(data) {
    if (data.releaseFlag==='0'){
        return '否'
    }else if (data.releaseFlag==='1'){
        return '是'
    }else {
        return ''
    }
}
function columnOpen(data) {
    if (data.ifOpen==='0'){
        return '否'
    }else if (data.ifOpen==='1'){
        return '是'
    }else {
        return ''
    }
}
function viewNum(data) {
    return data.viewNum;
}

function columnOperation(data) {
    return '<i class="icon icon-eye-open iconColor" title="查看" onclick="watchUserInfo(\'' + data.courseId + '\')"></i>';
}
function watchUserInfo(courseId) {
    let belong = $("#queryForm").formSerialize().split("belongCommission=")
    let belongCommission;
    if (isSearchFlag === true){
        belongCommission = belong[1].split("%2C");
    }else if (isSearchFlag === false){
        belongCommission = '';
    }
    const param = {
        courseId: courseId,
        belongCommission : belongCommission.toString()
    };
    parent.popWin('查看点播观看记录', '/coursePlayRecord/coursePlayRecordViewInit', param, '98%', '98%', '','','');
}

function ajaxTableQuery(tableId, url, queryInfo) {
    // 区分是否用户重新查询
    if ( queryInfo && typeof queryInfo !== "string" ) {
        queryInfo = $.param(queryInfo);
    }
    $("#"+tableId).data("query_click", true);
    $("#"+tableId).dataTable().api().ajax.url(contextPath+url+"?"+queryInfo).load();
    $("#"+tableId).dataTable().api().columns.adjust();
}
// 列表索引列
function renderColumnIndex(data, type, row, meta) {
    return meta.row + 1;
}
function courseName(data) {
    return data.courseName;
}
//学时
function period(data) {
    return data.period;
}
//业务专题
function courseType(data) {
    let str = data.courseType;
    if (str != null && str !== '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }
    return '<span title="' + data.courseType + '">' + str + '</span>';
}

function applyPerson(data) {
    let str = data.applyPerson;
    if (str != null && str !== '') {
        if (str.length > 12) {
            str = str.substring(0, 12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyPerson + '">' + str + '</span>';
}

function applyMechanism(data) {
    let str = data.applyMechanism;
    if (str!=null && str !==''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.applyMechanism + '">' + str + '</span>';
}

function teacher(data) {
    let str = data.teacher;
    if (str!=null && str !==''){
        if (str.length>12){
            str = str.substring(0,12) + "...";
        }
    }else {
        return ""
    }
    return '<span title="' + data.teacher + '">' + str + '</span>';
}
