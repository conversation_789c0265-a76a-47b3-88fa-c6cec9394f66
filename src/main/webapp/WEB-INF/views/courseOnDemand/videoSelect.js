//@sourceURL=videoSelect.js

$(document).ready(function() {
	$("#querySendMessageBtn").bind("click", function() {
		ajaxTableQuery("sendTable", "/basicInformation/videoInfoListInit", $("#videoForm").formSerialize());
	});

    $("#checkVideoType").bind("click", function() {
        checkVideoType();
    });


});

function renderColumnOperation(data, type, row, meta) {
	var num = parseInt(data.num);
	var tempRead ='<span onclick="editVideoType(\'' + data.id + '\')"><i style="cursor:pointer" class="icon icon-edit"></i></span>'
    + '<span onclick="deleteFile(\'' + data.id + '\',\'' + num + '\')"><i style="cursor:pointer" class="icon icon-trash"></i></span>';
	return tempRead;
}

function editVideoType(videoId){
	popWin('修改视频信息','/videomessage/editVideoInfo',{videoId:videoId}, '350px', '200px',editVideoTypeCallback)
}

function editVideoTypeCallback(){
	ajaxTableReload("sendTable",false);
}

function deleteFile(videoId,num) {
	var param = {videoId: videoId};
	if (num < 1) {
		ajaxData("/videomessage/deleteFile", param, function (data) {
			popMsg("删除成功!");
			ajaxTableReload("sendTable",false);
		})
	}else{
		popMsg("只能删除没有关联的视频!");
	}
}

document.onkeydown = function () {
	if (window.event && window.event.keyCode == 13) {
		window.event.returnValue = false;
	}
}

function renderColumnIndex(data, type, row, meta) {
	return meta.row + 1;
}

function renderColumnNum(data, type, row, meta) {
	if(parseInt(data.num)>0){
		return '是'
	}else{
		return '否'
	}
}

function renderColumnVideoType(data, type, row, meta) {
    if(data.videoType == '1'){
        return '操作演示'
    }else{
        return '课程视频'
    }
}



function checkVideoType(){
    var content = "<div>" +
        "<input type='radio' value='1' id='checkVideoType' name='checkVideoType' checked/>操作演示" +
        "<input type='radio' value='2' id='checkVideoType' name='checkVideoType'/>课程视频" +
        "</div>";
    var layerIndex = layer.open({
        title:'选择上传视频类型',
        content: content,
        btn: ['确定', '取消']
        ,yes: function(index, layero){
            $("#uploadVideoType").val($("#checkVideoType:checked").val());
            layer.close(layerIndex);
            $("#fileupload").trigger("click");
        }
        ,btn2: function(index, layero){
            layer.close(layerIndex);
        }
    });
}

function renderColumnSelect(data, type, row, meta) {
    //console.log(data);
    var renderString = '';
    var tempWrite = '<input type="radio" onclick="editMessage(\''+data.id+'\',\''+data.videoName+'\',\''+data.period+'\',\''+data.videoDuration+'\',this)",style="cursor:pointer" name="qwer"/>';
    return tempWrite;
};

function editMessage(id,videoName,period,videoDuration){

    var data = {
        'videoId' : id,
        'videoName' : videoName,
        'period' : period,
        'videoDuration':videoDuration
    };
    closeWinCallBack(data);
}





