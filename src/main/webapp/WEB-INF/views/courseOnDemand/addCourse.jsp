<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
        var id = '${id}';
        var modifyType = '${modifyType}';
        var editStatus = '${editStatus}';
        var completeCou = '${completeCou}';
    </script>
    <title>北京上市公司协会培训管理平台</title>
    <e:base/>
    <e:js/>
    <style>
        label {
            padding-top: 7px;
        }

        .col-md-mine {
            position: relative;
            min-height: 1px;
            padding-right: 5px;
            padding-left: 5px;
            float: left;
            width: 12%;
            font-size: 14px;
        }

        #livePicFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }
        #backImgFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            width: 100px;
            height: 32px;
        }

        #dataFile {
            margin: -16px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            direction: ltr;
            cursor: pointer;
            height: 32px;
        }

        #livePicImg {
            height: 200px;
            border: 0;
            margin: 10px 0;
        }
        #backImg {
            height: 200px;
            border: 0;
            margin: 10px 0;
        }

        .ztree li span.button.VIDEO_ico_open {
            margin-right: 2px;
            background: url('../../../static/images/video.png') no-repeat scroll 0 0 transparent;
            vertical-align: top;
        }

        .ztree li span.button.VIDEO_ico_close {
            margin-right: 2px;
            background: url('../../../static/images/video.png') no-repeat scroll 0 0 transparent;
            vertical-align: top;
        }

        .ztree li span.button.VIDEO_ico_docu {
            margin-right: 2px;
            background: url('../../../static/images/video.png') no-repeat scroll 0 0 transparent;
            vertical-align: top;
        }

        .ztree li span.button.TITLE_ico_open {
            margin-right: 2px;
            background: url('../../../static/images/title.png') no-repeat scroll 0 0 transparent;
            vertical-align: top;
        }

        .ztree li span.button.TITLE_ico_close {
            margin-right: 2px;
            background: url('../../../static/images/title.png') no-repeat scroll 0 0 transparent;
            vertical-align: top;
        }

        .ztree li span.button.TITLE_ico_docu {
            margin-right: 2px;
            background: url('../../../static/images/title.png') no-repeat scroll 0 0 transparent;
            vertical-align: top;
        }

        .ztree * {
            font-size: 14px !important;
        }

        .tree-div {
            /*display: none;*/
        }

        .table-th {
            background-color: #37C8F4 !important;
            color: white;
        }

        .table-del {
            font-style: inherit !important;
        }
        .isNumber{
            border: 1px solid #ccc;
            height: 30px;
            width: 50%;
            background: #F5F5F5;
        }

    </style>
</head>
<body>
<div class="panel">
    <div class="panel-heading">
        <div class="row" style="padding-top: 5px;padding-bottom: 10px">
            <div class="col-md-12">
                <div class="col-md-6 text-left">
                    <div style="font-size: 20px;font-weight: bold">课程基本信息</div>
                </div>
                <div class="col-md-6 text-right">
                    <span id="btnRelease" class="identity-limit btn btn-primary"
                    <c:if test="${releaseFlag == '0' || id==''}">
                        style="display: none"
                    </c:if>
                    >发布</span>
                    <span id="btnCancel" class="identity-limit btn btn-primary"
                    <c:if test="${releaseFlag == '1' || id==''}">
                        style="display: none"
                    </c:if>
                    >取消发布</span>
                    <span id="btnSave" class="identity-limit btn btn-primary"
                    >保存基本信息</span>
                    <span id="btnClose" class="btn">关闭</span>
                </div>
            </div>
        </div>
        <form:form modelAttribute="courseDto" id="queryForm" autocomplete="off">
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>课程名称</label>
                    <div class="col-md-7">
                        <form:input path="courseName" placeholder="请输入课程名称" cssClass="form-control"/>
                    </div>
                    <label class="col-xs-1 control-label">是否开放</label>
                    <div class="col-xs-1 controls">
                        <div class="switch">
                            <input id="ifOpenSwitch" type="checkbox" />
                        </div>
                        <form:input type="hidden" path="ifOpen" value="" />
                    </div>
                    <label class="col-xs-1 control-label">是否必修</label>
                    <div class="col-xs-1 controls">
                        <div class="switch">
                            <input id="compulsorySwitch" type="checkbox" />
                        </div>
                        <form:input type="hidden" path="compulsory" value="" />
                    </div>
<%--                    <label class="col-md-1 control-label">课程发布时间</label>--%>
<%--                    <div class="col-md-3 daterange">--%>
<%--                        <e:date cssClass="form-control" path="releaseTime" format="yyyy-mm-dd hh:ii:ss" placeholder="请选择课程发布时间" cssStyle="background-color: #e5e5e5;"/>--%>
<%--                        <i class="glyphicon glyphicon-calendar fa fa-calendar daterangetag" style="right: 18px; top: 7px;"></i>--%>
<%--                    </div>--%>
                </div>
            </div>
            <div class="row" >
                <div class="col-md-12">
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>业务专题</label>
                    <div class="col-md-3">

                        <input id="courseType" type="text" json-data='${courseTypeSelect001}' class="t-select isRequired"
                               selected-ids="${courseDto.courseType}" placeholder="请选择业务专题"/>

<%--                        <input id="courseType" type="text" class="t-select" json-data='${courseTypeSelect001}'--%>
<%--                               selected-ids="${courseDto.courseType}"/>--%>
<%--                        <input name="courseType" type="hidden" placeholder="请选择课程类型"--%>
<%--                               value='${courseDto.courseType}'/>--%>
                    </div>
                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>讲师</label>
                    <div class="col-md-3">
                        <input id="teacher" type="text" class="t-select" json-data='${teacherSelect}'
                               selected-ids="${courseDto.teacher}"/>
                        <input name="teacher" type="hidden" placeholder="请输入讲师" value='${courseDto.teacher}'/>
                    </div>
                    <label class="col-md-1 control-label">职务分类</label>
                    <div class="col-md-3">
                        <input id="subject" type="text" class="t-select" json-data='${courseTypeSelect005}'
                               selected-ids="${courseDto.subject}"/>
                        <input name="subject" type="hidden" placeholder="请选择板块" value='${courseDto.subject}'/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <label class="col-md-1 control-label">适用板块</label>
                    <div class="col-md-3">
                        <input id="applyPlate" type="text" class="t-select" json-data='${courseTypeSelect003}'
                               selected-ids="${courseDto.applyPlate}"/>
                        <input name="applyPlate" type="hidden" placeholder="请选择板块" value='${courseDto.applyPlate}'/>
                    </div>
                    <label class="col-md-1 control-label">课程类型</label>
                    <div class="col-md-3">
                        <input id="applyMechanism" type="text" class="t-select" json-data='${courseTypeSelect004}'
                               selected-ids="${courseDto.applyMechanism}"/>
                        <input name="applyMechanism" type="hidden" placeholder=""
                               value='${courseDto.applyMechanism}'/>
                    </div>
                    <label class="col-md-1 control-label">适用人群</label>
                    <div class="col-md-3 ">
                        <input id="applyPerson" type="text" class="t-select" json-data='${courseTypeSelect002}'
                               selected-ids="${courseDto.applyPerson}"/>
                        <input name="applyPerson" type="hidden" placeholder="请选择适用人群"
                               value='${courseDto.applyPerson}'/>
                    </div>
                </div>
                <div class="col-md-12">
                    <label class="col-md-1 control-label">课程试看时间</label>
                    <div class="col-md-3 ">
                        <input name="trialTime" type="number" placeholder="请输入课程试看时间" value='${courseDto.trialTime}'/>&nbsp;&nbsp;&nbsp;秒
                    </div>
<%--                    <label class="col-md-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>课程开放范围</label>--%>
<%--                    <div class="col-md-3">--%>

<%--                        <input id="usefulness" type="text" json-data='${usefulnessList}' class="t-select isRequired"--%>
<%--                               selected-ids="${courseDto.usefulness}" placeholder="请选择课程开放范围"/>--%>
<%--                    </div>--%>
                    <label class="col-md-1 control-label">监管课程</label>
                    <div class="col-md-3">
                        <input id="superviseType" type="text" json-data='${superviseTypeList}' class="t-select isRequired" selected-ids="${courseDto.superviseType}" placeholder="请选择监管课程"/>
                    </div>
                </div>
                <div class="col-md-12">
                    <label class="col-xs-1 control-label">
                        <span style="color: #FF0000;font-weight: bold;">*</span>课程图片
                        <div style="font-size: 12px;color: red;">（请上传16:9的图片）</div>
                    </label>
                    <div class="col-xs-6" style="margin: 10px 0">
                        <c:choose>
                            <c:when test="${courseDto.livePicImg == '' || courseDto.livePicImg==null}">
                                <div id="livePicDiv" class="col-xs-12 no-padding" style="display: none;">
                                    <img id="livePicImgSrc" src="${courseDto.livePicImg}" style="width: 320px;height: 180px;"/>
                                    <input type="hidden" name="livePicFileId" id="livePicFileId"/>
                                    <form:hidden path="livePicImg"/>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div id="livePicDiv" class="col-xs-12 no-padding" style="display: block;">
                                    <img id="livePicImgSrc" src="${courseDto.livePicImg}" style="width: 320px;height: 180px;"/>
                                    <input type="hidden" name="livePicFileId" id="livePicFileId"/>
                                    <form:hidden path="livePicImg"/>
                                </div>
                            </c:otherwise>
                        </c:choose>
                        <div class="col-xs-12 no-padding controls live-pic-btn-row">
                            <a href="javascript:void(0);" id="livePicUploadBtn"
                               class="file btn btn-warning btn-facebook btn-outline">
                                <i class="fa fa-upload"> </i> 上传图片
                                <input id="livePicFile" type="file" name="files"/>
                            </a>
                            <input type="button" id="livePicRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <label class="col-xs-1 control-label">选择标签权限</label>
                    <div class="col-xs-10">
                        <c:forEach items="${personLabelList}" var="item" varStatus="status">
                            <input type="checkbox" name="personLabel" value="${item.codeValue}"
                            <c:if test="${courseDto.personLabel.indexOf(item.codeValue)!=-1}">
                                   checked="checked"
                            </c:if>>${item.codeName}
                        </c:forEach>
                    </div>
                </div>
                <div class="col-md-12">
                    <label class="col-xs-1 control-label">无权限话术</label>
                    <div class="col-xs-10">
                        <div class="col-xs-12 no-padding">
                            <form:input maxlength="256" cssClass="form-control" path="speechArt" id="speechArt" rows="3"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-12" >
                    <label class="col-xs-1 control-label">原价</label>
                    <div class="col-xs-10">
                        <div class="col-xs-12 no-padding">
                            <form:input type="number" cssClass="form-control" placeholder="只能输入数字" path="originalPrice" id="originalPrice" rows="3"/>
                        </div>
                    </div>
                </div>
<%--                <div class="row">--%>
                    <div class="col-md-12" style="margin: 10px 0">
                        <label class="col-xs-1 control-label">
                            收费设置
<%--                            <i class="fa fa-angle-double-down btn-process-toggle"></i>--%>
<%--                            <i class="fa btn-process-toggle fa-angle-double-up"></i>--%>
                        </label>
                        <div class="col-xs-6">
                            <input id="btnSelectCost" type="button" style="margin-right: 15px;" class="btn" value="选择用户类型"/>
                        </div>
                    </div>
                <div class="col-md-12">
                    <label class="col-xs-1 control-label"><span style="color: #FF0000;font-weight: bold;">*</span>课程简介</label>
                    <div class="col-xs-10">
                        <div class="col-xs-12 no-padding">
                            <form:textarea cssClass="form-control" path="introduce" id="introduce" rows="3"/>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;" hidden>
                    <div class="col-md-12">
                        <label class="col-xs-1 control-label">课程大纲</label>
                        <div class="col-xs-10">
                            <div class="col-xs-12 no-padding">
                                <script id="editorPicture" type="text/plain" >${courseDto.picture}</script>
                            </div>
                        </div>
                    </div>
                </div>
                <form:hidden path="id" id="id"/>
                <form:hidden path="releaseFlag"/>
                <form:hidden path="personTypes"/>
            </div>
        <div class="panel-heading tree-div">
            <div class="row" style="padding-top: 5px;padding-bottom: 10px">
                <div class="col-md-12">
                    <div class="col-md-6 text-left">
                        <div style="font-size: 20px;font-weight: bold">关联设置</div>
                    </div>
                    <div class="col-md-6 text-right">
                    </div>
                </div>
            </div>
            <div class="row tree-div">

                <div class="col-md-4">
                    <div class="panel">
                        <div class="panel-heading">
                            <i class="icon icon-list">&nbsp;章节维护</i>
                        </div>
                        <div class="row" style="padding: 10px 0 0 0;">
                            <div class="col-md-12">
                                <div class="col-md-3">
                                    <input type="button" id="nodeCreate" class="btn btn-success" value="创建"/>
                                    <%--<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>--%>
                                </div>
                                <div class="col-md-3">
                                    <input type="button" id="refresh" class="btn" value="撤销修改"/>
                                    <%--<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>--%>
                                </div>
                                <div class="col-md-3" style="display: none;">
                                    <input type="button" id="getConsole" class="btn" value="Get"/>
                                    <%--<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>--%>
                                </div>
                                <div class="col-md-3">
                                    <input type="button" id="nodeSave" class="btn btn-primary" value="保存修改"/>
                                    <%--<a onclick="nodeCreate()"><img src="../static/images/plus.png"/></a>--%>
                                </div>
                            </div>
                        </div>
                        <div class="panel-body expandArea violateType">
                            <ul id="tree" class="ztree"></ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="panel">
                        <div class="panel-heading">
                            <i class="icon icon-list-alt"><label>&nbsp;视频关联</label></i>
                        </div>
                        <div class="panel-body">
                            <div class="row form-group">
                                <div class="col-md-mine"><label><strong>章节类型</strong></label></div>
                                <div class="col-md-4"><label id="iconSkin"></label></div>
                            </div>
                            <div class="row form-group">
                                <div class="col-md-mine"><label><strong>章节名称</strong></label></div>
                                <div class="col-md-4"><label id="itemName"></label></div>
                            </div>
                            <div class="row form-group" id="show">
                                <div class="col-md-mine"><label><strong>视频名称</strong></label></div>
                                <div class="col-md-4"><label id="videoName"></label></div>
                            </div>
                            <div class="row form-group" id="showT">
                                <div class="col-md-mine"><label><strong>视频时长</strong></label></div>
                                <div class="col-md-4"><label id="videoDuration"></label></div>
                            </div>
                            <div class="row form-group" id="showP">
                                <div class="col-md-mine"><label><strong>学时</strong></label></div>
                                <div class="col-md-4"><label id="period"></label></div>
                                <i id="edit" class="fa fa-edit" style="cursor: pointer;margin: 0 5px;" title="修改" onclick="editCoursePeriod(this)"></i>
                            </div>
                            <div class="row">
                                <div class="col-md-6 col-md-offset-3">
                                    <input type="button" class="btn btn-primary" id="selectBtn"
                                           style="margin-right: 20px" value="关联视频"/>
                                    <input type="button" class="btn" id="cancelBtn" style="margin-right: 20px"
                                           value="取消关联"/>
                                </div>
                            </div>
                            <div id="treeValue" style="display:none">${treeValue}</div>
                            <div id="dataValue" style="display:none">${dataValue}</div>
                            <div id="capitalValue" style="display:none">${capitalValue}</div>
                            <div id="capitalIds" style="display:none">${capitalIds}</div>
                            <input id="nodeId" type="hidden" value="">
                        </div>
                    </div>
                </div>
                    <div class="col-md-12">
                        <label class="col-xs-1 control-label">
                            <span style="color: #FF0000;font-weight: bold;">*</span>课程播放封面
                            <div style="font-size: 12px;color: red;">（请上传16:9的图片）</div>
                        </label>
                        <div class="col-xs-6" style="margin: 10px 0">
                            <c:choose>
                                <c:when test="${courseDto.backImg == '' || courseDto.backImg==null}">
                                    <div id="backImgDiv" class="col-xs-12 no-padding" style="display: none;">
                                        <img id="backImgSrc" src="${courseDto.backImg}" style="width: 320px;height: 180px;"/>
                                        <input type="hidden" name="backImgFileId" id="backImgFileId"/>
                                        <form:hidden path="backImg"/>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div id="backImgDiv" class="col-xs-12 no-padding" style="display: block;">
                                        <img id="backImgSrc" src="${courseDto.backImg}" style="width: 320px;height: 180px;"/>
                                        <input type="hidden" name="backImgFileId" id="backImgFileId"/>
                                        <form:hidden path="backImg"/>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                            <div class="col-xs-12 no-padding controls live-pic-btn-row">
                                <a href="javascript:void(0);" id="backImgUploadBtn" class="file btn btn-warning btn-facebook btn-outline">
                                    <i class="fa fa-upload"> </i> 上传图片
                                    <input id="backImgFile" type="file" name="files"/>
                                </a>
                                <input type="button" id="backImgRemoveBtn" class="btn btn-danger identity-limit" value="删除"/>
                            </div>
                        </div>
                    </div>
                <div class="col-md-12">
                    <label class="col-xs-1 control-label">资料权限话术</label>
                    <div class="col-xs-10">
                        <div class="col-xs-12 no-padding">
                            <form:input maxlength="256" cssClass="form-control" path="dataSpeechArt" id="dataSpeechArt" rows="3"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </form:form>

        <div class="panel-heading tree-div">
            <div class="row" style="padding-top: 5px;padding-bottom: 10px">
                <div class="col-md-12">
                    <div class="col-md-6 text-left">
                        <div style="font-size: 20px;font-weight: bold">课程附件</div>
                    </div>
                    <div class="col-md-6 text-right">
                    </div>
                </div>
            </div>
            <%--课程资料--%>
            <div id="dataDiv" class="row" style="margin-top: 10px;margin-bottom: 20px;" >
                <div class="col-md-12">
                    <a href="javascript:void(0);" id="dataUploadBtn" class="col-md-1 btn btn-primary identity-limit">
                    选择
                    <input id="dataFile" type="file" name="files" multiple value="课程资料"/>
                    </a>
                </div>
                <div class="col-md-12">
                    <div class="col-md-6">
                        <table id="courseData" class="table table-bordered no-margin"
                               style="text-align: center;border-color: #D7D7D7;margin-top: 10px !important;">
                            <thead>
                            <th class="text-center table-th" width="10%">序号</th>
                            <th class="text-center table-th">资料名称</th>
                            <th class="text-center table-th" width="10%">开放</th>
                            <th class="text-center table-th" width="10%">操作</th>
                            </thead>
                            <tbody id="courseDataBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-heading tree-div">
            <div class="row" style="padding-top: 5px;padding-bottom: 10px">
                <div class="col-md-12">
                    <div class="col-md-6 text-left">
                        <div style="font-size: 20px;font-weight: bold">课程基本信息</div>
                    </div>
                    <div class="col-md-6 text-right">
                    </div>
                </div>
            </div>
            <%--课程资料--%>
            <div id="dataDiv" class="row" style="margin-top: 10px;margin-bottom: 20px;" >
                <div class="col-md-12">
                    <label class="col-md-1 btn btn-primary identity-limit" onclick="relationLive()">
                        关联直播
                    </label>
                </div>
            </div>
        </div>
        <div class="row" style="padding-top: 5px;padding-bottom: 10px">
            <div class="col-md-12">
                <div class="col-md-6 text-right">
                    <span id="btnReleaseCopy" class="identity-limit btn btn-primary"
                            <c:if test="${releaseFlag == '0' || id==''}">
                                style="display: none"
                            </c:if>
                    >发布</span>
                    <span id="btnCancelCopy" class="identity-limit btn btn-primary"
                            <c:if test="${releaseFlag == '1' || id==''}">
                                style="display: none"
                            </c:if>
                    >取消发布</span>
                    <span id="btnSaveCopy" class="identity-limit btn btn-primary"
                    >保存基本信息</span>
                </div>
            </div>
        </div>
    </div>
    <%--课程附件模版--%>
    <script id="dataTmpl" type="text/x-jsrender">
    <tr>
        <td class="courseDataBodyTd">{{:#getIndex()+1}}</td>
        <td><a onclick="downloadFile('{{:attachmentId}}')" style="cursor: pointer;">{{:attachmentName}}</a></td>
        <td><input id="openFile" type="checkbox" {{if openFlag === '1'}} checked {{/if}}   onclick="checkbox('{{:attachmentId}}',this)"/></td>
        <td class="deleteFile"><a onclick="delData('{{:attachmentId}}',this)" style="cursor: pointer;">删除</a></td>
    </tr>
</script>
</div>
</body>
</html>
