//@ sourceURL=courseTypeInit.js
//树初始化设置
var setting = {
    view : {
        showIcon: false
    },
    callback : {
        onClick : letterTypeOnClick,
    },
    data : {
        simpleData : {
            enable : true,
            idKey : "itemNo",
            pIdKey : "parentItemNo",
            rootPId : 0
        },
        key : {
            name : "itemName"
        }
    }
};

var map = {};
//当前选择节点
var selectNode;
//按钮提交方式(1:添加同级节点；2：添加下级节点；3：修改节点)
var modifyMethod;
//当前节点路径
var curPath;
var maxSort;
var showFlag="1";
$(document).ready(function(){
    // $("#adjustBtn").hide();
    $("#forbiddenBtn1").hide();
    selectNode = undefined;
    modifyMethod = undefined;
    var zNodes = $.parseJSON($("#schoolCourseNodeTreeValue").text());
    map["treeObj"]=$.fn.zTree.init($("#schoolCourseNodeTree"), setting, zNodes);
    var treeObj=map["treeObj"];
    var fNodes = treeObj.getNodesByParamFuzzy("status", "0");
    treeObj.hideNodes(fNodes);
    // 设定表单校验规则
    $("#ipoClassForm").validate({
        rules : {
            "itemName" : {
                required : true,
                maxlength : 128
            },
            "courseTypeBrief" : {
                maxlength : 256
            }
        }
    });

    //需求2492 2017/09/19 by liuhuan START
    $("#modifyBtn").show();
    $("#modifyBtn1").show();
    $("#modifyBtn2").show();
    $("#modifyBtn3").show();
    $("#modifyBtn4").show();
    $("#modifyBtn5").show();
    //需求2492 2017/09/19 by liuhuan END

    if ($("#saveBtn").val() != undefined) {
        //禁用按钮
        $("#forbiddenBtn").bind("click",function(){
            var param = {
                id: selectNode.id,
                itemName: selectNode.itemName,
                status: selectNode.status,
                itemNo: selectNode.itemNo,
                customNo: selectNode.customItemNo,
                level: selectNode.level,
            }
            ajaxData("/courseType/courseTypeForbidden", param, saveIpoClassCallback);
            //当选择禁用按钮时 移除选中颜色
            $('.disableButton').find('input[type=button]').each(function(){
                $(this).attr('disabled','true');
                $(this).removeClass('btn-primary');
            })

            $(".btn.modifyBtn").attr(
                "disabled", true);
            // $("#adjustBtn").attr("disabled",
            //     true);
            $("#saveBtn")
                .attr("disabled", true);
            $("#forbiddenBtn1").show();
            $("#forbiddenBtn").hide();
            $("#show").hide();
        });
        //解禁按钮
        $("#forbiddenBtn1").bind("click",function(){
            var param = {
                id: selectNode.id,
                itemName: selectNode.itemName,
                status: selectNode.status,
                itemNo:selectNode.itemNo,
                customNo: selectNode.customItemNo,
                level: selectNode.level,
            }
            ajaxData("/courseType/courseTypeForbidden", param, saveIpoClassCallback);
            // $("#adjustBtn").toggleClass("btn-primary");
            $(".btn.modifyBtn").attr(
                "disabled", false);
            $("#saveBtn").attr("disabled",
                false);
            // $("#adjustBtn").attr("disabled",
            //     false);
            $("#forbiddenBtn").show();
            $("#forbiddenBtn1").hide();
        });
        //保存按钮
        $("#saveBtn").bind("click",function(){
            if ($("#ipoClassForm").valid()) {
                var parentId;
                var level;
                var id;
                var sort;
                var treeObj;
                var customNo='';
                treeObj = $.fn.zTree.getZTreeObj("schoolCourseNodeTree");
                //没有节点
                if (treeObj.getNodes().length == 0) {
                    level = 0;
                }
                if (selectNode == undefined && treeObj.getNodes().length != 0) {
                    popAlert("请选择分类");
                    return;
                } else {
                    //添加主分类
                    if (modifyMethod == 0) {
                        level = 0;
                        sort = selectNode.itemSort;
                        customNo=selectNode.customItemNo;
                        //修改节点
                    } else if (modifyMethod == 1) {
                        curPath = selectNode.itemTree;
                        parentId = selectNode.itemNo;
                        level = selectNode.level + 1;
                        sort = selectNode.itemSort;
                        customNo=selectNode.customItemNo;
                        if(level>2){
                            popAlert("无法继续添加下级节点");
                            return;
                        }
                        //修改节点
                    }else if (modifyMethod == 2) {
                        id = selectNode.id;
                        sort = selectNode.itemSort;
                        customNo=selectNode.customItemNo;
                        level = selectNode.level;
                    } else if (modifyMethod == 3 || modifyMethod == 4) {
                        id = selectNode.id;
                        sort = selectNode.itemSort;
                        level = selectNode.level;
                        parentId = selectNode.parentItemNo;

                        getmaxSort(parentId, level);
                        // if (modifyMethod == 4 && sort==0){
                        //     popAlert("无法移动节点")
                        //     return;
                        // }else if (modifyMethod == 3 && sort==1){
                        //     popAlert("无法移动节点")
                        //     return;
                        // }else
                        if(modifyMethod == 3 && sort==0){
                            popAlert("无法继续上移")
                            return;
                        } else if (modifyMethod == 4 && sort>=maxSort) {
                            popAlert("无法继续下移")
                            return;
                        }
                    }else {
                        if (treeObj.getNodes().length != 0) {
                            popAlert("请选择保存模式")
                            return;
                        }
                    }

                }
                var param = {
                    parentItemNo: parentId,
                    level: level,
                    curPath: curPath,
                    id: id,
                    itemSort: sort,
                    itemName: $("#itemName").val(),
                    modifyMode: modifyMethod,
                    status:selectNode.status,
                    customNo:customNo,
                    // courseTypePic : $("#fileId").val(),
                    courseTypeBrief : $("#courseTypeBrief").val(),
                }
                ajaxData("/courseType/courseTreeSave", param, saveIpoClassCallback);
            }
        })
    }
    //上传分类图片初始化
    uploadPng();
});




/**
 * 新增修改分类函数
 */
function changeFunction(changeFlag,obj) {
    //设置修改模式
    modifyMethod = changeFlag;
    $(".modifyBtn").removeClass("btn-primary");
    $(obj).addClass("btn-primary");

    if(changeFlag == "3" || changeFlag == "4"){
        $("#show").hide();
        // $("#adjustBtn").hide();
        $("#saveBtn").show();
    }else if (changeFlag == "5") {
        // $("#adjustBtn").show();
        $("#saveBtn").hide();
        $("#show").hide();
    } else if(changeFlag == "2"){
        $("#saveBtn").show();
        $("#show").show();
    } else {
        // $("#adjustBtn").hide();
        $("#saveBtn").show();
        $("#show").show();
        $(".itemNameClass").val("");
        $(".courseTypeBriefClass").val("");
    }
}
/**
 * 单击函件分类
 */
function letterTypeOnClick(event, treeId, zNodes) {
    debugger
    $('#tbody_add').empty();
    $("#currentClass").text(zNodes.itemName);
    $("#currentClassId").text(zNodes.id);
    // $("#ImgPr").attr('src',zNodes.courseTypePic)
    // $("#fileId").val(zNodes.courseTypePic)
    $("#itemName").val(zNodes.itemName)
    $("#courseTypeBrief").val(zNodes.courseTypeBrief)
    selectNode = zNodes;
    var tempNode = zNodes;
    if(zNodes.status == "0"){
        $("#saveBtn").attr("disabled",true);
        // $("#adjustBtn").attr("disabled",true);
        $(".btn.modifyBtn").attr("disabled",true);
        // $("#modifyBtn2").removeAttr("disabled");
        $("#forbiddenBtn").hide();
        $("#forbiddenBtn1").show();
        $("#imgId").css('display','none')
        $("#fileId").val("");
    }else{
        $("#saveBtn").removeAttr("disabled");
        // $("#adjustBtn").removeAttr("disabled");
        $(".btn.modifyBtn").removeAttr("disabled");
        $("#forbiddenBtn").show();
        $("#forbiddenBtn1").hide();
        // if (zNodes.courseTypePic != null && zNodes.courseTypePic != ''){
        //     $("#imgId").css('display','block')
        // }else {
        //     $("#imgId").css('display','none')
        //     $("#fileId").val("");
        // }


    }
    curPath = "";
    //获取当前节点路径
    while(tempNode.getParentNode()){
        tempNode = tempNode.getParentNode();
        curPath = tempNode.itemNo + "-" + curPath;
    }
}

/**
 * 保存函件问题分类回调
 */
function saveIpoClassCallback(data) {
    //$("#itemName").val("");
    popMsg("保存成功");
    if(data){
        curPath ="";
        $.fn.zTree.init($("#schoolCourseNodeTree"), setting, data);
        if(selectNode == undefined){
            return;
        }else{
            var treeObj;
            treeObj= $.fn.zTree.getZTreeObj("schoolCourseNodeTree");
            selectNode =  treeObj.getNodeByParam("id", selectNode.id, null);
            var tempNode;
            if(modifyMethod !=0) {
                tempNode = selectNode;
            }else{
                if(selectNode.level == 0){
                    tempNode = selectNode;
                }else{
                    tempNode = selectNode.getParentNode();
                }
            }
            if(tempNode){
                treeObj.selectNode(tempNode);
                $("#currentClass").text(tempNode.itemName);
                $("#currentClassId").text(tempNode.id);
                $("#itemNameClass").val("");
                treeObj.expandNode(tempNode, true, false, true);
                selectNode =tempNode;
            }
        }
        statusChange();
        $(".modifyBtn").removeClass("btn-primary");
        modifyMethod = undefined

    }
}


//获取关联案例回调
function callBackSelectItemCategoryTree(data) {
    $.fn.zTree.init($("#schoolCourseNodeTree"), setting, data);
}


// // demand 5769 全部展开
// function expandAll(){
//     $(".expandArea").show();
//     var icons = $("i[class*='violateIcon']");
//     $.each(icons, function(i,icon){
//         $(icon).attr("class","icon icon-angle-down violateIcon");
//     });
// }
// // demand 5769 全部收起
// function closeAll(){
//     $(".expandArea").hide();
//     var icons = $("i[class*='violateIcon']");
//     $.each(icons, function(i,icon){
//         $(icon).attr("class","icon icon-angle-right violateIcon");
//     });
// }
// // demand 5769 单个展开收起
// function showOrHide(type, event){
//     if($(event).hasClass("icon-angle-right")){
//         $("." + type).show();
//         $(event).removeClass("icon-angle-right").addClass("icon-angle-down");
//     } else {
//         $("." + type).hide();
//         $(event).removeClass("icon-angle-down").addClass("icon-angle-right");
//     }
// }
//

function getmaxSort(parentId, level) {
    var zparam = {
        parentId : parentId ,
        level : level ,
    }
    ajaxData("/courseType/getItemCategoryClassMaxSort" , zparam, function(data) {
        maxSort = data;
    })
}

function statusFun(obj) {
    var treeObj = map['treeObj'];
    var nodes = treeObj.transformToArray(treeObj.getNodes());
    var HidNodes = treeObj.getNodesByParamFuzzy("status", "0");
    if(obj.checked==true){
        treeObj.showNodes(nodes);
        //treeObj.showNodes(HidNodes);
    }else {
        treeObj.hideNodes(HidNodes);
    }
}

function statusChange() {
    var treeObj = map['treeObj'];
    var nodes = treeObj.transformToArray(treeObj.getNodes());
    var HidNodes = treeObj.getNodesByParamFuzzy("status", "0");
    if($("#statusForbidden").prop("checked")){
        treeObj.showNodes(nodes);
    }else {
        treeObj.hideNodes(HidNodes);
    }

}
//添加图片
function uploadPng() {
    var url = contextPath + '/filetempupload';
    $("#uploadPng").fileupload({
        url : url,
        dataType : 'json',
        autoUpload : true,
        submit : function(e, data) {
            index = layer.load(1, {
                shade : [ 0.1, '#fff' ]
                // 0.1透明度的白色背景
            });

        },
        done : function(e, data) {
            $.each(data.result, function(index, file) {
                $("#fileId").val(file.filePath);
                $("#ImgPr").show();
            });
            layer.close(index);
        }
    })
    if(getValue($('#fileId').val())  == ""){
        document.getElementById("imgId").style.display = "none";
    }
    else{
        document.getElementById("imgId").style.display = "block";
    }
    if(document.getElementById("uploadPng")){
        $("#uploadPng").uploadPreview({
            Img : "ImgPr",
            Width : 50,
            Height : 50
        });

    }
}
//删除图片
function clearImg(obi, fieldId) {
    popConfirm("确认删除图片", function() {
        $("#fileId").val("");
        $("#imgId").css("display", "none");
    });
}
$.fn
    .extend({
        uploadPreview : function(opts) {
            var _self = this, _this = $(this);
            opts = jQuery.extend({
                Img : "ImgPr",
                Width : 100,
                Height : 100,
                ImgType : [ "gif", "jpeg", "jpg", "bmp", "png" ],
                Callback : function() {
                }
            }, opts || {});
            _self.getObjectURL = function(file) {
                var url = null;
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file)
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file)
                }

                $("#imgId").css("display", "block");
                return url
            };

            _this.change(function() {
                if (this.value) {
                    if (!RegExp(
                        "\.(" + opts.ImgType.join("|") + ")$",
                        "i").test(this.value.toLowerCase())) {
                        alert("选择文件错误,图片类型必须是"
                            + opts.ImgType.join("，") + "中的一种");
                        this.value = "";
                        return false
                    }
                    $.browser = new Object();
                    $.browser.msie = /msie/
                        .test(navigator.userAgent.toLowerCase());
                    debugger
                    if ($.browser.msie) {
                        try {
                            $("#" + opts.Img)
                                .attr(
                                    'src',
                                    _self
                                        .getObjectURL(this.files[0]))
                        } catch (e) {
                            var src = "";
                            var obj = $("#" + opts.Img);
                            var div = obj.parent("div")[0];
                            _self.select();
                            if (top != self) {
                                window.parent.document.body.focus()
                            } else {
                                _self.blur()
                            }
                            debugger
                            src = document.selection.createRange().text;
                            document.selection.empty();
                            obj.hide();
                            obj
                                .parent("div")
                                .css(
                                    {
                                        'filter' : 'progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)',
                                        'width' : opts.Width
                                            + 'px',
                                        'height' : opts.Height
                                            + 'px'
                                    });
                            div.filters
                                .item("DXImageTransform.Microsoft.AlphaImageLoader").src = src
                        }
                    } else {
                        debugger
                        $("#" + opts.Img).attr('src',
                            _self.getObjectURL(this.files[0]))
                    }
                    opts.Callback()
                }
            })
        }
    })




