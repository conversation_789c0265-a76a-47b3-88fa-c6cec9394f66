$(document).ready(function () {
    //查询
    $("#btnQuery").bind("click", function () {
        search();
    });
    //清空
    $("#btnClear").bind("click", function () {
        clear();
    });
    tSelectInit();
})
//下拉初始化
function tSelectInit() {

    var teaSelectOptions = {
        id: 'teachId',
        name: 'teachName',
        value: 'teachId',
        grade: 1,
        resultType: 'all',
        inputSearch: true,
        style: {},
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#teacher').tselectInit(null, teaSelectOptions);

    var courseTypeSelectOptions = {
        id: 'itemNo',
        pid: 'parentItemNo',
        name: 'itemName',
        value: 'id',
        grade: 2,
        resultType: 'children',
        style: {},
        inputSearch: true,
        openDown: false,//是否初始展开tree
        customCallBack: tSelectCustomCallBack,
        submitCallBack: tSelectSubmitCallBack
    };
    $('#courseType').tselectInit(null, courseTypeSelectOptions);

}

//下拉框自定义方法
function tSelectCustomCallBack(t) {
}

//下拉框点击确定回调方法
function tSelectSubmitCallBack(t, d) {
    $('input[name="' + t.attr('id') + '"]').val(d.value);
}
//清空
function clear() {
    document.getElementById("queryForm").reset();
    var param = {}
    ajaxTableQuery("tableSort", "/courseRanking/getSortList", param);
}

//查询
function search() {
    var param = {
        status: $("#status").val(),
        releaseFlag: $("#releaseFlag").val(),
    }
    ajaxTableQuery("tableSort", "/courseRanking/getSortList", param);
}

//查询课程信息
function sortInfoList() {
    ajaxTableQuery("tableSort", "/courseRanking/getSortList");
}

//编辑
function modelOperation(data, type, row, meta) {
    var sortColumn = data.sort;
    var str = '';

    var moveSortAuth = '';
    var editSortAuth = '';

    if (document.getElementById("moveSortAuth")) {
        moveSortAuth = true
    }

    if (document.getElementById("editSortAuth")) {
        editSortAuth = true
    }

    if (moveSortAuth) {
        str = '<i class="icon-arrow-up" style="margin: 0 5px;" title="上移" onclick="moveUp(this,\'' + data.id + '\',\'' + data.sort + '\',\'up\')"></i>'
            + '<i class="icon-arrow-down" style="margin: 0 5px;" title="下移" onclick="moveDown(this,\'' + data.id + '\',\'' + data.sort + '\',\'down\')"></i>'
    }

    if (editSortAuth) {
        str += '<i class="fa fa-exchange iconStyle" style="margin: 0 5px;" title="顺序调整" onclick="quickMoveInfo(\'' + data.id + '\',\'' + sortColumn + '\',\'' + data.sort + '\')"></i>';
    }

    if (!str) {
        str = '-';
    }

    return str;
}

//序号
function sortColumn(data, type, row, meta) {
    return data.sort;
}

//向上顺序调整
function moveUp(obj, id, sort, moveType) {
    var tr = $(obj).parents("tr");
    var trPrev = tr.prev().find('td').html();
    if (moveType == 'up') {
        if (tr.index() == 0) {
            parent.popMsg("无法上移");
            return
        }
        var param = {
            id: id,
            sort: sort,
            exchangeSort: trPrev,
        }
        ajaxData('/courseRanking/saveSort', param, function (data) {
            if (data == false) {
                popMsg("不可执行此操作！");
            } else {
                tableReload(false);
            }
        });
    }
}

//向下顺序调整
function moveDown(obj, id, sort, moveType) {
    var tr = $(obj).parents("tr");
    var trNext = tr.next().find('td').html();
    var trLength = $('#tableSort tbody').children('tr').length;
    if (moveType == 'down') {
        if (tr.index() == trLength - 1) {
            parent.popMsg("无法下移");
            return;
        }
        var param = {
            id: id,
            sort: sort,
            exchangeSort: trNext,
        }
        ajaxData('/courseRanking/saveSort', param, function (data) {
            if (data == false) {
                popMsg("不可执行此操作！");
            } else {
                tableReload(false);
            }
        });
    }
}

//查询
function search() {
    ajaxTableQuery("tableSort", "/courseRanking/getSortList", $("#queryForm").formSerialize());
}

//指定顺序调整
function quickMoveInfo(id, sortColumn, sort) {
    
    var trLength = $('#tableSort tbody').children('tr').length;
    if (trLength == 1) {
        parent.popMsg("无法调整");
        return
    }
    var param = {
        id: id,
        sortColumn: sortColumn,
        sort: sort,
        status: $("#status").val(),
        releaseFlag: $("#releaseFlag").val(),
    }
    parent.popWin("顺序调整", "/courseRanking/editSortPage", param, "600px", "200px",
        search);
}

//重新加载table
function tableReload(resetPaging) {
    ajaxTableReload('tableSort', resetPaging);
}