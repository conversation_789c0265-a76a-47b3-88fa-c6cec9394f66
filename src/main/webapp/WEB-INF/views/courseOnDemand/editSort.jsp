<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec' %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="/edm-tags" prefix="e" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>易董 云端管理平台</title>
    <e:base/>
    <e:js/>
    <script type="text/javascript">
    </script>
    <style>
    </style>
</head>
<body style="background-color: #fff">
<div class="panel-body">
    <input type="hidden" id="id" value="${id}">
    <input type="hidden" id="maxSort" value="${maxSort}">
    <input type="hidden" id="sort" value="${sort}">
    <div class="row" style="display: flex;text-align: center">
        <div style="width: 200px;padding-top: 6px;"><span>当前序号为</span>
            <span id="sortColumn">${sortColumn}</span>
            <span>调整至</span></div>
        <div style="width: 200px">
            <input id="sortEnd" type="number" min="1" class="form-control"
                   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">
        </div>
    </div>
    <div class="row" style="text-align: center">
        <button type="button" class="btn btn-primary" style="margin-top:20px" id="btnSave">保存</button>
        <button type="button" class="btn btn-default" style="margin-top:20px" id="btnClose">关闭</button>
    </div>
</div>
</body>
</html>