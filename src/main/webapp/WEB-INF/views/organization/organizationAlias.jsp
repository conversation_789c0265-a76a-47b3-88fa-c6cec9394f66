<%--
/***************************************************************
* 程序名 : organizationAlias.jsp
* 日期  :  
* 作者  :  
* 模块  :  机构别名管理
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<e:js />
</head>
<body>
    <div class="panel">
        <div class="panel-heading">
            <i class="icon icon-search"></i>机构别名管理 
        </div>
        <div class="panel-body">
        <input type="hidden" id = "writeAuth" value="${writeAuth}">
            <input type="hidden" id = "readOnlyAuth" value="${readOnlyAuth}">
            <form:form id="organizationAliasForm" modelAttribute="organizationAliasDto">
                <div class="row">
                    <div class="col-md-2">
                        <form:input cssClass="form-control" path="orgName" placeholder="机构名称关键字" />
                    </div>
                    
                    <div class="col-md-2" align="right">
                        <form:select path="orgType" class="form-control">
                            <form:option value="">机构类型</form:option>
                            <form:option value="1">保荐机构</form:option>
                            <form:option value="2">会计师事务所</form:option>
                            <form:option value="3">律师事务所</form:option>
                        </form:select>
                    </div>
                    <div class="col-md-8" align="right">
                    <sec:authorize access="hasAuthority('RES_ORGANIZATIONALIAS_MANAGE_AUTHORITY_3')" >
                     <input type="button" id="btuAdd" value="新增" class="btn btn-primary" /> 
                     </sec:authorize>
                        <input type="button" id="sortSearch" value="查询" class="btn btn-primary" />   
                            
                    </div>
                </div>
            </form:form>
            <div class="row;margin-left:auto;margin-right:auto;">
                <e:grid id="table_id" action="/organizationAlias/query " cssClass="table table-striped table-hover">
                <e:gridColumn label="序号" renderColumn="renderNumber" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="机构标准名称" displayColumn="orgName" orderable="false" cssClass="text-center" cssStyle="width:50%" />
                    <e:gridColumn label="机构类型" renderColumn="renderType" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="别名数量" displayColumn="aliasNumber" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="更新日期" displayColumn="updateTime" orderable="false" cssClass="text-center" cssStyle="width:10%" />
                    <e:gridColumn label="操作" renderColumn="renderColumnOparate" orderable="false" cssStyle="width:10%;text-align:center" />
                </e:grid>
            </div>
        </div>
    </div>
</body>
</html>