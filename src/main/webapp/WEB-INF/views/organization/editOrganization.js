//@ sourceURL= editOrganization.js
var row="";
var sort=0;
var contentEditorNum=0;
var deleteUrlData = [];
$(document).ready(function() {
	addProcess();
	save();
	saveAndClose();
});

function addProcess() {
	var obj = null;
	if(data != ''){
		obj = $.parseJSON(data);
	}
		
	if (obj != null && obj.length > 0) {
				for (var i = 0; i < obj.length; i++) {
					
					var id = obj[i].id;
					var aliasName = obj[i].aliasName;
					row = "";
					var t = $("#addTable");
							row = row + '<div>'+'<div class="col-md-8">' + '<input value="' + aliasName + '" name="aliasName" class="form-control  aliasName"/>'+'</div>';
							row = row + '<div class="col-md-4"style="margin-top:7px;">' + '<a href="javascript:void(0)" title="删除"><i class="icon icon-trash  deletAttachment"></i></a></div>';
							row = row + "<div style='display: none;'><input type='hidden' name='tid' value='" + id + "'></div></div>";
							$(row).appendTo(t);	
						
						$('.deletAttachment').bind("click", function(obj) {
							var t = $(this);
							t.parent().parent().parent().remove();
						});
				
				}

		
	}
	$('#addCourse').bind("click", function(obj) {
		row = "";
		var t = $("#addTable");
		// 往table里动态添加一行
		row = row + '<div>'+'<div class="col-md-8">' + '<input value="" name="aliasName" class="form-control  aliasName"/>'+'</div>';
		row = row + '<div class="col-md-4"style="margin-top:7px;">' + '<a href="javascript:void(0)" title="删除"><i class="icon icon-trash  deletAttachment"></i></a></div>';
		row = row + "<div style='display: none;'><input type='hidden' name='tid'></div></div>";
		$(row).appendTo(t);

		$('.deletAttachment').bind("click", function(obj) {
			var t = $(this);
			t.parent().parent().parent().remove();
		});

	
	});
	
	
}

// 保存按钮
function save(){
$("#btnSave").bind("click",function() {
	if ($("#organizationAliasForm").valid()) {
//		ajaxSubmitForm("/lawsManage/saveLaw","",callBack,0);
		submitForm("/organizationAlias/save");
		popMsg("保存成功");
	}
});
}
//保存并关闭
function saveAndClose(){
	$("#btnSaveAndClose").bind("click",function() {
		if ($("#organizationAliasForm").valid()) {
			ajaxSubmitForm("/organizationAlias/saveAndeClose","",callBackT,0);
		}
	});
	}
function callBackT(data){
	popMsg("保存成功");
	setTimeout("closeWinCallBack()",2000);
}
