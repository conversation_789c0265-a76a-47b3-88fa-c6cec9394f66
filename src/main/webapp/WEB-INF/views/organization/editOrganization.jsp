<%--
/***************************************************************
* 程序名 :  
* 日期  :  
* 作者  :  
* 模块  :  
* 描述  : 
* 备注  : 
***************************************************************/
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix='sec'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="/edm-tags" prefix="e"%>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>易董 云端管理平台</title>
<style type="text/css">
.red_star {
    color: red;
    font-size: 7px;
}

.td_width1 {
    width: 200px;
}

.td_width2 {
    width: 80px;
}

.col-md-mine1 {
    position: relative;
    min-height: 1px;
    padding-right: 10px;
    padding-left: 10px;
    float: left;
    width: 56%;
}

.col-md-mine2 {
    position: relative;
    min-height: 1px;
    padding-right: 5px;
    padding-left: 5px;
    float: left;
    width: 6%;
}

.col-md-mine3 {
    position: relative;
    min-height: 1px;
    padding-right: 5px;
    padding-left: 5px;
    float: left;
    width: 5%;
}

.col-md-mine4 {
    position: relative;
    min-height: 1px;
    padding-right: 5px;
    padding-left: 5px;
    float: left;
    width: 7%;
    font-size: 14px;
}

.addDiv {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    padding-right: 5px;
    padding-left: 5px;
    width: 90%;
}

.col-xs-mine1{
    float:left;
    position:relative;
    min-height:1px;
    padding-right:10px;
    padding-left:0px;
    width:8.33333333%
}

.dateToggle {
    cursor: pointer;
}
</style>
<e:base />
<e:js />
<script>
var data = '${sList}';
</script>
</head>
<body>
    <div class="panel">
        <div class="panel-body">
            <form:form id="organizationAliasForm" modelAttribute="organizationAliasDto" cssClass="form-horizontal">
                <div class="row" style="padding: 5px;">
                    <label class="col-md-2 control-label"><span class="red_star">*</span>机构标准名称:</label>
                    <div class="col-md-8">
                        <form:input path="orgName" id="violateTitle" cssClass="form-control" readonly="readOnly" />
                    </div>
                </div>

                <div class="row" style="padding: 5px;">
                    <label class="col-md-2 control-label"><span class="red_star">*</span>机构类型:</label>
                    <div class="col-md-2" align="right">
                        <form:select path="orgType" class="form-control">
                            <form:option value="">请选择</form:option>
                            <form:option value="1">保荐机构</form:option>
                            <form:option value="2">会计师事务所</form:option>
                            <form:option value="3">律师事务所</form:option>
                        </form:select>
                    </div>
                </div>

                <div class="row" style="padding: 5px;">
                <label class="col-md-2 control-label">别名:</label>
                    <div class="col-md-1 control-label">
                    <sec:authorize access="hasAuthority('RES_ORGANIZATIONALIAS_MANAGE_AUTHORITY_3')" >
                        <button type="button" class="btn btn-success" id="addCourse">新增别名</button>
                        </sec:authorize>
                    </div>
                </div>

                <div id="demo_list"></div>
                <div id="demo" >
                    <div class="row" style="padding: 5px;">
                      <div class="col-md-12">
                           <div class=" sList" id="addTable"style="margin-left:150px;">
                           
                           </div>
                     </div>
                   </div>
                </div>
                <div class="col-md-offset-12 text-center">
                <sec:authorize access="hasAuthority('RES_ORGANIZATIONALIAS_MANAGE_AUTHORITY_3')" >
                    <button type="button" class="btn btn-primary" id="btnSave">保存</button>
                    <button type="button" class="btn btn-default" id="btnSaveAndClose">保存并关闭</button>
                    </sec:authorize>
                </div>
                <form:hidden path="id" id="id" />
            </form:form>
        </div>
    </div>
</body>

</html>