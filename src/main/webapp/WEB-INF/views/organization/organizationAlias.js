//@ sourceURL= organizationAlias.js
var errorPath = "";
$(document).ready(function() {
    $("#sortSearch").bind("click", function()
    		{
    	if($("#organizationAliasForm").valid())
    	{
    		ajaxTableQuery("table_id", "/organizationAlias/query", $("#organizationAliasForm").formSerialize());
    	}
    });
    btnAdd();
});
    	

function renderColumnOparate(data, type, row, meta) {
	var renderString = '';
	var tempRead ='<span onclick="editText(\''+data.orgName+'\')" style="cursor:pointer"><i class="icon icon-edit"></i></span>';
	var tempWrite = '<span onclick="editText(\''+data.orgName+'\')" style="cursor:pointer"><i class="icon icon-edit"></i></span>&nbsp;<span onclick="delText(\''+data.orgName+'\')" style="cursor:pointer"><i class="icon icon-trash"></i></span>';
	if($("#writeAuth").val().length>0){
	  return tempWrite;
	 }else{
	  return tempRead;
	 }
//	return  '<span onclick="editText(\''+data.orgName+'\')" style="cursor:pointer"><i class="icon icon-edit"></i></span>&nbsp;<span onclick="delText(\''+data.orgName+'\')" style="cursor:pointer"><i class="icon icon-trash"></i></span>';
}
function renderNumber(data, type, row, meta) {
	return meta.row + 1;
}

function editText(orgName){
	//2017.10.16 by zhanghaoyu start
//	var param = "orgName="+orgName;
	var param = {
			orgName : orgName
	}
	//2017.10.16 by zhanghaoyu end
	popWin("新增/编辑机构","/organizationAlias/editOrg", param,"1000px","600px",myWinCallback);
}
function  delText(id)
{
	popConfirm("是否删除？", delectCallBack, id);
}
function delectCallBack(orgName){
	//2017.10.9 by zhanghaoyu start
//	var param = "orgName=" + orgName;
	var param = {
			orgName : orgName
	}
	//2017.10.9 by zhanghaoyu end
	ajaxData("/organizationAlias/delet", param, function(data) {
		//
		myWinCallback1(data);
	});
}
function myWinCallback1(paraWin, paraCallBack) {
	ajaxTableReload("table_id",false);
	//ajaxTableReload("tableInfo",false);
	popMsg("删除成功");
}
function renderType(data, type, row, meta){
	var str =data.orgType;
	if(str=="1"){
		return "保荐机构";
	}else if(str=="2"){
		return "会计师事务所";
	}else if(str=="3"){
		return "律师事务所";
	}
	return '';
}
//新增
function  btnAdd(){
if ($("#btuAdd").val() != undefined) {
$("#btuAdd").bind("click", function(){
	popWin("新增/编辑机构","/organizationAlias/editOrg","","1000px","600px",myWinCallback);
})
}		
}

function myWinCallback()
{
	ajaxTableReload("table_id",false);
} 
    
    
    