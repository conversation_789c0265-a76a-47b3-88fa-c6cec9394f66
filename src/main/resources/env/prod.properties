#------------------------------------log------------------------------------
log.level=INFO
log.appender=stash
log.home=/data/program/capital/log/lcabManage_log
log.stash.address=node01.public.logstash.prod:9999,node02.public.logstash.prod:9999
#-----------------------------------mysql-----------------------------------
jdbc.url=*************************************************************************************************************
jdbc.username=service_lcab
jdbc.password=wb9ydEjm!N
jdbc.initialSize=1
jdbc.maxActive=10
jdbc.minIdle=1
jdbc.maxWait=60000

#-----------------------------------redis-----------------------------------
redis.host=************
redis.port=16379
redis.timeout=3000
redis.password=jzzx@123
redis.pool.maxTotal=200
redis.pool.maxIdle=50
redis.pool.minIdle=10
redis.pool.maxWaitMillis=60000

#----------------------------------session----------------------------------
session.timeout=86400

#-----------------------------------file------------------------------------
#local_base
file.basePath=/data/remote_dir/upload/capco_train/
#local_view
file.viewPath=https://lcab.obs.cn-north-4.myhuaweicloud.com/
#-----------------------------------api-------------------------------------
api.baseUrl=https://services.valueonline.cn/capital-cloud-api/

service.gui.clientId=8a0fc4a26ac28252
service.gui.clientSecret=67a87e078a0fc4a26ac282529642f7f0
service.gui.baseUrl= https://services.valueonline.cn
#-----------------------ES -------------------------
elasticsearch.cluster-nodes=************:9200,************:9200,************:9200
elasticsearch.cluster-name=capital-cluster
elasticsearch.request-timeout=600000

#--------------------------------eb-school------------------------------
capco.qrCodeUrl=https://www.valueonline.cn/laws/qrCode
#-------------------------------- pulsar ------------------------------
pulsar.servers=pulsar://node01.public.pulsar.prod:6650,node02.public.pulsar.prod:6650,node03.public.pulsar.prod:6650
pulsar.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJwdWxzYXItdXNlciJ9.dCSlOU2r55_Lr8ABbHQgjTONjv7Lq_ZuqpNCa9iJJMg

exam.baseUrl=https://exam.valueonline.com.cn/
capco.baseUrl=https://lcab.valueonline.cn

pushApi.baseUrl=https://services.valueonline.cn/stock-push/
http.rest-template.connect-timeout:30000 

#--------------------huaweicloud--------------------------------------

#----north-1------
huaweicloud.bucketName=lcab-bucket
#----?? ??1------
huaweicloud.location=cn-north-1
#----??id??????API?????????????id------
huaweicloud.projectId=544a6c7640104bf7ac329b835bd3ea06
#----????????------
huaweicloud.vodEndPoint=vod.cn-north-1.myhuaweicloud.com
#----IAM??????,?????????????????(region)------
huaweicloud.iamEndPoint=iam.cn-north-1.myhuaweicloud.com
#----????????------
huaweicloud.obsEndPoint=obs.cn-north-1.myhuaweicloud.com
#----????????????------
huaweicloud.mpsEndPoint=mts.cn-north-1.myhuaweicloud.com
#----AK------
huaweicloud.vodAk=EUD3T68PPED8VI2ZHX2K
#----SK------
huaweicloud.vodSk=qOe8kcPGpJk0ZTIo1baWqQubShQoiyfOZThbp5dE

#----north-4------
huaweicloud.bucketName4=lcab
#----region------
huaweicloud.location4=cn-north-4
#----projectId------
huaweicloud.projectId4=0831de916a00261e2f59c01425c0f547
#----obs------
huaweicloud.obsEndPoint4=obs.cn-north-4.myhuaweicloud.com
#----shi pin zhuan ma------
huaweicloud.mpsEndPoint4=mts.cn-north-4.myhuaweicloud.com
#----AK------
huaweicloud.ak4=NGOHU4HGUVFAWYRHPQV4
#----SK------
huaweicloud.sk4=bA06qMBTTWnKNV5OpASpUv4eHe4N0OhDTqNzPV8N

#-----------zhiyi----------------------
chatDong.baseUrl=https://services.easy-board.com.cn/chatDongWeb/
