error.undefined=\u5f02\u5e38\u4fe1\u606f\u672a\u5b9a\u4e49
error.500=\u670d\u52a1\u5668\u5185\u90e8\u9519\u8bef
error.404=\u627e\u4e0d\u5230\u8bf7\u6c42\u7684\u65b9\u6cd5
error.test=\u6d4b\u8bd5\u5f02\u5e38
error.10001=\u8bf7\u4e0a\u4f20ECXEL\u6587\u4ef6
error.10002=\u8bf7\u4e0a\u4f20\u6b63\u786e\u7684\u6587\u4ef6
error.10003=\u53c2\u6570\u4fe1\u606f\u4e0d\u5168


login.smsContent = \u9a8c\u8bc1\u7801[smsCode]\uff0c\u6709\u6548\u671f10\u5206\u949f\u3002\u60a8\u6b63\u5728\u8fdb\u884c\u4e91\u5e73\u53f0\u767b\u5f55\uff0c\u5982\u975e\u672c\u4eba\u64cd\u4f5c\uff0c\u8bf7\u8054\u7cfb\u6613\u8463\u5ba2\u670d\u3002
security.usernotfound = \u7528\u6237\u540d\u4e0d\u5b58\u5728
security.credentialsExpired = \u9a8c\u8bc1\u7801\u5df2\u8fc7\u671f
AbstractAccessDecisionManager.accessDenied=\u4e0d\u5141\u8bb8\u8bbf\u95ee
AbstractUserDetailsAuthenticationProvider.badCredentials=\u7528\u6237\u540d\u6216\u5bc6\u7801\u4e0d\u6b63\u786e
AbstractUserDetailsAuthenticationProvider.credentialsExpired=\u7528\u6237\u51ed\u8bc1\u5df2\u8fc7\u671f
AbstractUserDetailsAuthenticationProvider.disabled=\u7528\u6237\u5df2\u5931\u6548
AccountStatusUserDetailsChecker.locked=\u7528\u6237\u5e10\u53f7\u5df2\u88ab\u9501\u5b9a
AbstractUserDetailsAuthenticationProvider.locked=\u7528\u6237\u5df2\u9501\u5b9a\uff0c\u8bf7\u8054\u7cfb\u7ba1\u7406\u5458\u89e3\u9501
