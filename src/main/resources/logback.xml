<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %X{X-Request-ID} %logger %M - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.home}/%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %X{X-Request-ID} %logger %M - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${log.stash.address}</destination>
        <keepAliveDuration>5 minutes</keepAliveDuration>
        <reconnectionDelay>1 second</reconnectionDelay>
        <writeBufferSize>16384</writeBufferSize>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <includeCallerData>true</includeCallerData>
            <includeMdcKeyName>X-Request-ID</includeMdcKeyName>
            <customFields>{"type":"jetty_log","program":"newLcabManage"}</customFields>
        </encoder>
    </appender>

    <root level="${log.level}">
        <appender-ref ref="console" />
        <appender-ref ref="${log.appender}" />
    </root>
    <logger name="org.springframework" level="WARN"></logger>
    <!-- <logger name="org.springframework.security" level="DEBUG"></logger> -->
    <logger name="org.dozer" level="WARN"></logger>
    <logger name="org.hibernate" level="WARN"></logger>
    <logger name="org.mybatis" level="WARN"></logger>
    <logger name="org.apache" level="WARN"></logger>
    <logger name="org.apache.poi" level="fatal"></logger>


</configuration>