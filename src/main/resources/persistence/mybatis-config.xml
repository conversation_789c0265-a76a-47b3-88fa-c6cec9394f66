<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
	PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true" />
        <setting name="lazyLoadingEnabled" value="true" />
        <setting name="aggressiveLazyLoading" value="true" />
        <setting name="multipleResultSetsEnabled" value="true" />
        <setting name="useColumnLabel" value="true" />
        <setting name="useGeneratedKeys" value="false" />
        <setting name="autoMappingBehavior" value="PARTIAL" />
        <setting name="defaultExecutorType" value="REUSE" />
        <setting name="defaultStatementTimeout" value="120" />
    </settings>
    <plugins>
        <plugin interceptor="com.stock.core.dao.pager.PagerInterceptor">
            <property name="dialect" value="mariadb" />
        </plugin>
       <!-- <plugin interceptor="com.stock.core.dao.AuditInterceptor" />-->
        <plugin interceptor="com.stock.capital.cloud.common.dao.DatamapAuditInterceptor" />
    </plugins>
</configuration>