<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="
                    http://www.springframework.org/schema/beans
                    http://www.springframework.org/schema/beans/spring-beans.xsd
                    http://www.springframework.org/schema/rabbit
                    http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

    <!-- 连接配置 -->
    <rabbit:connection-factory id="rabbitConnectionFactory" host="#{app['mq.broker.host']}"
                               port="#{app['mq.broker.port']}" username="#{app['mq.broker.username']}"
                               password="#{app['mq.broker.password']}" virtual-host="/" publisher-confirms="true"/>

    <!-- 消息对象json转换类 -->
    <bean id="jsonMessageConverter" class="org.springframework.amqp.support.converter.Jackson2JsonMessageConverter"/>

    <!-- mq模板 -->
    <rabbit:template id="rabbitTemplate" connection-factory="rabbitConnectionFactory" message-converter="jsonMessageConverter"/>

    <bean id="messageClient" class="com.stock.core.message.RabbitMQMessageClient"/>

    <rabbit:admin auto-startup="true" connection-factory="rabbitConnectionFactory"/>



    <rabbit:topic-exchange name="amq.topic" auto-declare="true" auto-delete="false">

    </rabbit:topic-exchange>

    <!-- 队列消费者  -->
    <rabbit:listener-container connection-factory="rabbitConnectionFactory" message-converter="jsonMessageConverter">

    </rabbit:listener-container>



</beans>
