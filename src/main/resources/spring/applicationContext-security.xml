<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/security"
             xmlns:beans="http://www.springframework.org/schema/beans"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
             xmlns:util="http://www.springframework.org/schema/util"
             xmlns:beams="http://www.springframework.org/schema/context"
             xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/security
        http://www.springframework.org/schema/security/spring-security.xsd
        http://www.springframework.org/schema/util
        http://www.springframework.org/schema/util/spring-util.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <!-- 静态资源不作控制 -->
    <http pattern="/" security="none" />
    <http pattern="/static/**" security="none" />
    <http pattern="/resources/**" security="none" />
    <http pattern="/login" security="none" />
    <http pattern="/logout" security="none" />
    <http pattern="/denied" security="none" />
    <http pattern="/invalid" security="none" />
    <http pattern="/forceKickout" security="none" />
    <http pattern="/sample/**" security="none" />
    <http pattern="/lawsManage/lawAllView*" security="none" />
    <http pattern="/addIndex/addLawIndex" security="none" />
    <http pattern="/addIndex/delInteractQaIndex" security="none" />
    <http pattern="/getSmsCode" security="none" />
    <http pattern="/weixinCloudOutLoginSuccess" security="none" />
    <http pattern="weixinCloudCancelLoginSuccess" security="none" />
    <http pattern="/weixinAppletsBatch/receiveMsg" security="none" />
    <http pattern="/weixinAppletsBatch/receiveResult/**" security="none" />
    <http pattern="/weixinAppletsBatch/**" security="none" />
    <http pattern="/portalContentSetting/getNotifyUrlBack" security="none" />
    <http pattern="/announcementQuery/handleManageCatalogue" security="none" />
    <http pattern="/schVideoManage/getKey" security="none" />
    <http pattern="/subscribeReceive/dealVideoSubscribe" security="none" />


    <http auto-config="false" use-expressions="true" create-session="ifRequired" entry-point-ref="authenticationEntryPoint">

        <session-management invalid-session-url="/invalid" session-fixation-protection="migrateSession"
            session-authentication-error-url="/invalid">
        </session-management>

        <access-denied-handler ref="customAccessDeniedHandler" />

        <logout invalidate-session="true" logout-success-url="/login" logout-url="/logout" />

        <anonymous enabled="false" />

        <csrf disabled="true" />

        <headers>
            <frame-options policy="SAMEORIGIN" />
            <hsts include-subdomains="true" max-age-seconds="31536000" />
            <content-type-options />
            <xss-protection block="true" />
        </headers>

		<custom-filter ref="usernamePasswordSmsAuthenticationFilter" position="FORM_LOGIN_FILTER" />
        <custom-filter ref="forceKickoutFilter" before="CONCURRENT_SESSION_FILTER" />
        <custom-filter ref="customExceptionTranslationFilter" before="EXCEPTION_TRANSLATION_FILTER" />
        <custom-filter ref="filterSecurityInterceptor" before="FILTER_SECURITY_INTERCEPTOR" />

    </http>

	 <beans:bean id="authenticationEntryPoint" class="org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint">
        <beans:constructor-arg name="loginFormUrl" value="/login" />
         <beans:property name="forceHttps" value="true"/>
    </beans:bean>
    <!-- 自定义异常处理 -->
    <beans:bean id="customExceptionTranslationFilter" class="com.stock.core.security.CustomExceptionTranslationFilter">
        <beans:constructor-arg name="authenticationEntryPoint">
            <beans:bean class="org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint">
                <beans:constructor-arg name="loginFormUrl" value="/login" />
                <beans:property name="forceHttps" value="true"/>
            </beans:bean>
        </beans:constructor-arg>
        <beans:property name="accessDeniedHandler" ref="customAccessDeniedHandler" />
    </beans:bean>
    <!-- 自定义拒绝访问处理 -->
    <beans:bean id="customAccessDeniedHandler" class="com.stock.core.security.CustomAccessDeniedHandler">
        <beans:property name="errorPage" value="/denied" />
    </beans:bean>
    <beans:bean id="authenticationFailureHandler" class="com.stock.core.security.CustomAuthenticationFailureHandler">
        <beans:property name="defaultFailureUrl" value="/login" />
        <beans:property name="baseService" ref="baseService" />
        <beans:property name="useForward" value="false" />
    </beans:bean>

    <beans:bean id="forceKickoutFilter" class="com.stock.capital.cloud.security.ForceKickoutSessionFilter">
        <beans:property name="expiredUrl" value="/forceKickout" />
    </beans:bean>


    <!-- 用户认证管理器 -->
    <authentication-manager alias="authenticationManager">
        <authentication-provider user-service-ref="userManagerService">
            <password-encoder ref="passwordEncoder" />
        </authentication-provider>
    </authentication-manager>

    <beans:bean id="passwordEncoder" class="org.springframework.security.crypto.password.StandardPasswordEncoder" />

    <!-- 权限访问控制器 -->
    <beans:bean id="accessDecisionManager" class="org.springframework.security.access.vote.AffirmativeBased">
        <beans:constructor-arg>
            <beans:list>
                <beans:bean class="org.springframework.security.access.vote.AuthenticatedVoter" />
                <beans:bean class="org.springframework.security.access.vote.RoleVoter">
                    <beans:property name="rolePrefix" value="RES_" />
                </beans:bean>
            </beans:list>
        </beans:constructor-arg>
    </beans:bean>

    <beans:bean id="authenticationSuccessHandler" class="com.stock.capital.cloud.security.CustomAuthenticationSuccessHandler">
        <beans:property name="alwaysUseDefaultTargetUrl" value="true" />
        <beans:property name="defaultTargetUrl" value="/index" />
        <beans:property name="baseService" ref="baseService" />
        <beans:property name="redirectStrategy">
            <beans:bean class="org.springframework.security.web.DefaultRedirectStrategy" />
        </beans:property>
        <beans:property name="caoccoAccessTokenStoreStrategy">
            <beans:bean class="com.stock.core.security.AccessTokenRedisStoreStrategy" />
        </beans:property>
    </beans:bean>

    <!-- 安全拦截器 -->
    <beans:bean id="filterSecurityInterceptor" class="org.springframework.security.web.access.intercept.FilterSecurityInterceptor">
        <!-- 拒绝公共调用 -->
        <beans:property name="rejectPublicInvocations" value="false" />
        <beans:property name="authenticationManager" ref="authenticationManager" />
        <beans:property name="accessDecisionManager" ref="accessDecisionManager" />
        <beans:property name="securityMetadataSource">
            <beans:bean class="com.stock.capital.cloud.security.SystemSecurityMetadataSource" />
        </beans:property>
    </beans:bean>

	<!-- 用户认证管理器 -->
    <beans:bean id="usernamePasswordSmsAuthenticationFilter" class="com.stock.capital.cloud.security.UsernamePasswordSmsAuthenticationFilter">
        <beans:property name="filterProcessesUrl" value="/loginVerify"/>
        <beans:property name="authenticationManager" ref="authenticationManager"/>
        <beans:property name="authenticationSuccessHandler" ref="authenticationSuccessHandler"/>
        <beans:property name="authenticationFailureHandler" ref="authenticationFailureHandler"/>
        <beans:property name="redisDao" ref="redisDao" />
        <beans:property name="commonService" ref="commonService" />
    </beans:bean>

    <!-- 增加异常消息国际化 -->
    <beans:bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <beans:property name="basename" value="classpath:/messages" />
    </beans:bean>

    <beans:bean id="redisDao" class="com.stock.core.dao.RedisDao">
    </beans:bean>

     <beans:bean id="commonService" class="com.stock.capital.cloud.common.service.CommonService">
    </beans:bean>

</beans:beans>
