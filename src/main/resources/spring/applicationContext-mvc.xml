<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:aop="http://www.springframework.org/schema/aop" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:mvc="http://www.springframework.org/schema/mvc"
    xsi:schemaLocation="
                    http://www.springframework.org/schema/beans
                    http://www.springframework.org/schema/beans/spring-beans.xsd
                    http://www.springframework.org/schema/aop
                    http://www.springframework.org/schema/aop/spring-aop.xsd
                    http://www.springframework.org/schema/context
                    http://www.springframework.org/schema/context/spring-context.xsd
                    http://www.springframework.org/schema/mvc
                    http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <aop:aspectj-autoproxy proxy-target-class="true" />

    <!-- 把标记了@Controller注解的类转换为bean -->
    <context:component-scan base-package="com.stock" use-default-filters="false">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice" />
    </context:component-scan>

    <!--文件上传设置 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize">
            <value>209715200</value><!-- 文件上传最大为200M -->
        </property>
    </bean>

    <!-- 请求返回数据转换 -->
    <mvc:annotation-driven>
        <mvc:message-converters register-defaults="true">
            <!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <constructor-arg value="UTF-8" />
            </bean>
            <!-- 将Jackson2HttpMessageConverter的默认格式化输出设为true -->
            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter" />
        </mvc:message-converters>
    </mvc:annotation-driven>

    <!-- 定义JSP文件的位置 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/" />
        <property name="suffix" value=".jsp" />
    </bean>

    <mvc:redirect-view-controller redirect-url="/login" path="/" />
    <!-- 容器默认的DefaultServletHandler处理 所有静态内容与无RequestMapping处理的URL -->
    <mvc:resources location="/static/" mapping="/static/**">
        <mvc:cache-control max-age="31536000"/>
        <mvc:resource-chain auto-registration="false" resource-cache="true" cache-manager="cacheManager">
            <mvc:resolvers>
                <bean class="org.springframework.web.servlet.resource.PathResourceResolver"/>
            </mvc:resolvers>
        </mvc:resource-chain>
    </mvc:resources>
    <mvc:resources location="/WEB-INF/views/" mapping="/resources/**/*.js">
        <mvc:resource-chain auto-registration="false" resource-cache="true" cache-manager="cacheManager">
            <mvc:resolvers>
                <bean class="org.springframework.web.servlet.resource.PathResourceResolver"/>
            </mvc:resolvers>
        </mvc:resource-chain>
    </mvc:resources>
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.stock.capital.cloud.common.interceptor.RequestInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>
    <!-- 全局异常配置 -->
    <bean class="com.stock.core.exception.ContentNegotiatingExceptionResolver">
        <property name="order" value="1" />
        <property name="defaultErrorView" value="error/500" />
        <property name="notFoundErrorView" value="error/404" />
        <property name="exceptionAttribute" value="exception" />
    </bean>
</beans>
