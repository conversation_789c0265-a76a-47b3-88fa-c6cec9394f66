<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="
                    http://www.springframework.org/schema/beans
                    http://www.springframework.org/schema/beans/spring-beans.xsd
                    http://www.springframework.org/schema/context
                    http://www.springframework.org/schema/context/spring-context.xsd
                    http://www.springframework.org/schema/cache
                    http://www.springframework.org/schema/cache/spring-cache.xsd">

    <bean id="redisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <!--最大连接数 -->
        <property name="maxTotal" value="#{app['redis.pool.maxTotal']}"/>
        <!--最大空闲连接数 -->
        <property name="maxIdle" value="#{app['redis.pool.maxIdle']}"/>
        <!--初始化连接数 -->
        <property name="minIdle" value="#{app['redis.pool.minIdle']}"/>
        <!--最大等待时间 -->
        <property name="maxWaitMillis" value="#{app['redis.pool.maxWaitMillis']}"/>
        <!--对拿到的connection进行validateObject校验 -->
        <property name="testOnBorrow" value="true"/>
        <!--在进行returnObject对返回的connection进行validateObject校验 -->
        <property name="testOnReturn" value="true"/>
        <!--定时对线程池中空闲的链接进行validateObject校验 -->
        <property name="testWhileIdle" value="true"/>
    </bean>
    
    <bean id="stringRedisSerializer" class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
    <bean id="jsonRedisSerializer" class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer"/>

    <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="redisConnectionFactory"/>
        <property name="keySerializer" ref="stringRedisSerializer"/>
        <property name="valueSerializer" ref="jsonRedisSerializer"/>
        <!--<property name="hashKeySerializer" ref="stringRedisSerializer" />-->
        <!--<property name="hashValueSerializer" ref="stringRedisSerializer" />-->
    </bean>

    <!-- redis存储HTTP会话-->
    <bean id="IMMEDIATE" class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">
        <property name="staticField" value="org.springframework.session.data.redis.RedisFlushMode.IMMEDIATE" />
    </bean>
    <bean class="org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration">
        <property name="redisFlushMode" ref="IMMEDIATE" />
        <property name="redisNamespace" value="capital:cloud" />
        <property name="maxInactiveIntervalInSeconds" value="#{app['session.timeout']}"/>
        <property name="cookieSerializer">
            <bean class="org.springframework.session.web.http.DefaultCookieSerializer">
                <property name="cookieName" value="JSESSIONID"/>
                <property name="cookiePath" value="/"/>
                <property name="cookieMaxAge" value="-1"/>
                <!--<property name="domainNamePattern" value="^.+?\\.(\\w+\\.[a-z]+)$"/>-->
                <property name="useHttpOnlyCookie" value="true"/>
            </bean>
        </property>
    </bean>

    <bean class="com.stock.core.dao.RedisDao"/>

    <!-- API访问令牌存储策略 -->
    <bean id="accessTokenStoreStrategy" class="com.stock.core.security.AccessTokenRedisStoreStrategy" />
    
    <!-- 开发环境直连 -->
    <beans profile="dev,prod">
        <bean id="redisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
            <constructor-arg name="poolConfig" ref="redisPoolConfig" />
            <property name="usePool" value="true" />
            <property name="hostName" value="#{app['redis.host']}" />
            <property name="port" value="#{app['redis.port']}" />
            <property name="password" value="#{app['redis.password']}" />
            <property name="timeout" value="#{app['redis.timeout']}" />
        </bean>
    </beans>
    
    <!-- 测试和生产环境高可用 -->
    <beans profile="prepub-sz,sim">
        <bean id="redisSentinelConfiguration" class="org.springframework.data.redis.connection.RedisSentinelConfiguration">
            <constructor-arg index="0" ref="sentinelProperty" />
        </bean>
        <bean id="sentinelProperty" class="org.springframework.core.env.MapPropertySource">
            <constructor-arg index="0" value="RedisSentinelConfiguration" />
            <constructor-arg index="1">
                <map>
                    <entry key="spring.redis.sentinel.master" value="#{app['redis.sentinel.master']}" />
                    <entry key="spring.redis.sentinel.nodes" value="#{app['redis.sentinel.nodes']}" />
                </map>
            </constructor-arg>
        </bean>
        <bean id="redisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
            <constructor-arg name="sentinelConfig" ref="redisSentinelConfiguration" />
            <constructor-arg name="poolConfig" ref="redisPoolConfig" />
            <property name="usePool" value="true" />
            <property name="password" value="#{app['redis.password']}" />
            <property name="timeout" value="#{app['redis.timeout']}" />
        </bean>
    </beans>

</beans>