<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                    http://www.springframework.org/schema/beans/spring-beans.xsd
                    http://www.springframework.org/schema/aop
                    http://www.springframework.org/schema/aop/spring-aop.xsd
                    http://www.springframework.org/schema/context
                    http://www.springframework.org/schema/context/spring-context.xsd


                    http://www.springframework.org/schema/task
                    http://www.springframework.org/schema/task/spring-task.xsd
                    http://www.springframework.org/schema/cache
                    http://www.springframework.org/schema/cache/spring-cache.xsd">

    <!-- 应用配置参数 -->
    <bean id="app" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="locations">
            <list>
                <value>classpath*:app.properties</value>
            </list>
        </property>
    </bean>

    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PreferencesPlaceholderConfigurer">
        <property name="properties" ref="app"/>
    </bean>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 把标记了@Service等注解的类转换为bean -->
    <context:component-scan base-package="com.stock">
        <context:exclude-filter type="annotation"
                                expression="org.springframework.stereotype.Controller"/>
        <context:exclude-filter type="annotation"
                                expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
    </context:component-scan>

    <!-- 定时任务注解 -->
    <task:annotation-driven executor="asyncExecutor"
                            scheduler="scheduler"/>
    <!-- 异步任务的执行器 -->
    <task:executor id="asyncExecutor" pool-size="10"/>
    <!-- 定时任务的执行器 -->
    <task:scheduler id="scheduler" pool-size="10"/>

    <!-- RestClient配置 -->
    <bean id="pollingConnectionManager"
          class="org.apache.http.impl.conn.PoolingHttpClientConnectionManager">
        <property name="maxTotal" value="10"/>
        <property name="defaultMaxPerRoute" value="5"/>
    </bean>

    <bean id="httpClientBuilder" class="org.apache.http.impl.client.HttpClientBuilder"
          factory-method="create">
        <property name="connectionManager" ref="pollingConnectionManager"/>
    </bean>

    <bean id="httpClient" factory-bean="httpClientBuilder" factory-method="build"/>

    <bean id="clientHttpRequestFactory"
          class="org.springframework.http.client.HttpComponentsClientHttpRequestFactory">
        <constructor-arg ref="httpClient"/>
        <property name="connectTimeout" value="30000"/>
        <property name="readTimeout" value="50000"/>
    </bean>

    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate">
        <constructor-arg ref="clientHttpRequestFactory"/>
        <property name="messageConverters">
            <list>
                <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                    <constructor-arg value="UTF-8"/>
                </bean>
                <bean class="org.springframework.http.converter.FormHttpMessageConverter"/>
                <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
                    <property name="prettyPrint" value="true"/>
                </bean>
            </list>
        </property>
    </bean>

    <bean id="restClient" class="com.stock.core.rest.RestClient">
        <constructor-arg ref="restTemplate"/>
        <property name="interceptors">
            <list>
                <bean class="com.stock.core.rest.TokenAuthorizationInterceptor"/>
                <bean class="com.stock.core.rest.RestFromProjectInterceptor">
                    <constructor-arg name="projectCode" value="01"/>
                </bean>
                <bean class="com.stock.core.rest.TraceRequestInterceptor"/>
            </list>
        </property>
    </bean>

    <!-- 资源文件配置 -->
    <bean id="appMessageSource"
          class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="useCodeAsDefaultMessage" value="true"/>
        <property name="cacheSeconds" value="5"></property>
        <property name="basenames">
            <list>
                <value>classpath:/messages</value>
            </list>
        </property>
    </bean>

    <!-- DEBUG -->
    <bean id="customizableDebugInterceptor" class="com.stock.core.aop.CustomizableDebugInterceptor"
          p:enterMessage="Entering $[targetClassShortName].$[methodName]($[arguments])"
          p:exitMessage="Leaving $[targetClassShortName].$[methodName](): $[returnValue]"/>

    <aop:config>
        <aop:advisor advice-ref="customizableDebugInterceptor"
                     pointcut="execution(* com.stock..*.*Service.*(..))"/>
        <aop:advisor advice-ref="customizableDebugInterceptor"
                     pointcut="execution(* com.stock..*.*Controller.*(..))"/>
    </aop:config>

    <bean id="pagerBuilder" class="com.stock.capital.cloud.common.tag.CloudPagerBuilder"/>
    <bean id="codeListBean" class="com.stock.core.misc.CodeListBean"/>
    <bean id="staticListBean" class="com.stock.core.misc.StaticListBean"/>

    <!-- 业务日志 -->
    <bean id="eventBus" class="com.stock.core.misc.EventBusFactoryBean"/>
    <bean id="logEventListener" class="com.stock.core.log.LogEventListener"/>

    <!-- 简单缓存 -->
    <cache:annotation-driven cache-manager="cacheManager"/>
    <bean id="cacheManager" class="org.springframework.cache.support.SimpleCacheManager">
        <property name="caches">
            <set>
                <bean
                        class="org.springframework.cache.concurrent.ConcurrentMapCacheFactoryBean">
                    <property name="name" value="DEFAULT_CACHE"></property>
                </bean>
            </set>
        </property>
    </bean>
    <!-- exce解析xml 配置 -->
    <bean id="excelImportMappingFactoryBean" class="com.stock.core.misc.ExcelImportMappingFactoryBean">
        <property name="mappingFilePath" value="/WEB-INF/templates/**/*.ExcelRule.xml"/>
    </bean>

    <bean id="ExcelHandle" class="com.stock.core.misc.ExcelHandle"/>

    <!-- 文件服务器 -->
    <bean id="fileServer" class="com.stock.core.file.DefaultFileServer"/>

    <!-- 推送client -->
    <bean id="pushClient" class="com.stock.core.push.client.CloudPushClient" />
</beans>