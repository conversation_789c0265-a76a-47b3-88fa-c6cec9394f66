<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xsi:schemaLocation="
                    http://www.springframework.org/schema/beans
                    http://www.springframework.org/schema/beans/spring-beans.xsd
                    http://www.springframework.org/schema/tx
                    http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- 动态数据源 -->
    <bean id="dynamicDataSource" class="com.stock.core.dao.DynamicDataSource">
        <property name="targetDataSources">
            <map key-type="java.lang.String">
                <entry value-ref="dataSource" key="dataSource"></entry>
            </map>
        </property>
        <property name="defaultTargetDataSource" ref="dataSource" />
    </bean>

    <!-- 数据源配置 -->
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="url" value="#{app['jdbc.url']}" />
        <property name="username" value="#{app['jdbc.username']}" />
        <property name="password" value="#{app['jdbc.password']}" />
        <property name="filters" value="stat,slf4j" />
        <property name="maxActive" value="#{app['jdbc.maxActive']}" />
        <property name="initialSize" value="#{app['jdbc.initialSize']}" />
        <property name="minIdle" value="#{app['jdbc.minIdle']}" />
        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="#{app['jdbc.maxWait']}" />
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <!-- 配置一个连接在池中最大生存的时间，单位是毫秒 -->
        <property name="maxEvictableIdleTimeMillis" value="1800000" />
        <property name="validationQuery" value="SELECT 'x'" />
        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
    </bean>

    <!-- ORM配置 -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dynamicDataSource" />
        <property name="configLocation" value="classpath:/persistence/mybatis-config.xml" />
        <property name="mapperLocations" value="classpath*:/com/stock/core/dao/CommonDao.xml"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.stock.capital.cloud.**.dao,com.stock.core.push.dao" />
    </bean>
    
    <!-- 事务管理器配置, 使用jdbc事务 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dynamicDataSource" />
    </bean>

    <!-- 使用annotation定义事务 -->
    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true" />

</beans>
