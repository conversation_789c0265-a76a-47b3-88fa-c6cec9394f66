css.normalize=css/normalize.css
css.zui=lib/zui/css/zui.min.css
css.awesome=css/font-awesome.min.css
css.dashboard=css/dashboard.css
css.datetimepicker=lib/datetimepicker/bootstrap-datetimepicker.min.css
css.daterangepicker=lib/daterangepicker/daterangepicker.css
css.ztree=lib/ztree/zTreeStyle/zTreeStyle.css
css.fileupload=lib/fileupload/jquery.fileupload.css
css.datatables=lib/datatable/dataTables.bootstrap.css
css.tselect=lib/tselect/tselect.css
css.ueditor=lib/ueditor-all/ueditor/themes/default/css/ueditor.min.css
css.chosen=lib/chosen/chosen.min.css
css.common=css/style.css
css.autocomplete=lib/autocomplate/jquery.autocomplete.css
css.tagsinput=lib/bootstrap-tagsinput-master/jquery.tagsinput.css
css.bootstrap.tagsinput=lib/bootstrap-tagsinput-master/bootstrap-tagsinput.css
css.switcher=css/switcher.css
css.input=lib/datatable/input.css
css.select2=lib/select2/select2.css
css.bootstrap.switch=lib/bootstrap-switch/bootstrap-switch.min.css
css.jeDate=lib/jeDate/skin/jedate.css
css.icheck=lib/icheck/skins/all.css
js.jquery=js/jquery-3.7.1.min.js
js.jquery1=js/jquery.fix.clone.js
js.zui=lib/zui/js/zui.min.js
js.zui.sortable=lib/zui/lib/sortable/zui.sortable.min.js
js.template=js/template.js
js.sidebar=js/sidebar.js
js.json=lib/json/jquery.json.min.js
js.form=lib/form/jquery.form.min.js
js.validate=lib/validator/jquery.validate.min.js
js.validate.messages=lib/validator/messages_zh.min.js
js.validate.tooltip=lib/validator/jquery-validate-tooltip.min.js
js.datetimepicker=lib/datetimepicker/bootstrap-datetimepicker.min.js
js.datetimepicker.zh=lib/datetimepicker/locales/bootstrap-datetimepicker.zh-CN.js
js.ztree=lib/ztree/jquery.ztree.all-3.5.min.js
js.ztree.ex=lib/ztree/jquery.ztree.exhide-3.5.min.js
js.fileupload.ui=lib/fileupload/jquery.ui.widget.js
js.fileupload.transport=lib/fileupload/jquery.iframe-transport.js
js.fileupload=lib/fileupload/jquery.fileupload.js
js.layer=lib/layer/layer.js
js.datatables=lib/datatable/jquery.dataTables.min.js
js.datatables.bootstrap=lib/datatable/dataTables.bootstrap.min.js
js.datatables.ex=js/datatable.js
js.ueditor.config=lib/ueditor-all/ueditor/ueditor.config.js
js.ueditor=lib/ueditor-all/ueditor/ueditor.all.js
js.ueditor.parse=lib/ueditor-all/ueditor/ueditor.parse.min.js
js.ueditor.lang=lib/ueditor-all/ueditor/lang/zh-cn/zh-cn.js
js.ueditor.addKityFormulaDialog=lib/ueditor-all/ueditor/kityformula-plugin/addKityFormulaDialog.js
js.ueditor.getKfContent=lib/ueditor-all/ueditor/kityformula-plugin/getKfContent.js
js.ueditor.defaultFilterFix=lib/ueditor-all/ueditor/kityformula-plugin/defaultFilterFix.js
js.chosen=lib/chosen/chosen.min.js
js.moment=lib/moment/moment.min.js
js.daterangepicker=lib/daterangepicker/daterangepicker.js
js.common=js/common.js
js.autocomplete=lib/autocomplate/jquery.autocomplete.js
js.tablednd=lib/tablednd/jquery.tablednd.min.js
js.md5=lib/md5/jquery.md5.js
js.slimscroll=lib/slimscroll/jquery.slimscroll.min.js
js.tagsinput=lib/bootstrap-tagsinput-master/jquery.tagsinput.js
js.bootstrap.tagsinput=lib/bootstrap-tagsinput-master/bootstrap-tagsinput.js
js.diff=lib/tselect/diff.js
js.tselect=lib/tselect/tselect-new.js
js.switcher=js/switcher.js
js.input=lib/datatable/input.js
js.admin=js/pixel-admin.min.js
js.autosize=js/autosize.min.js
js.clipboard=js/clipboard.js
js.select2=lib/select2/select2.js
#js.echarts=lib/echarts/echarts.min.js
js.simple_numbers_no_ellipses=lib/datatable/simple_numbers_no_ellipses.js
js.jsrender=lib/jsrender/jsrender.js
js.bootstrap.switch=lib/bootstrap-switch/bootstrap-switch.js
js.jeDate=lib/jeDate/jedate.js
js.icheck=lib/icheck/icheck.min.js
js.base64=lib/base64/jquery.base64.js
