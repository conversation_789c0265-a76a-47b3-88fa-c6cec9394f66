#------------------------------------log------------------------------------
log.level=${log.level}
log.appender=${log.appender}
log.home=${log.home}
log.stash.address=${log.stash.address}
#-----------------------------------mysql-----------------------------------
jdbc.url=${jdbc.url}
jdbc.username=${jdbc.username}
jdbc.password=${jdbc.password}
jdbc.initialSize=${jdbc.initialSize}
jdbc.maxActive=${jdbc.maxActive}
jdbc.minIdle=${jdbc.minIdle}
jdbc.maxWait=${jdbc.maxWait}

#-----------------------------------redis-----------------------------------
redis.host=${redis.host}
redis.port=${redis.port}
redis.password=${redis.password}
redis.timeout=${redis.timeout}
redis.pool.maxTotal=${redis.pool.maxTotal}
redis.pool.maxIdle=${redis.pool.maxIdle}
redis.pool.minIdle=${redis.pool.minIdle}
redis.pool.maxWaitMillis=${redis.pool.maxWaitMillis}
redis.pool.testOnBorrow=${redis.pool.testOnBorrow}
redis.pool.testOnReturn=${redis.pool.testOnReturn}
redis.pool.testWhileIdle=${redis.pool.testWhileIdle}
redis.sentinel.master=${redis.sentinel.master}
redis.sentinel.nodes=${redis.sentinel.nodes}

#----------------------------------session----------------------------------
session.timeout=${session.timeout}
#-----------------------------------file------------------------------------
#local_base
file.basePath=${file.basePath}
#local_view
file.viewPath=${file.viewPath}
#-----------------------------------api------------------------------------
api.baseUrl=${api.baseUrl}
service.gui.baseUrl=${service.gui.baseUrl}
service.gui.clientId=${service.gui.clientId}
service.gui.clientSecret=${service.gui.clientSecret}
#-----------------------------------mq-------------------------------------
mq.broker.host=${mq.broker.host}
mq.broker.port=${mq.broker.port}
mq.broker.username=${mq.broker.username}
mq.broker.password=${mq.broker.password}

#-----------------------------------es-------------------------------------
elasticsearch.cluster-nodes=${elasticsearch.cluster-nodes}
elasticsearch.cluster-name=${elasticsearch.cluster-name}
elasticsearch.request-timeout=${elasticsearch.request-timeout}

#--------------------------------eb-school------------------------------
capco.qrCodeUrl=${capco.qrCodeUrl}

pulsar.servers=${pulsar.servers}
pulsar.token=${pulsar.token}

exam.baseUrl=${exam.baseUrl}
capco.baseUrl=${capco.baseUrl}
pushApi.baseUrl=${pushApi.baseUrl}
stock-core-push.pushFileBasePath=${stock-core-push.pushFileBasePath}

#--------------------huaweicloud--------------------------------------

#----cn-north1------
huaweicloud.bucketName=${huaweicloud.bucketName}
#----?? ??1------
huaweicloud.location=${huaweicloud.location}
#----??id??????API?????????????id------
huaweicloud.projectId=${huaweicloud.projectId}
#----????????------
huaweicloud.vodEndPoint=${huaweicloud.vodEndPoint}
#----IAM??????,?????????????????(region)------
huaweicloud.iamEndPoint=${huaweicloud.iamEndPoint}
#----????????------
huaweicloud.obsEndPoint=${huaweicloud.obsEndPoint}
#----????????????------
huaweicloud.mpsEndPoint=${huaweicloud.mpsEndPoint}
#----AK------
huaweicloud.vodAk=${huaweicloud.vodAk}
#----SK------
huaweicloud.vodSk=${huaweicloud.vodSk}

#----north-4------
huaweicloud.bucketName4=${huaweicloud.bucketName4}
#----region------
huaweicloud.location4=${huaweicloud.location4}
#----projectId------
huaweicloud.projectId4=${huaweicloud.projectId4}
#----obs------
huaweicloud.obsEndPoint4=${huaweicloud.obsEndPoint4}
#----shi pin zhuan ma------
huaweicloud.mpsEndPoint4=${huaweicloud.mpsEndPoint4}
#----AK------
huaweicloud.ak4=${huaweicloud.ak4}
#----SK------
huaweicloud.sk4=${huaweicloud.sk4}


#-----------zhiyi----------------------
chatDong.baseUrl=${chatDong.baseUrl}
