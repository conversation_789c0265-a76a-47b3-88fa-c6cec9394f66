package com.stock.capital.cloud.liveStreaming.service;

import com.google.common.base.Throwables;
import com.huawei.sis.bean.base.AsrcLongSentence;
import com.huawei.sis.bean.response.AsrCustomLongResponse;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.mpc.v1.MpcClient;
import com.huaweicloud.sdk.mpc.v1.model.*;
import com.huaweicloud.sdk.oms.v2.OmsClient;
import com.huaweicloud.sdk.oms.v2.model.*;
import com.huaweicloud.sdk.oms.v2.region.OmsRegion;
import com.stock.capital.cloud.capcoTrain.dao.SchoolIndexConfigBizMapper;
import com.stock.capital.cloud.capcoTrain.util.Base64;
import com.stock.capital.cloud.common.dao.AttachmentMapper;
import com.stock.capital.cloud.common.dao.SchLiveTeachRelaMapper;
import com.stock.capital.cloud.common.model.entity.Attachment;
import com.stock.capital.cloud.common.model.entity.SchLiveTeachRela;
import com.stock.capital.cloud.common.model.entity.SchLiveTeachRelaExample;
import com.stock.capital.cloud.common.model.entity.SchTeachInfo;
import com.stock.capital.cloud.common.service.CommonService;
import com.stock.capital.cloud.common.service.FileService;
import com.stock.capital.cloud.courseOnDemand.dao.BasicInformationBizMapper;
import com.stock.capital.cloud.courseOnDemand.dto.CourseTypeDetailedDto;
import com.stock.capital.cloud.liveStreaming.dao.LiveInfoMapper;
import com.stock.capital.cloud.liveStreaming.dao.LiveSubscribeBizMapper;
import com.stock.capital.cloud.liveStreaming.dao.SchLiveInfoBizMapper;
import com.stock.capital.cloud.liveStreaming.dto.*;
import com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper;
import com.stock.capital.cloud.trainUserManage.service.RequestCapcoSystem;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.OptionDto;
import com.stock.core.exception.ApplicationException;
import com.stock.core.file.FileServer;
import com.stock.core.message.pulsar.PulsarTemplate;
import com.stock.core.rest.RestClient;
import com.stock.core.util.DateUtil;
import com.stock.core.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@EnableAsync
@Service
public class LiveInfoService extends CommonService {

    private static final Logger logger = LoggerFactory.getLogger(LiveInfoService.class);
    private static final String LIVE_CODE = "rtmp://stream-push.valueonline.cn/live/";
    private static final String LIVE_PULL_CODE = "https://stream-pull.valueonline.cn/live/";
    private static final String RTMP_LIVE_PULL_CODE = "rtmp://stream-pull.valueonline.cn/live/";
    private static final byte[] ivBytes = "yCmE666N3YAq30SN".getBytes();
    private static final byte[] pushKey = "HAsdTn9jvBmRdAtELSRRkM7mcrBX7PTt".getBytes();
    private static final byte[] pullKey = "iu4UvKXsInES1PnPOx8r7jGtibRFrl5d".getBytes();

    @Resource
    private CommonService commonService;
    @Resource
    private FileService fileService;
    @Resource
    private FileServer fileServer;
    @Resource
    private AttachmentMapper attachmentMapper;
    @Resource
    private SchLiveInfoBizMapper schLiveInfoBizMapper;
    @Resource
    private LiveInfoMapper liveInfoMapper;
    @Resource
    private SchLiveTeachRelaMapper schLiveTeachRelaMapper;
    @Autowired
    private SchoolIndexConfigBizMapper schoolIndexConfigBizMapper;

    @Autowired
    private LiveSubscribeBizMapper liveSubscribeBizMapper;

    @Autowired
    private TrainUserManageBizMapper trainUserManageBizMapper;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private BasicInformationBizMapper basicInformationBizMapper;

    @Autowired
    private RequestCapcoSystem requestCapcoSystem;

    @Autowired
    private PulsarTemplate pulsarTemplate;
    @Value("#{app['file.viewPath']}")
    private String filePath;

    @Value("#{app['huaweicloud.bucketName']}")
    private String bucketName;

    @Value("#{app['huaweicloud.location']}")
    private String location;

    @Value("#{app['huaweicloud.projectId']}")
    private String projectId;

    @Value("#{app['huaweicloud.vodAk']}")
    private String ak;

    @Value("#{app['huaweicloud.vodSk']}")
    private String sk;

    @Value("#{app['huaweicloud.mpsEndPoint']}")
    private String mpsEndPoint;

    @Value("#{app['huaweicloud.obsEndPoint']}")
    private String obsEndPoint;

    @Value("#{app['chatDong.baseUrl']}")
    private String apiBaseUrl;

    @Value("#{app['huaweicloud.location4']}")
    private String location4;

    @Value("#{app['huaweicloud.projectId4']}")
    private String projectId4;

    @Value("#{app['huaweicloud.ak4']}")
    private String ak4;

    @Value("#{app['huaweicloud.sk4']}")
    private String sk4;

    @Value("#{app['huaweicloud.bucketName4']}")
    private String bucketName4;

    @Value("#{app['huaweicloud.obsEndPoint4']}")
    private String obsEndPoint4;

    @Autowired
    private RestClient restClient;

    /**
     * 删除直播信息
     *
     * @param liveInfoId
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    public JsonResponse<Boolean> delete(String liveInfoId) {
        JsonResponse<Boolean> result = new JsonResponse<>();
        String userId = getUserInfo().getUserId();
        if (StringUtils.isEmpty(liveInfoId)) {
            logger.warn("易董学院直播删除失败，ID参数为空！操作人：{}", userId);
            throw new ApplicationException("删除失败：直播ID为空");
        }
        SchLiveInfoExample example = new SchLiveInfoExample();
        example.createCriteria().andLiveIdEqualTo(liveInfoId);
        SchLiveInfoDto liveInfo = new SchLiveInfoDto();
        liveInfo.setLiveStatus("0");
        liveInfo.setUpdateTime(new Date());
        liveInfo.setUpdateUser(userId);
        int deleteCount = schLiveInfoBizMapper.updateByExampleSelective(liveInfo, example);
        //易董学院首页设置删除关联的轮播图
        schoolIndexConfigBizMapper.deleteBannerByLiveId(liveInfoId);
        if (deleteCount == 0) {
            logger.warn("易董学院直播删除失败，删除结果为零！直播ID: {}操作人：{}", liveInfoId, userId);
            throw new ApplicationException("删除失败：未找到对应的直播");
        }
        result.setResult(true);
        //logger.info("易董学院直播删除成功！直播ID: {}操作人：{}", liveInfoId, userId);
        return result;
    }

    /**
     * 保存直播信息
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveLiveInfo(LiveInfoParamDto param) {
        String userId = getUserInfo().getUserId();
        Date nowDate = new Date();
        SchLiveInfoDto insertLiveInfo = new SchLiveInfoDto();
        BeanUtils.copyProperties(param, insertLiveInfo);
        // 直播时间处理
        if (StringUtils.isNotEmpty(param.getLiveBegTimeStr())) {
            insertLiveInfo.setLiveBegTime(
                DateUtil.getDate(param.getLiveBegTimeStr(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        if (StringUtils.isNotEmpty(param.getLiveEndTimeStr())) {
            insertLiveInfo.setLiveEndTime(
                DateUtil.getDate(param.getLiveEndTimeStr(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        // 封面图临时文件ID
        String livePicTempId = param.getLivePic().replace(filePath, "");
//			// 保存封面图
        if (StringUtils.isNotEmpty(livePicTempId)) {
            String livePicViewUrl = param.getLivePic();
            insertLiveInfo.setLivePic(livePicViewUrl);
        }
//		}
        insertLiveInfo.setLiveStatus("1");
        insertLiveInfo.setCreateUser(userId);
        insertLiveInfo.setCreateTime(nowDate);
        insertLiveInfo.setUpdateUser(userId);
        insertLiveInfo.setUpdateTime(nowDate);
        insertLiveInfo.setOrgId((String) getUserInfo().getInfo().get("orgId"));
        insertLiveInfo.setIfVerifySub(param.getIfVerifySub());
        insertLiveInfo.setIfLearningHours(param.getIfLearningHours());
        insertLiveInfo.setIfForm(param.getIfForm());
        Integer learningTime=0;
        if (!ObjectUtils.isEmpty(param.getLearningMinute()) && !ObjectUtils.isEmpty(param.getLearningSecond())){
            learningTime = param.getLearningMinute()*60+param.getLearningSecond();
        }else if(!ObjectUtils.isEmpty(param.getLearningMinute())){
            learningTime = param.getLearningMinute()*60;
        }else if (!ObjectUtils.isEmpty(param.getLearningSecond())){
            learningTime = param.getLearningSecond();
        }
        insertLiveInfo.setLearningHours(learningTime);
        if (!ObjectUtils.isEmpty(param.getCredit())){
            insertLiveInfo.setCredit(param.getCredit());
        }
        insertLiveInfo.setSubNum(param.getSubNum());
        // 插入直播信息
        schLiveInfoBizMapper.insert(insertLiveInfo);
        SchLiveInfoDto updateLiveInfo = new SchLiveInfoDto();
        updateLiveInfo.setLiveId(insertLiveInfo.getLiveId());
        String timestamp = DateUtil.changeDateFormat(insertLiveInfo.getLiveBegTime(), "yyyyMMddHHmmss");
        // 获取推流地址
        String liveCode = LIVE_CODE + insertLiveInfo.getLiveId() + "?auth_info=" + getLiveAuthInfo(timestamp, "live", insertLiveInfo.getLiveId(), "5", pushKey);
        updateLiveInfo.setLiveCode(liveCode);
        // 获取拉流地址
        Map pullCodeObj = new HashMap();
        pullCodeObj.put("baseUrl", LIVE_PULL_CODE);
        Map authInfoObj = new HashMap();
        authInfoObj.put("sd", insertLiveInfo.getLiveId() + "_sd.m3u8?auth_info=" + getLiveAuthInfo(timestamp, "live", insertLiveInfo.getLiveId() + "_sd", "5", pullKey));
        authInfoObj.put("hd", insertLiveInfo.getLiveId() + "_hd.m3u8?auth_info=" + getLiveAuthInfo(timestamp, "live", insertLiveInfo.getLiveId() + "_hd", "5", pullKey));
        authInfoObj.put("ud", insertLiveInfo.getLiveId() + "_ud.m3u8?auth_info=" + getLiveAuthInfo(timestamp, "live", insertLiveInfo.getLiveId() + "_ud", "5", pullKey));
        authInfoObj.put("rtmp_ud", RTMP_LIVE_PULL_CODE + insertLiveInfo.getLiveId() + "_ud?auth_info=" + getLiveAuthInfo(timestamp, "live", insertLiveInfo.getLiveId() + "_ud", "5", pullKey));
        pullCodeObj.put("authInfo", authInfoObj);
        updateLiveInfo.setPullCode(JsonUtil.toJson(pullCodeObj));
        schLiveInfoBizMapper.updateByPrimaryKeySelective(updateLiveInfo);
        // 保存附件
        // 附件临时ID
        List<LiveInfoFileParamDto> newFileList = new ArrayList<>();
        Optional.ofNullable(param.getFileList())
            .orElse(new ArrayList<>())
            .forEach(
                fileDto -> {
                    if (StringUtils.isNotEmpty(fileDto.getTempId())) {
                        newFileList.add(fileDto);
                    }
                });
        if (CollectionUtils.isNotEmpty(newFileList)) {
            for (LiveInfoFileParamDto fileDto : newFileList) {
                Attachment attachment = new Attachment();
                attachment.setAttName(fileDto.getFileName());
                attachment.setAttUrl(fileDto.getTempId());
                attachment.setBusinessId(insertLiveInfo.getLiveId());
                attachment.setDocumentType(fileDto.getFileName().substring(fileDto.getFileName().lastIndexOf(".") + 1));
                attachment.setSize(Long.valueOf(fileDto.getSize()));
                attachment.setVersion("1");
                attachment.setStatus("1");
                attachmentMapper.insertSelective(attachment);
            }
        }
        // 关联讲师
        Optional.ofNullable(param.getTeachList())
            .orElse(new ArrayList<>())
            .forEach(
                teachId -> {
                    // 插入关联表
                    SchLiveTeachRela schLiveTeachRela = new SchLiveTeachRela();
                    schLiveTeachRela.setLiveId(insertLiveInfo.getLiveId());
                    schLiveTeachRela.setTeachId(teachId);
                    schLiveTeachRelaMapper.insert(schLiveTeachRela);
                });
//		List<CourseTypeDetailedDto> paramList = new ArrayList<>();
//		//业务分类
//		courseTypeOperation(param.getCourseType(), insertLiveInfo.getLiveId(), paramList, "custom001");
//		//插入关联分类
//		basicInformationBizMapper.insertCourseType(paramList);
//		//保存直播信息时，发消息处理直播小程序码
//		Map<String,Object> message = JsonUtil.fromJson(JsonUtil.toJson(insertLiveInfo),Map.class);
//		pulsarTemplate.sendAsync(Global.CREATE_LIVE, message);
        return true;
    }

    //课程业务操作
    public void courseTypeOperation(String typeIds, String id, List<CourseTypeDetailedDto> paramList, String type) {
        List<String> typeList = Arrays.asList(typeIds.split(","));
        for (String str : typeList) {
            CourseTypeDetailedDto courseTypeDetailedDto = new CourseTypeDetailedDto();
            courseTypeDetailedDto.setCourseInfoId(id);
            courseTypeDetailedDto.setCourseRelationId(str);
            courseTypeDetailedDto.setCourseType(type);
            courseTypeDetailedDto.setCreateUser(getUserInfo().getUserId());
            paramList.add(courseTypeDetailedDto);
        }
    }

    /**
     * 根据Id获取直播信息
     *
     * @param liveInfoId
     * @return
     */
    public LiveInfoDto getLiveInfo(String liveInfoId) {
        LiveInfoDto dto = new LiveInfoDto();
        dto.setLiveId(liveInfoId);
        dto.setFilePath(filePath);
        LiveInfoDto liveInfoDto = liveInfoMapper.getByLiveId(dto);
        if (liveInfoDto != null && CollectionUtils.isNotEmpty(liveInfoDto.getTeachInfoList())) {
            String teachIds = liveInfoDto.getTeachInfoList().stream().map(SchTeachInfo::getTeachId).collect(Collectors.joining(","));
            liveInfoDto.setTeachIds(teachIds);
        }
        if (liveInfoDto != null && (!ObjectUtils.isEmpty(liveInfoDto.getLearningHours()))){
            liveInfoDto.setLearningMinute(liveInfoDto.getLearningHours()/60);
            liveInfoDto.setLearningSecond(liveInfoDto.getLearningHours()%60);
        }
        return liveInfoDto;
    }

    /**
     * 更新直播信息
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateLiveInfo(LiveInfoParamDto param) {
        String userId = getUserInfo().getUserId();
        Date nowDate = new Date();
        SchLiveInfoDto updateLiveInfo = new SchLiveInfoDto();
        BeanUtils.copyProperties(param, updateLiveInfo);
        // 直播时间处理
        if (StringUtils.isNotEmpty(param.getLiveBegTimeStr())) {
            updateLiveInfo.setLiveBegTime(
                DateUtil.getDate(param.getLiveBegTimeStr(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        if (StringUtils.isNotEmpty(param.getLiveEndTimeStr())) {
            updateLiveInfo.setLiveEndTime(
                DateUtil.getDate(param.getLiveEndTimeStr(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        // 查询原有直播信息
        LiveInfoDto liveDto = new LiveInfoDto();
        liveDto.setLiveId(param.getLiveId());
        LiveInfoDto liveInfoOld = liveInfoMapper.getByLiveId(liveDto);
        // 封面图临时文件ID
        String livePicTempId = param.getLivePic().replace(filePath, "");
        String livePicId = "";
        if (!Optional.ofNullable(liveInfoOld.getLivePic())
            .orElse("")
            .equals(Optional.ofNullable(livePicTempId).orElse(""))) {
            if (StringUtils.isNotEmpty(livePicTempId)) {
                String livePicViewUrl = param.getLivePic().replace(filePath, "");
                updateLiveInfo.setLivePic(livePicViewUrl);
            } else {
                updateLiveInfo.setLivePic("");
            }
        }
        updateLiveInfo.setLiveCode(liveInfoOld.getLiveCode());
        updateLiveInfo.setPullCode(liveInfoOld.getPullCode());
        // 判断直播时间是否改变
        if (DateUtil.dateCompare(liveInfoOld.getLiveBegTime(), updateLiveInfo.getLiveBegTime()) != 0) {
            // 重新生成推流码、拉流码
            String timestamp = DateUtil.changeDateFormat(updateLiveInfo.getLiveBegTime(), "yyyyMMddHHmmss");
            // 获取推流地址
            String liveCode = LIVE_CODE + updateLiveInfo.getLiveId() + "?auth_info=" + getLiveAuthInfo(timestamp, "live", updateLiveInfo.getLiveId(), "5", pushKey);
            updateLiveInfo.setLiveCode(liveCode);
            // 获取拉流地址
            Map pullCodeObj = new HashMap();
            pullCodeObj.put("baseUrl", LIVE_PULL_CODE);
            Map authInfoObj = new HashMap();
            authInfoObj.put("sd", updateLiveInfo.getLiveId() + "_sd.m3u8?auth_info=" + getLiveAuthInfo(timestamp, "live", updateLiveInfo.getLiveId() + "_sd", "5", pullKey));
            authInfoObj.put("hd", updateLiveInfo.getLiveId() + "_hd.m3u8?auth_info=" + getLiveAuthInfo(timestamp, "live", updateLiveInfo.getLiveId() + "_hd", "5", pullKey));
            authInfoObj.put("ud", updateLiveInfo.getLiveId() + "_ud.m3u8?auth_info=" + getLiveAuthInfo(timestamp, "live", updateLiveInfo.getLiveId() + "_ud", "5", pullKey));
            authInfoObj.put("rtmp_ud", RTMP_LIVE_PULL_CODE + updateLiveInfo.getLiveId() + "_ud?auth_info=" + getLiveAuthInfo(timestamp, "live", updateLiveInfo.getLiveId() + "_ud", "5", pullKey));
            pullCodeObj.put("authInfo", authInfoObj);
            updateLiveInfo.setPullCode(JsonUtil.toJson(pullCodeObj));
        }
        updateLiveInfo.setLiveStatus("1");
        updateLiveInfo.setCreateUser(liveInfoOld.getCreateUser());
        updateLiveInfo.setCreateTime(liveInfoOld.getCreateTime());
        updateLiveInfo.setUpdateUser(userId);
        updateLiveInfo.setUpdateTime(nowDate);
        updateLiveInfo.setIfVerifySub(param.getIfVerifySub());
        updateLiveInfo.setIfLearningHours(param.getIfLearningHours());
        updateLiveInfo.setIfForm(param.getIfForm());
        Integer learningTime=0;
        if (!ObjectUtils.isEmpty(param.getLearningMinute()) && !ObjectUtils.isEmpty(param.getLearningSecond())){
             learningTime = param.getLearningMinute()*60+param.getLearningSecond();
        }else if(!ObjectUtils.isEmpty(param.getLearningMinute())){
            learningTime = param.getLearningMinute()*60;
        }else if (!ObjectUtils.isEmpty(param.getLearningSecond())){
            learningTime = param.getLearningSecond();
        }
        updateLiveInfo.setLearningHours(learningTime);
        if (!ObjectUtils.isEmpty(param.getCredit())){
            updateLiveInfo.setCredit(param.getCredit());
        }
        updateLiveInfo.setSubNum(param.getSubNum());
        // 更新直播信息
        schLiveInfoBizMapper.updateByPrimaryKeyWithBLOBs(updateLiveInfo);
        // 保存附件
        List<LiveInfoFileParamDto> newFileList = new ArrayList<>();
        List<String> oldFileList = new ArrayList<>();
        Optional.ofNullable(param.getFileList())
            .orElse(new ArrayList<>())
            .forEach(
                dto -> {
                    if ("0".equals(dto.getDataType())) {//新文件
                        if (StringUtils.isNotEmpty(dto.getTempId())) {
                            newFileList.add(dto);
                        }
                    } else if ("1".equals(dto.getDataType())) {//旧文件
                        oldFileList.add(dto.getAttachmentId());
                    }
                });
        Optional.ofNullable(liveInfoOld.getFileList())
            .orElse(new ArrayList<>())
            .forEach(
                dto -> {
                    if (!oldFileList.contains(dto.getAttId())) {
                        // 删除附件
                        delFileByAttId(dto.getAttId());//删除被删除的
                    }
                });
        if (CollectionUtils.isNotEmpty(newFileList)) {
            for (LiveInfoFileParamDto fileDto : newFileList) {
                Attachment attachment = new Attachment();
                attachment.setAttName(fileDto.getFileName());
                attachment.setAttUrl(fileDto.getTempId());
                attachment.setBusinessId(param.getLiveId());
                attachment.setDocumentType(fileDto.getFileName().substring(fileDto.getFileName().lastIndexOf(".") + 1));
                attachment.setSize(Long.valueOf(fileDto.getSize()));
                attachment.setVersion("1");
                attachment.setStatus("1");
                attachmentMapper.insertSelective(attachment);
            }
        }
        // 关联讲师
        // 删除本次直播讲师关联记录
        SchLiveTeachRelaExample relaExample = new SchLiveTeachRelaExample();
        relaExample.createCriteria().andLiveIdEqualTo(param.getLiveId());
        schLiveTeachRelaMapper.deleteByExample(relaExample);
        // 插入最新的
        Optional.ofNullable(param.getTeachList())
            .orElse(new ArrayList<>())
            .forEach(
                teachId -> {
                    SchLiveTeachRela liveTeachRela = new SchLiveTeachRela();
                    liveTeachRela.setLiveId(param.getLiveId());
                    liveTeachRela.setTeachId(teachId);
                    schLiveTeachRelaMapper.insert(liveTeachRela);
                });
//    List<CapcoTrainTypeCostDto> capcoTrainTypeCostDtoList =param.getPersonTypes();
//
//    //将标签合并
//    //将前台传入得personLabel合并在会员类型的list中。一起进行保存。
//    List<CapcoTrainTypeCostDto> labelDtoList = new ArrayList<>();
//    if(StringUtils.isNotEmpty(param.getPersonLabel())){
//      if(param.getPersonLabel().indexOf(",")!=-1){
//        List<String> list = Arrays.asList(param.getPersonLabel().split(","));
//        for(String s:list){
//          CapcoTrainTypeCostDto ofDto = new CapcoTrainTypeCostDto();
//          if(capcoTrainTypeCostDtoList.size()>0){
//            ofDto.setRelationId(capcoTrainTypeCostDtoList.get(0).getRelationId());
//          }
//          ofDto.setPersonType(s);
//          labelDtoList.add(ofDto);
//        }
//      }else{
//        CapcoTrainTypeCostDto ofDto = new CapcoTrainTypeCostDto();
//        if(capcoTrainTypeCostDtoList.size()>0){
//          ofDto.setRelationId(capcoTrainTypeCostDtoList.get(0).getRelationId());
//        }
//        ofDto.setPersonType(param.getPersonLabel());
//        labelDtoList.add(ofDto);
//      }
//      capcoTrainTypeCostDtoList.addAll(labelDtoList);
//    }
//
//    //关联收费
//    for (CapcoTrainTypeCostDto dto : capcoTrainTypeCostDtoList){
//      dto.setRelationId(param.getLiveId());
//    }
//    //删除旧关联
//    liveInfoMapper.deletePersonList(param.getLiveId());
//    if (!CollectionUtils.isEmpty(capcoTrainTypeCostDtoList)){
//      liveInfoMapper.insertTrainCost(capcoTrainTypeCostDtoList);
//    }
//		List<CourseTypeDetailedDto> paramList = new ArrayList<>();
//		//删除关联分类
//		liveInfoMapper.deleteCourseType(param.getLiveId());
//		//业务分类
//		courseTypeOperation(param.getCourseType(), param.getLiveId(), paramList, "custom001");
//		//插入关联分类
//		basicInformationBizMapper.insertCourseType(paramList);
        return true;
    }

    /**
     * 根据CodeNo获取下拉选项List
     *
     * @param codeNo
     * @return
     */
    public List<OptionDto> getOptionsByCodeNo(String codeNo) {
        return extractTransformCode(getCodeListByCodeNo(codeNo));
    }

    /**
     * 转换码表下拉选项
     *
     * @param itemList
     * @return
     */
    private List<OptionDto> extractTransformCode(List<Map<String, String>> itemList) {
        List<OptionDto> optionDtos = new ArrayList<>();
        Optional.ofNullable(itemList)
            .orElse(new ArrayList<>())
            .forEach(
                dto -> {
                    String validFlag = Optional.ofNullable(dto.get("valid_flag")).orElse("0");
                    if (validFlag.equals("1")) {
                        OptionDto optionDto = new OptionDto();
                        optionDto.setLabel(dto.get("code_name"));
                        optionDto.setValue(dto.get("code_value"));
                        optionDtos.add(optionDto);
                    }
                });
        return optionDtos;
    }

    /**
     * 获取图片链接
     */
    private String getPictureViewUrl(String fileId) {
        if (org.apache.commons.lang.StringUtils.isNotEmpty(fileId)) {
            // 得到奖品图片id
            Attachment attachment = attachmentMapper.selectByPrimaryKey(fileId);
            if (null != attachment) {
                String arrUrl = attachment.getAttUrl().substring(1);
                String viewUrl = fileService.getViewPath(arrUrl).replace("\\", "/");
                return viewUrl;
            }
        }
        return null;
    }

    /**
     * 根据附件表ID删除文件
     *
     * @param id
     * @return
     */
    private int delFileByAttId(String id) {
        int count = 0;
        Attachment attachment = attachmentMapper.selectByPrimaryKey(id);
        if (attachment != null) {
            count += attachmentMapper.deleteByPrimaryKey(id);
            // 从文件服务器删除文件
//			fileServer.delete(attachment.getAttUrl());
        }
        return count;
    }

    /**
     * 获取推流拉流鉴权
     *
     * @param appName
     * @param streamName
     * @param checkLevel
     * @return
     */
    private String getLiveAuthInfo(String timestamp, String appName, String streamName, String checkLevel, byte[] streamKey) {
        String data = "$" + timestamp + "$" + appName + "/" + streamName + "$" + checkLevel;
        String msg = aesCbcEncrypt(data, ivBytes, streamKey);
        try {
            return URLEncoder.encode(msg, "UTF-8") + "." + bytesToHexString(ivBytes);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String aesCbcEncrypt(String data, byte[] ivBytes, byte[] key) {
        try {
            SecretKeySpec sk = new SecretKeySpec(key, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");

            if (ivBytes != null) {
                cipher.init(Cipher.ENCRYPT_MODE, sk, new IvParameterSpec(ivBytes));
            } else {
                cipher.init(Cipher.ENCRYPT_MODE, sk);
            }
            return Base64.encode(cipher.doFinal(data.getBytes("UTF-8")));
        } catch (Exception e) {
            return null;
        }
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if ((src == null) || (src.length <= 0)) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    @Async
    public void sendToNoAppointment(LiveInfoParamDto param) throws InterruptedException {
        //先查当前直播的预约人
        LiveSubscribeInfoDto liveSubscribeInfoDto = new LiveSubscribeInfoDto();
        List<String> lives = new ArrayList<>();
        lives.add(param.getLiveId());
        liveSubscribeInfoDto.setLiveIds(lives);
        List<LiveSubscribeInfoDto> liveSubscribeInfoDtos = liveSubscribeBizMapper.querySubscribeListInfo(liveSubscribeInfoDto);
        List<String> phones = new ArrayList<>(liveSubscribeInfoDtos.size());
        if (CollectionUtils.isNotEmpty(liveSubscribeInfoDtos)) {
            for (LiveSubscribeInfoDto dto : liveSubscribeInfoDtos) {
                if (StringUtils.isNotEmpty(dto.getSubscribeUserId())) {
                    phones.add(dto.getPhone());
                }
            }

            phones = phones.stream().map(str -> str.replaceAll("--", "")).collect(Collectors.toList());
            String content = param.getRemindContent();
            boolean sendMessage = false;
            if (CollectionUtils.isNotEmpty(phones)) {
                //logger.info("直播id：{}，发送短信获取符合条件人数：{}", param.getLiveId(), phones.size());
                sendMessage = sendMessage(phones, content);
            }
            if (sendMessage) {
                redisDao.setObjectWithExpire("sendMessage" + param.getLiveId(), "true", 60 * 60 * 1000);
                //logger.info("直播id：{}，发送短信人数：{}", param.getLiveId(), phones.size());
                //发送短信时间、内容、发送人、发送人数
                param.setMessageNum(String.valueOf(phones.size()));
                String allContent = JsonUtil.toJson(param);
                param.setAllContent(allContent);
                liveInfoMapper.insertLiveMessageRecord(param);
            } else {
                redisDao.setObjectWithExpire("sendMessage" + param.getLiveId(), "error", 60 * 60 * 1000);
            }
        } else {
            redisDao.setObjectWithExpire("sendMessage" + param.getLiveId(), "false", 60 * 60 * 1000);
        }
    }

    public String getSendMessageInfo(String liveId) {
        //查询是否发送了短信
        Object object = redisDao.getObject("sendMessage" + liveId);
        return (String) object;
    }

    @Async
    public void sendAppointmentNotWatch(LiveInfoParamDto param) {
        //查询预约未观看人员id
        boolean sendMessage = false;
        List<String> phoneList = liveInfoMapper.getAppointmentNotWatch(param);
        if (CollectionUtils.isNotEmpty(phoneList)) {
            sendMessage = sendMessage(phoneList, param.getRemindContent());
        }
        if (sendMessage) {
            param.setMessageNum(String.valueOf(phoneList.size()));
            String allContent = JsonUtil.toJson(param);
            param.setAllContent(allContent);
            liveInfoMapper.insertLiveMessageRecord(param);
        }
    }

    public boolean queryProgress(Long taskId) {
        ICredential auth = new BasicCredentials()
            .withAk(ak)
            .withSk(sk);

        OmsClient client = OmsClient.newBuilder()
            .withCredential(auth)
            .withRegion(OmsRegion.valueOf("cn-north-4"))
            .build();
        ShowTaskRequest request = new ShowTaskRequest();
        request.withTaskId(String.valueOf(taskId));
        try {
            ShowTaskResponse response = client.showTask(request);
            Double progress = response.getProgress();
            if (progress == 1) {
                return true;
            } else {
                int count = 1;
                while (count <= 30) {
                    Thread.sleep(5000);
                    response = client.showTask(request);
                    progress = response.getProgress();
                    if (progress == 1) {
                        return true;
                    }
                    count++;
                }
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    // 数据迁移
    public String dataMigration(String audioUrl, String videoName) {
        ICredential auth = new BasicCredentials()
            .withAk(ak)
            .withSk(sk);

        OmsClient client = OmsClient.newBuilder()
            .withCredential(auth)
            .withRegion(OmsRegion.valueOf(location4))
            .build();
        CreateTaskRequest request = new CreateTaskRequest();
        CreateTaskReq body = new CreateTaskReq();
        DstNodeReq dstNodebody = new DstNodeReq();
        dstNodebody.withAk(ak4)
            .withSk(sk4)
            .withBucket(bucketName4)
            .withSavePrefix("backed/")
            .withRegion(location4);
        List<String> listSrcNodeObjectKey = new ArrayList<>();
        String splitUrl = audioUrl.split(obsEndPoint + "/")[1];
        if (StringUtils.isEmpty(splitUrl)) {
            return "";
        }
        listSrcNodeObjectKey.add(splitUrl);
        SrcNodeReq srcNodebody = new SrcNodeReq();
        srcNodebody.withCloudType("HuaweiCloud")
            .withRegion(location)
            .withAk(ak)
            .withSk(sk)
            .withBucket(bucketName)
            .withObjectKey(listSrcNodeObjectKey);
        body.withDstNode(dstNodebody);
        body.withSrcNode(srcNodebody);
        body.withTaskType(CreateTaskReq.TaskTypeEnum.fromValue("prefix"));
        request.withBody(body);
        try {
            CreateTaskResponse response = client.createTask(request);
            Long id = response.getId();
            boolean flag = queryProgress(id);
            if (flag) {
                return "https://" + bucketName4 + "." + obsEndPoint4 + "/backed/" + "value/" + videoName + ".mp3";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String generateLiveSummaryDocumentByAudioUrl(String audioUrl, String videoName) throws InterruptedException {
        // 转码成功，开始录音文件识别
        if (audioUrl.indexOf(obsEndPoint4) == -1) {
            audioUrl = dataMigration(audioUrl, videoName);
        }
        if (StringUtils.isEmpty(audioUrl)) {
            return "";
        }
        logger.info("mp3音频文件开始进行数据迁移，" + audioUrl);
        redisDao.setObjectWithExpire("audioUrl-" +  "value/" + videoName , audioUrl,7 * 24 * 60 * 60 * 1000);
        // 将该音频文件进行数据迁移 北京1 -> 北京4
        Lasr lasr = new Lasr(ak4, sk4, location4, projectId4, audioUrl, "auto", "chinese_8k_common");
        // 开始识别
        AsrCustomLongResponse response = lasr.lasr(redisDao);
        String content = "";
        if (response != null) {
            logger.info("开始进行AI总结");
            StringBuilder sb = new StringBuilder();
            for (AsrcLongSentence asrcLongSentence : response.getSentenceList()) {
                // 获取识别内容
                int seconds = (int) (asrcLongSentence.getStartTime() / 1000);
                int hours = seconds / 3600;
                int minutes = (seconds % 3600) / 60;
                int remainingSeconds = seconds % 60;
                sb.append(String.format("%02d:%02d:%02d", hours, minutes, remainingSeconds) + " : " + asrcLongSentence.getResult().getText() + "\n");
            }

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);


            String url = apiBaseUrl + "summary/doSummary?access_token=" + getYdToken();
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("content", sb);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<Object> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<Object>() {
                });

            String key = "";
            Object responseBody = responseEntity.getBody();
            if (responseBody instanceof Map) {
                key = (String) ((Map<String, Object>) responseBody).get("result");
            } else if (responseBody instanceof String) {
                JsonResponse json = commonService.decrypt((String) responseBody);
                key = (String) json.getResult();
            }


            url = apiBaseUrl + "summary/getSummary?access_token=" + getYdToken();
            requestBody.put("key", key);
            boolean flag = false;
            int count = 1;
            do {
                if (count > 30) {
                    break;
                }
                Thread.sleep(10000);
                responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    new ParameterizedTypeReference<Object>() {
                    });
                responseBody = commonService.decrypt((String) responseEntity.getBody());
                if (!ObjectUtils.isEmpty(((JsonResponse) responseBody).getResult())) {
                    String result = String.valueOf(((JsonResponse) responseBody).getResult());
                    if (StringUtils.isNotEmpty(result)) {
                        content = result;
                        flag = true;
                    }
                }
                count++;
            } while (!flag);
        }
        return content;
    }

    @Async
    public void generateLiveSummaryDocument(LiveInfoDto liveInfoDto) throws InterruptedException {
        // 获取回放视频name
        String videoName = "";
        LiveInfoDto live = liveInfoMapper.getByLiveId(liveInfoDto);
        videoName = live.getVideoName();
        String videoUrl = live.getLiveVideoUrl();
        String fileType = videoUrl.substring(videoUrl.lastIndexOf(".") + 1, videoUrl.length()).toLowerCase();
        // 桶路径
        String path = "value/";
        boolean task = false;
        // 获取录音文件地址
        String audioUrl = (String) redisDao.getObject("audioUrl-" +  path + videoName);
        if (StringUtils.isNotEmpty(audioUrl)) {
            task = true;
        } else {
            // 发送转码请求
            task = sendTranscodingTask(path, videoName, fileType);
            if (task) {
                audioUrl = "https://" + bucketName + "." + obsEndPoint + "/" + path + videoName + ".mp3";
                // 保存录音文件地址，30天有效
                redisDao.setObjectWithExpire("audioUrl-" +  path + videoName , audioUrl,7 * 24 * 60 * 60 * 1000);
            }
        }
        if (task) {
            String content = generateLiveSummaryDocumentByAudioUrl(audioUrl, videoName);
            if (StringUtils.isNotEmpty(content)){
                logger.info("生成文档：" + content);
                // 把content内容生成为word文档
                // 创建一个新的Word文档对象
                XWPFDocument document = new XWPFDocument();
                // 添加一个段落
                XWPFParagraph paragraph = document.createParagraph();
                // 添加文本到段落中
                XWPFRun run = paragraph.createRun();
                run.setText(content);
                // 创建字节数组输出流
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

                List<Map<String, String>> filesInfo = new ArrayList<>();
                try {
                    // 将文档写入字节数组输出流
                    document.write(byteArrayOutputStream);

                    // 将字节数组输出流转换为字节数组
                    byte[] byteArray = byteArrayOutputStream.toByteArray();

                    // 创建一个MockMultipartFile对象
                    MultipartFile multipartFile = new MockMultipartFile("概要文档.docx","概要文档.docx","docx", byteArray);

                    List<MultipartFile> fileList = new ArrayList<>();
                    fileList.add(multipartFile);
                    filesInfo = fileService.saveTempFiles(fileList);
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    try {
                        byteArrayOutputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                if (CollectionUtils.isNotEmpty(filesInfo)) {
                    // 更新附件
                    for (Map<String, String> map : filesInfo) {
                        Attachment attachment = new Attachment();
                        attachment.setAttName(map.get("fileName"));
                        attachment.setAttUrl(map.get("filePath"));
                        attachment.setBusinessId(liveInfoDto.getLiveId());
                        attachment.setDocumentType(map.get("fileName").substring(map.get("fileName").lastIndexOf(".") + 1));
                        attachment.setSize(Long.valueOf(map.get("fileSize")));
                        attachment.setVersion("1");
                        attachment.setStatus("1");
                        attachmentMapper.insertSelective(attachment);
                    }
                }
            }
        }
    }

    /**
     * 发送转码请求
     *
     * @param path
     * @param videoName
     * @param fileType
     * @return
     */
    private boolean sendTranscodingTask(String path, String videoName, String fileType) {
        boolean flag = false;
        //设置转码输入视频地址
        ObsObjInfo input = new ObsObjInfo().withBucket(bucketName).withLocation(location).withObject(path + videoName + "." + fileType);
        //设置转码输出路径
        ObsObjInfo output = new ObsObjInfo().withBucket(bucketName).withLocation(location).withObject(path);
        //创建转码请求
        CreateTranscodingTaskRequest request
            = new CreateTranscodingTaskRequest().withBody(new CreateTranscodingReq()
            .withInput(input)
            .withOutput(output)
            //设置转码模板，预置模板Id可以在MPC console页面“全局设置 > 预置模板”上查看
            .withTransTemplateId(Collections.singletonList(7001057))
            //设置输出名称，名称个数需要与模板个数一一对应
            .withOutputFilenames(Collections.singletonList(videoName))
        );
        CreateTranscodingTaskResponse response = new CreateTranscodingTaskResponse();
        try {
            // 发送转码请求
            response = initMpcClient().createTranscodingTask(request);
            logger.info("CreateTranscodingTaskResponse=" + response);
        } catch (Exception e) {
            logger.error(e.toString());
        }

        // 查询转码任务
        if (!ObjectUtils.isEmpty(response.getTaskId())) {
            ListTranscodingTaskRequest req = new ListTranscodingTaskRequest();
            int count = 0;
            int maxPollingNums = 100;
            logger.info("开始进行转码任务");

            while (count < maxPollingNums) {
                count++;
                logger.info("转码：正在进行第" + count + "次查询");
                try {
                    //按TaskId查询任务，TaskId是转码请求响应中返回的任务ID
                    req = new ListTranscodingTaskRequest().withTaskId(Collections.singletonList(Long.valueOf(response.getTaskId())));
                    //发送请求
                    ListTranscodingTaskResponse listTranscodingTaskResponse = initMpcClient().listTranscodingTask(req);
                    if (CollectionUtils.isNotEmpty(listTranscodingTaskResponse.getTaskArray()) && listTranscodingTaskResponse.getTaskArray().size() > 0 && "SUCCEEDED".equals(listTranscodingTaskResponse.getTaskArray().get(0).getStatus().getValue())) {
                        //转码成功。开始进行录音文件识别
                        flag = true;
                        break;
                    }
                } catch (Exception e) {
                }
                try {
                    Thread.sleep(20000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return flag;
    }

    /**
     * 初始化 MpcClient
     *
     * @return
     */
    public MpcClient initMpcClient() {
        HttpConfig httpConfig = HttpConfig.getDefaultHttpConfig().withIgnoreSSLVerification(true).withTimeout(3);

        BasicCredentials auth = new BasicCredentials().withAk(ak).withSk(sk).withProjectId(projectId);
        return MpcClient.newBuilder()
            .withHttpConfig(httpConfig)
            .withCredential(auth)
            .withEndpoint(mpsEndPoint)
            .build();
    }


//	// 查询直播费用
//	public Map<String, Object> liveCost(LiveInfoParamDto liveInfoParamDto) {
//		String liveId = liveInfoParamDto.getLiveId();
//		//判断直播是否结束 未结束不查询
//		LiveInfoDto byLiveId = liveInfoMapper.getByLiveId(liveId);
//		Date end =   byLiveId.getLiveEndTime();
//		String startTime = byLiveId.getLiveBegTimeStr();
//		String endTime = byLiveId.getLiveEndTimeStr();
//		LocalDateTime endLocalDateTime = LocalDateTime.ofInstant(end.toInstant(), ZoneId.systemDefault());
//		//   endLocalDateTime和当前时间比较 直播结束后才查询
//		if (endLocalDateTime.isAfter(LocalDateTime.now())) {
//			return null;
//		}
//		//查询直播费用
//		try {
//			return ListUsersOfStreamSolution.
//					listUsersOfStream(liveId,startTime,endTime);
//		} catch (Exception e) {
//			//logger.error("直播id：{}，获取直播费用异常：{}", liveInfoParamDto.getLiveId(), e.getMessage());
//		}
//		return null;
//	}
}
