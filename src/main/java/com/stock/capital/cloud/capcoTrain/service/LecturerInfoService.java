package com.stock.capital.cloud.capcoTrain.service;

import com.stock.capital.cloud.capcoTrain.dao.LecturerInfoBizMapper;
import com.stock.capital.cloud.capcoTrain.dto.CapcoTrainTypeDto;
import com.stock.capital.cloud.capcoTrain.dto.LecturerInfoDto;
import com.stock.capital.cloud.common.dao.AttachmentMapper;
import com.stock.capital.cloud.common.model.entity.Attachment;
import com.stock.capital.cloud.common.service.FileService;
import com.stock.capital.cloud.dataExport.dto.OnDemandDto;
import com.stock.core.file.FileServer;
import com.stock.core.service.BaseService;
import com.thoughtworks.xstream.mapper.Mapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.Null;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
@Transactional(rollbackFor = {Exception.class})
public class LecturerInfoService extends BaseService {

    private Logger logger = LoggerFactory.getLogger(BaseService.class);

    @Autowired
    private LecturerInfoBizMapper lecturerInfoBizMapper;
    @Autowired
    private AttachmentMapper attachmentMapper;
    @Resource
    private FileService fileService;
    @Resource
    private FileServer fileServer;

    //删除讲师信息
    public int delTeachInfo (LecturerInfoDto lecturerInfoDto){
        int count = 0;
        count = lecturerInfoBizMapper.delTeachInfo(lecturerInfoDto);
        if(count == 1){
            lecturerInfoBizMapper.delHomepageTeacherInfo(lecturerInfoDto);
            lecturerInfoBizMapper.deleteCourseTeach(lecturerInfoDto);
        }
        return count;
    }

    //查询讲师信息
    public LecturerInfoDto queryTeachInfo (LecturerInfoDto lecturerInfoDto){
        return lecturerInfoBizMapper.queryTeachInfo(lecturerInfoDto);
    }

    private String uploadFiles (String files){
        // 头像图临时文件ID
        String teachPicTempId = "temp/" + files;
        // 将图片存入正式目录
        List<Map<String, String>> imgMaps =
                Optional.ofNullable(
                        fileService.saveOpenFileFromTemp(
                                "", "", Arrays.asList(teachPicTempId), true))
                        .orElse(new ArrayList<>());
        String teachPicId = "";
        for (Map<String, String> map : imgMaps) {
            if (map.containsKey(teachPicTempId)) {
                teachPicId = map.get(teachPicTempId);
            }
        }
        String teachPicViewUrl = null;
        // 保存头像图
        if (StringUtils.isNotEmpty(teachPicId)) {
            //teachPicViewUrl = getPictureViewUrl(teachPicId) + "?" + teachPicId;
            teachPicViewUrl = getPictureViewUrl(teachPicId);
        }
        return teachPicViewUrl;
    }
    //查询讲师关联课程
    public String getTeacherCourseList(LecturerInfoDto lecturerInfoDto){
        return lecturerInfoBizMapper.getTeacherCourseList(lecturerInfoDto);
    }

    //同步
    public List<String> getSynList(){
        return lecturerInfoBizMapper.getSynList();
    }

    //导出
    public InputStream export(LecturerInfoDto lecturerInfoDto) throws IOException {
        List<LecturerInfoDto> lecturerInfoDtoList = lecturerInfoBizMapper.queryTeachInfoList(lecturerInfoDto);
        // 设置Excel内容
        XSSFWorkbook workbook = exportCourseSetting(lecturerInfoDtoList);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.flush();
        os.close();
        return new ByteArrayInputStream(os.toByteArray());
    }
    //拆分sheet
    private XSSFWorkbook exportCourseSetting(List<LecturerInfoDto> lecturerInfoDtoList) {
        int batchSize = 10000; // 每个sheet表的数据量
        int totalSheets = (lecturerInfoDtoList.size() + batchSize - 1) / batchSize; // 表的数量

        XSSFWorkbook workbook = new XSSFWorkbook();

        List<List<LecturerInfoDto>> batches = new ArrayList<>();
        for (int i = 0; i < totalSheets; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, lecturerInfoDtoList.size());
            List<LecturerInfoDto> batch = lecturerInfoDtoList.subList(fromIndex, toIndex);
            batches.add(batch);
        }
        for (int i = 0; i < totalSheets; i++) {
            getCourseListWorkbook(workbook, batches.get(i), i + 1);
        }
        return workbook;
    }
    public XSSFSheet getCourseListWorkbook(XSSFWorkbook workbook, List<LecturerInfoDto> list, int sheetNumber) {
        XSSFSheet sheet = workbook.createSheet("讲师详细信息" + sheetNumber);
        XSSFRow row = null;
        row = sheet.createRow(0);
        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
        cellStyle.setWrapText(true);//自动换行
        // 设置字体
        Font f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setFontName("宋体");
        // 将字体赋给样式
        cellStyle.setFont(f);
        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);//自动换行
        // 制作标题行
        XSSFCell cell = null;
        LecturerInfoDto dto = null;
        // 居中
        CellStyle conCenterStyle = workbook.createCellStyle();
        conCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        conCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 居右
        CellStyle conRightStyle = workbook.createCellStyle();
        conRightStyle.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //居左
        CellStyle conLeftStyle = workbook.createCellStyle();
        conLeftStyle.setAlignment(HorizontalAlignment.LEFT);
        conLeftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置标题数组
        String[] titles = new String[]{"讲师名称","讲师性别", "所属机构","电话","邮箱", "邮寄地址", "业务方向", "行业方向","推荐理由","登记日期"};
        for (int i = 0; i < list.size(); i++) {
            dto = list.get(i);
            row = sheet.createRow(i + 1);
            //讲师名称
            cell = row.createCell(0);
            String teachName = dto.getTeachName();
            cell.setCellValue(teachName);
            cell.setCellStyle(cellStyle);
            //讲师性别
            cell = row.createCell(1);
            String teachSex = dto.getTeacherSex();
            cell.setCellValue(teachSex);
            cell.setCellStyle(cellStyle);
            //所属机构
            cell = row.createCell(2);
            String teachOrg = dto.getTeachOrg();
            cell.setCellValue(teachOrg);
            cell.setCellStyle(cellStyle);
            //电话
            cell = row.createCell(3);
            String phone = dto.getPhone();
            cell.setCellValue(phone);
            cell.setCellStyle(cellStyle);
            //邮箱
            cell = row.createCell(4);
            String email = dto.getEmail();
            cell.setCellValue(email);
            cell.setCellStyle(cellStyle);
            //邮寄地址
            cell = row.createCell(5);
            String address = dto.getAddress();
            cell.setCellValue(address);
            cell.setCellStyle(cellStyle);
            //业务方向
            cell = row.createCell(6);
            String businessDirection = dto.getBusinessDirection();
            String result = "";
            // 映射关系
            Map<String, String> directionMap = new HashMap<>();
            directionMap.put("001", "政策解读");directionMap.put("002", "公司治理");directionMap.put("003", "信息披露");directionMap.put("004", "投关管理");
            directionMap.put("005", "资本运作");directionMap.put("006", "合规管理");directionMap.put("007", "财务管理");directionMap.put("008", "其它");
            directionMap.put("009", "期货与风险管理");directionMap.put("010", "ESG");directionMap.put("011", "新闻宣传");directionMap.put("012", "人力资源管理");
            if(businessDirection != null && businessDirection != ""){
                String[] directionValues = businessDirection.split(",");
                List<String> directionNames = new ArrayList<>();
                for (String value : directionValues) {
                    String mappedValue = directionMap.get(value);
                    if (mappedValue != null) {
                        directionNames.add(mappedValue);
                    } else {
                        directionNames.add("");
                    }
                }
                result = String.join(",", directionNames);
            }
            cell.setCellValue(result);
            cell.setCellStyle(cellStyle);
            //行业
            cell = row.createCell(7);
            String industryDirection = dto.getIndustryDirection();
            cell.setCellValue(industryDirection);
            cell.setCellStyle(cellStyle);
            //推荐理由
            cell = row.createCell(8);
            String reason = dto.getReason();
            cell.setCellValue(reason);
            cell.setCellStyle(cellStyle);
            //登记时间
            cell = row.createCell(9);
            String registrationTime = dto.getRegistrationTime();
            cell.setCellValue(registrationTime);
            cell.setCellStyle(cellStyle);
            // 设置第一行标题
            row = sheet.createRow(0);
            for (int j = 0; j < titles.length; j++) {
                sheet.setDefaultColumnStyle(j, cs);
                sheet.setColumnWidth(j, 4000);
                cell = row.createCell(j);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(titles[j]);
            }
            sheet.setColumnWidth(0, 4000);
            sheet.setColumnWidth(1, 7000);
            sheet.setColumnWidth(2, 7000);
            sheet.setColumnWidth(3, 7000);
            sheet.setColumnWidth(4, 7000);
            sheet.setColumnWidth(5, 7000);
            sheet.setColumnWidth(6, 7000);
            sheet.setColumnWidth(7, 7000);
            sheet.setColumnWidth(8, 7000);
            sheet.setColumnWidth(9, 7000);
        }
        return sheet;
    }

    //保存讲师信息
    public int saveTeacherInfo (LecturerInfoDto lecturerInfoDto){
        int count = 0;
        lecturerInfoDto.setOrgId((String)getUserInfo().getInfo().get("orgId"));
        if(StringUtils.isEmpty(lecturerInfoDto.getBirthday())){
            lecturerInfoDto.setBirthday(null);
        }
        if(StringUtils.isEmpty(lecturerInfoDto.getId())){
            lecturerInfoDto.setCreateUserId(getUserInfo().getUserId());
            LecturerInfoDto insertTeachInfo = new LecturerInfoDto();
            BeanUtils.copyProperties(lecturerInfoDto, insertTeachInfo);
            //判断是否上传头像
            if(StringUtils.isNotEmpty(lecturerInfoDto.getTeachPic())){
                //图片路径操作
                insertTeachInfo.setTeachPic(lecturerInfoDto.getTeachPic().replace(lecturerInfoDto.getFilePath(), ""));
            }
            if(StringUtils.isNotEmpty(lecturerInfoDto.getNewTeachPic())){
                //图片路径操作
                insertTeachInfo.setNewTeachPic(lecturerInfoDto.getNewTeachPic());
            }
            count = lecturerInfoBizMapper.insertTeachInfo(insertTeachInfo);
        }
        else{
            lecturerInfoDto.setUpdateUserId(getUserInfo().getUserId());
            //判断是否上传头像
            if(StringUtils.isNotEmpty(lecturerInfoDto.getTeachPic())){
                //判断头像图片是否修改
                //未修改
                if(lecturerInfoDto.getUrl().equals(lecturerInfoDto.getTeachPic()) && lecturerInfoDto.getNewUrl().equals(lecturerInfoDto.getNewTeachPic())){
                    lecturerInfoDto.setTeachPic(lecturerInfoDto.getTeachPic().replace(lecturerInfoDto.getFilePath(), ""));
                    lecturerInfoDto.setNewTeachPic(lecturerInfoDto.getNewTeachPic().replace(lecturerInfoDto.getFilePath(), ""));
                    count = lecturerInfoBizMapper.updateTeachInfo(lecturerInfoDto);
                }else{//修改
                    LecturerInfoDto updateTeachInfo = new LecturerInfoDto();
                    lecturerInfoDto.setTeachPic(lecturerInfoDto.getTeachPic().replace(lecturerInfoDto.getFilePath(), ""));
                    lecturerInfoDto.setNewTeachPic(lecturerInfoDto.getNewTeachPic().replace(lecturerInfoDto.getFilePath(), ""));
                    BeanUtils.copyProperties(lecturerInfoDto, updateTeachInfo);
                    //更新数据
                    count = lecturerInfoBizMapper.updateTeachInfo(updateTeachInfo);
                }
            }else{
                LecturerInfoDto updateTeachInfo = new LecturerInfoDto();
                BeanUtils.copyProperties(lecturerInfoDto, updateTeachInfo);
                //更新数据
                count = lecturerInfoBizMapper.updateTeachInfo(updateTeachInfo);
            }
        }
        return count;
    }

    /**
     * 根据附件表ID删除文件
     *
     * @param id
     * @return
     */
    private int delFileByAttId(String id) {
        int count = 0;
        Attachment attachment = attachmentMapper.selectByPrimaryKey(id);
        if (attachment != null) {
            count += attachmentMapper.deleteByPrimaryKey(id);
            // 从文件服务器删除文件
            fileServer.delete(attachment.getAttUrl());
        }
        return count;
    }

    /** 获取图片链接 */
    private String getPictureViewUrl(String fileId) {
        if (org.apache.commons.lang.StringUtils.isNotEmpty(fileId)) {
            // 得到奖品图片id
            Attachment attachment = attachmentMapper.selectByPrimaryKey(fileId);
            if (null != attachment) {
                String arrUrl = attachment.getAttUrl().substring(1);
                String viewUrl = fileService.getViewPath(arrUrl).replace("\\", "/");
                return viewUrl;
            }
        }
        return null;
    }



    /**
     * 查询讲师列表
     * @param lecturerInfoDto
     * @return
     */
    public List<LecturerInfoDto> queryTeachInfoList(LecturerInfoDto lecturerInfoDto) {
        lecturerInfoDto.setOrgId((String) getUserInfo().getInfo().get("orgId"));
        return lecturerInfoBizMapper.queryTeachInfoList(lecturerInfoDto);
    }

    /**
     * 新增直播页面讲师下拉
     * @param lecturerInfoDto
     * @return
     */
    public List<LecturerInfoDto> newTeacherList(LecturerInfoDto lecturerInfoDto) {
        lecturerInfoDto.setOrgId((String) getUserInfo().getInfo().get("orgId"));
        return lecturerInfoBizMapper.newTeacherList(lecturerInfoDto);
    }

    public List<Map<String, String>> getCodeListByCodeNo(String type) {
        CapcoTrainTypeDto capcoTrainTypeDto = new CapcoTrainTypeDto();
        capcoTrainTypeDto.setOrgId((String) getUserInfo().getInfo().get("orgId"));
        capcoTrainTypeDto.setType(type);
        return lecturerInfoBizMapper.getSchooliveOrg(capcoTrainTypeDto);
    }

    public List<Map<String,String>> getBusinessDirectionList(){
        return lecturerInfoBizMapper.getBusinessDirectionList();
    }

    public List<Map<String, String>> saveOrgName(LecturerInfoDto dto){
        if (dto.getOrgList()!=null){
            for (LecturerInfoDto org:dto.getOrgList()){

                if (StringUtils.isEmpty(org.getId())){
                    if(StringUtils.isNotEmpty(org.getTypeValue())){
                        //新增
                        org.setSortNo(org.getTypeValue());
                        org.setOrgId((String)getUserInfo().getInfo().get("orgId"));
                        org.setType(dto.getType());
                        if (Integer.valueOf(org.getTypeValue())<10){
                            org.setTypeValue("0"+org.getTypeValue());
                        }
                        org.setCreateUserId(getUserInfo().getUserId());
                        org.setUpdateUserId(getUserInfo().getUserId());
                        lecturerInfoBizMapper.addOrgName(org);
                    }
                }else {
                    //更新
                    org.setUpdateUserId(getUserInfo().getUserId());
                    lecturerInfoBizMapper.updateOrgName(org);
                }
            }
        }
        //更新缓存
//        Map<String, String> codeMap = null;
//        List<Map<String, String>> codeList = Lists.newArrayList();
//        CodeExample codeExample = new CodeExample();
//        codeExample.createCriteria().andCodeNoEqualTo("EB_SCHOOL_LIVE_ORG");
//        List<Code> list = codeMapper.selectByExample(codeExample);
//        if (list != null && list.size() > 0) {
//            for (Code code : list) {
//                codeMap = new HashMap<String, String>();
//                codeMap.put("code_no", "EB_SCHOOL_LIVE_ORG");
//                codeMap.put("code_desc", code.getCodeDesc());
//                codeMap.put("code_value", code.getCodeValue());
//                codeMap.put("code_name", code.getCodeName());
//                codeMap.put("code_type", code.getCodeType());
//                codeMap.put("valid_flag", code.getValidFlag());
//                codeMap.put("version", code.getVersion());
//                codeList.add(codeMap);
//            }
//            refreshCodeListByCodeNo("EB_SCHOOL_LIVE_ORG", codeList);
//        }
        return getCodeListByCodeNo(dto.getType());
    }
}
