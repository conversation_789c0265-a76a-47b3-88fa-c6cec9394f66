package com.stock.capital.cloud.capcoTrain.dao;

import com.stock.capital.cloud.capcoTrain.dto.CapcoTrainTypeDto;
import com.stock.capital.cloud.capcoTrain.dto.LecturerInfoDto;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/18 14:45
 */
public interface LecturerInfoBizMapper {

    //查询讲师列表
    List<LecturerInfoDto> queryTeachInfoList(LecturerInfoDto lecturerInfoDto);

    //新增直播页面讲师下拉
    List<LecturerInfoDto> newTeacherList(LecturerInfoDto lecturerInfoDto);

    //查询师资库标签列表
    List<LecturerInfoDto> queryTeachInfoLabelList(LecturerInfoDto lecturerInfoDto);

    //新增讲师信息
    int insertTeachInfo (LecturerInfoDto lecturerInfoDto);

    //更新讲师信息
    int updateTeachInfo (LecturerInfoDto lecturerInfoDto);

    //删除讲师信息
    int delTeachInfo (LecturerInfoDto lecturerInfoDto);

    void deleteCourseTeach (LecturerInfoDto lecturerInfoDto);

    //删除首页配置讲师关联表
    int delHomepageTeacherInfo (LecturerInfoDto lecturerInfoDto);

    //查询讲师信息
    LecturerInfoDto queryTeachInfo (LecturerInfoDto lecturerInfoDto);

    List<Map<String, String>> getSchooliveOrg(CapcoTrainTypeDto capcoTrainTypeDto);

    List<Map<String,String>> getBusinessDirectionList();

    List<Map<String, String>> getCourseList(@Param("orgId") String orgId);

    List<Map<String, String>> getBusinessList();

    String getTeacherCourseList(LecturerInfoDto lecturerInfoDto);

    List<String> getSynList();

    void updateIfOnce(String id);

    void addOrgName(LecturerInfoDto dto);

    void updateOrgName(LecturerInfoDto dto);

    void deleteOrg(CapcoTrainTypeDto capcoTrainTypeDto);

    String getValueInfo(CapcoTrainTypeDto capcoTrainTypeDto);
}
