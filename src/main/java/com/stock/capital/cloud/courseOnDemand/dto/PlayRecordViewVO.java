package com.stock.capital.cloud.courseOnDemand.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查看页 VO
 * @date 2022/12/13 15:23
 */
public class PlayRecordViewVO implements Serializable {
    /**
     * @description 课程id
     * @date 2022/12/13 15:58
     */
    private String courseId;
    /**
     * @description 用户id
     * @date 2022/12/13 15:44
     */
    private String userId;
    /**
     * @description 用户姓名
     * @date 2022/12/13 15:26
     */
    private String realName;
    /**
     * @description 用户类型
     * @date 2022/12/13 15:26
     */
    private String personType;
    /**
     * @description 证券代码
     * @date 2022/12/13 15:26
     */
    private String companyCode;
    /**
     * @description 公司名称/学校
     * @date 2022/12/13 15:26
     */
    private String companyName;
    /**
     * @description 是否完成观看,0未完成，1已完成
     * @date 2022/12/13 15:26
     */
    private String completeFlag;
    /**
     * @description 观看开始时间(创建时间)
     * @date 2022/12/13 15:26
     */
    private String createTime;
    /**
     * @description 辖区
     * @date 2022/12/14 17:28
     */
    private String belongCommission;
    /**
     * @description 辖区List
     * @date 2022/12/14 17:28
     */
    private List<String> belongCommissionList;

    public String getBelongCommission() {
        return belongCommission;
    }

    public void setBelongCommission(String belongCommission) {
        this.belongCommission = belongCommission;
    }

    public List<String> getBelongCommissionList() {
        return belongCommissionList;
    }

    public void setBelongCommissionList(List<String> belongCommissionList) {
        this.belongCommissionList = belongCommissionList;
    }

    public String getCompleteFlag() {
        return completeFlag;
    }

    public void setCompleteFlag(String completeFlag) {
        this.completeFlag = completeFlag;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Override
    public String toString() {
        return "PlayRecordViewVO{" +
                "courseId='" + courseId + '\'' +
                ", userId='" + userId + '\'' +
                ", realName='" + realName + '\'' +
                ", personType='" + personType + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", companyName='" + companyName + '\'' +
                ", completeFlag='" + completeFlag + '\'' +
                ", createTime='" + createTime + '\'' +
                ", belongCommission='" + belongCommission + '\'' +
                ", belongCommissionList=" + belongCommissionList +
                '}';
    }
}
