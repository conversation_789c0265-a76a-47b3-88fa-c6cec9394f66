package com.stock.capital.cloud.userCredit.controller;

import com.google.common.collect.Maps;
import com.stock.capital.cloud.common.controller.CommonController;
import com.stock.capital.cloud.onDemandCourse.dto.OnDemandCourseDto;
import com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper;
import com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto;
import com.stock.capital.cloud.trainUserManage.service.TrainUserManageService;
import com.stock.capital.cloud.userCredit.dto.CreditRecordDto;
import com.stock.capital.cloud.userCredit.dto.ExchangeReviewDto;
import com.stock.capital.cloud.userCredit.service.UserCreditService;
import com.stock.core.dto.JsonResponse;
import com.stock.core.util.JsonUtil;
import com.stock.core.web.DownloadView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping(value = "userCredit")
public class UserCreditController extends CommonController {

    @Autowired
    private TrainUserManageService trainUserManageService;

    @Autowired
    private UserCreditService userCreditService;

    @Autowired
    private TrainUserManageBizMapper trainUserManageBizMapper;

    @RequestMapping(value = "openUserCreditManage")
    public ModelAndView blackListInit(){
        ModelAndView mv = new ModelAndView("userCredit/userCreditManage");
        return mv;
    }
    @RequestMapping(value = "userCreditInit")
    public ModelAndView userCreditInit(){
        ModelAndView mv = new ModelAndView("userCredit/userCreditInit");
        mv.addObject("creditRecordDto", new CreditRecordDto());
        mv.addObject("courseCreditTypeList", JsonUtil.toJson(trainUserManageService.personTypeListA("COURSE_CREDIT_TYPE")));
        mv.addObject("postTypeList", JsonUtil.toJson(trainUserManageBizMapper.personTypeList("PERSON_POST")));
        //课程名称
        mv.addObject("courseSelectList",JsonUtil.toJson(trainUserManageService.getCourseSelect()));
        return mv;
    }

    //列表页初始化数据
    @RequestMapping(value = "getUserCreditList")
    @ResponseBody
    public Map<String, Object> getUserCreditList(CreditRecordDto creditRecordDto){
        Map<String, Object> info = Maps.newHashMap();
        processingParam(creditRecordDto);
        info = super.commonQuery("com.stock.capital.cloud.userCredit.dao.UserCreditMapper.getUserCriditList", creditRecordDto);
        return info;
    }

    @RequestMapping(value = "openUserCreditDetails")
    public ModelAndView openUserCreditDetails(CreditRecordDto creditRecordDto){
        processingParam(creditRecordDto);
        ModelAndView mv = new ModelAndView("userCredit/userCreditDetails");
        CreditRecordDto dto =  new CreditRecordDto();
        dto = userCreditService.getUserCreditDetails(creditRecordDto);
        mv.addObject("creditRecordDto", dto);
        mv.addObject("courseCreditTypeList", JsonUtil.toJson(trainUserManageService.personTypeListA("COURSE_CREDIT_TYPE")));
        return mv;
    }

    @RequestMapping(value = "getUserCreditDetails")
    @ResponseBody
    public JsonResponse<CreditRecordDto> getUserCreditDetails(@RequestBody CreditRecordDto creditRecordDto){
        processingParam(creditRecordDto);
        JsonResponse<CreditRecordDto> response = new JsonResponse<>();
        response.setResult(userCreditService.getUserCreditDetails(creditRecordDto));
        return response;
    }

    //处理参数
    public void processingParam(CreditRecordDto creditRecordDto){
        if(StringUtils.isNotEmpty(creditRecordDto.getPostTypes())){
            creditRecordDto.setPostTypeList(Arrays.asList(creditRecordDto.getPostTypes().split(",")));
        }
        if (StringUtils.isNotEmpty(creditRecordDto.getCourseCreditTypes())){
            creditRecordDto.setCourseCreditTypeList(Arrays.asList(creditRecordDto.getCourseCreditTypes().split(",")));
        }
        if (StringUtils.isNotEmpty(creditRecordDto.getObtainingCreditTimeStr())){
            String[] time = creditRecordDto.getObtainingCreditTimeStr().split(" 至 ");
            creditRecordDto.setObtainingCreditBegTime(time[0]);
            creditRecordDto.setObtainingCreditEndTime(time[1]);
        }
    }

    /**
     * 用户信息导出
     */
    @RequestMapping(value = "exportUserCredit")
    @ResponseBody
    public ModelAndView exportUserCredit (CreditRecordDto dto) throws IOException, ClassNotFoundException {
        ModelAndView mv = new ModelAndView();
        processingParam(dto);
        mv.setView(new DownloadView());
        mv.addObject(DownloadView.EXPORT_FILE, userCreditService.exportUserCredit(dto));
        mv.addObject(DownloadView.EXPORT_FILE_NAME, "用户学分统计.xlsx");
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
        return mv;
    }
    /**
     * 用户信息详情导出
     */
    @RequestMapping(value = "exportUserCreditDetails")
    @ResponseBody
    public ModelAndView exportUserCreditDetails (CreditRecordDto dto) throws IOException, ClassNotFoundException {
        ModelAndView mv = new ModelAndView();
        processingParam(dto);
        mv.setView(new DownloadView());
        mv.addObject(DownloadView.EXPORT_FILE, userCreditService.exportUserCreditDetails(dto));
        mv.addObject(DownloadView.EXPORT_FILE_NAME, "用户学分详情统计.xlsx");
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
        return mv;
    }

    /**
     * 打开管理员审核页面
     */
     @RequestMapping(value = "openUserAdminAudit")
     public ModelAndView openUserAdminAudit(){
         ModelAndView mv = new ModelAndView("userCredit/userAdminAuditManage");
         return mv;
     }

    /**
     * 打开管理员审核页面
     */
     @RequestMapping(value = "userAdminAuditInit")
     public ModelAndView userAdminAuditInit(){
         ModelAndView mv = new ModelAndView("userCredit/userAdminAuditInit");
         mv.addObject("schUserInfoDto", new SchUserInfoDto());
         mv.addObject("auditSchUserInfoDto", new SchUserInfoDto());
         //TODO 这个地方用公共的方法
         mv.addObject("adminAuditStatusList", JsonUtil.toJson(trainUserManageService.personTypeListA("ADMIN_AUDIT_STATUS")));
         mv.addObject("adminAuditStatusMap", trainUserManageService.personTypeListA("ADMIN_AUDIT_STATUS"));
         mv.addObject("postList", JsonUtil.toJson(trainUserManageBizMapper.personTypeList("PERSON_POST")));
         return mv;
     }

    /**
     * 管理员审核页面初始化
     */
    @RequestMapping(value = "getAdminAuditList")
    @ResponseBody
    public Map<String, Object> getAdminAuditList(SchUserInfoDto dto){
        if(!StringUtils.isEmpty(dto.getPost())){
            dto.setPostList(Arrays.asList(dto.getPost().split(",")));
        }
        if(!StringUtils.isEmpty(dto.getAdminAuditStr())){
            dto.setAdminAuditList(Arrays.asList(dto.getAdminAuditStr().split(",")));
        }
        Map<String, Object> info = new HashMap<String, Object>();
        info = super.commonQuery("com.stock.capital.cloud.userCredit.dao.UserCreditMapper.getAdminAuditList", dto);
        return info;
    }

    /**
     * 修改用户管理员状态
     */
    @RequestMapping(value = "userAdminAudit")
    @ResponseBody
    public JsonResponse<Boolean> userAdminAudit(SchUserInfoDto dto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        Integer num = userCreditService.userAdminAudit(dto);
        if (num>0){
            response.setResult(true);
        }else{
             response.setResult(false);
        }
        return response;
    }
}
