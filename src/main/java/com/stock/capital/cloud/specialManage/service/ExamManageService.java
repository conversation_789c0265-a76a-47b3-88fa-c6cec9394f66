package com.stock.capital.cloud.specialManage.service;

import com.github.houbb.paradise.common.util.ObjectUtil;
import com.stock.capital.cloud.common.dao.AttachmentMapper;
import com.stock.capital.cloud.common.model.entity.Attachment;
import com.stock.capital.cloud.common.service.CommonService;
import com.stock.capital.cloud.common.service.FileService;
import com.stock.capital.cloud.specialManage.dao.ExamManageBizMapper;
import com.stock.capital.cloud.specialManage.dto.*;
import com.stock.core.dto.JsonResponse;
import com.stock.core.misc.ExcelHandle;
import com.stock.core.rest.RestClient;
import com.stock.core.service.BaseService;
import com.stock.core.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.POIXMLDocumentPart;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = {Exception.class})
public class ExamManageService extends BaseService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ExamManageBizMapper examManageBizMapper;

    @Autowired
    private RestClient restClient;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    ExcelHandle excelHandle;

    @Autowired
    private FileService fileService;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Value("#{app['exam.baseUrl']}")
    private String examBaseUrl;

    private final Logger logger = LoggerFactory.getLogger(ExamManageService.class);

    public ExamDto getExamInfo(ExamDto examDto){
        return examManageBizMapper.getExamInfo(examDto);
    }


    public ExamDto saveExam(ExamDto examDto){
        if (StringUtils.isEmpty(examDto.getId())){//新增
            //保存考核基本信息
            examDto.setOrgId((String) getUserInfo().getInfo().get("orgId"));
            examDto.setCreateUser(getUserInfo().getUserId());
            examDto.setReleaseFlag("0");
            examManageBizMapper.insertExamInfo(examDto);
        }else {//编辑
            updateExamInfo(examDto);
        }
        if (StringUtils.isNotEmpty(examDto.getExamRanks())){
            //删除考核关联试卷
//            examManageBizMapper.deleteExamRank(examDto.getId());
            ParameterizedTypeReference<List<ExamRankDto>> type = new ParameterizedTypeReference<List<ExamRankDto>>() {
            };
            List<ExamRankDto> examRankDtoList = JsonUtil.fromJson(examDto.getExamRanks(), type);
            for (ExamRankDto dto : examRankDtoList){
                dto.setCreateUser(getUserInfo().getUserId());
                dto.setExamId(examDto.getId());
                if (StringUtils.isNotEmpty(dto.getAnswerInterval())){
                    dto.setAnswerInterval(String.valueOf(Float.parseFloat(dto.getAnswerInterval())*24));
                }
            }
            //插入考核关联试卷
            if (!CollectionUtils.isEmpty(examRankDtoList)){
                examManageBizMapper.insertExamRankInfo(examRankDtoList);
            }
        }
        return examDto;
    }

    public void updateExamInfo(ExamDto examDto){
        examDto.setUpdateUser(getUserInfo().getUserId());
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sf.format(new Date());
        examDto.setUpdateTime(date);
        examManageBizMapper.updateExamInfo(examDto);
    }

    public Map<String, Object> selectPaperList(PaperDto paperDto){
        //查询考试适用机构（适用机构不同，所属试卷也不同）
        ExamDto examDto = new ExamDto();
        examDto.setId(paperDto.getId());
//        ExamDto examInfo = examManageBizMapper.getExamInfo(examDto);
//        if ("111111".equals(examInfo.getExamType()) || "222222".equals(examInfo.getExamType())){
//            paperDto.setUserSource("1");
//        }else {
//            paperDto.setUserSource(examInfo.getExamType()+"exchange");
//        }
//        paperDto.setUserSource("1");
        ParameterizedTypeReference<String> responseType = new ParameterizedTypeReference<String>() {
        };
        HttpHeaders headers = new HttpHeaders();
        String url = examBaseUrl + "exam/examinationPaperManagement/getCapcoExamList";
        paperDto.setStatus("1");
        paperDto.setBelongingSystem("1");
        String responseStr = restTemplate.exchange(url, HttpMethod.POST,new HttpEntity<>(paperDto,headers),responseType).getBody();
        //解密成字符串
        responseStr = responseStr.replaceAll("\"","");
        JsonResponse json = commonService.decrypt(responseStr);
        Object object = json.getResult();
        Map<String,Object> map = (Map<String,Object>)object;
        Map<String, Object> info = new HashMap<String, Object>();
        info.put("recordsFiltered",map.get("totalCount"));
        info.put("data",map.get("data"));
        info.put("recordsTotal",map.get("totalCount"));
        return info;
    }

    public String getCourseHours(ExamDto examDto){
        if (StringUtils.isNotEmpty(examDto.getCourseType())){
            List<String> courseTypeList = Arrays.asList(examDto.getCourseType().split(","));
            examDto.setCourseTypeList(courseTypeList);
            String courseHours = examManageBizMapper.getCourseHours(examDto);
            return courseHours;
        }else {
            return "0";
        }
    }

    public void deleteExamRank(ExamDto examDto){
        examManageBizMapper.updateExamRank(examDto);
    }

    /**
     * <AUTHOR>
     * @description 删除一条关联人员
     * @date 2023/3/1 9:23
     */
    @Transactional(rollbackFor = {Exception.class})
    public int deleteRelevancePerson(ExamSpecialPersonDTO examSpecialPersonDTO) {
        // 删除关联人员的时候，同时将capco_separate_rooms里分配的人员也进行删除
        examSpecialPersonDTO = examManageBizMapper.selectRelevancePerson(examSpecialPersonDTO);
        examManageBizMapper.deleteSeparateRoom(examSpecialPersonDTO);
        return examManageBizMapper.updateRelevancePerson(examSpecialPersonDTO);
    }

    /**
     * <AUTHOR>
     * @description 批量新增关联人员
     * @date 2023/3/3 13:38
     */
    public Integer insertRelevancePerson(ExamSpecialPersonDTO examSpecialPersonDTO) {
        List<String> selectIdList = examSpecialPersonDTO.getSelectedIdList();
        List<ExamSpecialPersonDTO> examSpecialPersonDTOList = new ArrayList<>();
        List<String> alreadyExistPersonIdList = getAlreadyExistPersonIdList(examSpecialPersonDTO);
        selectIdList.forEach(id -> {
            if (alreadyExistPersonIdList.contains(id)) {
                return;
            }
            ExamSpecialPersonDTO insertDTO = new ExamSpecialPersonDTO();
            BeanUtils.copyProperties(examSpecialPersonDTO, insertDTO);
            insertDTO.setCreateUser(getUserInfo().getUserId());
            insertDTO.setUserId(id);
            examSpecialPersonDTOList.add(insertDTO);
        });
        if (examSpecialPersonDTOList.size() == 0) {
            return -1;
        }
        distriRoomId(examSpecialPersonDTOList,examSpecialPersonDTO.getExamRankUnifyId());
        return examManageBizMapper.insertRelevancePerson(examSpecialPersonDTOList);
    }

    /**
     * 分配房间号
     */
    public void distriRoomId(List<ExamSpecialPersonDTO> personList, String roomId) {
        SeparateRoom separateRoom = new SeparateRoom();
        separateRoom.setRoomId(roomId);
        /**
         * temp<String,Integer>,分别为分房间id号，和待补足人数
         */
        Map<String,Integer> temp = new HashMap<>();
        List<SeparateRoom> separateRooms = examManageBizMapper.selectSeparateRooms(separateRoom,CommonService.MAX_ROOM_PERSON_NUMBER);
        if (!ObjectUtils.isEmpty(separateRooms)) {
            // 取未满足最大房间人数的房间id和人数
            separateRooms.forEach(e -> {
                temp.put(e.getSeparateRoomId(),CommonService.MAX_ROOM_PERSON_NUMBER - e.getCount());
            });
        }
        List<SeparateRoom> result = new ArrayList<>();
        int index = 0; // personList的索引
        for(String key : temp.keySet()) {
            // 循环未满足最大房间人数的房间id，并补足
            Integer value = temp.get(key);
            for (Integer i = 0; i < value; i++) {
                SeparateRoom dto = new SeparateRoom();
                dto.setRoomId(roomId);
                dto.setSeparateRoomId(key);
                dto.setUserId(personList.get(index).getUserId());
                result.add(dto);
                index++;
                if (index == personList.size()) {
                    break;
                }
            }
            if (index == personList.size()) {
                break;
            }
        }
        int beleft = personList.size() - index;
        if (beleft > 0) {
            List<String> uuid = commonService.getUUID(beleft / CommonService.MAX_ROOM_PERSON_NUMBER + 1);
            personList = personList.subList(index, personList.size());
            // 如果补全房间后还有剩下的人数未添加
            int flag = 0;
            for (ExamSpecialPersonDTO e : personList) {
                SeparateRoom dto = new SeparateRoom();
                dto.setRoomId(roomId);
                dto.setSeparateRoomId(uuid.get(flag / CommonService.MAX_ROOM_PERSON_NUMBER));
                dto.setUserId(e.getUserId());
                result.add(dto);
                flag++;
            }
        }
        if (!ObjectUtils.isEmpty(result)) {
            examManageBizMapper.insertSeparateRooms(result);
        }
    }


    /**
     * @description 获取已存在的关联人员id List，即capco_exam_special_person里的idList
     * @date 2023/3/15 14:34
     */
    private List<String> getAlreadyExistPersonIdList(ExamSpecialPersonDTO examSpecialPersonDTO) {
        return examManageBizMapper
                .queryRelevancePersonList(examSpecialPersonDTO)
                .stream()
                .map(ExamSpecialPersonVO::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @description 通过examRankId查询统一考试list
     * @date 2023/3/6 16:24
     */
    public List<ExamRankUnifyMapDTO> queryUnifyExamList(ExamRankUnifyMapDTO examRankUnifyMapDTO) {
        List<ExamRankUnifyMapDTO> examRankUnifyMapDTOList = examManageBizMapper.queryUnifyExamList(examRankUnifyMapDTO);
        //排序,并把sort赋值
        for (int i = 0; i < examRankUnifyMapDTOList.size(); i++) {
            examRankUnifyMapDTOList.get(i).setSort(i+1);
        }
        return examRankUnifyMapDTOList;
    }

    /**
     * <AUTHOR>
     * @description 删除一条统一考试
     * @date 2023/3/7 10:21
     */
    public Integer deleteUnifyExam(ExamRankUnifyMapDTO examRankUnifyMapDTO) {
        return examManageBizMapper.updateUnifyExamStatus(examRankUnifyMapDTO);
    }

    /**
     * <AUTHOR>
     * @description 选中考试之后 update考试名称和id
     * @date 2023/3/7 15:27
     */
    public Integer updateUnifyExamPaper(ExamRankUnifyMapDTO examRankUnifyMapDTO) {
        return examManageBizMapper.updateUnifyExamPaper(examRankUnifyMapDTO);
    }

    /**
     * <AUTHOR>
     * @description 保存新增/编辑统一考试
     * @date 2023/3/8 9:23
     */
    public Integer saveUnifyExamEdit(ExamRankUnifyMapDTO examRankUnifyMapDTO) {
        examRankUnifyMapDTO.setCreateUser(getUserInfo().getUserId());
        int line;
        if (StringUtils.isNotBlank(examRankUnifyMapDTO.getId())) {
            line = examManageBizMapper.updateUnifyExamPaper(examRankUnifyMapDTO);
            return line;
        }
        line = examManageBizMapper.insertUnifyExam(examRankUnifyMapDTO);
        //新建统一考试，插入统一考试id至exam库ex_hrtc_room_info表的room_id
        examRankUnifyMapDTO.setFrom("1");
        String url = examBaseUrl + "exam/rtcRoom/insertRoomInfo";
        String result = restTemplate.exchange(
                url
                , HttpMethod.POST
                , new HttpEntity<>(examRankUnifyMapDTO, new HttpHeaders())
                , new ParameterizedTypeReference<String>() {
                }
        ).getBody();
        result = result.replaceAll("\"","");
        JsonResponse<?> json = commonService.decrypt(result);
        json.getResult();
        return line;
    }

    /**
     * <AUTHOR>
     * @description 通过统一考试id获取统一考试信息
     * @date 2023/3/8 15:45
     */
    public ExamRankUnifyMapDTO selectUnifyExamById(ExamRankUnifyMapDTO examRankUnifyMapDTO) {
        ExamRankUnifyMapDTO unifyExam = examManageBizMapper.examRankUnifyMapDTO(examRankUnifyMapDTO);
        return Optional.ofNullable(unifyExam).orElseGet(()-> new ExamRankUnifyMapDTO() {{
            setExamRankId(examRankUnifyMapDTO.getExamRankId());
        }});
    }
    /**
     * @description 导出关联人员excel
     * @date 2023/3/10 17:18
     */
    public ByteArrayInputStream exportRelevancePerson(ExamSpecialPersonDTO examSpecialPersonDTO) throws IOException {
        SXSSFWorkbook workbook;
        // 设置Excel内容
        workbook = getRelevancePersonWorkbook(examSpecialPersonDTO);
        java.io.ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.flush();
        os.close();
        return new ByteArrayInputStream(os.toByteArray());
    }
    /**
     * @description 导出关联人员
     * @date 2023/3/10 17:18
     */
    @SuppressWarnings("all")
    private SXSSFWorkbook getRelevancePersonWorkbook(ExamSpecialPersonDTO examSpecialPersonDTO) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet("Sheet1");
        SXSSFRow row = null;
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        Font titleF = workbook.createFont();
        titleF.setFontHeightInPoints((short) 14);
        titleF.setBold(true);
        titleF.setFontName("宋体");
        cellStyle.setFont(titleF);
        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);
        SXSSFCell cell = null;
        cell = sheet.createRow(0).createCell(0);

        Font conF = workbook.createFont();
        conF.setFontHeightInPoints((short) 12);
        conF.setFontName("宋体");
        // 内容居中
        CellStyle conCenterStyle = workbook.createCellStyle();
        conCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        conCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        conCenterStyle.setFont(conF);
        // 内容居左
        CellStyle conLeftStyle = workbook.createCellStyle();
        conLeftStyle.setAlignment(HorizontalAlignment.LEFT);
        conLeftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        conCenterStyle.setFont(conF);
        // 内容居右
        CellStyle conRightStyle = workbook.createCellStyle();
        conRightStyle.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        conCenterStyle.setFont(conF);

        List<ExamSpecialPersonVO> resultList = examManageBizMapper.queryRelevancePersonList(examSpecialPersonDTO);
        String[] titles = {"姓名", "手机号", "证券代码", "公司简称", "考试状态", "证书状态", "交卷时间", "证书下发时间", "作答时间", "分数"};
        if (StringUtils.isBlank(examSpecialPersonDTO.getExamRankUnifyId())){
            titles = new String[]{"姓名", "手机号"};
        }
        // 设置特殊标题
        row = sheet.createRow(0);
        row.setHeight((short) 500);
        for (int i = 0; i < titles.length; i++) {
            sheet.setDefaultColumnStyle(i, cs);
            if (i == 0) {
                sheet.setColumnWidth(0, 6000);
            } else {
                sheet.setColumnWidth(i, 6000);
            }
            cell = row.createCell(i);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(titles[i]);
        }
        if (StringUtils.isBlank(examSpecialPersonDTO.getExamRankUnifyId())){
            return workbook;
        }
        //设置内容
        if (resultList.size() > 0) {
            for (int i = 0; i < resultList.size(); i++) {
                row = sheet.createRow(i + 1);
                row.setHeight((short) 500);
                extracted(row, conCenterStyle, resultList, i);
            }
        }
        return workbook;
    }
    @SuppressWarnings("all")
    private void extracted(SXSSFRow row, CellStyle conCenterStyle, List<ExamSpecialPersonVO> resultList, int i) {
        SXSSFCell cell;
        //姓名
        cell = row.createCell(0);
        cell.setCellValue(getCellValue(resultList.get(i).getRealName()));
        cell.setCellStyle(conCenterStyle);

        //手机号
        cell = row.createCell(1);
        cell.setCellValue(getCellValue(String.valueOf(resultList.get(i).getPhone())));
        cell.setCellStyle(conCenterStyle);


        //证券代码
        cell = row.createCell(2);
        cell.setCellValue(getCellValue(resultList.get(i).getCompanyCode()));
        cell.setCellStyle(conCenterStyle);

        //公司简称
        cell = row.createCell(3);
        cell.setCellValue(getCellValue(resultList.get(i).getCompanyName()));
        cell.setCellStyle(conCenterStyle);

        //考试状态
        cell = row.createCell(4);
        cell.setCellValue(getCellValue(resultList.get(i).getExamStatus()));
        cell.setCellStyle(conCenterStyle);

        //证书状态
        cell = row.createCell(5);
        cell.setCellValue(getCellValue(resultList.get(i).getIfCertificate()));
        cell.setCellStyle(conCenterStyle);

        //交卷时间
        cell = row.createCell(6);
        cell.setCellValue(getCellValue(resultList.get(i).getSubmitTime()));
        cell.setCellStyle(conCenterStyle);
        //证书下发时间
        cell = row.createCell(7);
        cell.setCellValue(getCellValue(resultList.get(i).getExamineTime()));
        cell.setCellStyle(conCenterStyle);
        //作答时间
        cell = row.createCell(8);
        cell.setCellValue(getCellValue(resultList.get(i).getTimeSlot()));
        cell.setCellStyle(conCenterStyle);
        //分数
        cell = row.createCell(9);
        cell.setCellValue(getCellValue(resultList.get(i).getScore()));
        cell.setCellStyle(conCenterStyle);
    }

    public String getCellValue(String value) {
        if (StringUtils.isBlank(value)) {
            value = "-";
        }
        return value;
    }

    /**
     * <AUTHOR>
     * @description 导入关联人员
     * @date 2023/3/10 15:40
     */
    @SuppressWarnings("all")
    public JsonResponse<ExamSpecialPersonVO> importRelevancePersonExcel(MultipartFile excelFile,ExamSpecialPersonDTO examSpecialPersonDTO) {
            JsonResponse<ExamSpecialPersonVO> response = new JsonResponse<>();
            try {
                ExamSpecialPersonVO examSpecialPersonVO = readCustomerManagerExcelByIo(excelFile.getInputStream(),examSpecialPersonDTO);
                response.setResult(examSpecialPersonVO);
            } catch (IOException e) {
                e.printStackTrace();
            }
            return response;
        }
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("all")
    public ExamSpecialPersonVO readCustomerManagerExcelByIo(InputStream is,ExamSpecialPersonDTO dto) {
        // Excel读取结果集
        Map<String, Object> importRtnMap = new HashMap<>(16);
        // 导入错误信息List
        List<String> importErrorInfoList = new ArrayList<>();
        // Excel读取结果数据集
        Map<String, List<?>> rtn = new HashMap<String, List<?>>(16);
        //codeValue结果集
        List<ExamSpecialPersonDTO> examSpecialPersonDTOList = new ArrayList<>();
        // 导入Excel读取模板名称
        String templateId = "relevancePerson";
        //返回状态
        ExamSpecialPersonVO res = new ExamSpecialPersonVO();
        res.setImportStatus("0");
        try {
            // 读取Excel
            importRtnMap = excelHandle.readExcelByIo(templateId, is);
            //logger.info("第一个参数：importRtnMap:{}", importRtnMap);
            String importFlag = (String) importRtnMap.get(ExcelHandle.STR_IMPORT_RTNCODE);
            //logger.info("第二个参数：importFlag:{}", importFlag);
            //logger.info("第三个参数：true or false:{}", ExcelHandle.STR_IMPORT_ERROR.equals(importFlag));
            if (ExcelHandle.STR_IMPORT_ERROR.equals(importFlag)) {
                // 读取Excel失败
                importErrorInfoList.add((String) importRtnMap.get(ExcelHandle.STR_IMPORT_ERROR_PATH));
            } else {
                rtn = (Map<String, List<?>>) importRtnMap.get(ExcelHandle.STR_IMPORT_DATA);
                //logger.info("第四个参数：rtn:{}", rtn);
                if (rtn != null) {
                    try {
                        for (String key : rtn.keySet()) {
                            List<?> beanList = rtn.get(key);
                            if (beanList != null) {
                                // 将excel传给DTO
                                for (int i = 0, len = beanList.size(); i < len; i++) {
                                    ExamSpecialPersonVO examSpecialPersonVO = (ExamSpecialPersonVO) beanList.get(i);
                                    ExamSpecialPersonDTO examSpecialPersonDTO =  new ExamSpecialPersonDTO();
                                    examSpecialPersonDTO.setExamRankUnifyId(dto.getExamRankUnifyId());
                                    examSpecialPersonDTO.setCreateUser(getUserInfo().getUserId());
                                    String userId = examManageBizMapper.queryUserId(examSpecialPersonVO.getRealName(),examSpecialPersonVO.getPhone());
                                    //logger.info("第五个参数：userId:{}", userId);
                                    if (StringUtils.isBlank(userId)){
                                        examSpecialPersonVO.setImportStatus("0");
                                        return examSpecialPersonVO;
                                    }
                                    examSpecialPersonDTO.setUserId(userId);
                                    List<String> alreadyExistPersonIdList = getAlreadyExistPersonIdList(examSpecialPersonDTO);
                                    if (alreadyExistPersonIdList.contains(examSpecialPersonDTO.getUserId())){
                                        continue;
                                    }
                                    examSpecialPersonDTOList.add(examSpecialPersonDTO);
                                }
                            }
                            res.setImportStatus("1");
                        }
                        //插入
                        if (examSpecialPersonDTOList.size() != 0) {
                            distriRoomId(examSpecialPersonDTOList,dto.getExamRankUnifyId());
                            examManageBizMapper.insertRelevancePerson(examSpecialPersonDTOList);
                        }
                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    public CapcoOfflinePaperInfoDto getCapcoOfflinePaperInfo(CapcoOfflinePaperInfoDto dto) {
       return examManageBizMapper.getCapcoOfflinePaperInfo(dto);
    }

    public ByteArrayInputStream exportOfflineExam(CapcoOfflinePaperInfoDto capcoOfflinePaperInfoDto) throws IOException {
        SXSSFWorkbook workbook;
        // 设置Excel内容
        workbook = getOfflineExamWorkbook(capcoOfflinePaperInfoDto);
        java.io.ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.flush();
        os.close();
        return new ByteArrayInputStream(os.toByteArray());
    }
/**
 * @description 导出线下考试列表exportOfflineExamExcel
 * @date 2023/4/6 17:33
 */
@SuppressWarnings("all")
private SXSSFWorkbook getOfflineExamWorkbook(CapcoOfflinePaperInfoDto capcoOfflinePaperInfoDto) {
    SXSSFWorkbook workbook = new SXSSFWorkbook();
    SXSSFSheet sheet = workbook.createSheet("Sheet1");
    SXSSFRow row = null;
    CellStyle cellStyle = workbook.createCellStyle();
    cellStyle.setAlignment(HorizontalAlignment.CENTER);
    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    cellStyle.setWrapText(true);
    Font titleF = workbook.createFont();
    titleF.setFontHeightInPoints((short) 14);
    titleF.setBold(true);
    titleF.setFontName("宋体");
    cellStyle.setFont(titleF);
    CellStyle cs = workbook.createCellStyle();
    cs.setWrapText(true);
    SXSSFCell cell = null;
    cell = sheet.createRow(0).createCell(0);

    Font conF = workbook.createFont();
    conF.setFontHeightInPoints((short) 12);
    conF.setFontName("宋体");
    // 内容居中
    CellStyle conCenterStyle = workbook.createCellStyle();
    conCenterStyle.setAlignment(HorizontalAlignment.CENTER);
    conCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    conCenterStyle.setFont(conF);
    // 内容居左
    CellStyle conLeftStyle = workbook.createCellStyle();
    conLeftStyle.setAlignment(HorizontalAlignment.LEFT);
    conLeftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    conCenterStyle.setFont(conF);
    // 内容居右
    CellStyle conRightStyle = workbook.createCellStyle();
    conRightStyle.setAlignment(HorizontalAlignment.RIGHT);
    conRightStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    conCenterStyle.setFont(conF);

    List<CapcoOfflinePaperInfoDto> resultList = examManageBizMapper.queryOfflinePaperInfoList(capcoOfflinePaperInfoDto);
    String[] titles = {"姓名", "手机号", "分数", "通过(是/否)", "试卷"};
    // 设置特殊标题
    row = sheet.createRow(0);
    row.setHeight((short) 500);
    for (int i = 0; i < titles.length; i++) {
        sheet.setDefaultColumnStyle(i, cs);
        if (i == 0) {
            sheet.setColumnWidth(0, 6000);
        } else {
            sheet.setColumnWidth(i, 6000);
        }
        cell = row.createCell(i);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(titles[i]);
    }
    if (StringUtils.isBlank(capcoOfflinePaperInfoDto.getExamRankId())){
        return workbook;
    }
    //设置内容
    if (resultList.size() > 0) {
        for (int i = 0; i < resultList.size(); i++) {
            row = sheet.createRow(i + 1);
            row.setHeight((short) 500);
            extractedOfflineExamInfo(row, conCenterStyle, resultList, i);
        }
    }
    return workbook;
}
    @SuppressWarnings("all")
    private void extractedOfflineExamInfo(SXSSFRow row, CellStyle conCenterStyle, List<CapcoOfflinePaperInfoDto> resultList, int i) {
        SXSSFCell cell;
        //姓名
        cell = row.createCell(0);
        cell.setCellValue(getCellValue(resultList.get(i).getUserName()));
        cell.setCellStyle(conCenterStyle);

        //手机号
        cell = row.createCell(1);
        cell.setCellValue(getCellValue(String.valueOf(resultList.get(i).getPhone())));
        cell.setCellStyle(conCenterStyle);


        //分数
        cell = row.createCell(2);
        cell.setCellValue(getCellValue(resultList.get(i).getScore()));
        cell.setCellStyle(conCenterStyle);

        //是否通过
        cell = row.createCell(3);
        cell.setCellValue(getCellValue(resultList.get(i).getScore()));
        cell.setCellStyle(conCenterStyle);

        //试卷
        cell = row.createCell(4);
        cell.setCellValue(getCellValue(resultList.get(i).getAttaUrl()));
        cell.setCellStyle(conCenterStyle);
    }
    /**
     * @description 导入线下考试列表
     * @date 2023/4/6 18:49
     */
    @SuppressWarnings("all")
    public JsonResponse<CapcoOfflinePaperInfoDto> importOfflineExamExcel(MultipartFile excelFile,CapcoOfflinePaperInfoDto capcoOfflinePaperInfoDto) {
        JsonResponse<CapcoOfflinePaperInfoDto> response = new JsonResponse<>();
        try {
            response.setResult(readCustomerManagerExcelByIo(excelFile,capcoOfflinePaperInfoDto));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    @SuppressWarnings("all")
    public CapcoOfflinePaperInfoDto readCustomerManagerExcelByIo(MultipartFile excelFile,CapcoOfflinePaperInfoDto dto) throws IOException {
        //设置第几行是图片
        final String PICTURE_COLUMN = "4";

        InputStream is = excelFile.getInputStream();
        // Excel读取结果集
        Map<String, Object> importRtnMap = new HashMap<>(16);
        // 导入错误信息List
        List<String> importErrorInfoList = new ArrayList<>();
        // Excel读取结果数据集
        Map<String, List<?>> rtn = new HashMap<String, List<?>>(16);
        //codeValue结果集
        List<CapcoOfflinePaperInfoDto> capcoOfflinePaperInfoDtos = new ArrayList<>();
        // 导入Excel读取模板名称
        String templateId = "offlineExam";
        //返回状态
        CapcoOfflinePaperInfoDto res = new CapcoOfflinePaperInfoDto();
        res.setImportStatus("0");
        //获取整个文档页
        XSSFWorkbook wb;
        //表
        XSSFSheet sheet;
        //行
        XSSFRow row;
        // 打开文件
        try {
            wb = new XSSFWorkbook(is);
        } catch (Exception e) {
            e.printStackTrace();
            wb = new XSSFWorkbook();
        }
        //获取第1页
        sheet = wb.getSheetAt(0);
        // 得到总行数
        int rowNum2 = sheet.getLastRowNum();
        //获取excel中的图片
        Map<String, XSSFPictureData> pictureDataMap = getPictures(sheet);
        String savePath = "";
        List<String> urlList = new ArrayList<>();

        for (int k = 1; k <= rowNum2; k++) {
            //指定行和列
            String mapKey = k + "-" + PICTURE_COLUMN;
            if(pictureDataMap.get(mapKey)!=null){
                XSSFPictureData xssfPictureData = pictureDataMap.get(mapKey);
                byte[] data = xssfPictureData.getData();
                long time = System.currentTimeMillis();
                //得到保存的file
                File file = bytesToFile(data, "", String.valueOf(time) + k + ".jpg");
                MultipartFile cMultiFile = getMultipartFile(file);
                List<MultipartFile> files = new ArrayList<>();
                files.add(cMultiFile);
                List<Map<String, String>> maps = fileService.saveTempFiles(files);
                String url = uploadFiles(maps.get(0).get("fileRelaId"));
                urlList.add(url);
            }
        }
        try {
            // 读取Excel
            importRtnMap = excelHandle.readExcelByIo(templateId, excelFile.getInputStream());
            String importFlag = (String) importRtnMap.get(ExcelHandle.STR_IMPORT_RTNCODE);
            if (ExcelHandle.STR_IMPORT_ERROR.equals(importFlag)) {
                // 读取Excel失败
                importErrorInfoList.add((String) importRtnMap.get(ExcelHandle.STR_IMPORT_ERROR_PATH));
            } else {
                rtn = (Map<String, List<?>>) importRtnMap.get(ExcelHandle.STR_IMPORT_DATA);
                if (rtn != null) {
                    try {
                        for (String key : rtn.keySet()) {
                            List<?> beanList = rtn.get(key);
                            if (beanList != null) {
                                // 将excel传给DTO
                                CapcoOfflinePaperInfoDto capcoOfflinePaperInfoDto = new CapcoOfflinePaperInfoDto();
                                for (int i = 0, len = beanList.size(); i < len; i++) {
                                    capcoOfflinePaperInfoDto = (CapcoOfflinePaperInfoDto) beanList.get(i);
                                    capcoOfflinePaperInfoDto.setCreateUser(getUserInfo().getUserId());
                                    capcoOfflinePaperInfoDto.setExamRankId(dto.getExamRankId());
                                    capcoOfflinePaperInfoDto.setExamRankUnifyId(dto.getExamRankUnifyId());
                                    if (capcoOfflinePaperInfoDto.getIsPass().equals("是")){
                                        capcoOfflinePaperInfoDto.setIsPass("1");
                                    }else {
                                        capcoOfflinePaperInfoDto.setIsPass("0");
                                    }
                                    String userId = examManageBizMapper.queryUserId(capcoOfflinePaperInfoDto.getUserName(),capcoOfflinePaperInfoDto.getPhone());
                                    if (StringUtils.isBlank(userId)){
                                        capcoOfflinePaperInfoDto.setImportStatus("0");
                                        return capcoOfflinePaperInfoDto;
                                    }
                                    //判断是否有重复记录
                                    ExamRecordDto examRecordDto = new ExamRecordDto();
                                    examRecordDto.setExamRoomId(dto.getExamRankUnifyId());
                                    examRecordDto.setExamRankId(dto.getExamRankId());
                                    examRecordDto.setCreateUser(userId);
                                    int alreadyExistRecordCount = examManageBizMapper.judgeAlreadyExistRecord(examRecordDto);
                                    if (alreadyExistRecordCount > 0) {
                                        capcoOfflinePaperInfoDto.setImportStatus("0");
                                        return capcoOfflinePaperInfoDto;
                                    }
                                    capcoOfflinePaperInfoDto.setUserId(userId);
                                    capcoOfflinePaperInfoDtos.add(capcoOfflinePaperInfoDto);
                                }
                                //把图片urlList set给dtoList
                                for (int i = 0; i < urlList.size(); i++) {
                                    for (int j = 0; j < capcoOfflinePaperInfoDtos.size(); j++) {
                                        if (i == j){
                                            capcoOfflinePaperInfoDtos.get(j).setAttaUrl(urlList.get(i));
                                            continue;
                                        }
                                    }
                                }
                            }
                            res.setImportStatus("1");
                        }
                        //插入
                        if (ObjectUtil.isNotEmpty(capcoOfflinePaperInfoDtos)){
                            examManageBizMapper.insertOfflinePaperInfo(capcoOfflinePaperInfoDtos);
                        }
                        //筛选通过的考试
                        List<CapcoOfflinePaperInfoDto> passInfo = capcoOfflinePaperInfoDtos
                                .stream()
                                .filter(a -> a.getIsPass().equals("1"))
                                .collect(Collectors.toList());
                        List<ExamRecordDto> examRecordDtoList = new ArrayList<>();
                        for (int i = 0; i < passInfo.size(); i++) {
                            ExamRecordDto examRecordDto = new ExamRecordDto();
                            examRecordDto.setExamRankId(passInfo.get(i).getExamRankId());
                            examRecordDto.setCreateUser(passInfo.get(i).getUserId());
                            examRecordDto.setExamType("111111");
                            examRecordDto.setScore(passInfo.get(i).getScore());
                            examRecordDto.setIfQualified("5");
                            examRecordDto.setStatus("1");
                            examRecordDto.setExamRoomId(passInfo.get(i).getExamRankUnifyId());
                            examRecordDtoList.add(examRecordDto);
                        }
                        if (examRecordDtoList.size() > 0) {
                            examManageBizMapper.insertPassInfoToRecord(examRecordDtoList);
                        }
                        //筛选未通过的考试
                        List<CapcoOfflinePaperInfoDto> failInfo = capcoOfflinePaperInfoDtos
                                .stream()
                                .filter(a -> a.getIsPass().equals("0"))
                                .collect(Collectors.toList());
                        List<ExamSpecialPersonDTO> examSpecialPersonDTOList = new ArrayList<>();
                        failInfo.forEach(a -> {
                            ExamSpecialPersonDTO examSpecialPersonDto = new ExamSpecialPersonDTO();
                            examSpecialPersonDto.setUserId(a.getUserId());
                            examSpecialPersonDto.setExamRankUnifyId(a.getExamRankUnifyId());
                            examSpecialPersonDto.setExamForm("1");
                            examSpecialPersonDto.setStatus("1");
                            examSpecialPersonDto.setCreateUser(getUserInfo().getUserId());
                            List<String> alreadyExistPersonIdList = getAlreadyExistPersonIdList(examSpecialPersonDto);
                            if (!alreadyExistPersonIdList.contains(examSpecialPersonDto.getUserId())) {
                                examSpecialPersonDTOList.add(examSpecialPersonDto);
                            }
                        });
                        if (ObjectUtil.isNotEmpty(examSpecialPersonDTOList)) {
                            examManageBizMapper.insertFailInfoToSpecialPerson(examSpecialPersonDTOList);
                        }
                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    public static MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName());
        try (InputStream input = Files.newInputStream(file.toPath());
             OutputStream os = item.getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }

        return new CommonsMultipartFile(item);
    }
    //解析获取excel中的图片
    public static Map<String, XSSFPictureData> getPictures( XSSFSheet xssfSheet){
        Map<String,XSSFPictureData> sheetIndexPicMap=new HashMap<>();

        for (POIXMLDocumentPart dr : xssfSheet.getRelations()) {
            if (dr instanceof XSSFDrawing) {
                XSSFDrawing drawing = (XSSFDrawing) dr;
                List<XSSFShape> shapes = drawing.getShapes();
                for (XSSFShape shape : shapes) {
                    XSSFPicture pic = (XSSFPicture) shape;
                    XSSFClientAnchor xssfClientAnchor=(XSSFClientAnchor) pic.getAnchor();
                    XSSFPictureData pdata = pic.getPictureData();
                    // 行号-列号
                    String key = xssfClientAnchor.getRow1() + "-" + xssfClientAnchor.getCol1();
                    sheetIndexPicMap.put(key, pdata);
                }
            }
        }

        return sheetIndexPicMap;
    }
    @SuppressWarnings("all")
    public static File bytesToFile(byte[] bytes, String outPath, String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            file = new File(fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return file;
    }
    public String uploadFiles(String url){
        // 头像图临时文件ID
        String imageUrlTempId = "temp/" + url;
        // 将图片存入正式目录
        List<Map<String, String>> imgMaps =
                Optional.ofNullable(
                                fileService.saveOpenFileFromToTemp(
                                        "02", "", Arrays.asList(imageUrlTempId), true,"01"))
                        .orElse(new ArrayList<>());
        String imageUrlId = "";
        String imgUrl = "";
        for (Map<String, String> map : imgMaps) {
            if (map.containsKey(imageUrlTempId)) {
                imageUrlId = map.get(imageUrlTempId);
            }
        }
        if (StringUtils.isNotEmpty(imageUrlId)) {
            if (org.apache.commons.lang.StringUtils.isNotEmpty(imageUrlId)) {
                Attachment attachment = attachmentMapper.selectByPrimaryKey(imageUrlId);
                if (null != attachment) {
                    String arrUrl = attachment.getAttUrl().substring(1);
                    imgUrl = fileService.getViewPath(arrUrl).replace("\\", "/");
                }
            }
        }
        return imgUrl;
    }
}
