package com.stock.capital.cloud.trainUserManage.dto;

import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class CourseDto  implements Serializable{

    private static final long serialVersionUID = 1L;

    private String id;

    private String courseName;

    private String courseType;

    private String courseTypeName;
    //二级标签
    private String courseLevelTwoType;
    //适用板块
    private String applyPlate;
    //熟练度
    private String applyProficiency;
    //适用人群
    private String applyPerson;
    //课程类型
    private String applyMechanism;
    //主题
    private List<String> courseTheme;
    //用户类别
    private List<String> personTypeList;
    //辖区类型
    private List<String> belongCommissionList;
    //讲师 ids
    private String teacher;
    //讲师名称
    private String teacherNames;
    //发布状态  0：未发布 1 ：发布
    private String releaseFlag;
    //发布时间
    private String releaseTime;
    //收费标识 0：免费 1：收费
    private String chargeFlag;
    //商品价格
    private String commodityPrice;
    //划线价格
    private String markingPrice;
    //限免开始时间
    private String limitBegTime;
    //限免结束时间
    private String limitEndTime;
    //背景图片附件ID
    private String picAttId;

    @Value("${image.viewPath}")
    private String viewPath;
    //背景图片路径
    private String attUrl;
    //状态:OK|DELETED
    private String status;
    //课程介绍富文本
    private String introduce;
    //课程图片富文本
    private String picture;
    //课程总节数
    private String courseChapter;
    //课程总时间 单位是秒
    private Integer courseTime;
    //一定时间内播放次数
    private Integer watchNum;

    private String createUser;

    private Date createTime;
    //课程的类型数
    private Integer typeNum;

    private String updateUser;

    private Double average;
    //计算出的真实热度
    private BigDecimal trueHeat = new BigDecimal("0.00");
    //计算出的综合评分
    private BigDecimal comprehensiveScore = new BigDecimal("0.00");
    //课程匹配率
    private BigDecimal matchingRatio = new BigDecimal("0.00");
    //标签
    private List<String> labelList;

    private Date updateTime;

    private Integer sort;

    private String groupId;

    private List<String> courseTypeList;

    private List<String> applyPlateList;

    private String applyPersonOther;

    private List<String> applyPersonList;

    private List<String> applyMechanismList;

    private List<String> applyTeachermList;

    private String releaseStartTime;

    private String releaseEndTime;
    //背景图片
    private String livePic;

    private String livePicImg;

    private String bizId; //课程Id

    private String lawId; //关联法律法规Id

    private String sortLawIds; //法规参照排序

    private String lawsName; //关联法律法规名称

    private List<String> lawIds; //关联法律法规Id集合

    private String lawItemIds; //法规目录Id

    private String lawItemIdsNum; //法规目录Id数量

    private String lawSort; //法规目录Id数量

    private String lawShowFlag; //失效判定：1：失效 0：有效

    private String lawShowFlagNew;//修改判定：1：修改 0：未修改

    private String caseId; //违规案例Id

    private String caseName; //违规案例名称

    private String caseIds; //违规案例Ids

    private String caseSort; //违规案例排序

    private String linkNumber; //违规案例 关联案例组

    private String amount;

    private String amountTime;

    private String amounBegTime;

    private String amounEndTime;

    private String contactCourseId;

    private String courseTagType;//

    private String connectCourse;//

    private String titleType;//一级分类名称

    private String labelId;//一级分类id;

    private String mainTextTag;//一级分类id;

    private String favorite;

    private Integer videoNum;

    private String timeAll;//总时长

    private String watchTime;//观看总时长

    private String periodAll;//每个课的总学分

    private String courseStartTime;

    private String courseEndTime;

    private String courseCreateTime;

    private String classTime;

    private Integer completeNum;

    private String completeTimeAll;

    public Integer getCompleteNum() {
        return completeNum;
    }

    public void setCompleteNum(Integer completeNum) {
        this.completeNum = completeNum;
    }

    public String getCompleteTimeAll() {
        return completeTimeAll;
    }

    public void setCompleteTimeAll(String completeTimeAll) {
        this.completeTimeAll = completeTimeAll;
    }

    public String getClassTime() {
        return classTime;
    }

    public void setClassTime(String classTime) {
        this.classTime = classTime;
    }

    public String getCourseCreateTime() {
        return courseCreateTime;
    }

    public void setCourseCreateTime(String courseCreateTime) {
        this.courseCreateTime = courseCreateTime;
    }

    public String getCourseStartTime() {
        return courseStartTime;
    }

    public void setCourseStartTime(String courseStartTime) {
        this.courseStartTime = courseStartTime;
    }

    public String getCourseEndTime() {
        return courseEndTime;
    }

    public void setCourseEndTime(String courseEndTime) {
        this.courseEndTime = courseEndTime;
    }

    public String getPeriodAll() {
        return periodAll;
    }

    public void setPeriodAll(String periodAll) {
        this.periodAll = periodAll;
    }

    public String getWatchTime() {
        return watchTime;
    }

    public void setWatchTime(String watchTime) {
        this.watchTime = watchTime;
    }

    public String getTimeAll() {
        return timeAll;
    }

    public void setTimeAll(String timeAll) {
        this.timeAll = timeAll;
    }

    private List<VideoMapDto> videoMapDtoList;

    private String score;//判断是否评分标识；0；无评分

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCourseType() {
        return courseType;
    }

    public void setCourseType(String courseType) {
        this.courseType = courseType;
    }

    public String getApplyPlate() {
        return applyPlate;
    }

    public void setApplyPlate(String applyPlate) {
        this.applyPlate = applyPlate;
    }

    public String getApplyProficiency() {
        return applyProficiency;
    }

    public void setApplyProficiency(String applyProficiency) {
        this.applyProficiency = applyProficiency;
    }

    public String getApplyPerson() {
        return applyPerson;
    }

    public List<String> getCourseTheme() {
        return courseTheme;
    }

    public void setCourseTheme(List<String> courseTheme) {
        this.courseTheme = courseTheme;
    }

    public void setApplyPerson(String applyPerson) {
        this.applyPerson = applyPerson;
    }

    public String getApplyMechanism() {
        return applyMechanism;
    }

    public void setApplyMechanism(String applyMechanism) {
        this.applyMechanism = applyMechanism;
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher;
    }

    public String getTeacherNames() {
        return teacherNames;
    }

    public void setTeacherNames(String teacherNames) {
        this.teacherNames = teacherNames;
    }

    public String getReleaseFlag() {
        return releaseFlag;
    }

    public void setReleaseFlag(String releaseFlag) {
        this.releaseFlag = releaseFlag;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getChargeFlag() {
        return chargeFlag;
    }

    public void setChargeFlag(String chargeFlag) {
        this.chargeFlag = chargeFlag;
    }

    public String getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(String commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public String getMarkingPrice() {
        return markingPrice;
    }

    public void setMarkingPrice(String markingPrice) {
        this.markingPrice = markingPrice;
    }

    public String getLimitBegTime() {
        return limitBegTime;
    }

    public void setLimitBegTime(String limitBegTime) {
        this.limitBegTime = limitBegTime;
    }

    public String getLimitEndTime() {
        return limitEndTime;
    }

    public void setLimitEndTime(String limitEndTime) {
        this.limitEndTime = limitEndTime;
    }

    public String getPicAttId() {
        return picAttId;
    }

    public void setPicAttId(String picAttId) {
        this.picAttId = picAttId;
    }

    public String getAttUrl() {
        return attUrl;
    }

    public void setAttUrl(String attUrl) {
        this.attUrl = attUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getCourseChapter() {
        return courseChapter;
    }

    public void setCourseChapter(String courseChapter) {
        this.courseChapter = courseChapter;
    }

    public Integer getCourseTime() {
        return courseTime;
    }

    public void setCourseTime(Integer courseTime) {
        this.courseTime = courseTime;
    }


    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getWatchNum() {
        return watchNum;
    }

    public void setWatchNum(Integer watchNum) {
        this.watchNum = watchNum;
    }

    public BigDecimal getTrueHeat() {
        return trueHeat;
    }

    public void setTrueHeat(BigDecimal trueHeat) {
        this.trueHeat = trueHeat;
    }

    public BigDecimal getComprehensiveScore() {
        return comprehensiveScore;
    }

    public void setComprehensiveScore(BigDecimal comprehensiveScore) {
        this.comprehensiveScore = comprehensiveScore;
    }

    public Integer getTypeNum() {
        return typeNum;
    }

    public void setTypeNum(Integer typeNum) {
        this.typeNum = typeNum;
    }

    public BigDecimal getMatchingRatio() {
        return matchingRatio;
    }

    public void setMatchingRatio(BigDecimal matchingRatio) {
        this.matchingRatio = matchingRatio;
    }

    public List<String> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<String> labelList) {
        this.labelList = labelList;
    }

    public String getCourseLevelTwoType() {
        return courseLevelTwoType;
    }

    public void setCourseLevelTwoType(String courseLevelTwoType) {
        this.courseLevelTwoType = courseLevelTwoType;
    }

    public String getViewPath() {
        return viewPath;
    }

    public void setViewPath(String viewPath) {
        this.viewPath = viewPath;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCourseTypeName() {
        return courseTypeName;
    }

    public void setCourseTypeName(String courseTypeName) {
        this.courseTypeName = courseTypeName;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public List<String> getCourseTypeList() {
        return courseTypeList;
    }

    public void setCourseTypeList(List<String> courseTypeList) {
        this.courseTypeList = courseTypeList;
    }

    public List<String> getApplyPlateList() {
        return applyPlateList;
    }

    public void setApplyPlateList(List<String> applyPlateList) {
        this.applyPlateList = applyPlateList;
    }

    public String getApplyPersonOther() {
        return applyPersonOther;
    }

    public void setApplyPersonOther(String applyPersonOther) {
        this.applyPersonOther = applyPersonOther;
    }

    public List<String> getApplyPersonList() {
        return applyPersonList;
    }

    public void setApplyPersonList(List<String> applyPersonList) {
        this.applyPersonList = applyPersonList;
    }

    public String getReleaseStartTime() {
        return releaseStartTime;
    }

    public void setReleaseStartTime(String releaseStartTime) {
        this.releaseStartTime = releaseStartTime;
    }

    public String getReleaseEndTime() {
        return releaseEndTime;
    }

    public void setReleaseEndTime(String releaseEndTime) {
        this.releaseEndTime = releaseEndTime;
    }

    public String getLivePic() {
        return livePic;
    }

    public void setLivePic(String livePic) {
        this.livePic = livePic;
    }

    public String getLivePicImg() {
        return livePicImg;
    }

    public void setLivePicImg(String livePicImg) {
        this.livePicImg = livePicImg;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getLawId() {
        return lawId;
    }

    public void setLawId(String lawId) {
        this.lawId = lawId;
    }

    public String getSortLawIds() {
        return sortLawIds;
    }

    public void setSortLawIds(String sortLawIds) {
        this.sortLawIds = sortLawIds;
    }

    public String getLawsName() {
        return lawsName;
    }

    public void setLawsName(String lawsName) {
        this.lawsName = lawsName;
    }

    public List<String> getLawIds() {
        return lawIds;
    }

    public void setLawIds(List<String> lawIds) {
        this.lawIds = lawIds;
    }

    public String getLawItemIds() {
        return lawItemIds;
    }

    public void setLawItemIds(String lawItemIds) {
        this.lawItemIds = lawItemIds;
    }

    public String getLawItemIdsNum() {
        return lawItemIdsNum;
    }

    public void setLawItemIdsNum(String lawItemIdsNum) {
        this.lawItemIdsNum = lawItemIdsNum;
    }

    public String getLawSort() {
        return lawSort;
    }

    public void setLawSort(String lawSort) {
        this.lawSort = lawSort;
    }

    public String getLawShowFlag() {
        return lawShowFlag;
    }

    public void setLawShowFlag(String lawShowFlag) {
        this.lawShowFlag = lawShowFlag;
    }

    public String getLawShowFlagNew() {
        return lawShowFlagNew;
    }

    public void setLawShowFlagNew(String lawShowFlagNew) {
        this.lawShowFlagNew = lawShowFlagNew;
    }

    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }

    public String getCaseName() {
        return caseName;
    }

    public void setCaseName(String caseName) {
        this.caseName = caseName;
    }

    public String getCaseIds() {
        return caseIds;
    }

    public void setCaseIds(String caseIds) {
        this.caseIds = caseIds;
    }

    public String getCaseSort() {
        return caseSort;
    }

    public void setCaseSort(String caseSort) {
        this.caseSort = caseSort;
    }

    public String getLinkNumber() {
        return linkNumber;
    }

    public void setLinkNumber(String linkNumber) {
        this.linkNumber = linkNumber;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getAmountTime() {
        return amountTime;
    }

    public void setAmountTime(String amountTime) {
        this.amountTime = amountTime;
    }

    public String getAmounBegTime() {
        return amounBegTime;
    }

    public void setAmounBegTime(String amounBegTime) {
        this.amounBegTime = amounBegTime;
    }

    public String getAmounEndTime() {
        return amounEndTime;
    }

    public void setAmounEndTime(String amounEndTime) {
        this.amounEndTime = amounEndTime;
    }

    public String getContactCourseId() {
        return contactCourseId;
    }

    public void setContactCourseId(String contactCourseId) {
        this.contactCourseId = contactCourseId;
    }

    public String getCourseTagType() {
        return courseTagType;
    }

    public void setCourseTagType(String courseTagType) {
        this.courseTagType = courseTagType;
    }

    public String getConnectCourse() {
        return connectCourse;
    }

    public void setConnectCourse(String connectCourse) {
        this.connectCourse = connectCourse;
    }

    public String getTitleType() {
        return titleType;
    }

    public void setTitleType(String titleType) {
        this.titleType = titleType;
    }

    public String getLabelId() {
        return labelId;
    }

    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }

    public String getMainTextTag() {
        return mainTextTag;
    }

    public void setMainTextTag(String mainTextTag) {
        this.mainTextTag = mainTextTag;
    }

    public List<String> getApplyMechanismList() {
        return applyMechanismList;
    }

    public void setApplyMechanismList(List<String> applyMechanismList) {
        this.applyMechanismList = applyMechanismList;
    }

    public List<String> getApplyTeachermList() {
        return applyTeachermList;
    }

    public void setApplyTeachermList(List<String> applyTeachermList) {
        this.applyTeachermList = applyTeachermList;
    }

    public String getFavorite() {
        return favorite;
    }

    public void setFavorite(String favorite) {
        this.favorite = favorite;
    }

    public Integer getVideoNum() {
        return videoNum;
    }

    public void setVideoNum(Integer videoNum) {
        this.videoNum = videoNum;
    }

    public List<VideoMapDto> getVideoMapDtoList() {
        return videoMapDtoList;
    }

    public void setVideoMapDtoList(List<VideoMapDto> videoMapDtoList) {
        this.videoMapDtoList = videoMapDtoList;
    }

    public List<String> getPersonTypeList() { return personTypeList; }

    public void setPersonTypeList(List<String> personTypeList) { this.personTypeList = personTypeList; }

    public List<String> getBelongCommissionList() { return belongCommissionList; }

    public void setBelongCommissionList(List<String> belongCommissionList) { this.belongCommissionList = belongCommissionList; }
}
