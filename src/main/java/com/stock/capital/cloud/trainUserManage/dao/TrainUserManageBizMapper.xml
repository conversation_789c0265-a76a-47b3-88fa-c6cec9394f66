<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper">

    <select id="personTypeList" parameterType="java.lang.String" resultType="java.util.Map">
        select code_value codeValue,code_name codeName from sa_code where  code_no = #{codeNo} and status='1' order by sort_no;
    </select>

    <select id="getUserInfoList" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto"
            parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
            select t1.id,t1.nick_name nickName,t1.real_name realName,
            t1.org_name orgName,t1.person_type codeValue,
            t1.job_name jobName,
            (select sc.code_name from sa_code sc where sc.code_no = "PERSON_POST" and sc.code_value = t1.post) post,
            t1.acc_id accId,
            t1.phone phone, t1.mail mail,
            (select sc.code_name from sa_code sc where sc.code_no = "PERSON_TYPE" and sc.code_value = t1.person_type) personType,
            (select sc.code_name from sa_code sc where sc.code_no = "BELONG_COMMISSION" and sc.code_value = t1.belong_commission) belongCommission,
            (select sc.code_name from sa_code sc where sc.code_no = "CHANNEL_TYPE" and sc.code_value = t1.user_type) userType,
            date_format(t1.create_time,'%Y-%m-%d %H:%i:%s') createDate,
            t1.company_code companyCode,
            t1.company_name companyName,
            t1.lock_state lockState,
            case when  (t1.status = 'C' or t1.status = 'U') then '有效' when t1.status = 'R' then '已注销'  when t1.status = 'D' then '已删除' end status,
           (select sc.code_name from sa_code sc where sc.code_no = "CHECK_STATUS" and sc.code_value = t1.check_state) checkState
            from sch_user t1
            left join capco_train_member_unit ctmu on  t1.company_code = ctmu.company_code
            <where>
                t1.status in ('C','U')
                <if test="companyCode != null and companyCode != ''">
                    and t1.company_code like concat('%',#{companyCode},'%')
                </if>
                <if test="companyName != null and companyName != ''">
                    and t1.company_name like concat('%',#{companyName},'%')
                </if>
                <if test="realName != null and realName != ''">
                    and t1.real_name like CONCAT('%' , #{realName} , '%')
                </if>
                <if test="mail != null and mail != ''">
                    and t1.mail like CONCAT('%' , #{mail} , '%')
                </if>
                <if test="phone != null and phone != ''">
                    and t1.phone like CONCAT('%' , #{phone} , '%')
                </if>
                <if test="orgName != null and orgName != ''">
                    and
                    <foreach collection="orgName.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                        t1.org_name like CONCAT('%' , #{item} , '%')
                    </foreach>

                </if>
                <if test="notOrgName != null and notOrgName != ''">
                    and
                    <foreach collection="notOrgName.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                        t1.org_name not like CONCAT('%' , #{item} , '%')
                    </foreach>

                </if>
                <if test="notComapnyCode != null and notComapnyCode != ''">
                    and t1.company_code not in
                    <foreach collection="notComapnyCode.split(',')" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="comCode != null and comCode != ''">
                    and
                    <foreach collection="comCode.split(',')" item="item" index="index" open="(" close=")" separator="OR" >
                        t1.company_code like  CONCAT('%' , #{item} , '%')
                    </foreach>
                </if>
                <if test="(jobName != null and jobName != '') or (personPostList != null and personPostList.size() > 0)">
                    and (
                    <if test="jobName != null and jobName != ''">
                        <foreach collection="jobName.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                            t1.job_name = #{item}
                        </foreach>
                    </if>
                    <if test="personPostList != null and personPostList.size() > 0 " >
                        <if test="jobName != null and jobName != ''">
                            or
                        </if>
                        t1.post in
                        <foreach collection="personPostList" item="item" open="(" close=")" separator="," >
                            #{item,jdbcType = VARCHAR}
                        </foreach>
                    </if>
                    )
                </if>
                <if test="belongsPlate != null and belongsPlate != ''">
                    and ctmu.belongs_plate in
                    <foreach collection="belongsPlate.split(',')" index="index" item="item" open="(" separator="," close=")">
                          #{item}
                    </foreach>
                </if>
                <if test="notJobName != null and notJobName != ''">
                    and
                    <foreach collection="notJobName.split(',')" index="index" item="item" open="(" separator="and" close=")">
                        t1.job_name != #{item}
                    </foreach>

                </if>
                <if test="notPersonPostList != null and notPersonPostList.size() > 0 " >
                    and t1.post not in
                    <foreach collection="notPersonPostList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="personTypeList != null and personTypeList.size() > 0 " >
                    and t1.person_type in
                    <foreach collection="personTypeList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="personLabelList != null and personLabelList.size() > 0 " >
                    and t1.person_label in
                    <foreach collection="personLabelList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="belongCommissionList != null and belongCommissionList.size() > 0 " >
                    and t1.belong_commission in
                    <foreach collection="belongCommissionList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="checkStatusList != null and checkStatusList.size() > 0 " >
                    and t1.check_state in
                    <foreach collection="checkStatusList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="userId != null and userId != ''">
                   and t1.id = #{userId}
                </if>
                <if test="createDateAfter != null and createDateAfter != ''">
                    and  STR_TO_DATE(#{createDateAfter}, '%Y-%m-%d %H:%i') &lt; t1.create_time
                </if>
            </where>
        order by accId desc
    </select>

    <select id="getUserInfoListAll" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto"
            parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
            select t1.id,t1.real_name realName,
            (select sc.code_name from sa_code sc where sc.code_no = "PERSON_POST" and sc.code_value = t1.post) post,
            t1.acc_id accId,
            t1.phone phone,
            date_format(t1.create_time,'%Y-%m-%d %H:%i:%s') createDate,
            t1.company_code companyCode,
            t1.company_name companyName,
            sc.code_name checkState
            from sch_user t1
            left join sa_code sc on sc.code_value = t1.check_state and sc.code_no = "CHECK_STATUS"
            <where>
                (t1.status = 'C' or t1.status = 'U')
                <if test="companyCode != null and companyCode != ''">
                    and t1.company_code like concat('%',#{companyCode},'%')
                </if>
                <if test="companyName != null and companyName != ''">
                    and t1.company_name like concat('%',#{companyName},'%')
                </if>
                <if test="postList != null and postList.size() > 0 " >
                    and t1.post in
                    <foreach collection="postList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="checkStatusList != null and checkStatusList.size() > 0 " >
                    and t1.check_state in
                    <foreach collection="checkStatusList" item="item" open="(" close=")" separator="," >
                        #{item,jdbcType = VARCHAR}
                    </foreach>
                </if>
                <if test="phone != null and phone != ''">
                    and t1.phone like concat('%',#{phone},'%')
                </if>
                <if test="realName != null and realName != ''">
                    and t1.real_name like concat('%',#{realName},'%')
                </if>
            </where>
        order by accId desc
    </select>

    <select id="getUserInfoByPhoneOrEmail" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto"
            parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        select t1.id, t1.phone, t1.mail
        from sch_user t1
        <where>
            t1.id != #{id}
            and t1.status not in ('D','R')
            and t1.check_state != '2'
            and (t1.phone = #{phone} or t1.mail = #{mail})
        </where>
    </select>

    <select id="companyQuery" parameterType="map" resultType="java.util.Map">
        select id companyId,company_code companyCode,zh_name zhName,zh_sort_name zhSortName
        from sa_company
        <where>
            <if test="companyStr != null and companyStr != ''">
                AND (company_code LIKE CONCAT('%', #{companyStr}, '%')
                OR zh_name LIKE CONCAT('%', #{companyStr},'%')
                OR zh_sort_name LIKE CONCAT('%', #{companyStr},'%'))
            </if>
        </where>
        ORDER BY company_code DESC LIMIT 100
    </select>

    <insert id="userInfoSave" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        insert into sch_user(
        id,user_name,company_name,company_code,real_name,phone,org_name,job_name,mail,mailing_address,postal_code,post,
        belong_commission,password,user_type,person_type,create_user,update_user,check_state
        )value (
        #{id},#{userName},#{companyName},#{companyCode},#{realName},#{phone},#{orgName},#{jobName},#{mail},#{mailingAddress},#{postalCode},#{post}
        ,#{belongCommission},#{password},#{userType},#{personType},#{createUser},#{createUser},#{checkState}
        )
    </insert>

    <insert id="userInfoSaveList" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
       <foreach collection="list" item="item" separator=";">
        insert into sch_user(
          id,user_name,nick_name,user_avatar,password,user_type,person_type,company_id,company_code,create_user,create_time,update_user
        )value (
            uuid_short(),#{item.userName},#{item.nickName},#{item.userAvatar},#{item.password},#{item.userType},#{item.personType},
            #{item.companyId},#{item.companyCode},#{item.createUser},NOW(),#{item.createUser}
        )
       </foreach>
    </insert>

    <update id="delUserInfo" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        update sch_user
        set status=#{status}
        where id=#{id}
    </update>

    <select id="getTrainUserInfo" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        select t.id,t.user_name userName,t.company_name companyName
        from sch_user t
        where t.id=#{id}
    </select>

    <select id="getTrainUserInfoStr" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        select t.id,t.user_name userName,t.company_name companyName,t.company_code companyCode, t.real_name realName,
        t.phone,t.org_name orgName,t.job_name jobName,t.post post,t.mail,t.mailing_address mailingAddress,t.postal_code postalCode,
        t.user_avatar userAvatar,t.belong_commission  belongCommissionValue,
        t.user_type userType,
        t.ID_number as IDNumber,
        (select sc.code_name from sa_code sc where sc.code_no = "CHANNEL_TYPE" and sc.code_value = t.user_type) userTypeStr,
        t.person_type personType,
        (select sc.code_name from sa_code sc where sc.code_no = "PERSON_TYPE" and sc.code_value = t.person_type) personTypeStr,
        (select sc.code_name from sa_code sc where sc.code_no = "PERSON_POST" and sc.code_value = t.post ) postStr,
        t.lock_state lockState,
        t.check_state checkState,
        (select sc.code_name from sa_code sc where sc.code_no = "CHECK_STATUS" and sc.code_value = t.check_state) checkStateStr,
        t.remark,
        t.person_label personLabel,
        (select sc.code_name from sa_code sc where sc.code_no = "BELONG_COMMISSION" and sc.code_value = t.belong_commission) belongCommission,
        t.other_company_code as otherCompanyCode,
        <choose>
            <when test="companyNameStr != null and companyNameStr != ''">
                IFNULL(ctmu.region,sc.code_name) as region,
                sc.code_value
            </when>
            <otherwise>
                ctmu.region
            </otherwise>
        </choose>
        from sch_user t
        left join capco_train_member_unit ctmu on ctmu.company_code=t.company_code
        <if test="companyNameStr != null and companyNameStr != ''">
            left join sa_code sc on   sc.code_name =#{companyNameStr} and  sc.code_no = "BELONG_COMMISSION"
        </if>
        where t.id=#{id}
    </select>

    <update id="updateUserInfo" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        update sch_user
        set
        <if test="lockState!=null and lockState!=''">
          lock_state=#{lockState},
         </if>
        <if test="checkState!=null and checkState!=''">
            check_state=#{checkState},
            remark = #{remark}
        </if>
        where id=#{id}
    </update>

    <update id="updateUserInfoList" parameterType="com.stock.capital.cloud.trainUserManage.dto.BatchApprovalsDto">
        update sch_user
        <set>
            <if test="lockState!=null and lockState!=''">
                lock_state=#{lockState},
            </if>
            <if test="checkState!=null and checkState!=''">
                check_state=#{checkState},
                remark = #{remark}
            </if>
        </set>
        <where>
            id in (
            <foreach collection="schUserInfoList" item="user" separator=",">
                #{user.id}
            </foreach>
            )
        </where>
    </update>

    <select id="checkOnly" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        SELECT t1.id from sch_user t1 where t1.`status` != 'D' and t1.check_state != '2'
        <if test="id!=null">
            and t1.id!=#{id}
        </if>
        <if test="phone!=null">
            and t1.phone=#{phone}
        </if>
        <if test="mail!=null">
            and t1.mail=#{mail}
        </if>
    </select>

    <select id="getOrgInfoList" resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto"
            parameterType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto">
         select
          t.id ,t.capco_id capcoId,t.company_code companyCode,t.company_name companyName,t.company_short_name companyShortName,
        (select sc.code_name from sa_code sc where sc.code_no = "YES_OR_NO" and sc.code_value = t.member_flag) memberFlag,
        t.member_property memberProperty,
        (select sc.code_name from sa_code sc where sc.code_no = "CAPCO_MEMBER_PROPERTY" and sc.code_value = t.member_property) memberPropertyStr,
          t.region,t.create_date createDate
         from capco_train_member_unit t
         <where>
             <if test="companyCode != null and companyCode != ''">
                 and t.company_code like concat('%',#{companyCode},'%')
             </if>
             <if test="companyName != null and companyName != ''">
                 and t.company_name like concat('%',#{companyName},'%')
             </if>
             <if test="companyShortName != null and companyShortName != ''">
                 and t.company_short_name like concat('%',#{companyShortName},'%')
             </if>
         </where>
         order by t.create_date desc
    </select>

    <insert id="insertMemberUnitInfo" parameterType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto">
        insert into capco_train_member_unit (
        id,capco_id,company_code,company_name,company_short_name,region, listing_section,belongs_plate, member_type, member_property, member_flag
        )values
        <foreach collection="capcoTrainMemberUnitDtoList" item="item" index="index" separator="," >
        (
           CONCAT(uuid_short(),''),#{item.capcoId},#{item.companyCode},#{item.companyName},#{item.companyShortName},
            #{item.region},#{item.listingSection},#{item.belongsPlate},#{item.memberType},#{item.memberProperty},#{item.memberFlag}
        )
        </foreach>
    </insert>

    <update id="updatePassword" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" >
        update sch_user
        set password = #{password,jdbcType=VARCHAR}
        where id=#{id}
    </update>

    <select id="getWorkPic" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="java.lang.String">
        select concat(#{filePath},att_url)  attUrl from sa_attachment where business_id=#{id} and business_type='18'
    </select>

    <delete id="delMemberUnitInfo" parameterType="java.lang.String">
        delete from capco_train_member_unit where belongs_plate != '04'
    </delete>

    <update id="updateBelongCommission" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        update sch_user
        set belong_commission = #{belongCommission}
        where id=#{id}
    </update>
    <select id="getValue" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="java.util.Map">
        select sc.code_value,sc.code_name
       from sch_user su
       left join sa_code sc on sc.code_name=#{region} and sc.code_no='BELONG_COMMISSION'
       where su.id=#{id}
    </select>

    <select id="getValueStr" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="java.util.Map">
        select sc.code_value,sc.code_name
        from sa_code sc where sc.code_name=#{region} and sc.code_no='BELONG_COMMISSION'
    </select>

    <update id="updateUserMemberUnitInfo" parameterType="java.lang.String">
        update sch_user t
        left join (
            select su.id,su.company_code,ctmu.member_flag,su.person_type,if(ctmu.member_flag,'000','001') flag
            from sch_user su
            left join capco_train_member_unit ctmu on su.company_code=ctmu.company_code
            where su.person_type='000' or su.person_type='001'
        ) temp on t.id=temp.id
        set t.person_type=ifnull(temp.flag,t.person_type)
    </update>

    <select id="selCompanyUnit" parameterType="java.lang.String" resultType="java.lang.String">
        select id
        from capco_train_member_unit t
        where t.company_code=#{code} and member_flag='1'
    </select>
    <select id="getHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoWatchHistory">
        select cwh.id, cwh.company_code companyCode, real_name realName,phone ,job_name jobName ,
        column1,column2,column3,column4,column5,column6,column7,column8,column9,column10,column11,column12,column13,column14,column15,column16,column17,column18
        from capco_watch_history cwh
        <where>
            (cwh.company_code = #{companyCode} and cwh.real_name = #{realName} and cwh.phone = #{phone} )
            or (cwh.company_name = #{companyName} and cwh.real_name = #{realName} and cwh.phone = #{phone})

         </where>
    </select>
    <select id="phoneHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoWatchHistory">
        select cwh.id, cwh.company_code companyCode, real_name realName,phone ,job_name jobName ,
        column1,column2,column3,column4,column5,column6,column7,column8,column9,column10,column11,column12,column13,column14,column15,column16,column17,column18
        from capco_watch_history cwh
        <where>
            cwh.phone = #{phone}
        </where>
    </select>
    <select id="nameCodeHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoWatchHistory">
        select cwh.id, cwh.company_code companyCode, real_name realName,phone ,job_name jobName ,
        column1,column2,column3,column4,column5,column6,column7,column8,column9,column10,column11,column12,column13,column14,column15,column16,column17,column18
        from capco_watch_history cwh
        <where>
            cwh.company_code = #{companyCode} and cwh.real_name = #{realName}
        </where>
    </select>
    <select id="nameCopyNameHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoWatchHistory">
        select cwh.id, cwh.company_code companyCode, real_name realName,phone ,job_name jobName ,
        column1,column2,column3,column4,column5,column6,column7,column8,column9,column10,column11,column12,column13,column14,column15,column16,column17,column18
        from capco_watch_history cwh
        <where>
            cwh.company_name = #{companyName} and cwh.real_name = #{realName}
        </where>
    </select>
    <insert id="insertWatchInfo" parameterType="list">
        insert into  sch_watch_info(
            id,user_id,course_type,course_id,chapter_id,video_time,study_time,source,create_user,create_time,update_time
        )
         select uuid_short(),su.id ,"0",sci.id,scvm.id,svi.video_time,svi.video_time,"0",su.id ,now(),"1999-09-09 00:00:00"
         from capco_watch_history cwh
         left join sch_course_info sci on sci.id = #{courseId}
         left join sch_course_video_map scvm on sci.id =  scvm.biz_id  and scvm.`status` = '1'
         left join sch_video_info svi on svi.id = scvm.video_id
         left join sch_user su on su.phone = cwh.phone
         or (su.real_name = cwh.real_name and  su.company_code = cwh.company_code)
         or (su.real_name = cwh.real_name and  cwh.company_name = su.company_name )
         or (su.phone = cwh.phone and su.real_name = cwh.real_name and  su.company_code = cwh.company_code)
         or (su.phone = cwh.phone and su.real_name = cwh.real_name and   cwh.company_name = su.company_name )
         where  su.id = #{createUser}
    </insert>
    <delete id="delHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        delete from capco_watch_history   where phone = #{phone} and real_name = #{realName} and (company_code = #{companyCode} or company_name = #{companyName})
    </delete>
    <delete id="delPhoneHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        delete from capco_watch_history   where phone = #{phone}
    </delete>
    <delete id="delNameCodeHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        delete from capco_watch_history   where real_name = #{realName} and company_code = #{companyCode}
    </delete>
    <delete id="delNameCopyNameHistory" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        delete from capco_watch_history   where real_name = #{realName} and company_name = #{companyName}
    </delete>
    <update id="userInfoUpdate" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        update sch_user
        set
            real_name = #{realName},
            person_type = #{personType},
            company_name = #{companyName},
            company_code = #{companyCode},
            belong_commission =#{belongCommission},
            org_name = #{orgName},
            job_name =#{jobName},
            person_label = #{personLabel},
            post = #{post},
            other_company_code = #{otherCompanyCode}
        where id = #{id}
    </update>

    <update id="userUpdateList" parameterType="list">
        <foreach collection="list" item="item" separator=";">
            update sch_user
            <set>
                <if test="item.codeValue != null and item.codeValue !=''">
                    person_type = #{item.codeValue}
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="getTrainMemberUnitList" resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto">
        select
         id,
         capco_id as capcoId,
         company_code as companyCode,
         company_name as companyName,
         company_short_name  companyShortName,
         region,
         member_flag memberFlag,
         member_property memberProperty
         from capco_train_member_unit
    </select>

    <select id="belongCommissionList" parameterType="java.lang.String" resultType="java.util.Map">
        select code_value codeValue,code_name codeName from sa_code where  code_no = #{codeNo} and status='1'
        order by sort_no;
    </select>

    <update id="updateUserLabel" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        update sch_user
        set
        <if test="personLabel!=null and personLabel!=''">
            person_label=#{personLabel}
        </if>
        where id=#{id}
    </update>

    <update id="updateUserLabelNull" parameterType="list">
        <foreach collection="list" item="item"  separator=";" >
            update sch_user
            <set>
                person_label=''
            </set>
            where id=#{item.id}
        </foreach>
    </update>

    <delete id="deleteCapcoCode" parameterType="java.lang.String">
         delete from sa_code where  code_no  = 'CAPCO_LISTING_SECTION' or code_no  = 'CAPCO_MEMBER_TYPE' or code_no  = 'CAPCO_MEMBER_PROPERTY'
    </delete>

    <insert id="insertCapcoCode" parameterType="java.util.List">
        insert into sa_code (id, code_no, code_value,
        code_name, code_desc, sort_no,
        valid_flag, version,
        type, company_id, create_user,
        create_time, update_user, update_time,
        status)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (uuid_short(), #{item.codeNo,jdbcType=VARCHAR}, #{item.codeValue,jdbcType=VARCHAR},
            #{item.codeName,jdbcType=VARCHAR}, #{item.codeDesc,jdbcType=VARCHAR}, ${index} + 1,
            '1', '1',
            null, null, 'system',
            now(), 'system', now(),
            '1'
            )
        </foreach>
    </insert>
    <update id="updateMember" parameterType="java.lang.String">
        UPDATE sch_user
        SET  person_label = CASE WHEN IFNULL(person_label,'') = '' then #{tpye}
                                 ELSE CONCAT( person_label, concat(',',#{tpye})) END
        WHERE company_code IN
        (
        select  t.company_code from  capco_train_member_unit t  where t.member_property  in (select t.code_value
                                                    from sa_code  t where t.code_no = 'CAPCO_MEMBER_PROPERTY' and t.code_value!= 'memberProperty_1')
        )
        AND (person_label NOT LIKE concat('%',#{type},'%') or IFNULL(person_label,'') = '' );

        UPDATE sch_user

        SET  person_label = CASE WHEN person_label LIKE concat('%',',',#{type},'%') then REPLACE(person_label,concat(',',#{type}),'')
                                 WHEN person_label LIKE concat('%',#{type},',','%') then REPLACE(person_label,concat(#{type},','),'')
                                 ELSE REPLACE(person_label,#{type},'')  END
        WHERE company_code NOT IN  (
        select  t.company_code from  capco_train_member_unit t  where t.member_property  in (select t.code_value
                                                    from sa_code  t where t.code_no = 'CAPCO_MEMBER_PROPERTY' and t.code_value!= 'memberProperty_1')
        )
         AND person_label LIKE concat('%',#{type},'%');
    </update>
    <select id="getOverallDetailsData" resultType="java.util.Map" parameterType="java.util.List">
        SELECT
        t1.real_name realName,
        t1.org_name orgName,
        t1.person_type personType,
        ( SELECT sc.code_name FROM sa_code sc WHERE sc.code_value = t1.person_type AND sc.code_no = 'PERSON_TYPE' ) personTypeName,
        t1.job_name jobName,
        (select sc.code_name from sa_code sc where sc.code_no = "PERSON_POST" and sc.code_value = t1.post ) post,
        t1.phone,
        t1.mail,
        ( SELECT sc.code_name FROM sa_code sc WHERE sc.code_no = "BELONG_COMMISSION" AND sc.code_value = t1.belong_commission ) jurisdiction,
        ( SELECT sc.code_name FROM sa_code sc WHERE sc.code_no = "CHANNEL_TYPE" AND sc.code_value = t1.user_type ) userSource,
        date_format( t1.create_time, '%Y-%m-%d %H:%i:%s' ) createTime,
        t1.company_code companyCode,
        t1.company_name companyName,
        ( SELECT sc.code_name FROM sa_code sc WHERE sc.code_no = "CHECK_STATUS" AND sc.code_value = t1.check_state ) auditStatus
        FROM
        sch_user t1
        <where>
            ( t1.STATUS = 'C' OR t1.STATUS = 'U' ) and t1.check_state ='1'
            <if test="list != null and list.size() > 0 " >
                and t1.person_type in
                <foreach collection="list" item="listItem" open="(" close=")" separator="," >
                    #{listItem,jdbcType = VARCHAR}
                </foreach>
            </if>
        </where>

    </select>

    <select id="getRegisterCompanyDetailsData" resultType="java.util.Map" parameterType="java.util.List">
        select
        t1.company_code companyCode,
        t1.company_name companyName,
        t1.person_type personType,
        ctmu.belongs_plate as belongsPlate,
        count(1) registerNum
        from sch_user t1
        left join capco_train_member_unit ctmu on ctmu.company_code = t1.company_code
        where (t1.status = 'C' or t1.status = 'U') and t1.check_state ='1'
        and ifnull(t1.company_code,'') = ''
        group by ifnull(t1.company_name,'')
        union all
        select
        t1.company_code companyCode,
        t1.company_name companyName,
        t1.person_type personType,
        ctmu.belongs_plate as belongsPlate,
        count(1) registerNum
        from sch_user t1
        left join capco_train_member_unit ctmu on ctmu.company_code = t1.company_code
        where (t1.status = 'C' or t1.status = 'U') and t1.check_state ='1'
        and ifnull(t1.company_code,'') != ''
        group by ifnull(t1.company_code,'')
    </select>

    <select id="getStatisticalData" resultType="java.util.Map">
        SELECT
            sc.code_name personTypeName,
            (
                SELECT
                    count( t1.id ) personTypeNum
                FROM
                    sch_user t1
                WHERE
                    t1.person_type = sc.code_value
                  AND ( t1.STATUS = 'C' OR t1.STATUS = 'U' )
                and t1.check_state ='1'
            ) personTypeNum
        FROM
            sa_code sc
        WHERE
            sc.code_no = 'PERSON_TYPE'
    </select>

    <select id="selectMemberUnitInfo" parameterType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto"
            resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto">
        select  ctmu.id,ctmu.company_code AS companyCode,ctmu.company_name as companyName,ctmu.region as region,
        (select sc.code_value from sa_code sc where ctmu.region = sc.code_name and sc.code_no = 'BELONG_COMMISSION'  ) regionCode
        from capco_train_member_unit ctmu
        where ctmu.company_code = #{companyCode}
    </select>

    <select id="selectMemberUnitList" parameterType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto"
            resultType="com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto">
        select  ctmu.id,ctmu.company_code AS companyCode,ctmu.company_name as companyName,ctmu.region as region,
                (select sc.code_value from sa_code sc where ctmu.region = sc.code_name and sc.code_no = 'BELONG_COMMISSION'  ) regionCode
        from capco_train_member_unit ctmu
        <where>
            ctmu.company_code in (
            <foreach collection="dtoList" item="dto" separator=",">
                #{dto.companyCode}
            </foreach>
            )
        </where>
    </select>

    <select id="getBasicUserPhones" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        SELECT phone
        FROM sch_user
        WHERE job_name LIKE '%董秘%' OR job_name LIKE '%董事会秘书%';
    </select>

    <select id="getPartTimeCompanyList" resultType="com.stock.capital.cloud.trainUserManage.dto.PartTimeCompany" parameterType="java.lang.String">
        SELECT
            suoc.company_code companyCode,
            suoc.company_name companyName,
            sc.code_name postStr
        FROM
            sch_user_other_company suoc
                LEFT JOIN sa_code sc ON suoc.post = sc.code_value AND sc.code_no = 'PERSON_POST'
        WHERE
            suoc.user_id = #{id}
    </select>

    <select id="queryUserCount" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="java.lang.Integer">
        select count(distinct t1.id)
        from sch_user t1
        <where>
            (t1.status = 'C' or t1.status = 'U')
            <if test="companyCode != null and companyCode != ''">
                and t1.company_code like concat('%',#{companyCode},'%')
            </if>
            <if test="companyName != null and companyName != ''">
                and t1.company_name like concat('%',#{companyName},'%')
            </if>
            <if test="postList != null and postList.size() > 0 " >
                and t1.post in
                <foreach collection="postList" item="item" open="(" close=")" separator="," >
                    #{item,jdbcType = VARCHAR}
                </foreach>
            </if>
            <if test="checkStatusList != null and checkStatusList.size() > 0 " >
                and t1.check_state in
                <foreach collection="checkStatusList" item="item" open="(" close=")" separator="," >
                    #{item,jdbcType = VARCHAR}
                </foreach>
            </if>
            <if test="phone != null and phone != ''">
                and t1.phone like concat('%',#{phone},'%')
            </if>
            <if test="realName != null and realName != ''">
                and t1.real_name like concat('%',#{realName},'%')
            </if>
        </where>
    </select>

    <select id="exportUser" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto"
            parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        select t1.id,t1.real_name realName,
        t1.acc_id accId,
        sc1.code_name post,
        t1.phone phone,
        date_format(t1.create_time,'%Y-%m-%d %H:%i:%s') createDate,
        t1.company_code companyCode,
        t1.company_name companyName,
        sc.code_name checkState
        from sch_user t1
        left join sa_code sc on sc.code_value = t1.check_state and sc.code_no = "CHECK_STATUS"
        left join sa_code sc1 on sc1.code_value = t1.post and sc1.code_no = "PERSON_POST"
        <where>
            (t1.status = 'C' or t1.status = 'U')
            <if test="companyCode != null and companyCode != ''">
                and t1.company_code like concat('%',#{companyCode},'%')
            </if>
            <if test="companyName != null and companyName != ''">
                and t1.company_name like concat('%',#{companyName},'%')
            </if>
            <if test="postList != null and postList.size() > 0 " >
                and t1.post in
                <foreach collection="postList" item="item" open="(" close=")" separator="," >
                    #{item,jdbcType = VARCHAR}
                </foreach>
            </if>
            <if test="checkStatusList != null and checkStatusList.size() > 0 " >
                and t1.check_state in
                <foreach collection="checkStatusList" item="item" open="(" close=")" separator="," >
                    #{item,jdbcType = VARCHAR}
                </foreach>
            </if>
            <if test="phone != null and phone != ''">
                and t1.phone like concat('%',#{phone},'%')
            </if>
            <if test="realName != null and realName != ''">
                and t1.real_name like concat('%',#{realName},'%')
            </if>
        </where>
        order by accId desc
        <if test="startRow!=null">
          limit #{startRow}, #{pageSize}
        </if>
    </select>

    <select id="querySchUserList" parameterType="java.lang.String" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        select t1.id,t1.real_name realName,
               t1.acc_id accId,
               t1.phone phone,
               t1.company_code companyCode,
               t1.company_name companyName
        from sch_user t1
        where
            t1.id in
        <foreach collection="idList" item="item" open="(" close=")" separator="," >
            #{item,jdbcType = VARCHAR}
        </foreach>
    </select>

    <select id="checkPhone" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" resultType="int">
        select count(1) from sch_user where phone = #{phone}
    </select>

    <update id="changePhone" parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto" >
        update sch_user
        set phone = #{phone,jdbcType=VARCHAR}
        where id=#{id}
    </update>

    <select id="getCourseSelect" resultType="com.stock.capital.cloud.userCredit.dto.ExchangeReviewDto">
        SELECT
            sli.live_id businessId,
            sli.live_name businessName
        FROM
            sch_live_info sli
        where
            sli.rele_state = '1'
          AND sli.live_status = '1'
        ORDER BY
            sli.live_beg_time DESC
    </select>
</mapper>
