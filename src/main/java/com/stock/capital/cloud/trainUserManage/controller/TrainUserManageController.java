package com.stock.capital.cloud.trainUserManage.controller;

import com.stock.capital.cloud.system.dto.UserManagerDto;
import com.stock.capital.cloud.system.service.CodeService;
import com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper;
import com.stock.capital.cloud.trainUserManage.dto.BatchApprovalsDto;
import com.stock.capital.cloud.trainUserManage.dto.CapcoTrainMemberUnitDto;
import com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto;
import com.stock.capital.cloud.trainUserManage.dto.UserChangeDto;
import com.stock.capital.cloud.trainUserManage.service.TrainUserChangeService;
import com.stock.capital.cloud.trainUserManage.service.TrainUserManageService;
import com.stock.capital.cloud.userCredit.service.CreditAuditingService;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.util.JsonUtil;
import com.stock.core.web.DownloadView;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

@Controller
@RequestMapping("trainUserManage")
public class TrainUserManageController extends BaseController {
    @Autowired
    private TrainUserManageService trainUserManageService;

    @Autowired
    private TrainUserManageBizMapper trainUserManageBizMapper;

    @Autowired
    private CreditAuditingService creditAuditingService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private TrainUserChangeService trainUserChangeService;

    private Logger logger = LoggerFactory.getLogger(TrainUserManageController.class);

    /**
     * 前台用户管理菜单页面
     */
    @RequestMapping(value = "userManageInit")
    public ModelAndView userManageInit() {
        return  new ModelAndView("trainUserManage/comUserManage");
    }

    /**
     * 用户管理页面 Init
     */
    @RequestMapping(value = "trainUserManageInit")
    public ModelAndView trainUserManageInit() {
        ModelAndView mv = new ModelAndView("trainUserManage/userManage");
        mv.addObject("schUserInfoDto", new SchUserInfoDto());
        //TODO 这个地方用公共的方法
        mv.addObject("checkStatusList", JsonUtil.toJson(trainUserManageBizMapper.personTypeList("CHECK_STATUS")));
        mv.addObject("postList", JsonUtil.toJson(trainUserManageBizMapper.personTypeList("PERSON_POST")));
        //课程名称
//        mv.addObject("courseSelectList",JsonUtil.toJson(trainUserManageService.getCourseSelect()));
        return mv;
    }
    /**
     * 用户列表页跳转批量审核
     */

    /**
     * 用户管理查询列表
     */
    @RequestMapping(value = "getUserInfoList")
    @ResponseBody
    public Map<String, Object> getUserInfoList(SchUserInfoDto dto) {
        Map<String, Object> info = new HashMap<String, Object>();
        ParamHander(dto);
        if("1".equals(dto.getUserAll())){
            info = super.commonQuery(
                    "com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper.getUserInfoListAll",
                    dto);
        }
//        else {
//            dto.setCheckStatusList(Arrays.asList("1".split(",")));
//            info = super.commonQuery(
//                    "com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper.getUserInfoList",
//                    dto);
//        }
        return info;
    }
    private void ParamHander(SchUserInfoDto dto){
        if(!StringUtils.isEmpty(dto.getPost())){
            dto.setPostList(Arrays.asList(dto.getPost().split(",")));
        }
        if(!StringUtils.isEmpty(dto.getCheckStatus())){
            dto.setCheckStatusList(Arrays.asList(dto.getCheckStatus().split(",")));
        }
        // 如果第一次进入页面，用户状态为空，默认查有效
        // 如果用户状态选择“请选择” status是“”不是null
        if (Objects.isNull(dto.getStatus())){
            dto.setStatus("1");
        }
    }

    /**
     * 新增/查看用户信息
     */
    @RequestMapping(value = "addUserInfoInit")
    public ModelAndView addUserInfoInit(SchUserInfoDto schUserInfoDto) {
        ModelAndView mv = new ModelAndView();
            mv = new ModelAndView("trainUserManage/watchUserInfo");
        if (StringUtils.equals("1",schUserInfoDto.getAuditBtn())){
            mv.addObject("auditBtn",schUserInfoDto.getAuditBtn());
        }
        if (StringUtils.isNotEmpty(schUserInfoDto.getId())) {
            trainUserManageService.handerMv(mv,schUserInfoDto);
        }else {
            mv.addObject("schUserInfoDto",schUserInfoDto);
        }
        mv.addObject("checkStatusList", JsonUtil.toJson(trainUserManageService.checkStatusList("CHECK_STATUS")));
        mv.addObject("postList",JsonUtil.toJson(trainUserManageService.personTypeListA("PERSON_POST")));
        return mv;
    }

    /**
     * 公司联想
     */
//    @RequestMapping(value = "companyQuery", method = RequestMethod.POST)
//    @ResponseBody
//    public JsonResponse<List<Map<String, Object>>> companyQuery(String companyStr) {
//        Map<String, Object> map = new HashMap<>();
//        map.put("companyStr", companyStr);
//        List<Map<String, Object>> companyList = schUserManageService.companyQuery(map);
//        JsonResponse<List<Map<String, Object>>> response = new JsonResponse<List<Map<String, Object>>>();
//        response.setResult(companyList);
//        return response;
//    }
    /**
     * 批量审批
     */

    @RequestMapping(value = "batchApprovals", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<Boolean> batchApprovals(@RequestBody BatchApprovalsDto dto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        boolean infoSave = trainUserManageService.batchApprovals(dto);
//        if ("1".equals(dto.getCheckState())) {
//            List<CapcoTrainMemberUnitDto> depList = new ArrayList<>();
//            List<SchUserInfoDto>  userList = dto.getSchUserInfoList();
//            for (SchUserInfoDto user: userList) {
//                CapcoTrainMemberUnitDto dep = new CapcoTrainMemberUnitDto();
//                if (StringUtils.isNotEmpty(user.getCompanyCode())){
//                    dep.setCompanyCode(user.getCompanyCode());
//                    dep.setCompanyName(user.getCompanyName());
//                    depList.add(dep);
//                }
//            }
            // 审核通过后，根据证券代码查询会员单位表里面是否存在该公司，若不存在，则新增一条数据
//            trainUserChangeService.addMemberUnits(depList);
//        }
        response.setResult(infoSave);
        return response;
    }


    /**
     * 保存用户信息
     */
    @RequestMapping(value = "userInfoSave", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> userInfoSave(SchUserInfoDto dto) {
        JsonResponse<String> response = new JsonResponse<>();
        boolean infoSave = trainUserManageService.userInfoSave(dto);
        if ("1".equals(dto.getCheckState())) {
            UserChangeDto userChangeDto = new UserChangeDto();
            userChangeDto.setCompanyCode(dto.getCompanyCode());
            userChangeDto.setCompanyName(dto.getCompanyName());
            // 审核通过后，根据证券代码查询会员单位表里面是否存在该公司，若不存在，则新增一条数据
            trainUserChangeService.addMemberUnit(userChangeDto);
        }
        response.setResult(infoSave ? "1" : "0");
        return response;
    }

    /**
     * 更新用户信息
     */
    @RequestMapping(value = "userInfoUpdate", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> userInfoUpdate(SchUserInfoDto dto) {
        trainUserManageService.userInfoUpdate(dto);
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult("1");
        return response;
    }

    /**
     * 删除用户
     */
    @RequestMapping(value = "delUserInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> delUserInfo(SchUserInfoDto dto) {
        trainUserManageService.delUserInfo(dto);
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult("1");
        return response;
    }

    /**
     * 更新用户所属辖区
     */
    @RequestMapping(value = "updateBelongCommission", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> updateBelongCommission(SchUserInfoDto dto) {
        String str=trainUserManageService.updateBelongCommission(dto);
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult(str);
        return response;
    }

    /**
     * 验证用户账号唯一
     */
    @RequestMapping(value = "checkOnly", method = RequestMethod.POST)
    @ResponseBody
    public boolean checkOnly(SchUserInfoDto schUserInfoDto) {
            List<SchUserInfoDto> result = trainUserManageBizMapper.checkOnly(schUserInfoDto);
            return result == null || result.size() == 0;
    }


    /**
     * 刷新会员单位
     */
    @RequestMapping(value = "refreshMember", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> refreshMember() {
        trainUserManageService.refreshMember();
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult("1");
        return response;
    }


    /**
     * 机构信息管理Init
     */
    @RequestMapping(value = "orgUserManageInit")
    public ModelAndView orgUserManageInit() {
        ModelAndView mv = new ModelAndView("trainUserManage/orgUserManage");
        mv.addObject("capcoTrainMemberUnitDto", new CapcoTrainMemberUnitDto());
        return mv;
    }

    /**
    * 修改密码Init
     */
    @RequestMapping(value = "changePasswordpop")
    public ModelAndView editUserPasswordInit(SchUserInfoDto schUserInfoDto) {
        ModelAndView mv = new ModelAndView("trainUserManage/changePassword");
        mv.addObject("schUserInfoDto", schUserInfoDto);
        return mv;
    }

    /**
     * 修改手机号Init
     */
    @RequestMapping(value = "changePhoneInit")
    public ModelAndView changePhoneInit(SchUserInfoDto schUserInfoDto) {
        ModelAndView mv = new ModelAndView("trainUserManage/changePhone");
        mv.addObject("schUserInfoDto", schUserInfoDto);
        return mv;
    }


    /**
     * 修改手机号
     */
    @RequestMapping(value = "changePhone", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> changePhone(SchUserInfoDto schUserInfoDto) {
        String result = trainUserManageService.changePhone(schUserInfoDto);
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult(result);
        return response;
    }

    @RequestMapping(value = "changePasswordSave", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<UserManagerDto> changePasswordSave(SchUserInfoDto schUserInfoDto) {
        trainUserManageService.changePasswordSave(schUserInfoDto);
        JsonResponse<UserManagerDto> response = new JsonResponse<UserManagerDto>();
        response.setResult(new UserManagerDto());
        return response;
    }

    /**
     * 机构列表查询
     */
    @RequestMapping(value = "getOrgInfoList")
    @ResponseBody
    public Map<String, Object> getOrgInfoList(CapcoTrainMemberUnitDto dto) {
        Map<String, Object> info = new HashMap<String, Object>();
        info = super.commonQuery(
                "com.stock.capital.cloud.trainUserManage.dao.TrainUserManageBizMapper.getOrgInfoList",
                dto);
        return info;
    }
    /**
     * 导入历史数据
     */
    @RequestMapping(value = "historyImport")
    @ResponseBody
    public JsonResponse<Boolean> historyImport(){
        return null;
    }

    /**
     * 同步历史记录
     */
    @RequestMapping(value = "getHistory", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> getHistory(SchUserInfoDto dto) {
        trainUserManageService.getHistory(dto);
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult("1");
        return response;
    }

    /**
     * 前端用户信息列表导出
     */
    @RequestMapping(value = "export")
    @ResponseBody
    public ModelAndView export (SchUserInfoDto schUserInfoDto) throws IOException, InvocationTargetException, IllegalAccessException {
        ModelAndView mv = new ModelAndView();
        mv.setView(new DownloadView());
        ParamHander(schUserInfoDto);
        mv.addObject(DownloadView.EXPORT_FILE, trainUserManageService.exportDetailTable(schUserInfoDto));
        mv.addObject(DownloadView.EXPORT_FILE_NAME, "用户信息.xls");
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLS);
        return mv;
    }

//    /**
//     * 用户信息统计导出
//     */
//    @RequestMapping(value = "exportStatisticsData")
//    @ResponseBody
//    public ModelAndView exportStatisticsData () throws IOException, InvocationTargetException, IllegalAccessException {
//        ModelAndView mv = new ModelAndView();
//        mv.setView(new DownloadView());
//        mv.addObject(DownloadView.EXPORT_FILE, trainUserManageService.exportStatisticsData());
//        mv.addObject(DownloadView.EXPORT_FILE_NAME, "用户信息统计.xlsx");
//        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
//        return mv;
//    }
    /**
     * 用户信息导出
     */
    @RequestMapping(value = "exportUser")
    @ResponseBody
    public ModelAndView exportUser (SchUserInfoDto dto) throws IOException, ClassNotFoundException {
        ModelAndView mv = new ModelAndView();
        ParamHander(dto);
        mv.setView(new DownloadView());
        mv.addObject(DownloadView.EXPORT_FILE, trainUserManageService.exportUser(dto));
        mv.addObject(DownloadView.EXPORT_FILE_NAME, "用户信息统计.xlsx");
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
        return mv;
    }

    /**
     * 重置用户密码
     */
    @RequestMapping(value = "initPassword", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<String> initPassword(SchUserInfoDto dto) {
        trainUserManageService.initPassword(dto);
        JsonResponse<String> response = new JsonResponse<String>();
        response.setResult("1");
        return response;
    }

}
