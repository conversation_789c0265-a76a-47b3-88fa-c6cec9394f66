<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.capital.cloud.offline.dao.OfflineBizMapper" >

    <resultMap id="BaseResultMap" type="com.stock.capital.cloud.offline.dto.OfflineTrainDto" >
        <id column="trainId" property="trainId" jdbcType="INTEGER" />
        <result column="trainTitle" property="trainTitle" jdbcType="VARCHAR" />
        <result column="trainContent" property="trainContent" jdbcType="VARCHAR" />
        <result column="trainTeacher" property="trainTeacher" jdbcType="VARCHAR" />
        <result column="trainTeacherStr" property="trainTeacherStr" jdbcType="VARCHAR" />
        <result column="signUpStartTime" property="signUpStartTime" jdbcType="TIMESTAMP" />
        <result column="signUpEndTime" property="signUpEndTime" jdbcType="TIMESTAMP" />
        <result column="activityStartTime" property="activityStartTime" jdbcType="TIMESTAMP" />
        <result column="activityEndTime" property="activityEndTime" jdbcType="TIMESTAMP" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="createUser" property="createUser" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updateUser" property="updateUser" jdbcType="VARCHAR" />
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="publish" property="publish" jdbcType="VARCHAR" />
        <result column="publishType" property="publishType" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="trainType" property="trainType" jdbcType="VARCHAR" />
        <result column="learningForm" property="learningForm" jdbcType="TIMESTAMP" />
        <result column="peopleLimit" property="peopleLimit" jdbcType="VARCHAR" />
        <result column="signUpNumber" property="signUpNumber" jdbcType="VARCHAR" />
        <result column="specificAddress" property="specificAddress" jdbcType="VARCHAR" />
        <result column="issueTime" property="issueTime" jdbcType="VARCHAR" />
        <result column="post" property="post" jdbcType="VARCHAR" />
        <result column="belongCommission" property="belongCommission" jdbcType="VARCHAR" />
        <result column="ifCertified" property="ifCertified" jdbcType="VARCHAR" />
        <result column="ifPayShow" property="ifPayShow" jdbcType="VARCHAR" />
        <result column="ifInvoiceShow" property="ifInvoiceShow" jdbcType="VARCHAR" />
        <collection property="personTypeList" ofType="com.stock.capital.cloud.offline.dto.OfflineTrainTypeCostDto">
            <result column="id" jdbcType="VARCHAR" property="id" />
            <result column="relationId" jdbcType="VARCHAR" property="relationId" />
            <result column="personType" jdbcType="VARCHAR" property="personType" />
            <result column="ifFree" jdbcType="VARCHAR" property="ifFree" />
            <result column="price" jdbcType="VARCHAR" property="price" />
        </collection>
        <collection property="courseList" ofType="com.stock.capital.cloud.common.model.entity.Attachment">
            <result column="id" jdbcType="VARCHAR" property="id" />
            <result column="attName" jdbcType="VARCHAR" property="attName" />
            <result column="attUrl" jdbcType="VARCHAR" property="attUrl" />
            <result column="businessType" jdbcType="VARCHAR" property="businessType" />
            <result column="subType" jdbcType="VARCHAR" property="subType" />
            <result column="documentType" jdbcType="VARCHAR" property="documentType" />
        </collection>
        <collection property="attToList" ofType="com.stock.capital.cloud.liveStreaming.dto.LiveInfoFileDto">
            <id column="id" property="attId" jdbcType="VARCHAR" />
            <result column="attName" property="fileName" jdbcType="VARCHAR" />
            <result column="attUrl" property="attUrl" jdbcType="VARCHAR" />
            <result column="subType" property="attUrl" jdbcType="VARCHAR" />
            <result column="businessType" jdbcType="VARCHAR" property="businessType" />

        </collection>
    </resultMap>
   <select id="trainingOfflineInitList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto" resultType="java.util.Map">
       select  * from (
       select
       ctot.train_id AS trainId,
       ctot.train_title as trainTitle,
       ctot.train_type as trainType,
       ctot.train_content as trainContent,
       ctot.train_teacher as trainTeacher,
       DATE_FORMAT(ctot.sign_up_start_time,'%Y-%m-%d %H:%i:%s') as signUpStartTime,
       DATE_FORMAT(ctot.sign_up_end_time,'%Y-%m-%d %H:%i:%s') as signUpEndTime,
       DATE_FORMAT(ctot.activity_start_time,'%Y-%m-%d %H:%i:%s') as activityStartTime,
       DATE_FORMAT(ctot.activity_end_time,'%Y-%m-%d %H:%i:%s') as activityEndTime,
       ctot.address as address,
       ctot.people_limit as peopleLimit,
       ctot.sign_up_number as signUpNumber,
       ctot.create_user as createUser,
       ctot.create_time as createTime,
       ctot.update_user as updateUser,
       ctot.update_time as updateTime,
       case ctot.publish
       when '0' then '否'
       when '1'then '是' end as publish,
       ctot.publish as publishType,
       IFNULL(GROUP_CONCAT(sc.code_name), '') AS codeName,
       IFNULL(GROUP_CONCAT(ctau.person_type), '') AS personType
       from
       capco_train_offline_train ctot
       left join capco_train_type_cost ctau on ctot.train_id = ctau.relation_id
       LEFT JOIN sa_code sc ON sc.code_value = ctau.person_type AND sc.code_no = 'PERSON_TYPE'
       <where>ctot.status ='1' and ctot.org_id = #{orgId}
          <if test="publish != null and publish != ''">
              AND ctot.publish = #{publish}
          </if>
          <if test="trainTitle != null and trainTitle != ''">
              AND  ctot.train_title like CONCAT('%',#{trainTitle},'%')
          </if>
      </where>
        group by  ctot.train_id
        )a
        <where>
          <if test="codeName != null and codeName != ''">
                a.personType  like CONCAT('%',#{codeName},'%')
          </if>
        </where>
        order by a.createTime desc
   </select>

    <select id="trainingOfflineList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        select
        ctot.train_id AS trainId,
        ctot.train_title as trainTitle,
        ctot.train_content as trainContent,
        ctot.train_teacher as trainTeacher,
            DATE_FORMAT(ctot.sign_up_start_time,'%Y-%m-%d %H:%i:%s') as signUpStartTime,
            DATE_FORMAT(ctot.sign_up_end_time,'%Y-%m-%d %H:%i:%s') as signUpEndTime,
            DATE_FORMAT(ctot.activity_start_time,'%Y-%m-%d %H:%i:%s') as activityStartTime,
            DATE_FORMAT(ctot.activity_end_time,'%Y-%m-%d %H:%i:%s') as activityEndTime,
        ctot.address as address,
        ctot.people_limit as peopleLimit,
        ctot.sign_up_number as  signUpNumber,
        ctot.create_user as  createUser,
        ctot.create_time as createTime,
        ctot.update_user as updateUser,
        ctot.update_time as updateTime,
        case  ctot.publish
        when '0' then '否'
        when '1'then '是' end as publish,
        ctot.publish as publishType,
        IFNULL(GROUP_CONCAT(sc.code_name), '') AS codeName
        from
        capco_train_offline_train ctot
        left join  capco_train_type_cost ctau on ctot.train_id = ctau.relation_id
        LEFT JOIN sa_code sc ON sc.code_value = ctau.person_type AND sc.code_no = 'PERSON_TYPE'
        where ctot.status ='1'
        group by  ctot.train_id
    </select>

    <select id="trainingOfflineInitByIdList"  parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto" resultMap="BaseResultMap">
        select
            ctot.train_id AS trainId,
            ctot.train_title as trainTitle,
            ctot.train_content as trainContent,
            ctot.train_teacher as trainTeacher,
            cttcr.relation_teacher as trainTeacherStr,
            ctot.post as post,
            ctot.belong_commission as belongCommission,
            DATE_FORMAT(ctot.sign_up_start_time,'%Y-%m-%d %H:%i:%s') as signUpStartTime,
            DATE_FORMAT(ctot.sign_up_end_time,'%Y-%m-%d %H:%i:%s') as signUpEndTime,
            DATE_FORMAT(ctot.activity_start_time,'%Y-%m-%d %H:%i:%s') as activityStartTime,
            DATE_FORMAT(ctot.activity_end_time,'%Y-%m-%d %H:%i:%s') as activityEndTime,
            ctot.address as address,
            ctot.people_limit as peopleLimit,
            ctot.create_user as  createUser,
            ctot.create_time as createTime,
            ctot.update_user as updateUser,
            ctot.update_time as updateTime,
            ctot.train_type as trainType,
            ctot.issue_time as issueTime,
            ctot.if_certified as ifCertified,
            ctot.if_pay_show as ifPayShow,
            ctot.if_invoice_show as ifInvoiceShow,
            case  ctot.publish
            when '0' then '否'
            when '1'then '是' end as publish,
            ctot.publish as publishType,
            ctau.relation_id as relationId,
            ctau.person_type AS personType,
            ctau.if_free as ifFree,
            ctau.price as price,
            satt.att_name AS attName,
            satt.att_url AS attUrl,
            satt.id ,
            satt.business_type AS businessType,
            satt.sub_type AS subType,
            satt.document_type as documentType,
            ctot.learning_form AS learningForm,
            ctot.people_limit AS peopleLimit,
            case  ctot.sign_up_number
            when '0' then ''
            else ctot.sign_up_number end as signUpNumber,
            ctot.specific_address AS specificAddress
       from
        capco_train_offline_train ctot
        left join  capco_train_type_cost ctau on ctot.train_id = ctau.relation_id
        LEFT JOIN sa_code sc ON sc.code_value = ctau.person_type AND sc.code_no = 'PERSON_TYPE'
        left join sa_attachment satt on satt.business_id = ctot.train_id
        left join capco_train_type_cost_relation cttcr on cttcr.relation_id = ctot.train_id
        <where>  ctot.status ='1'
        <if test="trainId != null and trainId != ''">
            AND ctot.train_id = #{trainId}
        </if>
        </where>
    </select>

    <update id="updateTrainingOfflineInfo" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        update capco_train_offline_train
        <set >
            <if test="trainId != null and trainId != '' " >
                train_id = #{trainId,jdbcType=VARCHAR},
            </if>
            <if test="trainTitle != null and trainTitle != ''" >
                train_title = #{trainTitle,jdbcType=VARCHAR},
            </if>
            <if test="trainContent != null and trainContent != '' " >
                train_content = #{trainContent,jdbcType=VARCHAR},
            </if>
            <if test="trainTeacher != null and trainTeacher != ''" >
                train_teacher = #{trainTeacher,jdbcType=LONGVARCHAR},
            </if>
            <if test="signUpStartTime != null and signUpStartTime != ''" >
                sign_up_start_time = #{signUpStartTime,jdbcType=VARCHAR},
            </if>
            <if test="signUpEndTime != null and signUpEndTime != ''" >
                sign_up_end_time = #{signUpEndTime,jdbcType=VARCHAR},
            </if>
            <if test="activityStartTime != null and activityStartTime != ''" >
                activity_start_time = #{activityStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="activityEndTime != null and activityEndTime != ''" >
                activity_end_time = #{activityEndTime,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != ''" >
                address = #{address,jdbcType=TIMESTAMP},
            </if>
            <if test="publish != null and publish != ''" >
                publish = #{publish,jdbcType=VARCHAR},
            </if>
            <if test="peopleLimit != null and peopleLimit != ''" >
                people_limit = #{peopleLimit,jdbcType=VARCHAR},
            </if>
            <if test="signUpNumber != null and signUpNumber != '' " >
                sign_up_number = #{signUpNumber,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null and createUser != ''" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null and createTime != ''" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null and updateUser != ''" >
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null and updateTime != ''" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''" >
                status = #{status,jdbcType=VARCHAR},
            </if>
                issue_time = #{issueTime,jdbcType=VARCHAR},
        </set>
        where train_id = #{trainId}
    </update>

    <delete id="deleteTrainingOfflineInfo" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        delete from capco_train_offline_train
        <where>
            <if test="trainId != null and trainId != ''" >
                train_id = #{trainId,jdbcType=VARCHAR},
            </if>
        </where>
    </delete>

    <select id="trainingOfflineSignUpList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto"
            resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        SELECT csu.id,
               csu.train_id  trainId,
               csu.sign_user as signUser,
               csu.status as status,
               su.id as userId,
               su.real_name nickName,
               su.phone,
               su.company_name companyName,
               su.company_code companyCode,
               su.person_type as personType,
               su.belong_commission as belongCommission,
               su.mail mail,
               su.job_name as jobName,
            (select sc.code_name from sa_code sc where sc.code_no = 'BELONG_COMMISSION' and su.belong_commission = sc.code_value) belongCommissionStr,
            (SELECT sc.code_name FROM sa_code sc WHERE sc.code_no='CHECK_STATUS' AND sc.code_value = csu.status) statusStr,
            (SELECT sc.code_name FROM sa_code sc WHERE sc.code_no='PERSON_TYPE' AND sc.code_value = su.person_type) personTypeStr,
            if(cttc.if_free = '0', '免费', IFNULL(cttc.price, '0')) money,csu.payment_status paymentStatus,
            DATE_FORMAT(csu.create_time, '%Y-%m-%d %H:%i:%s') createTime,
            DATE_FORMAT(cer.submit_time, '%Y-%m-%d %H:%i:%s') examineTime,
            cstr.time_range timeRange
        FROM capco_train_offline_train_sign_up csu
        LEFT JOIN sch_user su ON csu.sign_user = su.id
        LEFT JOIN capco_train_type_cost cttc ON cttc.relation_id = csu.train_id and su.person_type = cttc.person_type
        left join (
        select cer.id,cer.create_user,cer.submit_time
        from capco_exam_record cer
        left join capco_exam_rank_map cerm on cerm.id=cer.exam_rank_id
        where cer.if_certificate = '1' and cerm.exam_rank='02'
        )cer on cer.create_user=csu.sign_user
        LEFT JOIN capco_train_offline_train_sign_time_range cstr on cstr.id = csu.time_range_id
        <where> csu.status!= '2' and csu.train_id = #{trainId} and csu.if_system = #{ifSystem}
            <if test="timeRange != null and timeRange !=''">
                and csu.time_range_id = #{timeRange}
            </if>
        </where>
        order by csu.create_time
    </select>

    <select id="trainingOfflineSignUpListIsSys" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto"
            resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        SELECT csu.id,
               csu.train_id  trainId,
               csu.sign_user as signUser,
               csu.status as status,
               su.id as userId,
               su.real_name nickName,
               su.phone,
               su.company_name companyName,
               su.company_code companyCode,
               su.person_type as personType,
               su.belong_commission as belongCommission,
               su.mail mail,
               su.job_name as jobName,
               coi.total_amount as totalAmount,
               (select sc.code_name from sa_code sc where sc.code_no = 'BELONG_COMMISSION' and su.belong_commission = sc.code_value) belongCommissionStr,
               (SELECT sc.code_name FROM sa_code sc WHERE sc.code_no='CHECK_STATUS' AND sc.code_value = csu.status) statusStr,
               (SELECT sc.code_name FROM sa_code sc WHERE sc.code_no='PERSON_TYPE' AND sc.code_value = su.person_type) personTypeStr,
               if(cttc.if_free = '0', '免费', IFNULL(cttc.price, '0')) money,csu.payment_status paymentStatus,
               DATE_FORMAT(csu.create_time, '%Y-%m-%d %H:%i:%s') createTime,
               DATE_FORMAT(cer.submit_time, '%Y-%m-%d %H:%i:%s') examineTime,
                cstr.time_range timeRange
        FROM capco_train_offline_train_sign_up csu
                 LEFT JOIN sch_user su ON csu.sign_user = su.id
                 LEFT JOIN capco_train_type_cost cttc ON cttc.relation_id = csu.train_id and su.person_type = cttc.person_type
                 left join (
            select cer.id,cer.create_user,cer.submit_time
            from capco_exam_record cer
                     left join capco_exam_rank_map cerm on cerm.id=cer.exam_rank_id
            where cer.if_certificate = '1' and cerm.exam_rank='02'
        )cer on cer.create_user=csu.sign_user
                 left join (
            select cod.ware_id,coi.total_amount,cod.create_user
            from capco_order_info coi
                     left join capco_order_detail cod on cod.order_id=coi.id
            where coi.`status`='1' AND coi.order_status='PAID' and cod.ware_type='7'
        ) coi on coi.ware_id = csu.train_id and coi.create_user=csu.sign_user
        LEFT JOIN capco_train_offline_train_sign_time_range cstr on cstr.id = csu.time_range_id
        <where>
            csu.train_id = #{trainId} and csu.if_system = #{ifSystem}
            <if test="nickName != '' and nickName != null">
                and su.real_name LIKE CONCAT('%', #{nickName}, '%')
            </if>
            <if test="phone != '' and phone != null">
                and su.phone = #{phone}
            </if>
            <if test="paymentStatus != '' and paymentStatus != null">
                and csu.payment_status = #{paymentStatus}
            </if>
            <if test="personTypeList != null">
                and su.person_type in
                <foreach collection="personTypeList" open="(" close=")" separator="," item="item" index="item">
                    #{item}
                </foreach>
            </if>
            <if test="statusList != null">
                and csu.status in
                <foreach collection="statusList" open="(" close=")" separator="," item="item" index="item">
                    #{item}
                </foreach>
            </if>
            <if test="belongCommissionList != null and belongCommissionList.size() > 0 ">
                and su.belong_commission in
                <foreach collection="belongCommissionList" item="item" open="(" close=")" separator="," >
                    #{item,jdbcType = VARCHAR}
                </foreach>
            </if>
            <if test="companyCode != null and companyCode != '' ">
                and su.company_code = #{companyCode}
            </if>
            <if test="companyName != null and companyName != '' ">
                and su.company_name = #{companyName}
            </if>
            <if test="createStartTime != null and createEndTime !=''">
                and csu.create_time between #{createStartTime} and CONCAT(#{createEndTime},' 23:59:59')
            </if>
            <if test="timeRange != null and timeRange !=''">
                and csu.time_range_id = #{timeRange}
            </if>
        </where>

        order by csu.create_time desc
    </select>

    <select id="selectTrainingOfflineSignUpListByIds" parameterType="java.util.ArrayList"
            resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        SELECT csu.id,
               csu.train_id  trainId,
               csu.sign_user as signUser
        FROM capco_train_offline_train_sign_up csu
        WHERE csu.id in
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="trainingOfflineById" parameterType="String"
            resultType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        SELECT ctot.train_id                                              AS trainId,
               ctot.train_title                                           AS trainTitle,
               ctot.train_content                                         AS trainContent,
               ctot.train_teacher                                         AS trainTeacher,
               DATE_FORMAT(ctot.sign_up_start_time, '%Y-%m-%d %H:%i:%s')  AS signUpStartTime,
               DATE_FORMAT(ctot.sign_up_end_time, '%Y-%m-%d %H:%i:%s')    AS signUpEndTime,
               DATE_FORMAT(ctot.activity_start_time, '%Y-%m-%d %H:%i:%s') AS activityStartTime,
               DATE_FORMAT(ctot.activity_end_time, '%Y-%m-%d %H:%i:%s')  AS activityEndTime,
               ctot.address                                               AS address,
               ctot.people_limit                                          AS peopleLimit,
               ctot.sign_up_number                                        AS signUpNumber,
               ctot.create_user                                           AS createUser,
               ctot.create_time                                           AS createTime,
               ctot.update_user                                           AS updateUser,
               ctot.update_time                                           AS updateTime,
               IF(ctot.sign_up_number = '0', '', ctot.sign_up_number)     AS signUpNumber,
               ctot.specific_address                                      AS specificAddress,
               sc.code_name                                               AS codeName,
               ctot.train_type                                            AS trainType
        FROM capco_train_offline_train AS ctot
                 LEFT JOIN sa_code AS sc ON sc.id = ctot.train_type
        WHERE ctot.train_id = #{id}
    </select>

    <update id="updateTrainingOfflineSignUpMoney" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        update capco_train_offline_train_sign_up
        <set >
            <if test="signUser != null and signUser != ''" >
                sign_user = #{signUser,jdbcType=VARCHAR},
            </if>
            <if test="trainId != null and trainId != '' " >
                train_id = #{trainId,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="ifSystem != null and ifSystem != ''" >
                if_system = #{ifSystem,jdbcType=VARCHAR},
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                payment_status = #{paymentStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkPerson != null and checkPerson != ''" >
                check_person = #{checkPerson,jdbcType=LONGVARCHAR},
            </if>
            <if test="createUser != null and createUser != ''" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null and createTime != ''" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''" >
                update_user = #{updateUser,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null and updateTime != ''" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="trainingOfflineSignUpMoneyList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        select
        ctotu.id AS id,
        sc.code_name codeName,
        scu.real_name AS nickName,
        scu.phone,
        (select sc.code_name from sa_code sc where sc.code_no = "BELONG_COMMISSION" and sc.code_value = scu.belong_commission) belongCommission,
        scu.company_name as companyName,
        ctmu.company_short_name companyShortName,
        scu.job_name as jobName,
        ( SELECT sc.code_name FROM sa_code sc WHERE sc.code_no = "PERSON_POST" AND sc.code_value = scu.post ) postStr,
        scu.mail as mail,
        scu.company_code AS companyCode,
        DATE_FORMAT(ctotu.create_time,'%Y-%m-%d %H:%i:%s') as createTime,
        case ctotu.status when
        '0' then '待审核'
        when '1' then '通过'
        when '2' then '未通过'end as status,
        case cttc.if_free when
        '0' then '免费'
        else  IFNULL(cttc.price, '0') end as money,
        case ctotu.payment_status when
        '0' then '未交费'
        when '1' then '已缴费' end as paymentStatus,
        cstr.time_range timeRange,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>
            <if test="trainId != null and trainId != ''"  >
                ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where> ) as singUpNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.status = '1'
            <if test="trainId != null and trainId != ''"  >
              and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where> ) as toExamineNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.status = '0'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where> ) as waitExamineNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.status = '2'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where>) as notExamineNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.payment_status = '1'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where>  ) as toMoneyNum,
        (select sum(cttc.price)
        from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id = ctotu.train_id
        <where>   cttc.if_free = '1' and ctotu.payment_status = '1'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where>  ) as moneyAllNum,
        temp.create_time as peymentTime
        from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN capco_train_member_unit ctmu on ctmu.company_code = scu.company_code
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        LEFT JOIN capco_train_offline_train_sign_time_range cstr on cstr.id = ctotu.time_range_id
        left join (
        select cod.ware_id,coi.create_time,coi.create_user
        from capco_order_info coi
        left join capco_order_detail cod ON coi.id = cod.order_id
        where coi.order_status = 'PAID'
        )  temp on temp.ware_id = ctotu.train_id  AND temp.create_user = scu.id
        <where>
        <if test="trainId != null and trainId != ''"  >
            ctotu.train_id = #{trainId}
        </if>
        <if test="nickName != null and nickName != ''" >
            and  scu.real_name like CONCAT('%',#{nickName},'%')
        </if>
        <if test="phone != null and phone != '' " >
            and    scu.phone  like CONCAT('%',#{phone},'%')
        </if>
        <if test="status != null and status != ''" >
            and   ctotu.status = #{status}
        </if>
        <if test="paymentStatus != null and paymentStatus != '' " >
            and    ctotu.payment_status= #{paymentStatus}
        </if>
        </where>
    </select>

    <select id="trainingOfflineSignUpByIdList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
     select id ,status,train_id as trainId
     from
     capco_train_offline_train_sign_up
     where id=#{id}
    </select>

    <select id="trainingOfflineRegistrationByIdList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        select cosu.train_id AS trainId,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>
            <if test="trainId != null and trainId != ''"  >
                ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where> ) as singUpNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.status = '1'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where> ) as toExamineNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.status = '0'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where> ) as waitExamineNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.status = '2'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where>) as notExamineNum,
        (select count(1) from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id  = ctotu.train_id
        <where>  ctotu.payment_status = '1'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where>  ) as toMoneyNum,
        (select sum(cttc.price)
        from capco_train_offline_train_sign_up ctotu
        left join sch_user scu on ctotu.sign_user = scu.id
        LEFT JOIN sa_code sc ON sc.code_value = scu.person_type
        AND sc.code_no = 'PERSON_TYPE'
        LEFT JOIN capco_train_type_cost cttc ON scu.person_type = cttc.person_type and cttc.relation_id = ctotu.train_id
        <where>   cttc.if_free = '1' and ctotu.payment_status = '1'
            <if test="trainId != null and trainId != ''"  >
                and   ctotu.train_id = #{trainId}
            </if>
            <if test="nickName != null and nickName != ''" >
                and  scu.real_name like CONCAT('%',#{nickName},'%')
            </if>
            <if test="phone != null and phone != '' " >
                and    scu.phone  like CONCAT('%',#{phone},'%')
            </if>
            <if test="status != null and status != ''" >
                and   ctotu.status = #{status}
            </if>
            <if test="paymentStatus != null and paymentStatus != '' " >
                and    ctotu.payment_status= #{paymentStatus}
            </if>
        </where>  ) as moneyAllNum
        from capco_train_offline_train_sign_up cosu
        where cosu.train_id = #{trainId}
        group by cosu.train_id
    </select>

    <insert id="insertOfflineTrain" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        <selectKey resultType="java.lang.String" keyProperty="trainId" order="BEFORE">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        insert into capco_train_offline_train (train_id,train_title, train_content,
        train_teacher, post,belong_commission,sign_up_start_time, sign_up_end_time,activity_start_time,activity_end_time,
        address, publish, people_limit,sign_up_number, create_user,if_pay_show,if_invoice_show,
        create_time, update_user, update_time,status,train_type,learning_form,org_id,specific_address,issue_time,if_certified
        )
        values (#{trainId,jdbcType=VARCHAR},#{trainTitle,jdbcType=VARCHAR}, #{trainContent,jdbcType=VARCHAR}, #{trainTeacher,jdbcType=LONGVARCHAR},
          #{post,jdbcType=VARCHAR},#{belongCommission,jdbcType=VARCHAR},
        #{signUpStartTime,jdbcType=VARCHAR},#{signUpEndTime,jdbcType=VARCHAR}, #{activityStartTime,jdbcType=TIMESTAMP},#{activityEndTime,jdbcType=VARCHAR},
          #{address,jdbcType=TIMESTAMP},#{publish,jdbcType=VARCHAR}, #{peopleLimit,jdbcType=VARCHAR},#{signUpNumber,jdbcType=TIMESTAMP},
        #{createUser,jdbcType=VARCHAR},#{ifPayShow,jdbcType=VARCHAR},#{ifInvoiceShow,jdbcType=VARCHAR},
          now(),#{updateUser,jdbcType=VARCHAR}, now(),#{status,jdbcType=VARCHAR},#{trainType},#{learningForm,jdbcType=VARCHAR},
          #{orgId,jdbcType=VARCHAR},#{specificAddress,jdbcType=VARCHAR},#{issueTime,jdbcType=VARCHAR},#{ifCertified,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateOfflineTrain" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        update capco_train_offline_train
        <set >
            train_content = #{trainContent,jdbcType=VARCHAR},
            train_teacher = #{trainTeacher,jdbcType=VARCHAR},
                post = #{post,jdbcType=VARCHAR},
                belong_commission= #{belongCommission,jdbcType=VARCHAR},
            if_pay_show = #{ifPayShow,jdbcType=VARCHAR},
            if_invoice_show = #{ifInvoiceShow,jdbcType=VARCHAR},
            <if test="trainTitle != null and trainTitle != ''" >
                train_title = #{trainTitle,jdbcType=VARCHAR},
            </if>
            <if test="signUpStartTime != null and signUpStartTime != '' " >
                sign_up_start_time = #{signUpStartTime,jdbcType=VARCHAR},
            </if>
            <if test="signUpEndTime != null and signUpEndTime != ''" >
                sign_up_end_time = #{signUpEndTime,jdbcType=LONGVARCHAR},
            </if>
            <if test="activityStartTime != null and activityStartTime != '' " >
                activity_start_time = #{activityStartTime,jdbcType=VARCHAR},
            </if>
            <if test="activityEndTime != null and activityEndTime != ''" >
                activity_end_time = #{activityEndTime,jdbcType=LONGVARCHAR},
            </if>
            <if test="createUser != null and createUser != ''" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != ''" >
                address = #{address,jdbcType=LONGVARCHAR},
            </if>
            <if test="publish != null and publish != '' " >
                publish = #{publish,jdbcType=VARCHAR},
            </if>
            <if test="peopleLimit != null and peopleLimit != ''" >
                people_limit = #{peopleLimit,jdbcType=LONGVARCHAR},
            </if>
            <if test="signUpNumber != null and signUpNumber != ''" >
                sign_up_number = #{signUpNumber,jdbcType=LONGVARCHAR},
            </if>
            <if test="createUser != null and createUser != ''" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null and createTime != ''" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''" >
                update_user = #{updateUser,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null and updateTime != ''" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="trainType != null and trainType != ''" >
                train_type = #{trainType},
            </if>
            <if test="learningForm != null and learningForm != ''" >
                learning_form = #{learningForm,jdbcType=VARCHAR},
            </if>
            <if test="specificAddress != null and specificAddress != ''" >
                specific_address = #{specificAddress,jdbcType=VARCHAR},
            </if>
            <if test="ifCertified != null and ifCertified != ''" >
                if_certified = #{ifCertified,jdbcType=VARCHAR},
            </if>
                issue_time = #{issueTime,jdbcType=VARCHAR},
        </set>
        where train_id = #{trainId}
    </update>

    <select id="getTeacherList" resultType="com.stock.core.dto.OptionDto">
        select
        teach_id as value,
        teach_name as label
        from sch_teach_info
        where status = '1'
        <if test="orgId !=null and orgId != '' ">
            org_id = #{orgId}
        </if>
    </select>
    <select id="getPersonTypeList" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainTypeCostDto">
        select sc.code_value as codeValue,sc.code_name as codeName,cttc.relation_id as relationId,cttc.person_type personType,cttc.if_free as ifFree,cttc.price as price
        from sa_code sc
        left join capco_train_type_cost cttc on cttc.person_type=sc.code_value and cttc.relation_id = #{id}
        where  sc.code_no = #{codeNo}
        order by sc.sort_no
    </select>

    <insert id="insertTrainCost" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainTypeCostDto">
        insert into capco_train_type_cost
        (
        id,relation_id,person_type,original_price,if_discount,discount,if_free,price,type,limit_number
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            uuid_short(),
             #{item.relationId,jdbcType=VARCHAR},
             #{item.personType,jdbcType=VARCHAR},
             #{item.originalPrice,jdbcType=VARCHAR},
             #{item.ifDiscount,jdbcType=VARCHAR},
             #{item.discount,jdbcType=VARCHAR},
             #{item.ifFree,jdbcType=VARCHAR},
             #{item.price,jdbcType=VARCHAR},
             #{item.type,jdbcType=VARCHAR},
             #{item.limitNumber,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <delete id="deletePersonList" parameterType="java.lang.String">
        delete from capco_train_type_cost
        <where>
            <if test="_parameter != null" >
               relation_id = #{relationId}
            </if>
        </where>
    </delete>

    <delete id="deletePersonListOfTags" parameterType="java.lang.String">
        delete from capco_train_type_cost
        <where>
            type = '1'
            <if test="_parameter != null" >
               and relation_id = #{relationId}
            </if>
        </where>
    </delete>

    <delete id="deleteByIdList" parameterType="com.stock.capital.cloud.common.model.entity.Attachment">
        delete from capco_train_type_cost
        where id in (
        <foreach collection="list" item="item" index="index" open="" close="" separator=",">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        )
    </delete>

    <update id="updateAttachList" parameterType="com.stock.capital.cloud.common.model.entity.Attachment">
    <foreach collection="list" item="item" separator=";">
        update sa_attachment
        <set>
             att_name = #{item.attName,jdbcType=VARCHAR},
        </set>
        where id = #{item.id}
    </foreach>
    </update>

    <update id="updateEbSchoolLiveInfoFileParam" parameterType="com.stock.capital.cloud.liveStreaming.dto.LiveInfoFileParamDto">
        <foreach collection="list" item="item" separator=";">
            update sa_attachment
            <set>
                att_name = #{item.fileName,jdbcType=VARCHAR},
            </set>
            where id = #{item.attachmentId}
        </foreach>
    </update>

    <select id="getPersonLabelList" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainTypeCostDto">
        SELECT relation_id relationId,person_type personType
        from capco_train_type_cost
        left join sa_code
        where relation_id = #{id}
    </select>

    <select id="getPersonTypeByRelationId" resultType="com.stock.capital.cloud.offline.dto.OfflineTrainTypeCostDto">
        SELECT person_type  AS personType,
               limit_number AS limitNumber
        FROM capco_train_type_cost
        WHERE relation_id = #{id}
    </select>

    <insert id="updateIfSystemByIds" parameterType="java.lang.String">
        update capco_train_offline_train_sign_up set if_system = '1',status = '0'
        where id in
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </insert>

    <insert id="insertRelationTrainCost" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainTypeCostRelationDto">
        insert into capco_train_type_cost_relation (id,relation_id,relation_type,relation_name,relation_img,relation_teacher)
        values (uuid_short(), #{relationId,jdbcType=VARCHAR}, #{relationType,jdbcType=VARCHAR}, #{relationName,jdbcType=VARCHAR},
                #{relationImg,jdbcType=VARCHAR},#{relationTeacher,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteRelationTrainCost" parameterType="java.lang.String">
        delete from capco_train_type_cost_relation
        <where>
            <if test="_parameter != null" >
                relation_id = #{relationId}
            </if>
        </where>
    </delete>

    <select id="getOfflineUserInfoList" resultType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto"
            parameterType="com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto">
        select ct.id,t1.nick_name nickName,
        t1.id userId,
        t1.real_name realName,
        t1.org_name orgName,t1.person_type codeValue,
        t1.job_name jobName,t1.acc_id accId,
        t1.phone phone, t1.mail mail,
        (select sc.code_name from sa_code sc where sc.code_no = "PERSON_TYPE" and sc.code_value = t1.person_type) personType,
        (select sc.code_name from sa_code sc where sc.code_no = "BELONG_COMMISSION" and sc.code_value = t1.belong_commission) belongCommission,
        (select sc.code_name from sa_code sc where sc.code_no = "CHANNEL_TYPE" and sc.code_value = t1.user_type) userType,
        date_format(ct.create_time,'%Y-%m-%d %H:%i:%s') createDate,
        DATE_FORMAT(cer.submit_time, '%Y-%m-%d %H:%i:%s') examineTime,
        t1.company_code companyCode,
        t1.company_name companyName,
        t1.lock_state lockState,
        case when  (t1.status = 'C' or t1.status = 'U') then '有效' when t1.status = 'R' then '已注销'  when t1.status = 'D' then '已删除' end status,
        (select sc.code_name from sa_code sc where sc.code_no = "CHECK_STATUS" and sc.code_value = ct.status) checkState
        from capco_train_offline_train_sign_up ct
        left join sch_user t1 on t1.id = ct.sign_user
        left join (
        select cer.id,cer.create_user,cer.submit_time
        from capco_exam_record cer
        left join capco_exam_rank_map cerm on cerm.id=cer.exam_rank_id
        where cer.if_certificate = '1' and cerm.exam_rank='02'
        )cer on cer.create_user=ct.sign_user
        <where>
            ct.train_id = #{id} and ct.if_system = #{ifSystem}
            <if test="personLabelList != null and personLabelList.size()>0">
                and ct.sign_user not in
                <foreach collection="personLabelList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="companyCode != null and companyCode != ''">
                and t1.company_code like concat('%',#{companyCode},'%')
            </if>
            <if test="companyName != null and companyName != ''">
                and t1.company_name like concat('%',#{companyName},'%')
            </if>
            <if test="realName != null and realName != ''">
                and t1.real_name like CONCAT('%' , #{realName} , '%')
            </if>
            <if test="mail != null and mail != ''">
                and t1.mail like CONCAT('%' , #{mail} , '%')
            </if>
            <if test="phone != null and phone != ''">
                and t1.phone like CONCAT('%' , #{phone} , '%')
            </if>
            <if test="jobName != null and jobName != ''">
                and(
                <foreach collection="jobName.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                    t1.job_name like CONCAT('%' , #{item} , '%')
                </foreach>
                )
            </if>
            <if test="notJobName != null and notJobName != ''">
                and(
                <foreach collection="notJobName.split(',')" index="index" item="item" open="(" separator="and" close=")">
                    t1.job_name not like CONCAT('%' , #{item} , '%')
                </foreach>
                )
            </if>
            <if test="personType != null and personType != ''">
                and(
                <foreach collection="personType.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{item},t1.person_type)
                </foreach>
                )
            </if>
            <if test="personLabel != null and personLabel != ''">
                and(
                <foreach collection="personLabel.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{item},t1.person_label)
                </foreach>
                )
            </if>
            <if test="belongCommission != null and belongCommission != ''">
                and(
                <foreach collection="belongCommission.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{item},t1.belong_commission)
                </foreach>
                )
            </if>
            <if test="checkStatus != null and checkStatus != ''">
                and(
                <foreach collection="checkStatus.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{item},ct.status)
                </foreach>
                )
            </if>
            <if test="userId != null and userId != ''">
                and t1.id = #{userId}
            </if>
            <if test="createDateAfter != null and createDateAfter != ''">
                and  STR_TO_DATE(#{createDateAfter}, '%Y-%m-%d %H:%i') &lt; t1.create_time
            </if>
        </where>
        order by cer.submit_time
    </select>

    <update id="updateSignUpStatus" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        update capco_train_offline_train_sign_up set status = '1'
        where id in
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <update id="batchExamine" parameterType="java.util.HashMap">
        update capco_train_offline_train_sign_up set status = #{status}
        where id in
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <insert id="insertExamRankMap" parameterType="com.stock.capital.cloud.specialManage.dto.ExamRankDto">
        insert into capco_exam_rank_map (id,exam_id,exam_rank,paper_name,status)
        value (uuid_short(),#{examId},#{examRank},#{paperName},'1')
    </insert>

    <select id="getPhoneList" parameterType="java.util.Map" resultType="java.lang.String">
        select su.phone
        from sch_user su
        where
        su.id in
        <foreach collection="userList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and su.id in (
        select t.sign_user
        from capco_train_offline_train_sign_up t
        where t.status = '0' and t.train_id = #{trainId}
        )
    </select>

    <update id="offPutTrain" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        update capco_train_offline_train_sign_up csu set csu.train_id = #{trainId}, status = '0', certificate_status = #{certificateStatus},
        check_person = #{checkPerson},check_time = #{checkTime}, csu.update_user = #{updateUser}, csu.update_time = #{updateTime}
        where csu.id in
        <foreach collection="selIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getTrainingOfflineList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto" resultType="java.util.Map">
        select  * from (
        select
        ctot.train_id AS trainId,
        ctot.train_title as trainTitle,
        DATE_FORMAT(ctot.sign_up_start_time,'%Y-%m-%d %H:%i:%s') as signUpStartTime,
        DATE_FORMAT(ctot.sign_up_end_time,'%Y-%m-%d %H:%i:%s') as signUpEndTime,
        DATE_FORMAT(ctot.activity_start_time,'%Y-%m-%d %H:%i:%s') as activityStartTime,
        DATE_FORMAT(ctot.activity_end_time,'%Y-%m-%d %H:%i:%s') as activityEndTime,
        ctot.address as address,
        ctot.create_time as createTime,
        case ctot.publish
        when '0' then '否'
        when '1'then '是' end as publish,
        ctot.publish as publishType
        from
        capco_train_offline_train ctot
        left join capco_train_type_cost ctau on ctot.train_id = ctau.relation_id
        LEFT JOIN sa_code sc ON sc.code_value = ctau.person_type AND sc.code_no = 'PERSON_TYPE'
        <where>ctot.status ='1' and ctot.org_id = #{orgId}
            <if test="trainType != null and trainType != ''">
                AND  ctot.train_type = #{trainType}
            </if>
            <if test="trainId != null and trainId != ''">
                AND  ctot.train_id != #{trainId}
            </if>
        </where>
        group by  ctot.train_id
        )a
        order by a.createTime desc
    </select>

    <insert id="trainOfflineAddNewUser" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        insert into capco_train_offline_train_sign_up (id,train_id,time_range_id,sign_user,status,if_system,payment_status,create_user,create_time)
        values
        <foreach collection="selIds" item="item" index="index" separator=",">
            (uuid_short(),#{trainId,jdbcType=VARCHAR},#{timeRangeId,jdbcType=VARCHAR},#{item},"1","1","0", #{createUser,jdbcType=VARCHAR},now())
        </foreach>
    </insert>

    <select id="getIdByPhone" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto"
        resultType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
          select ctotsu.id
          from capco_train_offline_train_sign_up ctotsu
          left join sch_user su on ctotsu.sign_user = su.id
          <where>
              ctotsu.train_id = #{trainId}
              and su.phone in
              <foreach collection="selIds" index="index" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>
          </where>
    </select>

    <delete id="deleteOfflineTrainTimeRange" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        delete from capco_train_offline_train_sign_time_range where train_id = #{trainId}
    </delete>

    <insert id="insertOfflineTrainTimeRange" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto">
        insert into capco_train_offline_train_sign_time_range
        <trim prefix="(" suffix=")" suffixOverrides="," >
            id,train_id,time_range,sign_limit
        </trim>
            values
            <foreach collection="timeRangeMapList" item="item" separator=",">
                <if test="item != null">
                    (<choose>
                    <when test="item.id != null and item.id != ''">
                        #{item.id}
                    </when>
                    <otherwise>
                        uuid_short()
                    </otherwise>
                </choose>,#{trainId,jdbcType=VARCHAR},#{item.timeRange,jdbcType=VARCHAR},#{item.signLimit})
                </if>
            </foreach>
    </insert>

    <select id="getTimeRangeList" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainDto" resultType="com.stock.capital.cloud.offline.dto.TimeRangeDto">
            SELECT t.id,t.train_id trainId, t.time_range timeRange,t.sign_limit signLimit, COUNT(s.id) AS signCount
            FROM capco_train_offline_train_sign_time_range t
                     LEFT JOIN capco_train_offline_train_sign_up s ON t.id = s.time_range_id and s.`status` != '2'
            WHERE t.train_id = #{trainId}
            GROUP BY t.train_id, t.time_range;
    </select>

    <update id="updateTimeRange" parameterType="com.stock.capital.cloud.offline.dto.OfflineTrainSignUpDto">
        update capco_train_offline_train_sign_up
        set time_range_id = #{timeRangeId}
        where id = #{id}
    </update>
</mapper>
