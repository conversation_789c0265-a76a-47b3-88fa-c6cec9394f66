package com.stock.capital.cloud.learningRecordDel.service;

import com.stock.capital.cloud.learningRecordDel.dao.LearningBizMapper;
import com.stock.capital.cloud.learningRecordDel.dto.LearningDto;
import com.stock.capital.cloud.liveStreaming.dto.LiveInfoDto;
import com.stock.core.service.BaseService;
import net.logstash.logback.encoder.org.apache.commons.lang3.ArrayUtils;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Transactional(rollbackFor = {Exception.class})
public class LearningService extends BaseService {
    private static Logger logger = LoggerFactory.getLogger(LearningService.class);

    @Autowired
    private LearningBizMapper learningBizMapper;

    private static HSSFPalette palette;


    public InputStream exportDetailTableList(LearningDto learningDto) throws IOException {
        // 判断是否有指定课程
        if (StringUtils.isNotEmpty(learningDto.getCourseIds())){
            learningDto.setCourseIdList(Arrays.asList(learningDto.getCourseIds().split(",")));
        }
        // 判断是否有指定职务
        List<String>dcField = new ArrayList<>();
        if(StringUtils.isNotEmpty(learningDto.getFieIdName())){
            dcField = Arrays.asList(learningDto.getFieIdName().split(","));
        }else{
            dcField.add("CHAIRMAN"); // 董事长
            dcField.add("VCHAIRMAN"); // 副董事长
            dcField.add("DIRECTOR"); // 董事
            dcField.add("INDEDIRECTOR"); // 独立董事
            dcField.add("INDPDIRECTORNOW"); // 公司独立董事(现任)
            dcField.add("CHAIRSUPERVISOR"); // 监事长
            dcField.add("SUPERVISOR"); // 监事
            dcField.add("WORKERSUPERVISOR"); // 职工监事
            dcField.add("EXSUPERVISOR"); // 外部监事
            dcField.add("EXECUTIVE"); // 高管
            dcField.add("CEO"); // 总经理
            dcField.add("VCEO"); // 副经理
            dcField.add("CFO"); // 财务总监
            dcField.add("BOARDSECRETARY"); // 董事会秘书
        }
        List<LearningDto>dcUserList = new ArrayList<>(); // 查询东财董监高用户
        List<LearningDto>dcUserAllList = new ArrayList<>(); // 查询东财董监高用户(去重)
        List<LearningDto> onDemandList = new ArrayList<>(); // 查询易董所有人
        List<String> userList = new ArrayList<>();
        int total = learningBizMapper.getOnDemandListCount();
        int endRow = 1000;
        Map<String,Object> param = new HashMap<>();
        for(int i=0;i<total;i= i + endRow){
            param.put("startRow", i);
            param.put("endRow", endRow);
            userList = learningBizMapper.getUserIdList(param);
            onDemandList.addAll(learningBizMapper.getOnDemandList(userList)); // 查询现有用户注册人员信息
        }
        List<LiveInfoDto>courseNameList = learningBizMapper.playbackRecordCourse(learningDto);//查询所有课程名字；
        // 查询东财董监高人员信息
        for(String str:dcField){
            dcUserList.addAll(learningBizMapper.selectDcUserList(str));
//            dcUserList.addAll(new ArrayList<>());
        }
       if(CollectionUtils.isNotEmpty(dcUserList)){
           // 将高管人员去重
           dcUserAllList = dcUserList.stream()
                   .collect(Collectors.collectingAndThen(
                           Collectors.toMap(
                                   dto -> dto.getCompanyCode() + "-" + dto.getPersonName(),
                                   dto -> dto,
                                   (existing, replacement) -> existing // 如果有重复的键，保留现有的
                           ),
                           map -> new ArrayList<>(map.values())
                   ));
       }
        logger.info("LearningService------------------------全部东财人员条数"+dcUserAllList.size());
        List<LearningDto> mergedList = new ArrayList<>();
        //将现有注册用户list与董监高用户list进行比对合并
        mergedList = dcUserAllList.parallelStream()
                .map(dcUser -> onDemandList.parallelStream()
                        .filter(onDemand -> onDemand.getCompanyCode().equals(dcUser.getCompanyCode())
                                && onDemand.getPersonName().equals(dcUser.getPersonName()))
                        .findFirst()
                        .orElse(dcUser))
                .sorted(Comparator.comparing(LearningDto::getCompanyCode)) // 按公司代码排序
                .collect(Collectors.toList());
        logger.info("LearningService--one----------------------全部人员条数" +mergedList.size());
       if(StringUtils.isEmpty(learningDto.getUserType())){
           mergedList.addAll(onDemandList);
           logger.info("LearningService--two----------------------全部人员条数" +mergedList.size());
           mergedList = mergedList.stream()
                   .collect(Collectors.collectingAndThen(
                           Collectors.toMap(
                                   dto -> dto.getCompanyCode() + "-" + dto.getPersonName() + "-" + dto.getId(),
                                   dto -> dto,
                                   (existing, replacement) -> existing // 如果有重复的键，保留现有的
                           ),
                           map -> new ArrayList<>(map.values())
                   ));
           mergedList = mergedList.parallelStream().sorted(Comparator.comparing(LearningDto::getCompanyCode)) // 按公司代码排序
                   .collect(Collectors.toList());
           logger.info("LearningService--twotwo----------------------全部人员条数" +mergedList.size());
       }
        logger.info("LearningService--three----------------------全部人员条数"+mergedList.size());

        List<LearningDto> unregisteredExecutives = mergedList.stream()
                .filter(dto -> org.apache.commons.lang3.StringUtils.isEmpty(dto.getId()))
                .collect(Collectors.toList());
        // 设置Excel内容
        SXSSFWorkbook workbook = exportCourseSetting(mergedList,courseNameList,unregisteredExecutives);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.flush();
        os.close();
        return new ByteArrayInputStream(os.toByteArray());
    }
    //拆分sheet

    /**
     * @param LearningDtoList 高管学习详情sheet页数据
     * @param courseNameList  课程名字
     * @param unregisteredExecutives 未注册高管sheet页数据
     * @return
     */
    private SXSSFWorkbook exportCourseSetting(List<LearningDto> LearningDtoList,List<LiveInfoDto>courseNameList,List<LearningDto> unregisteredExecutives) {
        int batchSize = 10000; // 每个sheet表的数据量
        // 高管学习详情sheet页
        int learningSheets = (LearningDtoList.size() + batchSize - 1) / batchSize; // 表的数量
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        List<List<LearningDto>> learningBatches = new ArrayList<>();
        for (int i = 0; i < learningSheets; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, LearningDtoList.size());
            List<LearningDto> batch = LearningDtoList.subList(fromIndex, toIndex);
            learningBatches.add(batch);
        }
        for (int i = 0; i < learningSheets; i++) {
            getCourseListWorkbook(workbook, learningBatches.get(i), i + 1,courseNameList);
        }

        // "未注册公司"sheet - 放在未注册高管sheet页前面
        List<LearningDto> unregisteredCompanies = LearningDtoList.stream()
                .filter(dto -> org.apache.commons.lang3.StringUtils.isEmpty(dto.getId())) // 未注册人员
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                dto -> dto.getCompanyCode(), // 按公司代码去重
                                dto -> dto,
                                (existing, replacement) -> existing // 如果有重复的键，保留现有的
                        ),
                        map -> new ArrayList<>(map.values())
                ))
                .stream()
                .sorted(Comparator.comparing(LearningDto::getCompanyCode)) // 按公司代码排序
                .collect(Collectors.toList());

        if (!unregisteredCompanies.isEmpty()) {
            exportUnregisteredCompaniesSetting(workbook, unregisteredCompanies);
        }

        // 添加未注册高管sheet页
        int unregisteredExecutivesSheets = (unregisteredExecutives.size() + batchSize - 1) / batchSize; // 表的数量
        List<List<LearningDto>> unregisteredExecutivesBatches = new ArrayList<>();
        for (int i = 0; i < unregisteredExecutivesSheets; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, unregisteredExecutives.size());
            List<LearningDto> batch = unregisteredExecutives.subList(fromIndex, toIndex);
            unregisteredExecutivesBatches.add(batch);
        }

        for (int i = 0; i < unregisteredExecutivesSheets; i++) {
            getUnregisteredExecutivesWorkbook(workbook, unregisteredExecutivesBatches.get(i), i + 1);
        }
        // "已注册未学习高管"sheet
        List<LearningDto> registeredUnlearnedExecutives = LearningDtoList.stream()
                .filter(dto -> org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getId())) // 已注册
                .filter(dto -> {
                    // 判断是否有任一课程未获得学分
                    if (dto.getLiveList() == null || dto.getLiveList().isEmpty()) {
                        return true; // 没有学习记录，算作未学习
                    }

                    // 检查是否有任一课程未获得学分
                    for (LiveInfoDto course : courseNameList) {
                        boolean hasLearned = false;
                        boolean hasCredit = false;

                        for (LiveInfoDto liveInfo : dto.getLiveList()) {
                            if (org.apache.commons.lang3.StringUtils.isNotEmpty(liveInfo.getLiveId())
                                && StringUtils.equals(course.getLiveId(), liveInfo.getLiveId())) {
                                hasLearned = true;
                                if (liveInfo.getCredit() > 0) {
                                    hasCredit = true;
                                }
                                break;
                            }
                        }

                        // 如果有任一课程未学习或未获得学分，则符合条件
                        if (!hasLearned || !hasCredit) {
                            return true;
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());

        if (!registeredUnlearnedExecutives.isEmpty()) {
            exportRegisteredUnlearnedExecutivesSetting(workbook, registeredUnlearnedExecutives, courseNameList);
        }

        return workbook;
    }

    public SXSSFSheet getCourseListWorkbook(SXSSFWorkbook workbook, List<LearningDto> list, int sheetNumber,List<LiveInfoDto>courseNameList) {
        SXSSFSheet sheet = workbook.createSheet("高管学习详情" + sheetNumber);
        SXSSFRow row = null;
        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
        cellStyle.setWrapText(true);//自动换行
        // 设置字体
        Font f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setFontName("宋体");
        // 将字体赋给样式
        cellStyle.setFont(f);
        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);//自动换行
        // 制作标题行
        SXSSFCell cell = sheet.createRow(0).createCell(0);;
        LearningDto dto = null;
        // 居中
        CellStyle conCenterStyle = workbook.createCellStyle();
        conCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        conCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 居右
        CellStyle conRightStyle = workbook.createCellStyle();
        conRightStyle.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        CellStyle conRightStyleDc = workbook.createCellStyle();
        conRightStyleDc.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyleDc.setVerticalAlignment(VerticalAlignment.CENTER);
        conRightStyleDc.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        conRightStyleDc.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        CellStyle conRightStyleDcw = workbook.createCellStyle();
        conRightStyleDcw.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyleDcw.setVerticalAlignment(VerticalAlignment.CENTER);
        conRightStyleDcw.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
        conRightStyleDcw.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        CellStyle conRightStyleLs = workbook.createCellStyle();
        conRightStyleLs.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyleLs.setVerticalAlignment(VerticalAlignment.CENTER);
        conRightStyleLs.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        conRightStyleLs.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //居左
        CellStyle conLeftStyle = workbook.createCellStyle();
        conLeftStyle.setAlignment(HorizontalAlignment.LEFT);
        conLeftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置标题数组
        String[] titles1 = new String[]{"用户id", "用户姓名","职务", "公司代码", "公司简称", "公司全称", "获得总学分"};
        String[] courseName  = new String[courseNameList.size()];
        for(int i = 0;i<courseNameList.size();i++){
            courseName[i] = courseNameList.get(i).getLiveName();
        }
        String[] titles = (String[]) ArrayUtils.addAll(titles1, courseName);
        row = sheet.createRow(0);
        row.setHeight((short) 800);
        for (int j = 0; j < titles.length; j++) {
            sheet.setDefaultColumnStyle(j, cs);
            sheet.setColumnWidth(j, 4000);
            cell = row.createCell(j);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(titles[j]);
        }
        for (int i = 0; i < list.size(); i++) {
            dto = list.get(i);
            row = sheet.createRow(i + 1);
            //id
            cell = row.createCell(0);
            String id = dto.getId();
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(id)){
                cell.setCellValue(id);
            }else{
                cell.setCellValue("未注册");
            }
            cell.setCellStyle(cellStyle);
            //用户姓名
            cell = row.createCell(1);
            String personName = dto.getPersonName();
            cell.setCellValue(personName);
            cell.setCellStyle(cellStyle);
            //职务
            cell = row.createCell(2);
            String post = dto.getPostStr();
            cell.setCellValue(post);
            cell.setCellStyle(cellStyle);
            //公司代码
            cell = row.createCell(3);
            String companyCode = dto.getCompanyCode();
            cell.setCellValue(companyCode);
            cell.setCellStyle(cellStyle);
            //公司简称
            cell = row.createCell(4);
            String companyName = dto.getCompanyShortName();
            cell.setCellValue(companyName);
            cell.setCellStyle(cellStyle);
            // 公司全称
            cell = row.createCell(5);
            String zh = dto.getCompanyName();
            cell.setCellValue(zh);
            cell.setCellStyle(cellStyle);

            // 获得总学分 - 只计算当前选中课程的学分
            double num = 0;
            if(dto.getLiveList() != null && dto.getLiveList().size() > 0) {
                for (LiveInfoDto selectedCourse : courseNameList) {
                    for (LiveInfoDto userCourse : dto.getLiveList()) {
                        if(org.apache.commons.lang3.StringUtils.isNotEmpty(userCourse.getLiveId())
                           && StringUtils.equals(selectedCourse.getLiveId(), userCourse.getLiveId())) {
                            num += userCourse.getCredit();
                            break; // 找到匹配的课程后跳出内层循环
                        }
                    }
                }
            }
            if(num > 0){
                cell = row.createCell(6);
                cell.setCellValue(num);
                cell.setCellStyle(cellStyle);
            }else{
                cell = row.createCell(6);
                String credit = dto.getCredit();
                cell.setCellValue(credit);
                cell.setCellStyle(cellStyle);
            }
            int cellNum = 7;
            for(int x=0;x<courseNameList.size();x++) {
                if(dto.getLiveList() != null && dto.getLiveList().size() > 0) {
                    cell = row.createCell(cellNum++);
                    Boolean flag = false;
                    Boolean ifLookFlag = false;
                    double credit = 0;
                    for (int j = 0; j < dto.getLiveList().size(); j++) {
                        if(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getLiveList().get(j).getLiveId()) && StringUtils.equals(courseNameList.get(x).getLiveId(),dto.getLiveList().get(j).getLiveId())){
                            ifLookFlag = true;
                            if(dto.getLiveList().get(j).getCredit() > 0){
                                flag = true;
                                credit = dto.getLiveList().get(j).getCredit();
                            }
                        }
                    }
                    if(flag && ifLookFlag){
                        cell.setCellValue("（是）已获得学分 " + credit); // 黄色
                        cell.setCellStyle(conRightStyleDc);
                    }else if(!ifLookFlag && !flag){
                        cell.setCellValue("（否）未获得学分"); // 红色
                        cell.setCellStyle(conRightStyleDcw);
                    }else if(ifLookFlag && !flag){
                        cell.setCellValue("（是）未获得学分"); // 蓝色
                        cell.setCellStyle(conRightStyleLs);
                    }
                }else{
                    cell = row.createCell(cellNum++);
                    cell.setCellValue("（否）未获得学分");
                    cell.setCellStyle(conRightStyleDcw);
                }
            }

            sheet.setColumnWidth(0, 7000);
            sheet.setColumnWidth(1, 3000);
            sheet.setColumnWidth(2, 3000);
            sheet.setColumnWidth(3, 7000);
            sheet.setColumnWidth(4, 7000);
            sheet.setColumnWidth(5, 3000);
            sheet.setColumnWidth(6, 3000);
            for(int x=0;x<courseNameList.size();x++) {
                sheet.setColumnWidth(x+6, 6000);
            }
        }
        return sheet;
    }

    /**
     * 创建未注册高管sheet页
     */
    public SXSSFSheet getUnregisteredExecutivesWorkbook(SXSSFWorkbook workbook, List<LearningDto> list, int sheetNumber) {
        SXSSFSheet sheet = workbook.createSheet("未注册高管" + sheetNumber);
        SXSSFRow row = null;

        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
        cellStyle.setWrapText(true);//自动换行

        // 设置字体
        Font f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setFontName("宋体");
        // 将字体赋给样式
        cellStyle.setFont(f);

        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);//自动换行

        // 设置标题数组 - 只保留用户姓名、公司代码、公司简称、公司全称
        String[] titles = new String[]{"用户姓名", "公司代码", "公司简称", "公司全称"};

        // 创建标题行
        row = sheet.createRow(0);
        row.setHeight((short) 800);
        for (int j = 0; j < titles.length; j++) {
            sheet.setDefaultColumnStyle(j, cs);
            sheet.setColumnWidth(j, 4000);
            SXSSFCell cell = row.createCell(j);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(titles[j]);
        }

        // 填充数据
        for (int i = 0; i < list.size(); i++) {
            LearningDto dto = list.get(i);
            row = sheet.createRow(i + 1);

            // 用户姓名
            SXSSFCell cell = row.createCell(0);
            String personName = dto.getPersonName();
            cell.setCellValue(personName);
            cell.setCellStyle(cellStyle);

            // 公司代码
            cell = row.createCell(1);
            String companyCode = dto.getCompanyCode();
            cell.setCellValue(companyCode);
            cell.setCellStyle(cellStyle);

            // 公司简称
            cell = row.createCell(2);
            String companyShortName = dto.getCompanyShortName();
            cell.setCellValue(companyShortName);
            cell.setCellStyle(cellStyle);

            // 公司全称
            cell = row.createCell(3);
            String companyName = dto.getCompanyName();
            cell.setCellValue(companyName);
            cell.setCellStyle(cellStyle);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 5000); // 用户姓名
        sheet.setColumnWidth(1, 3000); // 公司代码
        sheet.setColumnWidth(2, 5000); // 公司简称
        sheet.setColumnWidth(3, 10000); // 公司全称

        return sheet;
    }

    /**
     * 处理已注册未学习高管数据分页并创建sheet页
     */
    private void exportRegisteredUnlearnedExecutivesSetting(SXSSFWorkbook workbook, List<LearningDto> registeredUnlearnedExecutives, List<LiveInfoDto> courseNameList) {
        int batchSize = 10000; // 每个sheet表的数据量
        int totalSheets = (registeredUnlearnedExecutives.size() + batchSize - 1) / batchSize; // 表的数量

        List<List<LearningDto>> batches = new ArrayList<>();
        for (int i = 0; i < totalSheets; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, registeredUnlearnedExecutives.size());
            List<LearningDto> batch = registeredUnlearnedExecutives.subList(fromIndex, toIndex);
            batches.add(batch);
        }

        for (int i = 0; i < totalSheets; i++) {
            getRegisteredUnlearnedExecutivesWorkbook(workbook, batches.get(i), i + 1, courseNameList);
        }
    }

    /**
     * 创建已注册未学习高管sheet页
     */
    public SXSSFSheet getRegisteredUnlearnedExecutivesWorkbook(SXSSFWorkbook workbook, List<LearningDto> list, int sheetNumber, List<LiveInfoDto> courseNameList) {
        SXSSFSheet sheet = workbook.createSheet("已注册未学习高管" + sheetNumber);
        SXSSFRow row = null;

        // 创建单元格样式 - 与getCourseListWorkbook保持一致
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
        cellStyle.setWrapText(true);//自动换行

        // 设置字体
        Font f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setFontName("宋体");
        // 将字体赋给样式
        cellStyle.setFont(f);

        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);//自动换行

        CellStyle conRightStyleDc = workbook.createCellStyle();
        conRightStyleDc.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyleDc.setVerticalAlignment(VerticalAlignment.CENTER);
        conRightStyleDc.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        conRightStyleDc.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        CellStyle conRightStyleDcw = workbook.createCellStyle();
        conRightStyleDcw.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyleDcw.setVerticalAlignment(VerticalAlignment.CENTER);
        conRightStyleDcw.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
        conRightStyleDcw.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        CellStyle conRightStyleLs = workbook.createCellStyle();
        conRightStyleLs.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyleLs.setVerticalAlignment(VerticalAlignment.CENTER);
        conRightStyleLs.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        conRightStyleLs.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置标题数组 - 与getCourseListWorkbook保持一致
        String[] titles1 = new String[]{"用户id", "用户姓名","职务", "公司代码", "公司简称", "公司全称", "获得总学分"};
        String[] courseName  = new String[courseNameList.size()];
        for(int i = 0;i<courseNameList.size();i++){
            courseName[i] = courseNameList.get(i).getLiveName();
        }
        String[] titles = (String[]) ArrayUtils.addAll(titles1, courseName);

        row = sheet.createRow(0);
        row.setHeight((short) 800);
        for (int j = 0; j < titles.length; j++) {
            sheet.setDefaultColumnStyle(j, cs);
            sheet.setColumnWidth(j, 4000);
            SXSSFCell cell = row.createCell(j);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(titles[j]);
        }

        // 填充数据 - 与getCourseListWorkbook保持一致
        for (int i = 0; i < list.size(); i++) {
            LearningDto dto = list.get(i);
            row = sheet.createRow(i + 1);

            //id
            SXSSFCell cell = row.createCell(0);
            String id = dto.getId();
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(id)){
                cell.setCellValue(id);
            }else{
                cell.setCellValue("未注册");
            }
            cell.setCellStyle(cellStyle);

            //用户姓名
            cell = row.createCell(1);
            String personName = dto.getPersonName();
            cell.setCellValue(personName);
            cell.setCellStyle(cellStyle);

            //职务
            cell = row.createCell(2);
            String post = dto.getPostStr();
            cell.setCellValue(post);
            cell.setCellStyle(cellStyle);

            //公司代码
            cell = row.createCell(3);
            String companyCode = dto.getCompanyCode();
            cell.setCellValue(companyCode);
            cell.setCellStyle(cellStyle);

            //公司简称
            cell = row.createCell(4);
            String companyName = dto.getCompanyShortName();
            cell.setCellValue(companyName);
            cell.setCellStyle(cellStyle);

            // 公司全称
            cell = row.createCell(5);
            String zh = dto.getCompanyName();
            cell.setCellValue(zh);
            cell.setCellStyle(cellStyle);

            // 获得总学分 - 只计算当前选中课程的学分
            double num = 0;
            if(dto.getLiveList() != null && dto.getLiveList().size() > 0) {
                for (LiveInfoDto selectedCourse : courseNameList) {
                    for (LiveInfoDto userCourse : dto.getLiveList()) {
                        if(org.apache.commons.lang3.StringUtils.isNotEmpty(userCourse.getLiveId())
                           && StringUtils.equals(selectedCourse.getLiveId(), userCourse.getLiveId())) {
                            num += userCourse.getCredit();
                            break; // 找到匹配的课程后跳出内层循环
                        }
                    }
                }
            }
            if(num > 0){
                cell = row.createCell(6);
                cell.setCellValue(num);
                cell.setCellStyle(cellStyle);
            }else{
                cell = row.createCell(6);
                String credit = dto.getCredit();
                cell.setCellValue(credit);
                cell.setCellStyle(cellStyle);
            }

            // 课程学分详情
            int cellNum = 7;
            for(int x=0;x<courseNameList.size();x++) {
                if(dto.getLiveList() != null && dto.getLiveList().size() > 0) {
                    cell = row.createCell(cellNum++);
                    Boolean flag = false;
                    Boolean ifLookFlag = false;
                    double credit = 0;
                    for (int j = 0; j < dto.getLiveList().size(); j++) {
                        if(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getLiveList().get(j).getLiveId()) && StringUtils.equals(courseNameList.get(x).getLiveId(),dto.getLiveList().get(j).getLiveId())){
                            ifLookFlag = true;
                            if(dto.getLiveList().get(j).getCredit() > 0){
                                flag = true;
                                credit = dto.getLiveList().get(j).getCredit();
                            }
                        }
                    }
                    if(flag && ifLookFlag){
                        cell.setCellValue("（是）已获得学分 " + credit); // 黄色
                        cell.setCellStyle(conRightStyleDc);
                    }else if(!ifLookFlag && !flag){
                        cell.setCellValue("（否）未获得学分"); // 红色
                        cell.setCellStyle(conRightStyleDcw);
                    }else if(ifLookFlag && !flag){
                        cell.setCellValue("（是）未获得学分"); // 蓝色
                        cell.setCellStyle(conRightStyleLs);
                    }
                }else{
                    cell = row.createCell(cellNum++);
                    cell.setCellValue("（否）未获得学分");
                    cell.setCellStyle(conRightStyleDcw);
                }
            }

            sheet.setColumnWidth(0, 7000);
        }

        return sheet;
    }

    /**
     * 处理未注册公司数据分页并创建sheet页
     */
    private void exportUnregisteredCompaniesSetting(SXSSFWorkbook workbook, List<LearningDto> unregisteredCompanies) {
        int batchSize = 10000; // 每个sheet表的数据量
        int totalSheets = (unregisteredCompanies.size() + batchSize - 1) / batchSize; // 表的数量

        List<List<LearningDto>> batches = new ArrayList<>();
        for (int i = 0; i < totalSheets; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, unregisteredCompanies.size());
            List<LearningDto> batch = unregisteredCompanies.subList(fromIndex, toIndex);
            batches.add(batch);
        }

        for (int i = 0; i < totalSheets; i++) {
            getUnregisteredCompaniesWorkbook(workbook, batches.get(i), i + 1);
        }
    }

    /**
     * 创建未注册公司sheet页
     */
    public SXSSFSheet getUnregisteredCompaniesWorkbook(SXSSFWorkbook workbook, List<LearningDto> list, int sheetNumber) {
        SXSSFSheet sheet = workbook.createSheet("未注册公司" + sheetNumber);
        SXSSFRow row = null;

        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
        cellStyle.setWrapText(true);//自动换行

        // 设置字体
        Font f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setFontName("宋体");
        // 将字体赋给样式
        cellStyle.setFont(f);

        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);//自动换行

        // 设置标题数组 - 只保留公司代码、公司简称、公司全称
        String[] titles = new String[]{"公司代码", "公司简称", "公司全称"};

        row = sheet.createRow(0);
        row.setHeight((short) 800);
        for (int j = 0; j < titles.length; j++) {
            sheet.setDefaultColumnStyle(j, cs);
            sheet.setColumnWidth(j, 4000);
            SXSSFCell cell = row.createCell(j);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(titles[j]);
        }

        // 填充数据
        for (int i = 0; i < list.size(); i++) {
            LearningDto dto = list.get(i);
            row = sheet.createRow(i + 1);

            // 公司代码
            SXSSFCell cell = row.createCell(0);
            String companyCode = dto.getCompanyCode();
            cell.setCellValue(companyCode);
            cell.setCellStyle(cellStyle);

            // 公司简称
            cell = row.createCell(1);
            String companyShortName = dto.getCompanyShortName();
            cell.setCellValue(companyShortName);
            cell.setCellStyle(cellStyle);

            // 公司全称
            cell = row.createCell(2);
            String companyName = dto.getCompanyName();
            cell.setCellValue(companyName);
            cell.setCellStyle(cellStyle);
        }

        // 设置列宽
        sheet.setColumnWidth(0, 5000); // 公司代码
        sheet.setColumnWidth(1, 5000); // 公司简称
        sheet.setColumnWidth(2, 10000); // 公司全称

        return sheet;
    }

}
