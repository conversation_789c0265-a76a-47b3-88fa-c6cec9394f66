<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.capital.cloud.system.dao.UserManagerBizMapper">
    <resultMap id="BaseResultMap" type="com.stock.capital.cloud.common.model.entity.User">
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="ding_id" property="dingId" jdbcType="VARCHAR" />
    <result column="ding_no" property="dingNo" jdbcType="VARCHAR" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="real_name" property="realName" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="mail" property="mail" jdbcType="VARCHAR" />
    <result column="fax" property="fax" jdbcType="VARCHAR" />
    <result column="user_type" property="userType" jdbcType="VARCHAR" />
    <result column="age" property="age" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="VARCHAR" />
    <result column="jobs" property="jobs" jdbcType="VARCHAR" />
    <result column="qq" property="qq" jdbcType="VARCHAR" />
    <result column="wechat" property="wechat" jdbcType="VARCHAR" />
    <result column="company_code" property="companyCode" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="lock_type" property="lockType" jdbcType="VARCHAR" />
    <result column="is_admin" property="isAdmin" jdbcType="VARCHAR" />
    <result column="role" property="role" jdbcType="VARCHAR" />
    <result column="department" property="department" jdbcType="VARCHAR" />
    <result column="role_id" property="roleId" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="UserInfoResultMap" type="com.stock.capital.cloud.common.model.entity.User" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="org_id" property="orgId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="password" property="password" jdbcType="VARCHAR" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="telephone" property="telephone" jdbcType="VARCHAR" />
        <result column="phone" property="phone" jdbcType="VARCHAR" />
        <result column="mail" property="mail" jdbcType="VARCHAR" />
        <result column="fax" property="fax" jdbcType="VARCHAR" />
        <result column="user_type" property="userType" jdbcType="VARCHAR" />
        <result column="age" property="age" jdbcType="VARCHAR" />
        <result column="sex" property="sex" jdbcType="VARCHAR" />
        <result column="jobs" property="jobs" jdbcType="VARCHAR" />
        <result column="qq" property="qq" jdbcType="VARCHAR" />
        <result column="wechat" property="wechat" jdbcType="VARCHAR" />
        <result column="company_code" property="companyCode" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="department" property="department" jdbcType="VARCHAR" />
        <result column="password_update_time" property="passwordUpdateTime" jdbcType="DATE" />
        <result column="unlock_time" property="unlockTime" jdbcType="TIMESTAMP" />
        <result column="error_num" property="errorNum" jdbcType="INTEGER" />
        <result column="lock_type" property="lockType" jdbcType="VARCHAR" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="lock_state" property="lockState" jdbcType="VARCHAR" />
        <result column="is_admin" property="isAdmin" jdbcType="VARCHAR" />
    </resultMap>

    <select id="testPage" parameterType="com.stock.capital.cloud.common.model.entity.User" resultMap="BaseResultMap">
        SELECT * FROM SA_USER T
        <where>
             <if test="userName != null and userName != '' ">
                T.USER_NAME LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="realName != null and realName !='' ">
                AND T.REAL_NAME LIKE CONCAT('%', #{realName}, '%')
            </if>
        </where>
        ORDER BY ID ASC
    </select>
    <select id="getUserInfoByUserNameOrTel" resultMap="UserInfoResultMap" parameterType="map" >
        select
        id, org_id, user_name, password, real_name, telephone, phone, mail, fax, user_type,
        age, sex, jobs, qq, wechat, company_code, remark, department, password_update_time,
        unlock_time, error_num, lock_type, create_user, create_time, update_user,
        update_time, status, lock_state, is_admin
        from sa_user
        where (status != 'D' OR status IS NULL OR status = '')
        <if test="userName != null and userName !='' ">
            AND user_name = #{userName}
        </if>
        <if test="tel != null and tel !='' ">
            AND telephone = #{tel}
        </if>
    </select>
    <select id="queryUserNameByName" parameterType="String" resultMap="BaseResultMap">
        SELECT MAX(USER_NAME) AS user_name FROM SA_USER WHERE USER_NAME = #{userName,jdbcType=VARCHAR}
    </select>
    
    <select id="queryRole" parameterType="String" resultType="com.stock.capital.cloud.common.model.entity.Role">
        SELECT
        T1.ID AS id,
        T1.ROLE_NAME AS roleName,
        T1.ROLE_DES AS roleDes
        FROM SA_ROLE T1
        where T1.id != '1'
         and NOT EXISTS
       (SELECT 1 FROM SA_USER T2,SA_USER_ROLE_MAP T3 WHERE T2.ID=T3.USER_ID AND T1.ID=T3.ROLE_ID AND T2.ID= #{userId,jdbcType=VARCHAR})
    </select>
    
    <select id="queryUserRole" parameterType="String" resultType="com.stock.capital.cloud.common.model.entity.Role">
        SELECT
        T1.ID AS id,
        T1.ROLE_NAME AS roleName,
        T1.ROLE_DES AS roleDes
        FROM SA_ROLE T1 
         WHERE EXISTS 
       (SELECT 1 FROM SA_USER T2,SA_USER_ROLE_MAP T3 WHERE T2.ID=T3.USER_ID AND T1.ID=T3.ROLE_ID AND T2.ID= #{userId,jdbcType=VARCHAR})
    </select>
    <select id="queryUserRoleResource" parameterType="String" resultType="String">
        select CONCAT(t2.resource_name,'_AUTHORITY_3') name
        from sa_resource t2
        where  exists(
             select 1 from sa_menu t ,sa_role_resource_map t1
             where  t1.resource_id = t2.id
             and t.resource_id = t1.resource_id
             and t1.authority_flag = '3'
             and (t.p_menu_id is not null and t.p_menu_id &lt;&gt; 0)
             and t.resource_id is not null
             and exists(
                select 1 from sa_user_role_map t3 where t3.role_id = t1.role_id
                and t3.user_id = #{userId,jdbcType=VARCHAR}
             )
         )
    </select>
    
    <select id="queryUserRoleResourceForReadonly" parameterType="String" resultType="String">
        select CONCAT(t2.resource_name,'_AUTHORITY_2') name
        from sa_resource t2
        where  exists(
             select 1 from sa_menu t ,sa_role_resource_map t1
             where  t1.resource_id = t2.id
             and t.resource_id = t1.resource_id
             and t1.authority_flag = '2'
             and (t.p_menu_id is not null and t.p_menu_id &lt;&gt; 0)
             and t.resource_id is not null
             and exists(
                select 1 from sa_user_role_map t3 where t3.role_id = t1.role_id
                and t3.user_id = #{userId,jdbcType=VARCHAR}
             )
         )
    </select>
    
    <select id="testPageNew" parameterType="com.stock.capital.cloud.system.dto.UserManagerDto" resultType="Map">
        SELECT *
        FROM(SELECT 
            t.id id,
            t.user_name userName,
            t.real_name realName,
            t.telephone telephone,
            (select so.org_name from sa_org so where so.id = t.org_id) orgId,
            GROUP_CONCAT(distinct map.role_id) roleId,
            GROUP_CONCAT(distinct role.role_name) role,
            t.lock_type lockType,
            t.is_admin isAdmin
            FROM sa_user t
            LEFT JOIN sa_user_role_map map ON map.user_id = t.id
            LEFT JOIN sa_role role ON role.id = map.role_id
            <where>
                (t.`status` != 'D' OR t.`status` IS NULL OR t.`status` = '')
                <if test="userName != null and userName != '' ">
                    AND t.user_name LIKE CONCAT('%', #{userName}, '%')
                </if>
                <if test="realName != null and realName !='' ">
                    AND t.real_name LIKE CONCAT('%', #{realName}, '%')
                </if>
                <if test="orgList != null and orgList.size() > 0">
                    AND t.org_id in
                    <foreach collection="orgList" item="orgId" open="(" close=")" separator=",">
                        #{orgId}
                    </foreach>
                </if>
                AND t.id != '1'
            </where>
            GROUP BY t.id
            ORDER BY t.id ASC) a
            <where>
                <if test="roleList != null and roleList.size() > 0">
                    <foreach collection="roleList" item="role" open="(" close=")" separator=" OR ">
                    Find_in_set(#{role}, roleId)
                    </foreach>
                </if>
            </where>
    </select>

    <select id="queryUserByTelphone" resultMap="BaseResultMap">
        select u.id, u.ding_id, u.real_name, u.telephone from sa_user u where u.telephone = #{telephone,jdbcType=VARCHAR} and (u.`status` != 'D' or u.`status` is null or u.`status` = '') limit 0,1
    </select>
    
    <select id="queryUserIdListByDingIdList" parameterType="java.util.List" resultType="java.lang.String">
        select u.id from sa_user u where u.ding_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    
    <select id="queryIdByDingId" parameterType="java.lang.String" resultType="java.lang.String"> 
        select u.id from sa_user u where (u.`status` != 'D' or u.`status` is null or u.`status` = '') and u.ding_flag = '1' and u.ding_id = #{dingId,jdbcType=VARCHAR} limit 0,1
    </select>
    
    <select id="queryUserDingDepartment" parameterType="java.lang.String" resultType="java.lang.String">
            select GROUP_CONCAT(t.name) from user_ding_talk_departments_map s
                left join ding_talk_departments t on s.department_id = t.id
            where s.user_id = #{userId,jdbcType=VARCHAR}
            AND s.`status` = '1'

    </select>
    
    <update id="updateByDingId" parameterType="com.stock.capital.cloud.common.model.entity.User" >
	    update sa_user
	    <set >
	      <if test="realName != null" >
	        real_name = #{realName,jdbcType=VARCHAR},
	      </if>
	      <if test="dingNo != null" >
	        ding_no = #{dingNo,jdbcType=VARCHAR},
	      </if>
	      <if test="telephone != null" >
	        telephone = #{telephone,jdbcType=VARCHAR},
	      </if>
	      <if test="mail != null" >
	        mail = #{mail,jdbcType=VARCHAR},
	      </if>
	      <if test="jobs != null" >
	        jobs = #{jobs,jdbcType=VARCHAR},
	      </if>
	      <if test="dingFlag != null" >
	        ding_flag = #{dingFlag,jdbcType=VARCHAR},
	      </if>
	    </set>
	    where ding_id = #{dingId,jdbcType=VARCHAR}
    </update>
    
    <update id="updateById" parameterType="com.stock.capital.cloud.common.model.entity.User" >
	    update sa_user
	    <set >
	      <if test="realName != null" >
	        real_name = #{realName,jdbcType=VARCHAR},
	      </if>
	      <if test="dingNo != null" >
	        ding_no = #{dingNo,jdbcType=VARCHAR},
	      </if>
	      <if test="dingId != null" >
	        ding_id = #{dingId,jdbcType=VARCHAR},
	      </if>
	      <if test="mail != null" >
	        mail = #{mail,jdbcType=VARCHAR},
	      </if>
	      <if test="jobs != null" >
	        jobs = #{jobs,jdbcType=VARCHAR},
	      </if>
	      <if test="dingFlag != null" >
	        ding_flag = #{dingFlag,jdbcType=VARCHAR},
	      </if>
	    </set>
	    where id = #{id,jdbcType=VARCHAR}
    </update>
    
    <update id="updateByRealNameList" parameterType="java.util.List">
        update sa_user
        set ding_flag = '1'
        where real_name in 
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <resultMap id="ResourceResultMap" type="map">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="resource_name" property="resourceName" jdbcType="VARCHAR" />
        <result column="authority_flag" property="authorityFlag" jdbcType="VARCHAR" />
    </resultMap>

    <select id="getUserResources" resultMap="ResourceResultMap" parameterType="map">
        SELECT DISTINCT t3.resource_name,t2.authority_flag
        FROM sa_user_role_map t1
        INNER JOIN sa_role_resource_map t2 ON t1.role_id = t2.role_id
        INNER JOIN sa_resource t3 ON t3.id = t2.resource_id
        WHERE t1.user_id = #{userId}
    </select>
</mapper>
