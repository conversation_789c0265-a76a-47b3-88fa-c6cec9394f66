package com.stock.capital.cloud.dataExport.service;

import com.stock.capital.cloud.dataExport.dao.RegisterMemberMapper;
import com.stock.capital.cloud.dataExport.dto.DataDto;
import com.stock.capital.cloud.dataExport.dto.SchUserDto;
import com.stock.core.service.BaseService;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
@Transactional(rollbackFor = {Exception.class})
public class RegisterMemberService extends BaseService {
    @Autowired
    private RegisterMemberMapper registerMemberMapper;

    //注册信息数据统计
    public DataDto getNums(){
        //家数
        int comListedVIP = 0;//上市公司-会员单位
        int comListedUNVIP = 0;//上市公司-非会员单位
        int comCSRC = 0;//证监会及派出机构
        int comOtherVIP = 0;//其他-会员单位
        int comOtherUNVIP = 0;//其他-非会员单位
        int comStudent = 0;//学校
        int comUnlistedVIP = 0;//非上市公司-会员单位
        int comUnlistedUNVIP = 0;//非上市公司-非会员单位
        int comPrelistedVIP = 0;//拟上市公司-会员单位
        int comPrelistedUNVIP = 0;//拟上市公司-非会员单位
        int comNewlistedVIP = 0;//新三板挂牌公司-会员单位
        int comNewlistedUNVIP = 0;//新三板挂牌公司-非会员单位
        int comLOCVIP = 0;//地方上市公司协会-会员单位
        int comLOCUNVIP = 0;//地方上市公司协会-非会员单位
        //人数
        int memListedVIP = 0;//上市公司-会员单位
        int memListedUNVIP = 0;//上市公司-非会员单位
        int memCSRC = 0;//证监会及派出机构
        int memLOCVIP = 0;//地方上市公司协会-会员单位
        int memOtherUNVIP = 0;//其他-非会员单位
        int memStudent = 0;//学生
        int memOtherVIP = 0;//其他-会员单位
        int memUnlistedUNVIP = 0;//非上市公司-非会员单位
        int memWorker = 0;//协会工作人员及委员
        int memUnlistedVIP = 0;//非上市公司-会员单位
        int memPrelistedUNVIP = 0;//拟上市公司-非会员单位
        int memNewlistedVIP = 0;//新三板挂牌公司-会员单位
        int memNewlistedUNVIP = 0;//新二板挂牌公司-非会员单位
        int memPrelistedVIP = 0;//拟上市公司-会员单位
        int memLOCUNVIP = 0;//地方上市公司协会-非会员单位

        //查询注册公司
        List<SchUserDto> registerCompanyList = registerMemberMapper.getRegisterCompanyDetailsData();
        for (SchUserDto schUser : registerCompanyList){
            switch (schUser.getPersonType()){
                case "001-0"://上市公司-非会员单位
                    comListedUNVIP += 1;
                    memListedUNVIP += schUser.getRegisterNum();
                    break;
                case "001-1-1"://上市公司-会员单位
                    comListedVIP += 1;
                    memListedVIP += schUser.getRegisterNum();
                    break;
                case "001-1-2"://上市公司-会员单位
                    comListedVIP += 1;
                    memListedVIP += schUser.getRegisterNum();
                    break;
                case "001-1-3"://上市公司-会员单位
                    comListedVIP += 1;
                    memListedVIP += schUser.getRegisterNum();
                    break;
                case "001-1-4"://上市公司-会员单位
                    comListedVIP += 1;
                    memListedVIP += schUser.getRegisterNum();
                    break;
                case "001-1-5"://上市公司-会员单位
                    comListedVIP += 1;
                    memListedVIP += schUser.getRegisterNum();
                    break;
                case "001-1-6"://上市公司-会员单位
                    comListedVIP += 1;
                    memListedVIP += schUser.getRegisterNum();
                    break;
                case "002-0"://拟上市公司-非会员单位
                    comPrelistedUNVIP += 1;
                    memPrelistedUNVIP += schUser.getRegisterNum();
                    break;
                case "002-1"://拟上市公司-会员单位
                    comPrelistedVIP += 1;
                    memPrelistedVIP += schUser.getRegisterNum();
                    break;
                case "003"://协会工作人员及委员 协会工作人员
                    memWorker += schUser.getRegisterNum();//人数
                    break;
                case "006"://协会工作人员及委员 委员
                    memWorker += schUser.getRegisterNum();//人数
                    break;
                case "004-0"://地方上市公司协会-非会员单位
                    comLOCUNVIP += 1;//家数
                    memLOCUNVIP += schUser.getRegisterNum();//人数
                    break;
                case "004-1-1"://地方上市公司协会-会员
                    comLOCVIP += 1;//家数
                    memLOCVIP += schUser.getRegisterNum();//人数
                    break;
                case "004-1-4"://地方上市公司协会-会员
                    comLOCVIP += 1;//家数
                    memLOCVIP += schUser.getRegisterNum();//人数
                    break;
                case "005"://证监会及派出机构
                    comCSRC += 1;//家数
                    memCSRC += schUser.getRegisterNum();//人数
                    break;
                case "007"://学生
                    comStudent += 1;//家数
                    memStudent += schUser.getRegisterNum();//人数
                    break;
                case "008-0"://非上市公司-非会员单位
                    comUnlistedUNVIP += 1;//家数
                    memUnlistedUNVIP += schUser.getRegisterNum();//人数
                    break;
                case "008-1"://非上市公司-会员单位
                    comUnlistedVIP += 1;
                    memUnlistedVIP += schUser.getRegisterNum();
                    break;
                case "009-0"://新三板挂牌公司-非会员单位
                    comNewlistedUNVIP += 1;//家数
                    memNewlistedUNVIP += schUser.getRegisterNum();//人数
                    break;
                case "009-1"://新三板挂牌公司-会员单位
                    comNewlistedVIP += 1;//家数
                    memNewlistedVIP += schUser.getRegisterNum();//人数
                    break;
                case "999-0"://其他-非会员单位
                    comOtherUNVIP += 1;//家数
                    memOtherUNVIP += schUser.getRegisterNum();//人数
                    break;
                case "999-1"://其他-会员单位
                    comOtherVIP += 1;//家数
                    memOtherVIP += schUser.getRegisterNum();//人数
                    break;
            }
        }
        DataDto datas = new DataDto();
        datas.setComListedVIP(comListedVIP);
        datas.setComListedUNVIP(comListedUNVIP);
        datas.setComCSRC(comCSRC);
        datas.setComOtherVIP(comOtherVIP);
        datas.setComOtherUNVIP(comOtherUNVIP);
        datas.setComUnlistedVIP(comUnlistedVIP);
        datas.setComUnlistedUNVIP(comUnlistedUNVIP);
        datas.setComPrelistedVIP(comPrelistedVIP);
        datas.setComPrelistedUNVIP(comPrelistedUNVIP);
        datas.setComNewlistedVIP(comNewlistedVIP);
        datas.setComNewlistedUNVIP(comNewlistedUNVIP);
        datas.setMemListedVIP(memListedVIP);
        datas.setMemListedUNVIP(memListedUNVIP);
        datas.setMemCSRC(memCSRC);
        datas.setMemLOCVIP(memLOCVIP);
        datas.setMemOtherUNVIP(memOtherUNVIP);
        datas.setMemStudent(memStudent);
        datas.setMemOtherVIP(memOtherVIP);
        datas.setMemUnlistedUNVIP(memUnlistedUNVIP);
        datas.setMemWorker(memWorker);
        datas.setMemUnlistedVIP(memUnlistedVIP);
        datas.setMemPrelistedUNVIP(memPrelistedUNVIP);
        datas.setMemNewlistedVIP(memNewlistedVIP);
        datas.setMemNewlistedUNVIP(memNewlistedUNVIP);
        datas.setComLOCVIP(comLOCVIP);
        datas.setMemPrelistedVIP(memPrelistedVIP);
        datas.setMemLOCUNVIP(memLOCUNVIP);
        datas.setComLOCUNVIP(comLOCUNVIP);
        datas.setComStudent(comStudent);

        return datas;
    }
    /**
     * 会员数量列表导出Excel
     *
     * @throws IOException
     */
    public InputStream exportDetailTable() throws IOException {
        //List<SchUserDto> schUserDtoList = registerMemberMapper.getRegisterCompanyDetailsData();
        // 设置Excel内容
        XSSFWorkbook workbook = getRegisterDataWorkbook(getNums());
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.flush();
        os.close();
        return new ByteArrayInputStream(os.toByteArray());
    }

    public XSSFWorkbook getRegisterDataWorkbook(DataDto datas) throws IOException{
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet statisticalSheet = workbook.createSheet("数据统计");
        XSSFRow row = null;
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        XSSFFont f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setBold(true);
        cellStyle.setFont(f);
        XSSFCellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);
        XSSFCell cell = null;
        cell = statisticalSheet.createRow(0).createCell((int) 0);
        cell.setCellValue("");
        // 内容居中
        XSSFCellStyle conCenterStyle = workbook.createCellStyle();
        conCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        conCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 内容居左
        XSSFCellStyle conLeftStyle = workbook.createCellStyle();
        conLeftStyle.setAlignment(HorizontalAlignment.LEFT);
        conLeftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 内容居右
        XSSFCellStyle conRightStyle = workbook.createCellStyle();
        conRightStyle.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置标题数组
        String[] titles = new String[]{"用户类型","数量"};
        SchUserDto dto = null;
        //上市公司-会员单位-家数
        row = statisticalSheet.createRow(1);
        cell = row.createCell(0);
        cell.setCellValue("上市公司-会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComListedVIP());
        cell.setCellStyle(conLeftStyle);
        //上市公司-非会员单位-家数
        row = statisticalSheet.createRow(2);
        cell = row.createCell(0);
        cell.setCellValue("上市公司-非会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComListedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //其他-会员单位-家数
        row = statisticalSheet.createRow(3);
        cell = row.createCell(0);
        cell.setCellValue("其他-会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComOtherVIP());
        cell.setCellStyle(conLeftStyle);
        //其他-非会员单位-家数
        row = statisticalSheet.createRow(4);
        cell = row.createCell(0);
        cell.setCellValue("其他-非会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComOtherUNVIP());
        cell.setCellStyle(conLeftStyle);
        //非上市公司-会员单位-家数
        row = statisticalSheet.createRow(5);
        cell = row.createCell(0);
        cell.setCellValue("非上市公司-会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComUnlistedVIP());
        cell.setCellStyle(conLeftStyle);
        //非上市公司-非会员单位-家数
        row = statisticalSheet.createRow(6);
        cell = row.createCell(0);
        cell.setCellValue("非上市公司-非会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComUnlistedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //拟上市公司-会员单位-家数
        row = statisticalSheet.createRow(7);
        cell = row.createCell(0);
        cell.setCellValue("拟上市公司-会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComPrelistedVIP());
        cell.setCellStyle(conLeftStyle);
        //拟上市公司-非会员单位-家数
        row = statisticalSheet.createRow(8);
        cell = row.createCell(0);
        cell.setCellValue("拟上市公司-非会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComPrelistedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //新三板挂牌公司-会员单位-家数
        row = statisticalSheet.createRow(9);
        cell = row.createCell(0);
        cell.setCellValue("新三板挂牌公司-会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComNewlistedVIP());
        cell.setCellStyle(conLeftStyle);
        //新三板挂牌公司-非会员单位-家数
        row = statisticalSheet.createRow(10);
        cell = row.createCell(0);
        cell.setCellValue("新三板挂牌公司-非会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComNewlistedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //地方上市公司协会-会员单位-家数
        row = statisticalSheet.createRow(11);
        cell = row.createCell(0);
        cell.setCellValue("地方上市公司协会-会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComLOCVIP());
        cell.setCellStyle(conLeftStyle);
        //地方上市公司协会-非会员单位-家数
        row = statisticalSheet.createRow(12);
        cell = row.createCell(0);
        cell.setCellValue("地方上市公司协会-非会员单位-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComLOCUNVIP());
        cell.setCellStyle(conLeftStyle);
        //证监会及派出机构-家数
        row = statisticalSheet.createRow(13);
        cell = row.createCell(0);
        cell.setCellValue("证监会及派出机构-家数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComCSRC());
        cell.setCellStyle(conLeftStyle);
        //学校
        row = statisticalSheet.createRow(14);
        cell = row.createCell(0);
        cell.setCellValue("学校");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getComStudent());
        cell.setCellStyle(conLeftStyle);

        //上市公司-会员单位-人数
        row = statisticalSheet.createRow(16);
        cell = row.createCell(0);
        cell.setCellValue("上市公司-会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemListedVIP());
        cell.setCellStyle(conLeftStyle);
        //上市公司-非会员单位-人数
        row = statisticalSheet.createRow(17);
        cell = row.createCell(0);
        cell.setCellValue("上市公司-非会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemListedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //其他-会员单位-人数
        row = statisticalSheet.createRow(18);
        cell = row.createCell(0);
        cell.setCellValue("其他-会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemOtherVIP());
        cell.setCellStyle(conLeftStyle);
        //其他-非会员单位-人数
        row = statisticalSheet.createRow(19);
        cell = row.createCell(0);
        cell.setCellValue("其他-非会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemOtherUNVIP());
        cell.setCellStyle(conLeftStyle);
        //非上市公司-会员单位-人数
        row = statisticalSheet.createRow(20);
        cell = row.createCell(0);
        cell.setCellValue("非上市公司-会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemUnlistedVIP());
        cell.setCellStyle(conLeftStyle);
        //非上市公司-非会员单位-人数
        row = statisticalSheet.createRow(21);
        cell = row.createCell(0);
        cell.setCellValue("非上市公司-非会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemUnlistedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //拟上市公司-会员单位-人数
        row = statisticalSheet.createRow(22);
        cell = row.createCell(0);
        cell.setCellValue("拟上市公司-会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemPrelistedVIP());
        cell.setCellStyle(conLeftStyle);
        //拟上市公司-非会员单位-人数
        row = statisticalSheet.createRow(23);
        cell = row.createCell(0);
        cell.setCellValue("拟上市公司-非会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemPrelistedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //新三板挂牌公司-会员单位-人数
        row = statisticalSheet.createRow(24);
        cell = row.createCell(0);
        cell.setCellValue("新三板挂牌公司-会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemNewlistedVIP());
        cell.setCellStyle(conLeftStyle);
        //新二板挂牌公司-非会员单位-人数
        row = statisticalSheet.createRow(25);
        cell = row.createCell(0);
        cell.setCellValue("新二板挂牌公司-非会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemNewlistedUNVIP());
        cell.setCellStyle(conLeftStyle);
        //地方上市公司协会-会员单位-人数
        row = statisticalSheet.createRow(26);
        cell = row.createCell(0);
        cell.setCellValue("地方上市公司协会-会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemLOCVIP());
        cell.setCellStyle(conLeftStyle);
        //地方上市公司协会-非会员单位-人数
        row = statisticalSheet.createRow(27);
        cell = row.createCell(0);
        cell.setCellValue("地方上市公司协会-非会员单位-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemLOCUNVIP());
        cell.setCellStyle(conLeftStyle);
        //证监会及派出机构-人数
        row = statisticalSheet.createRow(28);
        cell = row.createCell(0);
        cell.setCellValue("证监会及派出机构-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemCSRC());
        cell.setCellStyle(conLeftStyle);
        //学生-人数
        row = statisticalSheet.createRow(29);
        cell = row.createCell(0);
        cell.setCellValue("学生-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemStudent());
        cell.setCellStyle(conLeftStyle);
        //协会工作人员及委员-人数
        row = statisticalSheet.createRow(30);
        cell = row.createCell(0);
        cell.setCellValue("协会工作人员及委员-人数");
        cell.setCellStyle(conLeftStyle);
        cell = row.createCell(1);
        cell.setCellValue(datas.getMemWorker());
        cell.setCellStyle(conLeftStyle);
        // 设置第一行标题
        row = statisticalSheet.createRow(0);
        for (int j = 0; j < titles.length; j++) {
            statisticalSheet.setDefaultColumnStyle(j, cs);
            statisticalSheet.setColumnWidth(j, 4000);
            cell = row.createCell(j);
            cell.setCellStyle(conLeftStyle);
            cell.setCellValue(titles[j]);
        }
        statisticalSheet.setColumnWidth(0, 10000);
        return workbook;
    }
    /**
     * 会员信息列表导出Excel
     *
     * @throws IOException
     */
    public InputStream exportDetailTableList(SchUserDto schUserDto) throws IOException {
        List<SchUserDto> schUserDtoList = registerMemberMapper.getCourseList(schUserDto);
        // 设置Excel内容
        XSSFWorkbook workbook = exportCourseSetting(schUserDtoList);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.flush();
        os.close();
        return new ByteArrayInputStream(os.toByteArray());
    }
    //拆分sheet
    private XSSFWorkbook exportCourseSetting(List<SchUserDto> schUserDtoList) {
        int batchSize = 10000; // 每个sheet表的数据量
        int totalSheets = (schUserDtoList.size() + batchSize - 1) / batchSize; // 表的数量

        XSSFWorkbook workbook = new XSSFWorkbook();

        List<List<SchUserDto>> batches = new ArrayList<>();
        for (int i = 0; i < totalSheets; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, schUserDtoList.size());
            List<SchUserDto> batch = schUserDtoList.subList(fromIndex, toIndex);
            batches.add(batch);
        }

        for (int i = 0; i < totalSheets; i++) {
            getRegisterListWorkbook(workbook, batches.get(i), i + 1);
        }

        return workbook;
    }
    public XSSFSheet getRegisterListWorkbook(XSSFWorkbook workbook,List<SchUserDto> list, int sheetNumber) {
        XSSFSheet sheet = workbook.createSheet("注册信息详情" + sheetNumber);
        XSSFRow row = null;
        row = sheet.createRow(0);
        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
        cellStyle.setWrapText(true);//自动换行
        // 设置字体
        Font f = workbook.createFont();
        f.setFontHeightInPoints((short) 12);
        f.setFontName("宋体");
        // 将字体赋给样式
        cellStyle.setFont(f);
        CellStyle cs = workbook.createCellStyle();
        cs.setWrapText(true);//自动换行
        // 制作标题行
        XSSFCell cell = null;
        SchUserDto dto = null;
        // 居中
        CellStyle conCenterStyle = workbook.createCellStyle();
        conCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        conCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 居右
        CellStyle conRightStyle = workbook.createCellStyle();
        conRightStyle.setAlignment(HorizontalAlignment.RIGHT);
        conRightStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //居左
        CellStyle conLeftStyle = workbook.createCellStyle();
        conLeftStyle.setAlignment(HorizontalAlignment.LEFT);
        conLeftStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置标题数组
        String[] titles = new String[]{"公司代码", "公司名称","公司简称","公司人数","是否为A股上市公司" , "用户类型", "所属区域"};

        for (int i = 0; i < list.size(); i++){
            dto = list.get(i);
            row = sheet.createRow(i + 1);
            //公司代码
            cell = row.createCell(0);
            String companyCode = dto.getCompanyCode();
            cell.setCellValue(companyCode);
            cell.setCellStyle(conLeftStyle);
            //公司名称
            cell = row.createCell(1);
            String companyName = dto.getCompanyName();
            cell.setCellValue(companyName);
            cell.setCellStyle(cellStyle);
            //公司简称
            cell = row.createCell(2);
            String companyShortName = dto.getCompanyShortName();
            cell.setCellValue(companyShortName);
            cell.setCellStyle(cellStyle);
            //公司人数
            cell = row.createCell(3);
            int memNumber = dto.getMemNumber();
            cell.setCellValue(memNumber);
            cell.setCellStyle(cellStyle);
            //是否为A股上市公司
            cell = row.createCell(4);
            String belongsPlate = "";
            if("00".equals(dto.getBelongsPlate()) || "01".equals(dto.getBelongsPlate()) || "02".equals(dto.getBelongsPlate())){
                belongsPlate = "是";
            }else {
                belongsPlate = "否";
            }
            cell.setCellValue(belongsPlate);
            cell.setCellStyle(cellStyle);
            //用户类型
            cell = row.createCell(5);
            String personType = dto.getPersonType();
            cell.setCellValue(personType);
            cell.setCellStyle(cellStyle);
            //所属区域
            cell = row.createCell(6);
            String belongCommission = dto.getBelongCommission();
            cell.setCellValue(belongCommission);
            cell.setCellStyle(cellStyle);
        }
        // 设置第一行标题
        row = sheet.createRow(0);
        for (int j = 0; j < titles.length; j++) {
            sheet.setDefaultColumnStyle(j, cs);
            sheet.setColumnWidth(j, 4000);
            cell = row.createCell(j);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(titles[j]);
        }
        sheet.setColumnWidth(0, 3000);
        sheet.setColumnWidth(1, 8000);
        sheet.setColumnWidth(2, 8000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 8000);
        sheet.setColumnWidth(6, 3000);

        return sheet;
    }

}
