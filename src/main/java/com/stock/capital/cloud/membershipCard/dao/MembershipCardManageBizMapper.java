package com.stock.capital.cloud.membershipCard.dao;

import com.stock.capital.cloud.membershipCard.dto.MembershipCardDetail;
import com.stock.capital.cloud.membershipCard.dto.MembershipCardDto;
import com.stock.capital.cloud.membershipCard.dto.MembershipCardSlave;
import com.stock.capital.cloud.trainUserManage.dto.SchUserInfoDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@Repository
public interface MembershipCardManageBizMapper {

   List<MembershipCardDto> queryMembershipCardList(MembershipCardDto membershipCardDto);

   void updateMembershipCardInfo(MembershipCardDto membershipCardDto);

   void insertMembershipCardInfo(MembershipCardDto membershipCardDto);

   MembershipCardDto queryMembershipCardInfo(MembershipCardDto membershipCardDto);

   void saveCardSlave(MembershipCardSlave membershipCardSlave);

   void saveCardDetails(List<MembershipCardDetail> list);

   List<TreeMap<String,String>> getCurrentCardHoldUser(@Param("cardId") String cardId);

   void updateMembercardStatus(MembershipCardDto membershipCardDto);

   void updateMembercardExpireTime(MembershipCardDetail membershipCardDetail);
}
