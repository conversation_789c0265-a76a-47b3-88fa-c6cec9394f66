2024-03-18 10:43:59.594 [DEBUG]  druid.sql.Connection connectionLog - {conn-10001} connected
2024-03-18 10:43:59.766 [INFO ]  com.alibaba.druid.pool.DruidDataSource init - {dataSource-1} inited
2024-03-18 10:44:07.623 [DEBUG]  com.stock.core.message.pulsar.config.PulsarListenerAnnotationBeanPostProcessor postProcessAfterInitialization - 2 @PulsarListener methods processed on bean 'costTemplateService': {public void com.stock.capital.cloud.costTemplate.service.CostTemplateService.recoverCost(com.stock.capital.cloud.costTemplate.dto.SelectCostTemplateDto)=[@com.stock.core.message.pulsar.annotation.PulsarListener(containerFactory=, subscriptionType=Shared, beanRef=__listener, topics=[capital/capco_back/recover_train_type_cost], subscriptionName=recover_train_type_cost, id=, properties=[], topicsPattern=)], public void com.stock.capital.cloud.costTemplate.service.CostTemplateService.executeCost(com.stock.capital.cloud.costTemplate.dto.SelectCostTemplateDto)=[@com.stock.core.message.pulsar.annotation.PulsarListener(containerFactory=, subscriptionType=Shared, beanRef=__listener, topics=[capital/capco_back/execute_train_type_cost], subscriptionName=execute_train_type_cost, id=, properties=[], topicsPattern=)]}
2024-03-18 10:44:10.745 [DEBUG]  com.stock.core.misc.ExcelImportMappingFactoryBean buildMap - loading excel import mapping file: file:/D:/ideaWorkSpace/capital-lcab-client/manage/src/main/webapp/WEB-INF/templates/school/newsLabel.ExcelRule.xml
2024-03-18 10:44:11.152 [DEBUG]  com.stock.core.misc.ExcelImportMappingFactoryBean buildMap - loading excel import mapping file: file:/D:/ideaWorkSpace/capital-lcab-client/manage/src/main/webapp/WEB-INF/templates/school/offlineExam.ExcelRule.xml
2024-03-18 10:44:11.157 [DEBUG]  com.stock.core.misc.ExcelImportMappingFactoryBean buildMap - loading excel import mapping file: file:/D:/ideaWorkSpace/capital-lcab-client/manage/src/main/webapp/WEB-INF/templates/school/offlineTrain.ExcelRule.xml
2024-03-18 10:44:11.164 [DEBUG]  com.stock.core.misc.ExcelImportMappingFactoryBean buildMap - loading excel import mapping file: file:/D:/ideaWorkSpace/capital-lcab-client/manage/src/main/webapp/WEB-INF/templates/school/relevancePerson.ExcelRule.xml
2024-03-18 10:44:11.728 [DEBUG]  com.stock.core.message.pulsar.config.PulsarListenerAnnotationBeanPostProcessor postProcessAfterInitialization - 1 @PulsarListener methods processed on bean 'preheatStatusDelayListener': {public void com.stock.capital.cloud.common.listener.PreheatStatusDelayListener.handleMessage(java.util.Map)=[@com.stock.core.message.pulsar.annotation.PulsarListener(containerFactory=, subscriptionType=Shared, beanRef=__listener, topics=[capital/capco_back/get_preheat_status_delay], subscriptionName=get_preheat_status_delay, id=, properties=[], topicsPattern=)]}
2024-03-18 10:44:11.871 [DEBUG]  com.stock.core.aop.CustomizableDebugInterceptor writeToLog - Entering BaseService.cacheAllCodeList()
2024-03-18 10:44:11.905 [DEBUG]  druid.sql.Connection connectionLog - {conn-10001} pool-connect
2024-03-18 10:44:11.910 [DEBUG]  com.stock.core.dao.CommonDao.getAllCodeList debug - ==>  Preparing: SELECT code_no, code_value, code_name, code_type, valid_flag, version FROM sa_code ORDER BY code_no, sort_no asc 
2024-03-18 10:44:11.926 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20000} created. 
SELECT code_no, code_value, code_name, code_type, valid_flag, version
        FROM sa_code ORDER BY code_no, sort_no asc
2024-03-18 10:44:11.939 [DEBUG]  com.stock.core.dao.CommonDao.getAllCodeList debug - ==> Parameters: 
2024-03-18 10:44:11.944 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20000} Parameters : []
2024-03-18 10:44:11.945 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20000} Types : []
2024-03-18 10:44:12.064 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20000} executed. 123.849701 millis. 
SELECT code_no, code_value, code_name, code_type, valid_flag, version
        FROM sa_code ORDER BY code_no, sort_no asc
2024-03-18 10:44:12.070 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} open
2024-03-18 10:44:12.072 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Header: [code_no, code_value, code_name, code_type, valid_flag, version]
2024-03-18 10:44:12.083 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [AUTHORITY_FLAG, 1, 不可见, null, 1, 1]
2024-03-18 10:44:12.086 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [AUTHORITY_FLAG, 2, 可读, null, 1, 1]
2024-03-18 10:44:12.086 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [AUTHORITY_FLAG, 3, 可写, null, 1, 1]
2024-03-18 10:44:12.088 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [AUTHORITY_FLAG, 4, 可导, null, 1, 1]
2024-03-18 10:44:12.088 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.089 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 07, 上交所科创板, null, 1, 1]
2024-03-18 10:44:12.090 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 01, 深交所中小板(原), null, 1, 1]
2024-03-18 10:44:12.090 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 00, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.091 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 04, 上交所主板, null, 1, 1]
2024-03-18 10:44:12.091 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 05, 新三板, null, 1, 1]
2024-03-18 10:44:12.092 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [A_STOCK_TYPE, 06, 非以上（默认用深主板系统）, null, 1, 1]
2024-03-18 10:44:12.093 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE, 00, 深交所, null, 1, 1]
2024-03-18 10:44:12.093 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE, 01, 上交所, null, 1, 1]
2024-03-18 10:44:12.094 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE, 02, 北交所, null, 1, 1]
2024-03-18 10:44:12.095 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE, 03, 港交所, null, 1, 1]
2024-03-18 10:44:12.097 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE, 04, 境外交易所, null, 1, 1]
2024-03-18 10:44:12.098 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE_X, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.099 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE_X, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.099 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE_X, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.100 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE_X, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.101 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONGS_PLATE_X, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.101 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 1, 北京, null, 1, 1]
2024-03-18 10:44:12.101 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 2, 山西, null, 1, 1]
2024-03-18 10:44:12.102 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 3, 吉林, null, 1, 1]
2024-03-18 10:44:12.103 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 4, 江苏, null, 1, 1]
2024-03-18 10:44:12.103 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 5, 福建, null, 1, 1]
2024-03-18 10:44:12.104 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 6, 河南, null, 1, 1]
2024-03-18 10:44:12.104 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 7, 广东, null, 1, 1]
2024-03-18 10:44:12.105 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 8, 重庆, null, 1, 1]
2024-03-18 10:44:12.105 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 9, 云南, null, 1, 1]
2024-03-18 10:44:12.106 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 10, 甘肃, null, 1, 1]
2024-03-18 10:44:12.106 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 11, 新疆, null, 1, 1]
2024-03-18 10:44:12.107 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 12, 宁波, null, 1, 1]
2024-03-18 10:44:12.107 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 13, 天津, null, 1, 1]
2024-03-18 10:44:12.108 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 14, 内蒙古, null, 1, 1]
2024-03-18 10:44:12.108 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 15, 黑龙江, null, 1, 1]
2024-03-18 10:44:12.109 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 16, 浙江, null, 1, 1]
2024-03-18 10:44:12.110 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 17, 江西, null, 1, 1]
2024-03-18 10:44:12.110 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 18, 湖北, null, 1, 1]
2024-03-18 10:44:12.110 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 19, 广西, null, 1, 1]
2024-03-18 10:44:12.111 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 20, 四川, null, 1, 1]
2024-03-18 10:44:12.111 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 21, 西藏, null, 1, 1]
2024-03-18 10:44:12.113 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 22, 青海, null, 1, 1]
2024-03-18 10:44:12.113 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 23, 深圳, null, 1, 1]
2024-03-18 10:44:12.114 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 24, 厦门, null, 1, 1]
2024-03-18 10:44:12.114 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 25, 河北, null, 1, 1]
2024-03-18 10:44:12.115 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 26, 辽宁, null, 1, 1]
2024-03-18 10:44:12.116 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 27, 上海, null, 1, 1]
2024-03-18 10:44:12.116 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 28, 安徽, null, 1, 1]
2024-03-18 10:44:12.117 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 29, 山东, null, 1, 1]
2024-03-18 10:44:12.118 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 30, 湖南, null, 1, 1]
2024-03-18 10:44:12.118 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 31, 海南, null, 1, 1]
2024-03-18 10:44:12.119 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 32, 贵州, null, 1, 1]
2024-03-18 10:44:12.119 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 33, 陕西, null, 1, 1]
2024-03-18 10:44:12.120 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 34, 宁夏, null, 1, 1]
2024-03-18 10:44:12.121 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 35, 大连, null, 1, 1]
2024-03-18 10:44:12.121 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, 36, 青岛, null, 1, 1]
2024-03-18 10:44:12.122 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION, Hk1, 港股, null, 1, 1]
2024-03-18 10:44:12.123 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 0, 中国证监会, null, 1, 1]
2024-03-18 10:44:12.124 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 1, 北京证监局, null, 1, 1]
2024-03-18 10:44:12.124 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 2, 天津证监局, null, 1, 1]
2024-03-18 10:44:12.125 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 3, 河北证监局, null, 1, 1]
2024-03-18 10:44:12.126 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 4, 山西证监局, null, 1, 1]
2024-03-18 10:44:12.126 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 5, 内蒙古证监局, null, 1, 1]
2024-03-18 10:44:12.127 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 6, 辽宁证监局, null, 1, 1]
2024-03-18 10:44:12.128 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 7, 吉林证监局, null, 1, 1]
2024-03-18 10:44:12.129 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 8, 黑龙江证监局, null, 1, 1]
2024-03-18 10:44:12.129 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 9, 上海证监局, null, 1, 1]
2024-03-18 10:44:12.130 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 10, 江苏证监局, null, 1, 1]
2024-03-18 10:44:12.130 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 11, 浙江证监局, null, 1, 1]
2024-03-18 10:44:12.131 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 12, 安徽证监局, null, 1, 1]
2024-03-18 10:44:12.131 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 13, 福建证监局, null, 1, 1]
2024-03-18 10:44:12.132 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 14, 江西证监局, null, 1, 1]
2024-03-18 10:44:12.132 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 15, 山东证监局, null, 1, 1]
2024-03-18 10:44:12.133 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 16, 河南证监局, null, 1, 1]
2024-03-18 10:44:12.133 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 17, 湖北证监局, null, 1, 1]
2024-03-18 10:44:12.134 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 18, 湖南证监局, null, 1, 1]
2024-03-18 10:44:12.135 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 19, 广东证监局, null, 1, 1]
2024-03-18 10:44:12.135 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 20, 广西证监局, null, 1, 1]
2024-03-18 10:44:12.136 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 21, 海南证监局, null, 1, 1]
2024-03-18 10:44:12.136 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 22, 重庆证监局, null, 1, 1]
2024-03-18 10:44:12.137 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 23, 四川证监局, null, 1, 1]
2024-03-18 10:44:12.137 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 24, 贵州证监局, null, 1, 1]
2024-03-18 10:44:12.138 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 25, 云南证监局, null, 1, 1]
2024-03-18 10:44:12.138 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 26, 西藏证监局, null, 1, 1]
2024-03-18 10:44:12.139 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 27, 陕西证监局, null, 1, 1]
2024-03-18 10:44:12.139 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 28, 甘肃证监局, null, 1, 1]
2024-03-18 10:44:12.140 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 29, 青海证监局, null, 1, 1]
2024-03-18 10:44:12.140 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 30, 宁夏证监局, null, 1, 1]
2024-03-18 10:44:12.141 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 31, 新疆证监局, null, 1, 1]
2024-03-18 10:44:12.141 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 32, 深圳证监局, null, 1, 1]
2024-03-18 10:44:12.141 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 33, 大连证监局, null, 1, 1]
2024-03-18 10:44:12.142 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 34, 宁波证监局, null, 1, 1]
2024-03-18 10:44:12.142 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 35, 厦门证监局, null, 1, 1]
2024-03-18 10:44:12.143 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 36, 青岛证监局, null, 1, 1]
2024-03-18 10:44:12.143 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 37, 上海证券监管专员办事处, null, 1, 1]
2024-03-18 10:44:12.143 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BELONG_COMMISSION_AND_AGENCY, 38, 深圳证券监管专员办事处, null, 1, 1]
2024-03-18 10:44:12.145 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BEL_PLATE, 60, 上交所, null, 1, 1]
2024-03-18 10:44:12.145 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BEL_PLATE, 000, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.146 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BEL_PLATE, 002, 深交所中小板, null, 1, 1]
2024-03-18 10:44:12.146 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BEL_PLATE, 300, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.146 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, NOTAPPLIED, 未申请, null, 1, 1]
2024-03-18 10:44:12.147 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, ISSUING, 已申请, null, 1, 1]
2024-03-18 10:44:12.147 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, ISSUED, 已开具, null, 1, 1]
2024-03-18 10:44:12.147 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, REVERSED, 已红冲, null, 1, 1]
2024-03-18 10:44:12.148 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, INVALIDED, 已作废, null, 1, 1]
2024-03-18 10:44:12.149 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, CLOSED, 已关闭, null, 1, 1]
2024-03-18 10:44:12.149 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BILL_INVOICE_STATUS, SPLITED, 已拆分, null, 1, 1]
2024-03-18 10:44:12.149 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 001, 政策解读, null, 1, 1]
2024-03-18 10:44:12.150 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 002, 公司治理, null, 1, 1]
2024-03-18 10:44:12.151 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 003, 信息披露, null, 1, 1]
2024-03-18 10:44:12.151 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 004, 投关管理, null, 1, 1]
2024-03-18 10:44:12.152 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 005, 资本运作, null, 1, 1]
2024-03-18 10:44:12.152 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 006, 合规管理, null, 1, 1]
2024-03-18 10:44:12.153 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 007, 财务管理, null, 1, 1]
2024-03-18 10:44:12.153 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 008, 其它, null, 1, 1]
2024-03-18 10:44:12.154 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 009, 期货与风险管理, null, 1, 1]
2024-03-18 10:44:12.154 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 010, ESG, null, 1, 1]
2024-03-18 10:44:12.156 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 011, 新闻宣传, null, 1, 1]
2024-03-18 10:44:12.156 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [BUSINESS_TYPE, 012, 人力资源管理, null, 1, 1]
2024-03-18 10:44:12.157 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LISTING_SECTION, listingSection_2, 中小企业板, null, 1, 1]
2024-03-18 10:44:12.157 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LISTING_SECTION, listingSection_3, 创业板, null, 1, 1]
2024-03-18 10:44:12.157 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LISTING_SECTION, listingSection_4, H股, null, 1, 1]
2024-03-18 10:44:12.158 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LISTING_SECTION, listingSection_5, 科创板, null, 1, 1]
2024-03-18 10:44:12.158 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LISTING_SECTION, listingSection_1, 主板, null, 1, 1]
2024-03-18 10:44:12.158 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LISTING_SECTION, listingSection_6, 北证, null, 1, 1]
2024-03-18 10:44:12.159 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LIVE_SUB_EXAMINE_STATE, 0, 待审核, null, 1, null]
2024-03-18 10:44:12.159 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LIVE_SUB_EXAMINE_STATE, 1, 审核通过, null, 1, null]
2024-03-18 10:44:12.161 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_LIVE_SUB_EXAMINE_STATE, 2, 审核未通过, null, 1, null]
2024-03-18 10:44:12.161 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_PROPERTY, memberProperty_1, 普通, null, 1, 1]
2024-03-18 10:44:12.161 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_PROPERTY, memberProperty_3, 副监事长, null, 1, 1]
2024-03-18 10:44:12.162 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_PROPERTY, memberProperty_2, 副会长, null, 1, 1]
2024-03-18 10:44:12.162 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_PROPERTY, memberProperty_5, 监事, null, 1, 1]
2024-03-18 10:44:12.162 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_PROPERTY, memberProperty_4, 理事, null, 1, 1]
2024-03-18 10:44:12.163 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_PROPERTY, memberProperty_6, 常务理事, null, 1, 1]
2024-03-18 10:44:12.163 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_TYPE, memberType_1, 普通会员, null, 1, 1]
2024-03-18 10:44:12.163 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_TYPE, memberType_4, 联系会员, null, 1, 1]
2024-03-18 10:44:12.164 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_MEMBER_TYPE, memberType_3, 团体会员, null, 1, 1]
2024-03-18 10:44:12.165 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_QUESTIONNAIRE_STATUS, 0, 未发布, null, 1, 1]
2024-03-18 10:44:12.165 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_QUESTIONNAIRE_STATUS, 1, 发布, null, 1, 1]
2024-03-18 10:44:12.166 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_QUESTION_TYPE, 0, 单选, null, 1, 1]
2024-03-18 10:44:12.166 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_QUESTION_TYPE, 1, 多选, null, 1, 1]
2024-03-18 10:44:12.168 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CAPCO_QUESTION_TYPE, 2, 问答, null, 1, 1]
2024-03-18 10:44:12.169 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CASE_BELONGS_PLATE, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.169 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CASE_BELONGS_PLATE, 07, 上交所科创板, null, 1, 1]
2024-03-18 10:44:12.170 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CASE_BELONGS_PLATE, 01, 深交所中小板, null, 1, 1]
2024-03-18 10:44:12.170 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CASE_BELONGS_PLATE, 00, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.171 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CASE_BELONGS_PLATE, 04, 上交所主板, null, 1, 1]
2024-03-18 10:44:12.171 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CASE_BELONGS_PLATE, 05, 新三板, null, 1, 1]
2024-03-18 10:44:12.172 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CHANNEL_TYPE, 0, 管理端, null, 1, 1]
2024-03-18 10:44:12.173 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CHANNEL_TYPE, 1, 移动端, null, 1, 1]
2024-03-18 10:44:12.173 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CHANNEL_TYPE, 2, 网站, null, 1, 1]
2024-03-18 10:44:12.174 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CHECK_STATUS, 0, 待审核, null, 1, 1]
2024-03-18 10:44:12.174 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CHECK_STATUS, 1, 通过, null, 1, 1]
2024-03-18 10:44:12.175 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [CHECK_STATUS, 2, 未通过, null, 1, 1]
2024-03-18 10:44:12.177 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [COURSE_TYPE, custom001, 业务分类, null, 1, 1]
2024-03-18 10:44:12.177 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [COURSE_TYPE, custom002, 适用人群, null, 1, 1]
2024-03-18 10:44:12.178 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [COURSE_TYPE, custom003, 适用板块, null, 1, 1]
2024-03-18 10:44:12.178 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [COURSE_TYPE, custom004, 适用机构, null, 1, 1]
2024-03-18 10:44:12.180 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [COURSE_TYPE, custom005, 课程讲师, null, 1, 1]
2024-03-18 10:44:12.180 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [DECLARE_TYPE_NO, 01, 深交所中小板(原), null, 1, 1]
2024-03-18 10:44:12.181 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [DECLARE_TYPE_NO, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.181 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [DECLARE_TYPE_NO, 03, 自主版块, null, 1, 1]
2024-03-18 10:44:12.182 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [DECLARE_TYPE_NO, 00, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.182 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_FEE_TYPE, 0, 付费, null, 1, null]
2024-03-18 10:44:12.183 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_FEE_TYPE, 1, 免费, null, 1, null]
2024-03-18 10:44:12.184 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_FEE_TYPE, 2, 加密, null, 1, null]
2024-03-18 10:44:12.185 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 00, 机构1, null, 1, null]
2024-03-18 10:44:12.185 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 01, 机构2, null, 1, null]
2024-03-18 10:44:12.185 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 02, 价值在线, null, 1, null]
2024-03-18 10:44:12.186 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 03, , null, 1, null]
2024-03-18 10:44:12.187 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 04, , null, 1, null]
2024-03-18 10:44:12.187 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 05, , null, 1, null]
2024-03-18 10:44:12.188 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 06, , null, 1, null]
2024-03-18 10:44:12.189 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 07, , null, 1, null]
2024-03-18 10:44:12.189 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 08, , null, 1, null]
2024-03-18 10:44:12.191 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 09, , null, 1, null]
2024-03-18 10:44:12.193 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 10, , null, 1, null]
2024-03-18 10:44:12.195 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 11, , null, 1, null]
2024-03-18 10:44:12.195 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 12, , null, 1, null]
2024-03-18 10:44:12.196 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_ORG, 13, , null, 1, null]
2024-03-18 10:44:12.196 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_RELEASE_STATUS, 0, 未发布, null, 1, null]
2024-03-18 10:44:12.197 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EB_SCHOOL_LIVE_RELEASE_STATUS, 1, 已发布, null, 1, null]
2024-03-18 10:44:12.198 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EDIT_STATE, 0, 未编辑, null, 1, 1]
2024-03-18 10:44:12.198 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EDIT_STATE, 1, 编辑中, null, 1, 1]
2024-03-18 10:44:12.199 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EDIT_STATE, 2, 编辑完, null, 1, 1]
2024-03-18 10:44:12.199 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ENABLE_TYPE, 0, 启用, null, 1, 1]
2024-03-18 10:44:12.201 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ENABLE_TYPE, 1, 禁用, null, 1, 1]
2024-03-18 10:44:12.201 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 01, 初级, null, 1, 1]
2024-03-18 10:44:12.201 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 02, 中级, null, 1, 1]
2024-03-18 10:44:12.202 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 03, 高级, null, 1, 1]
2024-03-18 10:44:12.202 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 04, 基础篇, null, 1, 1]
2024-03-18 10:44:12.203 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 05, 进阶篇, null, 1, 1]
2024-03-18 10:44:12.203 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 06, 辅导篇, null, 1, 1]
2024-03-18 10:44:12.203 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 07, 初阶, null, 1, 1]
2024-03-18 10:44:12.204 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 08, 进阶, null, 1, 1]
2024-03-18 10:44:12.204 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_RANK, 09, 高阶, null, 1, 1]
2024-03-18 10:44:12.204 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_STATUS, 0, 未开始, null, 1, 1]
2024-03-18 10:44:12.205 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_STATUS, 1, 人脸验证失败, null, 1, 1]
2024-03-18 10:44:12.205 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_STATUS, 2, 人脸验证通过, null, 1, 1]
2024-03-18 10:44:12.206 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_STATUS, 3, 考试中, null, 1, 1]
2024-03-18 10:44:12.206 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_STATUS, 4, 未通过, null, 1, 1]
2024-03-18 10:44:12.208 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_STATUS, 5, 通过, null, 1, 1]
2024-03-18 10:44:12.208 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 111111, 证代分级考试, null, 1, 1]
2024-03-18 10:44:12.208 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 222222, 拟上市培训考试, null, 1, 1]
2024-03-18 10:44:12.209 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 588624, 北交所考试, null, 1, 1]
2024-03-18 10:44:12.209 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 333333, 辅导验收考试, null, 1, 1]
2024-03-18 10:44:12.210 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 444444, 违法违规考试, null, 1, 1]
2024-03-18 10:44:12.211 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 666666, 小程序签到试题, null, 1, 1]
2024-03-18 10:44:12.211 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 555555, 监管专区考试, null, 1, 1]
2024-03-18 10:44:12.212 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXAM_TYPE, 777777, 董秘考试, null, 1, 1]
2024-03-18 10:44:12.212 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXIST_TYPE, 1, 有, null, 1, 1]
2024-03-18 10:44:12.213 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EXIST_TYPE, 0, 无, null, 1, 1]
2024-03-18 10:44:12.213 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_CHECK_BLANK, /[^\d-]/g, 整数, null, 1, 1]
2024-03-18 10:44:12.213 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_CHECK_BLANK, /[^\d\.-]/g, 小数, null, 1, 1]
2024-03-18 10:44:12.214 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUESTION_TYPE, 0, 单选题, null, 1, 1]
2024-03-18 10:44:12.214 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUESTION_TYPE, 1, 多选题, null, 1, 1]
2024-03-18 10:44:12.215 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUESTION_TYPE, 2, 单项填空题, null, 1, 1]
2024-03-18 10:44:12.215 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUESTION_TYPE, 3, 多项填空题, null, 1, 1]
2024-03-18 10:44:12.216 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUESTION_TYPE, 4, 排序题, null, 1, 1]
2024-03-18 10:44:12.216 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUESTION_TYPE, 99, 段落说明, null, 1, 1]
2024-03-18 10:44:12.217 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUE_STATE, 0, 已查看, null, 1, null]
2024-03-18 10:44:12.217 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUE_STATE, 1, 已保存, null, 1, null]
2024-03-18 10:44:12.218 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [EX_QUE_STATE, 2, 已提交, null, 1, null]
2024-03-18 10:44:12.218 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [FINANCE_COMPANY_TYPE, 1, 省级, null, 1, 1]
2024-03-18 10:44:12.219 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [FINANCE_COMPANY_TYPE, 2, 市级, null, 1, 1]
2024-03-18 10:44:12.219 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [FINANCE_COMPANY_TYPE, 3, 区级, null, 1, 1]
2024-03-18 10:44:12.219 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [HANDLE_STATUS, 0, 未处理, null, 1, 1]
2024-03-18 10:44:12.220 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [HANDLE_STATUS, 1, 已审阅, null, 1, 1]
2024-03-18 10:44:12.220 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [INDEXSTATUS, 0, 开启, null, 1, 1]
2024-03-18 10:44:12.221 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [INDEXSTATUS, 1, 关闭, null, 1, 1]
2024-03-18 10:44:12.222 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [INVOICE_STATUS, 0, 未申请, null, 1, 1]
2024-03-18 10:44:12.222 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [INVOICE_STATUS, 1, 未开具, null, 1, 1]
2024-03-18 10:44:12.223 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [INVOICE_STATUS, 2, 已开具, null, 1, 1]
2024-03-18 10:44:12.225 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPODATA_BELONG_PLATE, 03, 创业板, , 1, null]
2024-03-18 10:44:12.225 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPODATA_BELONG_PLATE, 04, 科创板, , 1, null]
2024-03-18 10:44:12.226 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPODATA_BELONG_PLATE, 02, 中小板, , 0, null]
2024-03-18 10:44:12.227 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPODATA_BELONG_PLATE, 01, 深主板, , 1, null]
2024-03-18 10:44:12.227 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPODATA_BELONG_PLATE, 00, 沪主板, , 1, null]
2024-03-18 10:44:12.228 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPO_MALE_OR_FEMALE, 1, 男, 0, 1, 1]
2024-03-18 10:44:12.228 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [IPO_MALE_OR_FEMALE, 2, 女, 0, 1, 1]
2024-03-18 10:44:12.229 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LISTSTATE, 0, 正常上市, null, 1, 1]
2024-03-18 10:44:12.229 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LISTSTATE, 9, 未上市, null, 1, 1]
2024-03-18 10:44:12.229 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LISTSTATE, 1, 暂停上市, null, 1, 1]
2024-03-18 10:44:12.230 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LISTSTATE, 2, 终止上市, null, 1, 1]
2024-03-18 10:44:12.230 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LISTSTATE, 3, 恢复上市, null, 1, 1]
2024-03-18 10:44:12.231 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LISTSTATE, 10, 废弃, null, 1, 1]
2024-03-18 10:44:12.231 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LOGIN_METHOD_CONTROL, 01, 账号密码登录, null, 1, 1]
2024-03-18 10:44:12.232 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LOGIN_METHOD_CONTROL, 02, 手机号登录, null, 1, 1]
2024-03-18 10:44:12.232 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [LOGIN_METHOD_CONTROL, 03, 扫码登录, null, 1, 1]
2024-03-18 10:44:12.232 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MAIN_BOARD, 00, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.233 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MAIN_BOARD, 01, 深交所中小板(原), null, 1, 1]
2024-03-18 10:44:12.233 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MAIN_BOARD, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.234 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MAIN_BOARD, 04, 上交所主板, null, 1, 1]
2024-03-18 10:44:12.235 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MAIN_BOARD, 07, 上交所科创板, null, 1, 1]
2024-03-18 10:44:12.235 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MAIN_BOARD, 11, 新三板, null, 1, 1]
2024-03-18 10:44:12.236 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MENU_ID, 0, 菜单资源, 0, 1, 1]
2024-03-18 10:44:12.237 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [MENU_ID, 1, 数据资源, 0, 1, 1]
2024-03-18 10:44:12.237 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [NO_OR_YES, 1, 是, null, 1, 1]
2024-03-18 10:44:12.239 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [NO_OR_YES, 2, 否, null, 1, 1]
2024-03-18 10:44:12.239 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [NO_OR_YES, 3, 难以判断, null, 1, 1]
2024-03-18 10:44:12.240 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, PAID, 已支付, null, 1, 1]
2024-03-18 10:44:12.240 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, UNPAID, 待支付, null, 1, 1]
2024-03-18 10:44:12.241 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, REFUNDING, 退款中, null, 1, 1]
2024-03-18 10:44:12.241 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, REFUND, 已退款, null, 1, 1]
2024-03-18 10:44:12.242 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, CLOSED, 已关闭, null, 1, 1]
2024-03-18 10:44:12.242 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, DELETED, 已删除, null, 1, 1]
2024-03-18 10:44:12.243 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_STATUS, EXPIRE, 已过期, null, 1, 1]
2024-03-18 10:44:12.243 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 0, 单个课程, null, 1, 1]
2024-03-18 10:44:12.244 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 1, 单个直播, null, 1, 1]
2024-03-18 10:44:12.244 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 2, 课程包, null, 1, 1]
2024-03-18 10:44:12.245 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 3, 购物车, null, 1, 1]
2024-03-18 10:44:12.245 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 4, 证代分级培训分级课程, null, 1, 1]
2024-03-18 10:44:12.246 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 5, 年卡, null, 1, 1]
2024-03-18 10:44:12.246 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 6, 优惠码购物, null, 1, 1]
2024-03-18 10:44:12.246 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ORDER_TYPE, 7, 面授课程, null, 1, 1]
2024-03-18 10:44:12.247 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, Alipay 1.0, 支付宝, null, 1, 1]
2024-03-18 10:44:12.247 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, Alipay 2.0, 支付宝, null, 1, 1]
2024-03-18 10:44:12.248 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, WXPay, 微信, null, 1, 1]
2024-03-18 10:44:12.248 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, YQB, 壹钱包, null, 1, 1]
2024-03-18 10:44:12.248 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, QMF, 全民付远程快捷, null, 1, 1]
2024-03-18 10:44:12.249 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, UnionPay, 银联钱包, null, 1, 1]
2024-03-18 10:44:12.249 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, BaiDu, 百度钱包, null, 1, 1]
2024-03-18 10:44:12.252 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, JD, 京东钱包, null, 1, 1]
2024-03-18 10:44:12.252 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, SF, 顺丰顺手付, null, 1, 1]
2024-03-18 10:44:12.252 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, COMM, 交通银行, null, 1, 1]
2024-03-18 10:44:12.253 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, BestPay, 翼支付, null, 1, 1]
2024-03-18 10:44:12.253 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, ACP, 银联全渠道立码付, null, 1, 1]
2024-03-18 10:44:12.254 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, NetPayBills, 银商网付平台账单模块, null, 1, 1]
2024-03-18 10:44:12.255 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, NetPayGtwy, 银商网付平台网关模块, null, 1, 1]
2024-03-18 10:44:12.255 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PAY_WAY, QmfWebPay, POS通插件WEB版, null, 1, 1]
2024-03-18 10:44:12.257 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_LABEL, D, 职务会员, null, 1, 1]
2024-03-18 10:44:12.257 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_LABEL, A, 讲师, null, 1, 1]
2024-03-18 10:44:12.258 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_LABEL, C, 证代评委, null, 1, 1]
2024-03-18 10:44:12.259 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_LABEL, B, ESG评委, null, 1, 1]
2024-03-18 10:44:12.259 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_LABEL, E, 期货评委, null, 1, 1]
2024-03-18 10:44:12.260 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_LABEL, F, 曾任证代, null, 1, 1]
2024-03-18 10:44:12.260 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 000, 实控人, null, 1, 1]
2024-03-18 10:44:12.260 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 001, 董事长, null, 1, 1]
2024-03-18 10:44:12.261 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 002, 总经理/总裁, null, 1, 1]
2024-03-18 10:44:12.261 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 003, 董事会秘书, null, 1, 1]
2024-03-18 10:44:12.262 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 008, 财务总监, null, 1, 1]
2024-03-18 10:44:12.262 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 004, 监事会主席, null, 1, 1]
2024-03-18 10:44:12.262 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 005, 独立董事, null, 1, 1]
2024-03-18 10:44:12.262 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 006, 董事, null, 1, 1]
2024-03-18 10:44:12.263 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 007, 监事, null, 1, 1]
2024-03-18 10:44:12.263 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 009, 证券事务代表, null, 1, 1]
2024-03-18 10:44:12.264 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 010, 人力资源, null, 1, 1]
2024-03-18 10:44:12.264 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 011, 部门经理, null, 1, 1]
2024-03-18 10:44:12.264 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 012, 职员, null, 1, 1]
2024-03-18 10:44:12.266 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 013, 教师, null, 1, 1]
2024-03-18 10:44:12.266 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 014, 学生, null, 1, 1]
2024-03-18 10:44:12.266 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_POST, 999, 其他, null, 1, 1]
2024-03-18 10:44:12.267 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-1-1, 上市公司-会员单位-普通, null, 1, 1]
2024-03-18 10:44:12.269 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-1-2, 上市公司-会员单位-副会长, null, 1, 1]
2024-03-18 10:44:12.270 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-1-3, 上市公司-会员单位-副监事长, null, 1, 1]
2024-03-18 10:44:12.270 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-1-4, 上市公司-会员单位-理事, null, 1, 1]
2024-03-18 10:44:12.271 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-1-5, 上市公司-会员单位-监事, null, 1, 1]
2024-03-18 10:44:12.272 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-1-6, 上市公司-会员单位-常务理事, null, 1, 1]
2024-03-18 10:44:12.272 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 001-0, 上市公司-非会员单位, null, 1, 1]
2024-03-18 10:44:12.272 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 009-1, 新三板挂牌公司-会员单位, null, 1, 1]
2024-03-18 10:44:12.273 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 009-0, 新三板挂牌公司-非会员单位, null, 1, 1]
2024-03-18 10:44:12.274 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 002-1, 拟上市公司-会员单位, null, 1, 1]
2024-03-18 10:44:12.275 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 002-0, 拟上市公司-非会员单位, null, 1, 1]
2024-03-18 10:44:12.275 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 008-1, 非上市公司-会员单位, null, 1, 1]
2024-03-18 10:44:12.276 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 008-0, 非上市公司-非会员单位, null, 1, 1]
2024-03-18 10:44:12.277 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 004-1-1, 地方上市公司协会-普通会员, null, 1, 1]
2024-03-18 10:44:12.277 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 004-1-4, 地方上市公司协会-理事会员, null, 1, 1]
2024-03-18 10:44:12.278 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 004-0, 地方上市公司协会-非会员单位, null, 1, 1]
2024-03-18 10:44:12.278 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 999-1, 其他-会员单位, null, 1, 1]
2024-03-18 10:44:12.279 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 999-0, 其他-非会员单位, null, 1, 1]
2024-03-18 10:44:12.279 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 007, 学生, null, 1, 1]
2024-03-18 10:44:12.280 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 006, 中上协各委员会, null, 1, 1]
2024-03-18 10:44:12.280 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 005, 证监会及派出机构, null, 1, 1]
2024-03-18 10:44:12.281 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [PERSON_TYPE, 003, 中国上市公司协会, null, 1, 1]
2024-03-18 10:44:12.281 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [QUESTION_TYPE, 0, 判断题, null, 1, 1]
2024-03-18 10:44:12.282 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [QUESTION_TYPE, 1, 单项选择, null, 1, 1]
2024-03-18 10:44:12.282 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [QUESTION_TYPE, 2, 多项选择, null, 1, 1]
2024-03-18 10:44:12.284 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [QUESTION_TYPE, 300, 问题类别, null, 1, 1]
2024-03-18 10:44:12.285 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [QUIZ_TYPE, 02, 互动交流, 0, 1, 1]
2024-03-18 10:44:12.285 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [QUIZ_TYPE, 05, 意见反馈, 0, 1, 1]
2024-03-18 10:44:12.286 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [REVIEW_STATUS, 01, 待审核, null, 1, 1]
2024-03-18 10:44:12.286 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [REVIEW_STATUS, 02, 已发布, null, 1, 1]
2024-03-18 10:44:12.287 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [REVOKE_TYPE, 0, 未撤销, 0, 1, 1]
2024-03-18 10:44:12.288 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [REVOKE_TYPE, 1, 已撤销, 0, 1, 1]
2024-03-18 10:44:12.288 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 01, , null, 1, 1]
2024-03-18 10:44:12.290 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 02, 1108003026528334830, null, 1, 1]
2024-03-18 10:44:12.291 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 03, 1107442362270133074, null, 1, 1]
2024-03-18 10:44:12.291 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 04, 1107442362270135552, null, 1, 1]
2024-03-18 10:44:12.292 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 05, 1107442362270135587, null, 1, 1]
2024-03-18 10:44:12.292 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 06, 1107442362270135556, null, 1, 1]
2024-03-18 10:44:12.293 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 07, 1107442362270135555, null, 1, 1]
2024-03-18 10:44:12.293 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 08, 1107442362270135554, null, 1, 1]
2024-03-18 10:44:12.294 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 09, 1107442362270135592, null, 1, 1]
2024-03-18 10:44:12.294 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_COURSE_IMAGE, 10, 1107442362270135590, null, 1, 1]
2024-03-18 10:44:12.295 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_APP_NOTICE_FLAG, 1, 是否打开, null, 1, 1]
2024-03-18 10:44:12.295 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 1, V1.0, null, 1, 1]
2024-03-18 10:44:12.296 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 2, V2.0, null, 1, 1]
2024-03-18 10:44:12.296 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 3, V3.0, null, 1, 1]
2024-03-18 10:44:12.297 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 4, V4.0, null, 1, 1]
2024-03-18 10:44:12.297 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 5, V5.0, null, 1, 1]
2024-03-18 10:44:12.298 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 6, V6.0, null, 1, 1]
2024-03-18 10:44:12.299 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 7, V7.0, null, 1, 1]
2024-03-18 10:44:12.299 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 8, V8.0, null, 1, 1]
2024-03-18 10:44:12.299 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 9, V9.0, null, 1, 1]
2024-03-18 10:44:12.299 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VERSION_TYPE, 10, V10.0, null, 1, 1]
2024-03-18 10:44:12.300 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VIDEO_TYPE, 1, 操作演示, null, 1, 1]
2024-03-18 10:44:12.301 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SCH_VIDEO_TYPE, 2, 课程视频, null, 1, 1]
2024-03-18 10:44:12.301 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 07, 董秘初阶, null, 1, 1]
2024-03-18 10:44:12.302 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 08, 董秘进阶, null, 1, 1]
2024-03-18 10:44:12.302 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 09, 董秘高阶, null, 1, 1]
2024-03-18 10:44:12.303 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 01, 初级课程, null, 1, 1]
2024-03-18 10:44:12.303 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 02, 中级课程, null, 1, 1]
2024-03-18 10:44:12.304 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 03, 高级课程, null, 1, 1]
2024-03-18 10:44:12.304 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 04, 基础篇, null, 1, 1]
2024-03-18 10:44:12.305 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 05, 进阶篇, null, 1, 1]
2024-03-18 10:44:12.305 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_GRADE, 06, 辅导篇, null, 1, 1]
2024-03-18 10:44:12.307 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_TYPE, 0, 分级专题, 1, 1, 1]
2024-03-18 10:44:12.307 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SPECIAL_TYPE, 1, 课程专题, 1, 1, 1]
2024-03-18 10:44:12.308 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.308 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD, 07, 上交所科创板, null, 1, 1]
2024-03-18 10:44:12.308 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD, 01, 深交所中小板(原), null, 1, 1]
2024-03-18 10:44:12.309 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD, 00, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.309 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD, 04, 上交所主板, null, 1, 1]
2024-03-18 10:44:12.310 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD, 05, 新三板, null, 1, 1]
2024-03-18 10:44:12.310 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD_HAITONG, 00, 深交所主板, null, 1, 1]
2024-03-18 10:44:12.311 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD_HAITONG, 01, 深交所中小板, null, 1, 1]
2024-03-18 10:44:12.312 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD_HAITONG, 02, 深交所创业板, null, 1, 1]
2024-03-18 10:44:12.312 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [STOCK_BOARD_HAITONG, 04, 上交版, null, 1, 1]
2024-03-18 10:44:12.312 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SUPERVISE_TYPE, 1, 监管类 - 上市部, null, 1, 1]
2024-03-18 10:44:12.313 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SUPERVISE_TYPE, 2, 监管类 - 非公部, null, 1, 1]
2024-03-18 10:44:12.313 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [SUPERVISE_TYPE, 3, 监管类 - 处罚委, null, 1, 1]
2024-03-18 10:44:12.314 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 00, 会议类型, null, 1, 1]
2024-03-18 10:44:12.314 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 01, 会议名称, null, 1, 1]
2024-03-18 10:44:12.315 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 02, 是否临时, null, 1, 1]
2024-03-18 10:44:12.317 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 03, 召开地点, null, 1, 1]
2024-03-18 10:44:12.318 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 04, 召开日期, null, 1, 1]
2024-03-18 10:44:12.318 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 05, 召开日期对应星期几, null, 1, 1]
2024-03-18 10:44:12.318 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 06, 召开时间, null, 1, 1]
2024-03-18 10:44:12.318 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 07, 议案列表, null, 1, 1]
2024-03-18 10:44:12.319 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 08, 联系人姓名, null, 1, 1]
2024-03-18 10:44:12.319 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 09, 联系人手机, null, 1, 1]
2024-03-18 10:44:12.319 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 10, 联系人传真, null, 1, 1]
2024-03-18 10:44:12.320 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 16, 联系人固定电话, null, 1, 1]
2024-03-18 10:44:12.320 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 11, 联系人邮箱, null, 1, 1]
2024-03-18 10:44:12.320 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 12, 当前日期, null, 1, 1]
2024-03-18 10:44:12.321 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 13, 当前日期全中文, null, 1, 1]
2024-03-18 10:44:12.321 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 14, 公司全称, null, 1, 1]
2024-03-18 10:44:12.321 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 15, 公司简称, null, 1, 1]
2024-03-18 10:44:12.323 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TEMPLATE_PARA, 17, 联系人姓名2, null, 1, 1]
2024-03-18 10:44:12.323 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 0, 直播, 1, 1, 1]
2024-03-18 10:44:12.324 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 2, 培训规则, 2, 1, 1]
2024-03-18 10:44:12.324 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 4, 近期热点1, 3, 1, 1]
2024-03-18 10:44:12.324 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 1, 点播, 1, 1, 1]
2024-03-18 10:44:12.324 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 3, 平台账号, 2, 1, 1]
2024-03-18 10:44:12.325 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 5, 舆论导向2, 3, 1, 1]
2024-03-18 10:44:12.325 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 7, 嘻嘻3, 3, 1, 1]
2024-03-18 10:44:12.326 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 8, 哈哈4, 3, 1, 1]
2024-03-18 10:44:12.326 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 9, 嘿嘿5, 3, 1, 1]
2024-03-18 10:44:12.326 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 10, 嘿嘿6, 3, 1, 1]
2024-03-18 10:44:12.326 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 1222, 3, 1, 1]
2024-03-18 10:44:12.326 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 11111, 3, 1, 1]
2024-03-18 10:44:12.327 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 1111, 3, 1, 1]
2024-03-18 10:44:12.327 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 333333, 3, 1, 1]
2024-03-18 10:44:12.327 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 1, 3, 1, 1]
2024-03-18 10:44:12.328 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 5555, 3, 1, 1]
2024-03-18 10:44:12.328 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, , 3, 1, 1]
2024-03-18 10:44:12.328 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 11, 3, 1, 1]
2024-03-18 10:44:12.328 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 11, 3, 1, 1]
2024-03-18 10:44:12.330 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_NOTICE_SUB_TYPE, 11, 2, 3, 1, 1]
2024-03-18 10:44:12.330 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_OFFLINE_ACTIVITY, 0, 普通活动, 1, 1, 1]
2024-03-18 10:44:12.330 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_OFFLINE_ACTIVITY, 1, 证代高级, 1, 1, 1]
2024-03-18 10:44:12.331 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [TRAIN_OFFLINE_ACTIVITY, 2, 董秘履职, 1, 1, 1]
2024-03-18 10:44:12.331 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [USE_FULNESS, 0, 正常, null, 1, 1]
2024-03-18 10:44:12.332 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [USE_FULNESS, 1, 北交所培训, null, 1, 1]
2024-03-18 10:44:12.332 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 00, 未开始, null, 1, 1]
2024-03-18 10:44:12.332 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 11, 切片中, null, 1, 1]
2024-03-18 10:44:12.333 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 12, 切片成功, null, 1, 1]
2024-03-18 10:44:12.333 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 21, 预热中, null, 1, 1]
2024-03-18 10:44:12.333 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 22, 预热成功, null, 1, 1]
2024-03-18 10:44:12.334 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 91, 切片失败, null, 1, 1]
2024-03-18 10:44:12.334 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 92, 预热失败, null, 1, 1]
2024-03-18 10:44:12.334 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [VIDEO_PROCESS_TYPE, 99, 完成, null, 1, 1]
2024-03-18 10:44:12.335 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 0, 点播, null, 1, 1]
2024-03-18 10:44:12.335 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 1, 直播, null, 1, 1]
2024-03-18 10:44:12.335 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 2, 课程包, null, 1, 1]
2024-03-18 10:44:12.336 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 3, 购物车, null, 1, 1]
2024-03-18 10:44:12.336 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 4, 专题课, null, 1, 1]
2024-03-18 10:44:12.337 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 5, 会员卡, null, 1, 1]
2024-03-18 10:44:12.337 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [WARE_TYPE, 7, 面授课程, null, 1, 1]
2024-03-18 10:44:12.337 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [YES_OR_NO, 1, 是, 0, 1, 1]
2024-03-18 10:44:12.338 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [YES_OR_NO, 0, 否, 0, 1, 1]
2024-03-18 10:44:12.338 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 0, 团购, , 1, 1]
2024-03-18 10:44:12.338 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 1, 职务会员扫码免费, , 1, 1]
2024-03-18 10:44:12.339 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 2, 校园大使, , 1, 1]
2024-03-18 10:44:12.339 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 3, 特约用户, , 1, 1]
2024-03-18 10:44:12.340 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 4, 配置免费, , 1, 1]
2024-03-18 10:44:12.340 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 5, 头奖用户, , 1, 1]
2024-03-18 10:44:12.341 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} Result: [ZERO_TYPE, 6, 赠送会员卡, , 1, 1]
2024-03-18 10:44:12.341 [DEBUG]  com.stock.core.dao.CommonDao.getAllCodeList debug - <==      Total: 459
2024-03-18 10:44:12.342 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20000, rs-50000} closed
2024-03-18 10:44:12.343 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20000} closed
2024-03-18 10:44:12.345 [DEBUG]  druid.sql.Connection connectionLog - {conn-10001} pool-recycle
2024-03-18 10:44:12.350 [DEBUG]  com.stock.core.aop.CustomizableDebugInterceptor writeToLog - Leaving BaseService.cacheAllCodeList(): void
2024-03-18 10:44:13.475 [DEBUG]  druid.sql.Connection connectionLog - {conn-10001} pool-connect
2024-03-18 10:44:13.476 [DEBUG]  com.stock.core.dao.CommonDao.getAllResources debug - ==>  Preparing: SELECT * FROM SA_RESOURCE 
2024-03-18 10:44:13.477 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20001} created. 
SELECT * FROM SA_RESOURCE
2024-03-18 10:44:13.477 [DEBUG]  com.stock.core.dao.CommonDao.getAllResources debug - ==> Parameters: 
2024-03-18 10:44:13.478 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20001} Parameters : []
2024-03-18 10:44:13.478 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20001} Types : []
2024-03-18 10:44:13.808 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20001} executed. 330.1888 millis. 
SELECT * FROM SA_RESOURCE
2024-03-18 10:44:13.809 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} open
2024-03-18 10:44:13.809 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Header: [id, p_id, resource_name, resource_des, resource_type, resource_url, curr_status, relation, company_id, create_user, create_time, update_user, update_time, status]
2024-03-18 10:44:13.810 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1, , RES_INDEX, 主页, 0, /index*, null, 96210244698712078, null, null, null, 1, 2022-09-26 20:22:50.0, null]
2024-03-18 10:44:13.812 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [*********, , RES_FILEDOWNLOAD_OPEN_FILE, 加载文件下载页面, 0, /fileDownload/openFile, null, null, , null, null, null, 2019-09-05 18:22:32.0, null]
2024-03-18 10:44:13.814 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [*********, , RES_FILEDOWNLOAD_PULL_FILE, 加载文件下载页面, 0, /fileDownload/pullFile, null, null, , null, null, null, 2019-09-05 18:27:48.0, null]
2024-03-18 10:44:13.816 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [101, , RES_SCH_COMMENT_INIT, 评论管理初始化, 1, /feedBack/init, null, null, null, null, null, null, 2021-09-27 13:46:44.0, null]
2024-03-18 10:44:13.818 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1010002, 101, RES_SCH_LIVE_COMMENT_LIST, 获取直播评论列表, 0, /offlineTraining/getLiveCommentList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.819 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [102001, 28280253919312774, RES_TRAIN_INTERACTION_INIT, 互动交流, 1, /trainingDynamics/interactionInit*, null, null, null, null, null, null, 2023-06-19 15:30:18.0, null]
2024-03-18 10:44:13.821 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [102002, 28280253919312774, RES_TRAIN_COMMON_INIT, 常见问题, 1, /trainingDynamics/commonProblemInit*, null, null, null, null, 2021-09-27 15:03:23.0, 1, 2023-06-19 15:30:20.0, null]
2024-03-18 10:44:13.822 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [102003, 28280253919312774, RES_TRAIN_NOTICE_INIT, 培训通知, 1, /trainingDynamics/trainingNoticeInit*, null, null, null, null, 2021-09-27 15:04:04.0, 1, 2023-06-19 15:30:24.0, null]
2024-03-18 10:44:13.824 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [102004, , RES_TRAIN_OFFLINE_INIT, 线下培训动态初始化, 0, /trainingOffline/trainingOfflineInit*, null, null, null, null, 2021-09-27 15:04:04.0, 1, 2023-06-19 14:17:13.0, null]
2024-03-18 10:44:13.825 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [102005, 28280253919312774, RES_TRAIN_WONDERFUL_REVIEW_INIT, 精彩回顾, 1, /trainingDynamics/wonderfulReviewInit*, null, null, null, null, 2021-09-27 15:04:04.0, 1, 2023-06-19 15:30:28.0, null]
2024-03-18 10:44:13.826 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [108002, 28280253919310305, RES_SCH_LIVE_SUBSCRIBE_INIT, 预约管理, 1, /liveSubscribe/listInit, null, null, , null, null, null, 2023-06-19 11:13:52.0, null]
2024-03-18 10:44:13.827 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1080020001, 108002, RES_SCH_LIVE_SUBSCRIBE_LIST, 查询, 1, /liveSubscribe/queryLiveSubscribeList, null, null, , null, null, null, 2023-06-05 16:36:01.0, null]
2024-03-18 10:44:13.828 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1080020002, 108002, RES_LIVE_SAVE_EXAMINEINFO, 审核/修改审核状态, 1, /liveSubscribe/saveExamineInfo, null, null, , null, null, null, 2023-06-05 16:39:30.0, null]
2024-03-18 10:44:13.828 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [108003, 28280253919310305, RES_SCH_LIVE_WATCH_INIT, 观看记录, 1, /watchLive/listInit, null, null, , null, null, null, 2023-06-19 11:13:57.0, null]
2024-03-18 10:44:13.829 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1080030001, 108003, RES_SCH_VIEW_INFO_INIT, 查询, 1, /watchLive/queryWatchLiveList, null, null, , null, null, null, 2023-06-05 16:42:12.0, null]
2024-03-18 10:44:13.831 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [108004, 28280253919310305, RES_SCH_LIVE_TRAININIG_RECORDS, 参训记录, 1, /liveSubscribe/trainingRecordsInit, null, null, , null, null, null, 2024-03-04 17:24:26.0, null]
2024-03-18 10:44:13.834 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1106725448742695664, , RES_FILE_TEMP_UPLOAD_ANALYSIS, 上传ppt并解析, 1, /fileuploadImgAnalysis*, null, null, null, null, null, null, 2018-11-30 14:25:26.0, null]
2024-03-18 10:44:13.835 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1106921178550919286, 1106921178549301041, RES_UPLOAD_VIDEO, 上传视频, 1, /modulesFileUpload, null, null, null, null, null, null, null, null]
2024-03-18 10:44:13.836 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268640762, 28280253919310305, RES_EB_SCHOOL_LIVE_INFO, 直播设置, 1, /ebSchoolLiveInfo/listInit, null, null, null, 1, 2020-03-24 09:39:06.0, 1, 2023-06-19 11:13:46.0, null]
2024-03-18 10:44:13.837 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268640893, , RES_EB_SCHOOL_LECTURER, 易董学院讲师管理初始化, 0, /ebSchoolLecturerInfo/listInit, null, null, null, 1, 2020-03-24 09:45:53.0, 1, 2023-06-06 14:30:32.0, null]
2024-03-18 10:44:13.838 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268641071, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_QUERY_LIST, 查询, 1, /ebSchoolLiveInfo/queryPagingBySelective, null, null, , 1, 2020-03-24 10:02:50.0, 1, 2023-06-05 16:17:21.0, null]
2024-03-18 10:44:13.839 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268642337, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_ADD_INIT, 新增/编辑, 1, /ebSchoolLiveInfo/addInit, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-06-05 16:17:32.0, null]
2024-03-18 10:44:13.840 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268642338, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_SAVE, 直播管理保存直播信息, 2, /ebSchoolLiveInfo/saveLiveInfo, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-05-31 10:32:11.0, null]
2024-03-18 10:44:13.843 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268642339, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_EDIT_INIT, 直播管理编辑页面初始化, 2, /ebSchoolLiveInfo/editInit/*, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-05-31 10:32:11.0, null]
2024-03-18 10:44:13.845 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268642340, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_UPDATE, 直播管理更新直播信息, 2, /ebSchoolLiveInfo/updateLiveInfo, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-05-31 10:32:11.0, null]
2024-03-18 10:44:13.846 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268642341, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_DELETE, 删除, 1, /ebSchoolLiveInfo/delete/*, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-06-05 16:19:01.0, null]
2024-03-18 10:44:13.849 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268642342, 1107442362268640762, RES_EB_SCHOOL_LIVE_INFO_FILE_DOWNLOAD, 直播管理课件下载, 2, /ebSchoolLiveInfo/filedownload, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-05-31 10:32:11.0, null]
2024-03-18 10:44:13.851 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268644390, 1107442362268640893, RES_EB_SCHOOL_LECTURER_QUERY, 查询, 1, /ebSchoolLecturerInfo/queryLecturerInfoList, null, null, , 1, 2020-03-24 12:08:50.0, 1, 2023-06-06 14:34:49.0, null]
2024-03-18 10:44:13.853 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268644391, 1107442362268640893, RES_EB_SCHOOL_LECTURER_DELETE, 删除, 1, /ebSchoolLecturerInfo/teachInfoDel, null, null, , 1, 2020-03-24 12:08:51.0, 1, 2023-06-06 14:39:52.0, null]
2024-03-18 10:44:13.861 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268644392, 1108003011071836963, RES_EB_SCHOOL_LECTURER_ADD_INIT, 特聘专家-编辑, 1, /homepageConfig/teacherInit, null, null, , 1, 2020-03-24 12:08:51.0, 1, 2023-06-07 09:54:34.0, null]
2024-03-18 10:44:13.863 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362268644393, 1107442362268640893, RES_EB_SCHOOL_LECTURER_SAVE, 直播管理保存讲师, 2, /ebSchoolLecturerInfo/saveTeacherInfo, null, null, , 1, 2020-03-24 12:08:51.0, 1, 2023-06-06 14:30:26.0, null]
2024-03-18 10:44:13.865 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534550, , RES_SCH_INDEX_INFO, 首页设置页面初始化, 2, /schoolIndexConfig/init, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.866 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534551, 1107442362291534550, RES_SCH_BANNER_INIT, 新增轮播页面初始化, 2, /schoolIndexConfig/bannerAddInit, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.868 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534552, 1107442362291534550, RES_SCH_ADD_BANNER, 保存轮播信息, 2, /schoolIndexConfig/addBanner, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.869 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534553, 1107442362291534550, RES_SCH_GET_BANNER, 新增banner回调, 2, /schoolIndexConfig/getBanner, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.870 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534554, 1107442362291534550, RES_SCH_DELETE_BANNER, 删除轮播图信息, 2, /schoolIndexConfig/deleteBanner, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.871 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534555, 1107442362291534550, RES_SCH_MOVE_DETAIL, 上下移动, 2, /schoolIndexConfig/moveDetail, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.872 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534556, 1107442362291534550, RES_SCH_ADD_GROUP, 批量认领录入人, 2, /schoolIndexConfig/addGroup, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.873 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534557, 1107442362291534550, RES_SCH_DEL_GROUP, 删除课程和教师组, 2, /schoolIndexConfig/delGroup, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.874 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534558, 1107442362291534550, RES_SCH_MOVE_GROUP, 上下移课程和教师分组, 2, /schoolIndexConfig/moveGroup, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.875 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534559, 1107442362291534550, RES_SCH_DEL_COURSE_DETAIL, 删除课程detail和教师detail, 2, /schoolIndexConfig/delCourseDetail, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.875 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534560, 1107442362291534550, RES_SCH_MOVE_COURSE_DEATIL, 上下移课程和教师detail, 2, /schoolIndexConfig/moveCourseDetail, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.877 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534561, 1107442362291534550, RES_SCH_ADD_GROUP_DETAIL, 修改组的title和description, 2, /schoolIndexConfig/addGroupDetail, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.878 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534562, 1107442362291534550, RES_SCH_SINGLE_ELECTION, 轮播图选择课程（单选）, 2, /schoolIndexConfig/singleElectionCourse, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.880 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534563, 1107442362291534550, RES_SCH_CHOOSE_LIVE, 轮播图选择直播（单选）, 2, /schoolIndexConfig/chooseBannerLive, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.881 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534564, 1107442362291534550, RES_SCH_GET_LIVE, 轮播图选择直播（单选）列表查询, 2, /schoolIndexConfig/getLive, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.881 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534565, 1107442362291534550, RES_SCH_CHOOSE_COURSE, 修改组的title和description, 2, /schoolIndexConfig/chooseCourse, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.882 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534566, 1107442362291534550, RES_SCH_QUERY_CHOOSE_COURSELY, 轮播图选择课程（多选）列表查询, 2, /schoolIndexConfig/queryChooseCourseLY, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.883 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534567, 1107442362291534550, RES_SCH_CHOOSE_TEACHER, 轮播图选择讲师（多选）, 2, /schoolIndexConfig/chooseTeacher, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.884 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534568, 1107442362291534550, RES_SCH_QUERY_CHOOS_TEACHER, 轮播图选择讲师（多选）列表查询, 2, /schoolIndexConfig/queryChooseTeacher, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.885 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534569, 1107442362291534550, RES_SCH_QUERY_CHOOSE_COURSE, 轮播图选择课程（单选）列表查询, 2, /schoolIndexConfig/queryChooseCourse, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.886 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534570, 1107442362291534550, RES_SCH_ADD_CHOOSE, 轮播图选择讲师（多选）, 2, /schoolIndexConfig/addCourse, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.886 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534571, 1107442362291534550, RES_SCH_ADD_TEACHER, 给教师组添加教师, 2, /schoolIndexConfig/addTeacher, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.887 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534572, 1107442362291534550, RES_SCH_ADD_TEACHERBG, 设置讲师背景图, 2, /schoolIndexConfig/addTeacherBg, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.888 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534573, 1107442362291534550, RES_SCH_SELECT_LIVE, 根据直播id查询信息, 2, /schoolIndexConfig/selectLive, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.888 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362291534574, 1107442362291534550, RES_SCH_SELECT_COURSE, 根据课程id查询信息, 2, /schoolIndexConfig/selectCourse, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.890 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362300218388, 28280253919314185, RES_TRAIN_USER_MANAGE_INIT, 注册审核, 1, /trainUserManage/userManageInit, null, null, null, null, null, null, 2023-06-19 17:00:50.0, null]
2024-03-18 10:44:13.891 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362302143759, 1107442362300218388, RES_TRAIN_USER_MANAGER_INFOLIST, 查询, 1, /trainUserManage/getUserInfoList, null, null, null, null, null, null, 2023-06-06 15:21:56.0, null]
2024-03-18 10:44:13.892 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [110744236230214387, 28280253919314186, RES_USER_LOGIN_LOG_INIT, 登录查询, 1, /userLoginLog/userLoginLogInit, null, null, null, null, null, null, 2023-06-19 17:58:31.0, null]
2024-03-18 10:44:13.892 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362302145819, 1107442362300218388, RES_TRAIN_USER_MANAGER_ADD, 新增/编辑/查看, 1, /trainUserManage/addUserInfoInit, null, null, null, null, null, null, 2023-06-06 15:22:30.0, null]
2024-03-18 10:44:13.893 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362302145956, 1107442362300218388, RES_TRAIN_USER_MANAGER_SAVEINFO, 锁定, 1, /trainUserManage/userInfoSave, null, null, null, null, null, null, 2023-06-06 15:24:03.0, null]
2024-03-18 10:44:13.894 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362302145958, 1107442362300218388, RES_TRAIN_USER_MANAGER_DELINFO, 删除, 1, /trainUserManage/delUserInfo, null, null, null, null, null, null, 2023-06-06 15:24:17.0, null]
2024-03-18 10:44:13.895 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [110744236230219475, 110744236230214387, RES_USER_LOGIN_LOG_INFOLIST, 查询, 1, /userLoginLog/getUserLoginLogList, null, null, null, null, null, null, 2023-06-06 15:37:01.0, null]
2024-03-18 10:44:13.896 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442923, 1107442362315442954, RES_TRAIN_USER_REFRESHMEMBER, 同步, 1, /trainUserManage/refreshMember, null, null, null, null, null, null, 2023-06-20 10:06:33.0, null]
2024-03-18 10:44:13.897 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442934, 1107442362300218388, RES_TRAIN_USER_MANAGE, 用户管理页面, 2, /trainUserManage/trainUserManageInit, null, null, null, null, null, null, 2023-06-06 15:21:26.0, null]
2024-03-18 10:44:13.898 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442954, 28280253919314185, RES_ORG_USER_MANAGE, 会员单位, 1, /trainUserManage/orgUserManageInit, null, null, null, null, null, null, 2023-06-20 10:06:13.0, null]
2024-03-18 10:44:13.899 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442955, 1107442362300218388, RES_UPDATE_USER_REGION, 更新用户所属辖区, 2, /trainUserManage/updateBelongCommission, null, null, null, null, null, null, 2023-06-06 15:21:26.0, null]
2024-03-18 10:44:13.901 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442956, 1107442362300218388, RES_CHANGE_PASSWORD_SAVE, 修改密码保存, 2, /trainUserManage/changePasswordSave, null, null, null, null, null, null, 2023-06-06 15:21:26.0, null]
2024-03-18 10:44:13.902 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442963, 1107442362300218388, RES_CHANGE_PASSWORD_POP, 修改密码, 1, /trainUserManage/changePasswordpop, null, null, null, null, null, null, 2023-06-06 15:23:50.0, null]
2024-03-18 10:44:13.903 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442975, 1107442362300218388, RES_TRAIN_USER_CHECK_ONLY, 验证手机号、邮箱是否唯一, 2, /trainUserManage/checkOnly, null, null, null, null, null, null, 2023-06-06 15:21:26.0, null]
2024-03-18 10:44:13.903 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107442362315442987, 1107442362315442954, RES_ORG_USER_MANAGE_LIST, 查询, 1, /trainUserManage/getOrgInfoList, null, null, null, null, null, null, 2023-06-20 10:06:34.0, null]
2024-03-18 10:44:13.904 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107586525619902914, 1107586525619902719, RES_BWG_MANAGE_PASSWORD_CHECK_INIT, 易董灰阶控制_修改密码_初始化, 1, /bwgManage/passwordCheckInit*, null, null, null, null, null, 1, 2020-07-06 16:49:22.0, null]
2024-03-18 10:44:13.905 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107586525619902941, 1107586525619902719, RES_BWG_MANAGE_SAVE_CHANGE, 易董灰阶控制_保存, 1, /bwgManage/saveChange*, null, null, null, null, null, 1, 2020-07-06 16:49:25.0, null]
2024-03-18 10:44:13.906 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107734413573359360, 34, RES_USERMANAGER_SYNCDINGDING, 用户管理钉钉同步, 2, /userManager/synchDingding*, null, null, , null, 2020-10-22 11:14:37.0, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.906 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107734413573359396, 34, RES_USERMANAGER_SYNCDINGDINGDEPARTMENTS, 用户管理钉钉同步部门, 2, /userManager/syncDingdingDepartments*, null, null, , null, 2020-10-22 11:14:37.0, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.907 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107734413573359432, 34, RES_USERMANAGER_REMOTEVALIDATETELEPHONE, 用户管理手机号校验, 2, /userManager/remoteValidateTelephone*, null, null, , null, 2020-10-22 11:14:41.0, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.908 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107734413596590511, 102001, RES_SCH_ORG_INIT, 所属机构维护, 1, /ebSchoolLecturerInfo/orgInit, null, null, null, null, 2020-11-19 11:15:25.0, null, 2023-06-06 14:32:33.0, null]
2024-03-18 10:44:13.909 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107734413596590512, 1107442362268640893, RES_SCH_SAVE_ORG_NAME, 所属机构新增修改, 2, /ebSchoolLecturerInfo/saveOrgName, null, null, null, null, 2020-11-19 11:15:25.0, null, 2023-06-06 14:30:26.0, null]
2024-03-18 10:44:13.911 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107734413596590513, 1107442362268640893, RES_SCH_ORG_LIST, 获取所属机构下拉, 2, /ebSchoolLecturerInfo/orgList, null, null, null, null, 2020-11-19 11:15:26.0, null, 2023-06-06 14:30:26.0, null]
2024-03-18 10:44:13.911 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062684, 1107442362291534550, RES_SCH_EDIT_TYPE, 易董学院首页-业务分类编辑页面, 0, /schoolIndexConfig/editCourseType, null, null, null, null, null, null, 2020-12-09 11:52:41.0, null]
2024-03-18 10:44:13.912 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062685, 1107442362291534550, RES_SCH_SAVE_TYPE, 易董学院首页-保存编辑业务分类, 0, /schoolIndexConfig/saveConfigCourseType, null, null, null, null, null, null, 2020-12-09 11:52:41.0, null]
2024-03-18 10:44:13.913 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062686, 1107442362291534550, RES_SCH_EDIT_COURSE_INFO, 易董学院首页-热门课程编辑页面, 0, /schoolIndexConfig/editCourseInfo, null, null, null, null, null, null, 2020-12-09 11:52:41.0, null]
2024-03-18 10:44:13.914 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062687, 1107442362291534550, RES_SCH_EDIT_COURSE_TEACHER, 易董学院首页-热门讲师编辑页面, 0, /schoolIndexConfig/editCourseTeacher, null, null, null, null, null, null, 2020-12-09 11:52:41.0, null]
2024-03-18 10:44:13.915 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062688, 1107442362291534550, RES_SCH_SAVE_COURSE_TEACHER, 易董学院首页-热门讲师编辑保存, 0, /schoolIndexConfig/saveConfigCourseTeacher, null, null, null, null, null, null, 2020-12-09 11:52:42.0, null]
2024-03-18 10:44:13.915 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062689, 1107442362291534550, RES_SCH_DELETE_COURSE_TEACHER, 易董学院首页-热门讲师删除, 0, /schoolIndexConfig/deleteCourseTeacher, null, null, null, null, null, null, 2020-12-09 11:52:42.0, null]
2024-03-18 10:44:13.916 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107811146876062690, 1107442362291534550, RES_SCH_EDIT_TEACHER_SORT, 易董学院首页-热门讲师排序, 0, /schoolIndexConfig/editCourseTeacherSort, null, null, null, null, null, null, 2020-12-09 11:52:42.0, null]
2024-03-18 10:44:13.917 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107842162352060800, 1106921178549301041, RES_GET_HW_CLOUD_TOKEN, 上传视频, 1, /getHuaWeiCloudUploadToken, null, null, null, null, null, null, null, null]
2024-03-18 10:44:13.918 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107901295244023861, 1613105796726824382, RES_EB_SCHOOL_LIVE_INFO_CREATE_VIDEO, 直播管理上传视频, 1, /video/createVideo, null, null, , 1, 2021-02-19 09:12:38.0, 1, 2023-06-05 15:28:23.0, null]
2024-03-18 10:44:13.919 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1107901295244023862, 1613105796726824382, RES_EB_SCHOOL_GET_UPLOAD_VIDEO_STATUS, 获取上传视频状态, 2, /video/getUploadVideoStatus, null, null, , 1, 2021-02-19 09:12:38.0, 1, 2023-06-05 15:25:46.0, null]
2024-03-18 10:44:13.920 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071836963, , RES_HOME_CONFIT, 首页设置页面初始化, 0, /homepageConfig/homepageConfigInit, null, null, null, null, null, null, 2021-10-12 10:57:20.0, null]
2024-03-18 10:44:13.920 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837858, 101, RES_SCH_GET_FEED_LIST, 获取意见反馈列表, 0, /feedBack/getFeedBackList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.921 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837860, 101, RES_SCH_BUNCH_PLANT_LIST, 获取点播评论列表, 0, /bunchPlanting/getBunchPlantingList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.921 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837861, 101, RES_SCH_AUDIT_STATUS_INIT, 审核状态页面初始化, 0, /bunchPlanting/auditStatusInit, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.922 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837862, 101, RES_SCH_REPLY_STATUS_INIT, 回复页面初始化, 0, /bunchPlanting/replyCommentInit, null, null, , null, null, null, 2021-10-08 15:23:01.0, null]
2024-03-18 10:44:13.923 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837863, 101, RES_SCH_REPLY_HISTROY_INIT, 回复历史页面初始化, 0, /bunchPlanting/replyHistoryInit, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.923 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837864, 101, RES_SCH_UPDATE_AUDIT_STATUS, 修改审核状态, 0, /bunchPlanting/auditStatus, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.925 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071837866, 101, RES_SCH_GET_LINE_INIT, 获取线下培训评论列表, 0, /offlineTraining/getOfflineTrainingList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.925 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842517, 101, RES_SCH_GET_FEED_LIST, 获取意见反馈列表, 0, /feedBack/getFeedBackList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.926 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842518, 102001, RES_CAPCO_GET_TDL_LIST, 培训动态页面数据, 2, /trainingDynamics/queryTrainingDynamicsInfoList, null, null, , null, null, null, 2023-06-06 09:40:58.0, null]
2024-03-18 10:44:13.927 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842519, 102001, RES_CAPCO_GET_TD_INIT, 培训动态-新增/编辑, 1, /trainingDynamics/trainingDynamicsInit, null, null, , null, null, null, 2023-06-06 09:54:14.0, null]
2024-03-18 10:44:13.928 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842520, 102001, RES_CAPCO_GET_TD_SAVE, 培训动态-发布/置顶, 1, /trainingDynamics/saveTrainingDynamicsInfo, null, null, , null, null, null, 2023-06-06 09:54:10.0, null]
2024-03-18 10:44:13.928 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842521, 102001, RES_CAPCO_GET_QT_LIST, 培训动态-查询, 1, /trainingDynamics/queryTrainInfoList, null, null, , null, null, null, 2023-06-06 09:54:09.0, null]
2024-03-18 10:44:13.929 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842522, 102001, RES_CAPCO_GET_TD_DEL, 培训动态-删除, 1, /trainingDynamics/trainingDynamicsInfoDel, null, null, , null, null, null, 2023-06-06 09:54:07.0, null]
2024-03-18 10:44:13.931 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842523, 102001, RES_CAPCO_GET_CF_INIT, 分类列表初始化, 2, /trainingDynamics/classificationInit, null, null, , null, null, null, 2023-06-06 09:40:58.0, null]
2024-03-18 10:44:13.932 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842524, 102001, RES_CAPCO_GET_CF_SAVE, 保存分类信息, 2, /trainingDynamics/saveClassificationName, null, null, , null, null, null, 2023-06-06 09:40:59.0, null]
2024-03-18 10:44:13.932 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003011071842525, 102001, RES_CAPCO_GET_CF_LIST, 获取所属分类下拉, 2, /trainingDynamics/saveClassificationList, null, null, , null, null, null, 2023-06-06 09:40:59.0, null]
2024-03-18 10:44:13.933 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026538068822, 8801, RES_SCHOOL_ADD_SERVICE, 易董学院-添加服务协议附件, 0, /schoolIndexConfig/addServiceAgreement, null, null, null, null, null, null, 2021-06-03 15:19:35.0, null]
2024-03-18 10:44:13.934 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026557076916, , RES_CLASS_ROOM_INIT, 产品课程页面初始化, 0, /classRoom/init, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.935 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026557076917, 1108003026557076916, RES_CLASS_ROOM_GET_LIST, 产品课程页面获取列表, 0, /classRoom/getTableList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.935 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571550, 1108003026557076916, RES_SEL_CLASS_MANAGE_INIT, 直播回放页面初始化, 0, /selClassManage/selClassManagementInit, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.936 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571552, 1108003026557076916, RES_SEL_CLASS_LIST, 直播回放列表查询, 0, /selClassManage/getSelClassTableList, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.937 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571553, 1108003026557076916, RES_CLASS_ORDER_INIT, 易董课堂排序页面初始化, 0, /selClassManage/classOrderManage, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.938 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571554, 1108003026557076916, RES_SEL_CLASS_ADD_INIT, 直播回放编辑页面初始化, 0, /selClassManage/addSelClass, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.938 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571555, 1108003026557076916, RES_SEL_CLASS_CREATE_VIDEO, 直播回放编辑页面创建视频, 0, /selClassManage/createVideo, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.939 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571558, 1108003026557076916, RES_SEL_CLASS_DEL_VIDEO, 直播回放编辑页面删除视频, 0, /selClassManage/delVideo, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.940 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571559, 1108003026557076916, RES_SEL_CLASS_SAVE, 直播回放编辑页面保存, 0, /selClassManage/saveSelClass, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.941 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571563, 1108003026557076916, RES_SEL_CLASS_DEL, 直播回放删除, 0, /selClassManage/delSelClassInfo, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.942 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026568571565, 1108003026557076916, RES_SEL_CLASS_DEL_FILE, 直播回放删除附件, 0, /selClassManage/delFile, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.942 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026569320756, 1108003026557076916, RES_SEL_CLASS_EXPORT_EXCEL, 直播回放导出, 0, /selClassManage/exportDetailExcel, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.943 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026570392633, 8801, RES_SCHOOL_TO_SCHOOL, 易董学院-直通车单点登录, 0, /schoolIndexConfig/toSchoolLink, null, null, null, null, null, null, 2021-07-26 15:19:35.0, null]
2024-03-18 10:44:13.943 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026572412328, 8801, RES_SCHOOL_BATCH_USER_CENTER, 易董学院-批量刷新用户表与用户中心关联字段, 0, /schUserManage/batchUpdateUserCenterData, null, null, null, null, null, null, 2021-07-30 15:19:35.0, null]
2024-03-18 10:44:13.944 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026576126930, 1107442362268640762, RES_EB_SCHOOL_LIVE_STA_INIT, 直播统计页面初始化, 2, /ebSchoolLiveInfo/liveSta, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-05-31 10:32:12.0, null]
2024-03-18 10:44:13.945 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026577691000, 1108003026577690998, RES_SCHEDULER_SITUATION_SELECTLIST, 系统管理-定时任务执行情况_列表查询, 2, /schedulerSituation/selectList*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.946 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026577691002, 1108003026577690998, RES_SCHEDULER_SITUATION_DETAILS, 系统管理-定时任务执行情况_详细信息, 2, /schedulerSituation/showDetails*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:13.947 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560853, 8801, RES_SCHOOL_ORG_USER_MANAGE_INIT, 易董学院-机构信息页面初始化, 0, /schUserManage/orgUserManageInit, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.947 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560854, 8801, RES_SCHOOL_ORG_INFO_LIST, 易董学院-机构列表查询, 0, /schUserManage/getOrgInfoList, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.948 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560856, 8801, RES_SCHOOL_EDIT_ORG_INFO, 易董学院-编辑机构信息, 0, /schUserManage/editOrgInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.949 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560857, 8801, RES_SCHOOL_QUERY_CONTRACT_INFO, 易董学院-查询机构下合同数据, 0, /schUserManage/queryContractInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.950 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560858, 8801, RES_SCHOOL_EDIT_CONTRACT, 易董学院-编辑合同信息, 0, /schUserManage/editContract, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.951 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560860, 8801, RES_SCHOOL_ORG_INFO_SAVE, 易董学院-机构用户保存信息, 0, /schUserManage/orgInfoSave, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.952 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560861, 8801, RES_SCHOOL_CONTRACT_INFO_SAVE, 易董学院-合同信息保存, 0, /schUserManage/contractInfoSave, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.953 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560862, 8801, RES_SCHOOL_QUERY_CONTRACT_USER_INFO, 易董学院-查询合同下用户数据, 0, /schUserManage/queryContractUserInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.954 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560864, 8801, RES_SCHOOL_ORG_USER_SETTING_INIT, 易董学院-编辑合同基本信息, 0, /schUserManage/orgUserSettingInit, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.955 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560865, 8801, RES_SCHOOL_EDIT_ORG_USER_INIT, 易董学院-编辑机构用户信息初始化, 0, /schUserManage/editOrgUserInit, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.957 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560866, 8801, RES_SCHOOL_ORG_USER_SAVE, 易董学院-保存机构用户信息, 0, /schUserManage/orgUserSave, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.958 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560867, 8801, RES_SCHOOL_EDIT_ORG_USER_SAVE, 易董学院-编辑合同用户信息, 0, /schUserManage/editOrgUserSave, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.959 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560869, 8801, RES_SCHOOL_USER_OVERVIEW, 易董学院-用户总览初始化, 0, /schUserManage/userOverview, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.960 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560870, 8801, RES_SCHOOL_DEL_ORG_USER_INFO, 易董学院-删除合同关联用户, 0, /schUserManage/delOrgUserInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.961 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560871, 8801, RES_SCHOOL_DEL_ORG_INFO, 易董学院-删除机构信息, 0, /schUserManage/delOrgInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.962 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560873, 8801, RES_SCHOOL_DEL_CONTRACT_INFO, 易董学院-删除合同信息, 0, /schUserManage/delContractInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.962 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560874, 8801, RES_SCHOOL_UPDATE_USER_TIME, 易董学院-更新授权时间, 0, /schUserManage/updateUserTime, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.963 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560876, 8801, RES_SCHOOL_DOWNLOAD_INFO, 易董学院-EXCEL模板下载, 0, /schUserManage/downloadInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.964 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589560877, 8801, RES_SCHOOL_IMPORT_USER_INFO, 易董学院-导入excel, 0, /schUserManage/importUserInfo, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.965 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589562143, 8801, RES_SCH_USER_MANAGER_INIT, 用户管理, 0, /schUserManage/schUserManageInit, null, null, null, null, 2021-08-16 15:08:05.0, null, 2021-08-16 15:08:06.0, null]
2024-03-18 10:44:13.965 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026589642740, 8801, RES_SCHOOL_QUERY_CONTRACT_USER_INFO_DETAIL, 易董学院-编辑机构用户信息详情, 0, /schUserManage/queryContractUserInfoDetail, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.966 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026592009649, 8801, RES_SCHOOL_CHECK_CONTRACT_USER_ONLY, 易董学院-合同手机号检验是否唯一, 0, /schUserManage/checkContractUserOnly, null, null, null, null, null, null, 2021-08-16 15:19:35.0, null]
2024-03-18 10:44:13.967 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026592808883, 8801, RES_SCHOOL_USER_CONTRACT_OVERVIEW_INIT, 易董学院-用户总览页面初始化, 0, /schUserManage/userContractOverviewInit, null, null, null, null, 2021-08-20 10:24:30.0, null, 2021-08-20 10:24:30.0, null]
2024-03-18 10:44:13.967 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026592808884, 8801, RES_SCHOOL_GET_USER_CONTRACT_OVERVIEW_LIST, 易董学院-用户总览列表查询, 0, /schUserManage/getUserContractOverviewList, null, null, null, null, 2021-08-20 10:24:30.0, null, 2021-08-20 10:24:30.0, null]
2024-03-18 10:44:13.968 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026592808885, 8801, RES_SCHOOL_USER_CONTRACT_DETAILS_INIT, 易董学院-用户授权次数详情初始化, 0, /schUserManage/userContractDetailsInit, null, null, null, null, 2021-08-20 10:24:30.0, null, 2021-08-20 10:24:30.0, null]
2024-03-18 10:44:13.970 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026592808886, 8801, RES_SCHOOL_GET_USER_CONTRACT_DETAILS_LIST, 易董学院-用户授权次数详情列表查询, 0, /schUserManage/getUserContractDetailsList, null, null, null, null, 2021-08-20 10:24:30.0, null, 2021-08-20 10:24:30.0, null]
2024-03-18 10:44:13.971 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108003026592808887, 8801, RES_SCHOOL_USER_CONTRACT_INFO_INIT, 易董学院-用户详情页面初始化, 0, /schUserManage/userContractInfoInit, null, null, null, null, 2021-08-20 10:24:30.0, null, 2021-08-20 10:24:30.0, null]
2024-03-18 10:44:13.972 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356347, 101, RES_SCH_SAVE_REPLY_COMMENT, 保存回复, 0, /bunchPlanting/saveReplyComment, null, null, , null, null, null, null, null]
2024-03-18 10:44:13.974 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356912, 1108003011071836963, RES_BANNER_INIT, 轮播设置-新增/编辑, 1, /homepageConfig/bannerAddInit, null, null, null, null, null, null, 2023-06-05 11:57:00.0, null]
2024-03-18 10:44:13.975 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356913, 1108003011071836963, RES_EDITCOURSE_INIT, 热门课程-选择远程/直播课程, 1, /homepageConfig/editCourseInfo, null, null, null, null, null, null, 2023-06-05 11:53:36.0, null]
2024-03-18 10:44:13.977 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356914, 1108003011071836963, RES_EDITCOURSE_TYPE_INIT, 热门专题-新增/编辑, 1, /homepageConfig/editCourseType, null, null, null, null, null, null, 2023-06-05 14:11:53.0, null]
2024-03-18 10:44:13.978 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356915, 1108003011071836963, RES_EDITCOURSE_TEACHER_INIT, 特聘专家-选择导师, 1, /homepageConfig/editCourseTeacher, null, null, null, null, null, null, 2023-06-05 14:32:18.0, null]
2024-03-18 10:44:13.979 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356916, 1108003011071836963, RES_SAVE_COURSE_INFO, 中上协-首页配置保存热门课程, 2, /homepageConfig/saveConfigCourseInfo, null, null, null, null, null, null, 2023-06-05 10:58:27.0, null]
2024-03-18 10:44:13.980 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356917, 1108003011071836963, RES_SAVE_COURSE_TYPE_INFO, 中上协-首页配置保存热门分类, 2, /homepageConfig/saveConfigCourseType, null, null, null, null, null, null, 2023-06-05 10:58:27.0, null]
2024-03-18 10:44:13.985 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356918, 1108003011071836963, RES_SAVE_COURSE_TEACHER_INFO, 中上协-首页配置保存热门讲师, 2, /homepageConfig/saveConfigCourseTeacher, null, null, null, null, null, null, 2023-06-05 10:58:27.0, null]
2024-03-18 10:44:13.986 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677356919, 1108003011071836963, RES_EDIT_COURSE_TEACHER_SORT, 编辑排序, 1, /homepageConfig/editCourseTeacherSort, null, null, null, null, null, null, 2023-06-05 11:52:54.0, null]
2024-03-18 10:44:13.989 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357055, 1108003011071836963, RES_BANNER_SELECT_COURSE, 中上协-首页配置轮播图选课程, 2, /homepageConfig/selectBannerCourse, null, null, null, null, null, null, 2023-06-05 10:58:28.0, null]
2024-03-18 10:44:13.992 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357056, 1108003011071836963, RES_BANNER_SELECT_COURSE_QUERY, 中上协-首页配置轮播图选课程（查询）, 2, /homepageConfig/queryChooseCourse, null, null, null, null, null, null, 2023-06-05 10:58:28.0, null]
2024-03-18 10:44:13.997 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357066, 1108003011071836963, RES_EDIT_COURSE_DELETE_TEACHER, 培训通知/精彩回顾/特聘专家-删除, 1, /homepageConfig/deleteHomePageTeach, null, null, null, null, null, null, 2023-06-05 14:32:15.0, null]
2024-03-18 10:44:14.000 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357304, 1108003011071836963, RES_SAVE_ADD_BANNER, 中上协-首页保存轮播图, 2, /homepageConfig/addBanner, null, null, null, null, null, null, 2023-06-05 10:58:28.0, null]
2024-03-18 10:44:14.003 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357792, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_INIT,  线下培训通知页面初始化, 2, /trainingOffline/trainingOfflineInit, null, null, , null, 2021-10-17 16:30:43.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.010 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357793, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_INIT_ADD, 新建现场活动/编辑, 1, /trainingOffline/trainingOfflineInitAdd, null, null, , null, 2021-10-17 16:30:47.0, null, 2023-06-06 11:02:46.0, null]
2024-03-18 10:44:14.012 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357794, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_REGISTRATION,  线下培训报名统计, 2, /trainingOffline/trainingOfflineRegistration, null, null, , null, 2021-10-17 16:30:50.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.013 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357795, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_TO_EXAMINE, 线下培训报名统计--审核, 2, /trainingOffline/trainingOfflineToExamine, null, null, , null, 2021-10-17 16:30:53.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.014 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357796, 102004, RES_TRAINING_OFFLINE_SAVE_TRAINING_OFFLINE_INFO, 发布/删除, 1, /trainingOffline/saveTrainingOfflineInfo, null, null, , null, 2021-10-17 16:30:57.0, null, 2023-06-06 11:04:24.0, null]
2024-03-18 10:44:14.019 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357797, 102004, RES_TRAINING_OFFLINE_SAVE_TRAINING_OFFLINE_SIGN_UP, 线下培训报名统计缴费, 2, /trainingOffline/saveTrainingOfflineSignUpMoney, null, null, , null, 2021-10-17 16:31:01.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.020 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357798, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_SIGN_UP_LIST, 线下培训报名统计列表页面数据, 2, /trainingOffline/trainingOfflineSignUpList, null, null, , null, 2021-10-17 16:31:05.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.024 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357799, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_INIT_LIST, 查询, 1, /trainingOffline/trainingOfflineInitList, null, null, , null, 2021-10-17 16:31:08.0, null, 2023-06-06 11:02:09.0, null]
2024-03-18 10:44:14.025 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357800, 102004, RES_TRAINING_OFFLINE_EXPORT_DETAIL_TABLE, 线下培训报名统计-导出, 2, /trainingOffline/exportDetailTable, null, null, , null, 2021-10-17 16:31:12.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.027 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357801, 102004, RES_TRAINING_OFFLINE_TRAINING_OFFLINE_NUM, 线下培训报名统计-计算数值, 2, /trainingOffline/trainingOfflineRegistrationByIdList, null, null, , null, 2021-10-17 16:31:15.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.029 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677357802, 102004, RES_TRAINING_OFFLINE_SAVE_TRAINING_ADD, 线下培训报名新增保存, 2, /trainingOffline/saveTrainingAdd, null, null, , null, 2021-10-17 16:31:19.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.033 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677380041, 1107442362268640893, RES_SCH_DELETE_ORG, 删除机构, 2, /ebSchoolLecturerInfo/deleteOrg, null, null, null, null, null, null, 2023-06-06 14:30:32.0, null]
2024-03-18 10:44:14.036 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677380234, 1107442362268640893, RES_SCH_GET_TYPEVALUE, 查询机构代码, 2, /ebSchoolLecturerInfo/getValueInfo, null, null, null, null, null, null, 2023-06-06 14:30:32.0, null]
2024-03-18 10:44:14.037 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677380301, 1108003011071836963, RES_BANNER_SELECT_BANNER, 中上协-首页配置轮播图选课程, 2, /homepageConfig/getBanner, null, null, null, null, null, null, 2023-06-05 10:58:28.0, null]
2024-03-18 10:44:14.038 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677392456, , RES_WEB_MANAGE, 网站管理页面初始化, 0, /webManage/webManageInit, null, null, null, null, null, null, 2021-10-28 17:12:50.0, null]
2024-03-18 10:44:14.040 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677392457, , RES_COURSE_REVIEW, 课程审核页面初始化, 0, /courseReview/courseReviewInit, null, null, null, null, null, null, 2021-10-28 17:12:50.0, null]
2024-03-18 10:44:14.041 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677433802, 28280253919314186, RES_PLAYBACK_RECORD_INIT, 学习记录, 1, /playbackRecord/playbackRecordInit, null, null, null, null, null, null, 2023-06-19 17:58:35.0, null]
2024-03-18 10:44:14.042 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677433803, 1108264963677433802, RES_PLAYBACK_RECORD_QUERY, 查询, 1, /playbackRecord/playbackRecordQuery, null, null, null, null, null, null, 2023-06-06 14:19:43.0, null]
2024-03-18 10:44:14.044 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1108264963677433804, 1108264963677433802, RES_PLAYBACK_RECORD_EXPORT, 学习记录列表导出, 2, /playbackRecord/export, null, null, null, null, null, null, 2023-06-06 14:18:47.0, null]
2024-03-18 10:44:14.045 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [118, , RES_MENU_INIT, 菜单管理初始化, 0, /menu/menudata*, null, null, null, null, null, 1, 2015-08-29 11:41:52.0, null]
2024-03-18 10:44:14.047 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [119, 118, RES_MENU_NODE, 菜单分类, 1, /menu/addMenuNode*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.061 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [120, 118, RES_MENU_DIS, 菜单禁用, 1, /menu/addLowNode*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.062 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [121, 118, RES_MENU_UPDATE, 菜单更新, 1, /menu/delSelectNode*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.068 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [122, 118, RES_MENU_TREE, 菜单新数据, 1, /menu/updateTree*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.069 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [123, 118, RES_MENU_CHECKNO, 菜单序号验证, 1, /menu/checkNo*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.071 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [124, 118, RES_MENU_CHECKNO, 菜单名称验证, 1, /menu/checkName*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.072 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556608725340, 102004, RES_TRAINING_GET_USER_LIST, 线下培训报名未符合查询, 2, /trainingOffline/getOfflineUserInfoList, null, null, , null, 2022-09-06 15:57:23.0, null, 2023-06-06 11:00:36.0, null]
2024-03-18 10:44:14.073 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556608822259, 102004, RES_TRAINING_OFFLINE_SEND_DETER, 一键发送短信审核, 2, /trainingOffline/sendSMSNotification, null, null, , null, 2022-09-01 20:42:33.0, null, 2023-06-06 11:00:36.0, null]
2024-03-18 10:44:14.073 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556610409467, , RES_SELECT_COURSE_PACK, 选择课程包, 1, /selectCoursePackage, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.074 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556610419255, 1613105796726824383, RES_COURSE_RELATION_LIVE_INIT, 关联直播, 2, /basicInformation/relationLiveInit, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:14.075 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556610419256, 1613105796726824383, RES_COURSE_RELATION_LIVE, 关联直播, 2, /basicInformation/relationLive, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:14.076 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706120, 748214900903776660, RES_DELETE_RELEVANCE_PERSON, 删除关联人员, 2, /examConfig/deleteRelevancePerson, null, null, null, null, 2023-03-01 09:59:42.0, null, 2023-06-06 15:48:28.0, null]
2024-03-18 10:44:14.076 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706121, 748214900903776660, RES_DELETE_UNIFY_EXAM, 删除一条统一考试, 2, /examConfig/deleteUnifyExam, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:28.0, null]
2024-03-18 10:44:14.077 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706122, 748214900903776660, RES_EXPORT_RELEVANCE_PERSON, 导出关联人员, 2, /examConfig/exportRelevancePerson, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:29.0, null]
2024-03-18 10:44:14.078 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706123, 748214900903776660, RES_IMPORT_RELEVANCE_PERSON_EXCEL, 导出关联人员, 2, /examConfig/importRelevancePersonExcel, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:29.0, null]
2024-03-18 10:44:14.079 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706124, 748214900903776660, RES_INSERT_RELEVANCE_PERSON, 新增关联人员, 2, /examConfig/insertRelevancePerson, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:29.0, null]
2024-03-18 10:44:14.081 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706125, 748214900903776660, RES_QUERY_RELEVANCE_PERSON_LIST, 关联人员List, 2, /examConfig/queryRelevancePersonList, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.082 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706126, 748214900903776660, RES_QUERY_UNIFY_EXAM_LIST, 统一考试List, 2, /examConfig/queryUnifyExamList, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.083 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706127, 748214900903776660, RES_RELEVANCE_PERSON_ADD_INIT, 新增关联人员初始化, 2, /examConfig/relevancePersonAddInit, null, null, null, null, 2023-03-02 14:04:01.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.084 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706128, 748214900903776660, RES_RELEVANCE_PERSON_INIT, 关联人员初始化, 2, /examConfig/relevancePersonInit, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.085 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706129, 748214900903776660, RES_UNIFY_EXAM_EDIT, 统一考试编辑, 2, /examConfig/unifyExamEdit, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.086 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706130, 748214900903776660, RES_SAVE_UNIFY_EXAM_EDIT, 保存新增/编辑统一考试, 2, /examConfig/saveUnifyExamEdit, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.087 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706131, 748214900903776660, RES_UNIFY_EXAM_INIT, 统一考试init, 2, /examConfig/unifyExamInit, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.088 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556611706132, 748214900903776660, RES_UPDATE_UNIFY_EXAM_PAPER, 更新考试名称和id, 2, /examConfig/updateUnifyExamPaper, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:30.0, null]
2024-03-18 10:44:14.089 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612352862, 748214900903776660, RES_GET_OFFLINE_PAPER_INFO, 获取线下考试信息, 2, /examConfig/getCapcoOfflinePaperInfo, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:31.0, null]
2024-03-18 10:44:14.090 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612567523, 108003, RES_LIVE_WATCH_LOG_DETAILS_INIT, 直播观看记录详情页初始化, 1, /watchLive/watchLiveDetailsInit, null, null, , null, null, null, 2023-05-12 09:53:18.0, null]
2024-03-18 10:44:14.091 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612568144, 108003, RES_QUERY_WATCH_LIVE_DETAILS, 查询直播观看记录, 2, /watchLive/queryWatchLiveDetails, null, null, , null, null, null, 2023-06-05 16:42:28.0, null]
2024-03-18 10:44:14.093 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612586218, 9250280212976379142, RES_VIEW_MONITORING_INFO, 查看监控, 1, /examMonitoring/viewMonitoringInfo, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:43:12.0, null]
2024-03-18 10:44:14.098 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612586219, 9250280212976379142, RES_VIEW_HISTORY_MONITORING_INFO, 历史监控, 1, /examMonitoring/viewHistoryMonitoringInfo, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:43:25.0, null]
2024-03-18 10:44:14.102 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612586220, 9250280212976379142, RES_VIEW_EXAM_INFO, 查看试卷, 1, /examMonitoring/viewExamInfo, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:43:47.0, null]
2024-03-18 10:44:14.106 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [12782114556612586221, 9250280212976379142, RES_PAPER_MARKING, 批卷, 1, /examMonitoring/paperMarking, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:43:54.0, null]
2024-03-18 10:44:14.127 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [131, 118, RES_MENU_UPDATE, 菜单更新, 1, /menu/updateSelectNode*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.128 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [132, 118, RES_MENU_POP, 菜单弹出框, 1, /menu/addpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.129 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [136, 118, RES_MENU_ADD, 插入一条数据, 1, /menu/addSave*, null, null, null, null, 2015-08-17 14:49:48.0, null, 2015-08-17 14:49:49.0, null]
2024-03-18 10:44:14.131 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [137, , RES_USERMANAGER_CENTER, 个人中心初始化, 0, /userManager/editUserCenter*, null, null, null, null, null, 1, 2015-08-29 11:42:39.0, null]
2024-03-18 10:44:14.132 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [140, , RES_RESOURCE, 资源管理初始化, 0, /resource/crudInit*, null, null, null, null, null, 1, 2015-08-29 11:42:48.0, null]
2024-03-18 10:44:14.133 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [141, 140, RES_RESOURCE_QUERY, 资源管理查询, 1, /resource/query*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.135 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [144, 140, RES_RESOURSE, 资源管理, 1, /resource/addpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.144 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [145, , RES_ROLEMANAGER_INIT, 角色管理初始化, 0, /roleManager/roleManagerInit*, null, null, null, null, null, 1, 2015-08-29 11:54:20.0, null]
2024-03-18 10:44:14.159 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [146, 145, RES_ROLEMANAGER_QUERY, 角色管理查询, 1, /roleManager/query*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.160 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [147, 145, RES_ROLEMANAGER_ADD, 角色管理新增, 1, /roleManager/addRolepop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.162 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [148, 145, RES_ROLEMANAGER_ADDSAVE, 角色管理新增保存, 1, /roleManager/addSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.163 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [149, 145, RES_ROLEMANAGER_DEL, 角色管理删除, 1, /roleManager/del*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.165 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [150, 145, RES_ROLEMANAGER_EDIT, 角色管理编辑, 1, /roleManager/editRolepop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.166 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [151, 145, RES_ROLEMANAGER_EDITSAVE, 角色管理编辑保存, 1, /roleManager/editSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.168 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [152, 145, RES_ROLEMANAGER_CHECK, 角色管理校验角色是否存在, 1, /roleManager/remoteValidate*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.169 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [153, 140, RES_RESOURCE_ADD, 资源管理新增, 1, /resource/addSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.175 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [154, 140, RES_RESOURCE_EDIT, 资源管理编辑, 1, /resource/editpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.176 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [155, 140, RES_RESOURCE_EDIT_SAVE, 资源管理编辑保存, 1, /resource/editSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.177 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [156, 140, RES_RESOURCE_DEL, 资源管理删除, 1, /resource/delect*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.178 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [157, 34, RES_USERMANAGER_ROLE, 用户管理选择角色, 1, /userManager/roleChoose*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.179 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [159, 160, RES_PARAMETER_ADD_INIT, 系统参数设置_新增初始化, 1, /parameter/parameterAddInit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.190 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [160, , RES_PARAMETER_INIT, 系统参数设置初始化, 0, /parameter/parameterInit*, null, null, null, null, null, 1, 2015-08-29 11:43:01.0, null]
2024-03-18 10:44:14.193 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [161, 160, RES_PARAMETER_EDIT_INIT, 系统参数设置_编辑初始化, 1, /parameter/parameterEditInit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.197 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613046640699040560, 110744236230214387, RES_USER_LOGIN_LOG_EXPORT, 用户登录信息导出, 1, /userLoginLog/export, null, null, null, null, null, null, 2023-06-06 15:37:07.0, null]
2024-03-18 10:44:14.206 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613046640699040627, 108002, RES_SCH_LIVE_SUBSCRIBE_EXPORTBYLIVE, 预约观看情况导出, 1, /liveSubscribe/exportByLive, null, null, , null, null, null, 2023-06-05 16:36:40.0, null]
2024-03-18 10:44:14.208 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726711869, null, RES_DIS_CODE, capital_capco_train优惠码, 0, /discountCode/discountCodeInit, null, null, null, 1, 2022-03-18 17:10:33.0, 1, 2023-06-06 14:49:46.0, null]
2024-03-18 10:44:14.209 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726711870, 1613105796726711869, RES_CREATE_DIS_CODE, 生成优惠码, 1, /discountCode/getDiscountCode, null, null, null, 1, 2022-03-18 17:10:33.0, 1, 2023-06-06 14:50:11.0, null]
2024-03-18 10:44:14.212 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726742515, 9250280212975642689, RES_INVOICE_REVERSE, 红冲发票, 1, /orderManage/invoiceReverse, null, null, null, 1, 2022-01-25 15:35:42.0, 1, 2023-06-06 14:45:24.0, null]
2024-03-18 10:44:14.213 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726745532, 28280253919312841, RES_SEND_SMS_INIT, 发送短信, 1, /sendSms/sendSmsInit, null, null, null, null, null, null, 2023-06-19 16:28:09.0, null]
2024-03-18 10:44:14.222 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726745537, 1613105796726745532, RES_SEND_SMS, 发送短信, 1, /sendSms/sendSms, null, null, null, null, null, null, 2022-08-09 17:08:57.0, null]
2024-03-18 10:44:14.223 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726777496, 748112110776736334, RES_TRAIN_USER_CHANGE, 用户修改页面, 2, /trainUserChange/trainUserChangeInit, null, null, null, null, null, null, 2023-06-06 15:28:30.0, null]
2024-03-18 10:44:14.230 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726777501, 748112110776736334, RES_TRAIN_USER_CHANGE_LIST, 查询, 1, /trainUserChange/getUserChangeInfoList, null, null, null, null, null, null, 2023-06-06 15:28:58.0, null]
2024-03-18 10:44:14.232 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726777506, 748112110776736334, RES_TRAIN_DEL_USER_CHANGE, 删除, 1, /trainUserChange/delUserInfo, null, null, null, null, null, null, 2023-06-06 15:29:53.0, null]
2024-03-18 10:44:14.238 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726777511, 748112110776736334, RES_TRAIN_USER_INFO_CHANGE, 修改用户信息, 2, /trainUserChange/userInfoChange, null, null, null, null, null, null, 2023-06-06 15:28:30.0, null]
2024-03-18 10:44:14.241 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726777516, 748112110776736334, RES_TRAIN_CHANGE_USER_INFO, 查看, 1, /trainUserChange/changeUserinfo, null, null, null, null, null, null, 2023-06-06 15:29:14.0, null]
2024-03-18 10:44:14.242 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726824381, 28280253919283262, RES_SCHOOL_COURSE_TYPE, 类别管理, 1, /courseType/courseTypeManage, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-17 14:27:42.0, null]
2024-03-18 10:44:14.243 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726824382, 28280253919283262, RES_SCHOOL_COURSE_VIDEO, 视频管理, 1, /schoolCourse/courseVideoManage, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-17 14:27:42.0, null]
2024-03-18 10:44:14.245 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726824383, 28280253919283262, RES_SCHOOL_COURSE_ESSENTIAL, 课程设置, 1, /basicInformation/basicInformationManage, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-17 14:24:08.0, null]
2024-03-18 10:44:14.246 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796726824384, 28280253919283262, RES_SCHOOL_COURSE_SORTINIT, 课程排序, 1, /courseRanking/courseRankingManage, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-17 14:26:04.0, null]
2024-03-18 10:44:14.247 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613105796727169037, , RES_TRAIN_TYPE_COST, 选择用户类型, 1, /selectTrainTypeCost, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.248 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570521007, , RES_SAVE_TRAIN_TYPE_COST, 保存用户类型, 1, /saveTrainTypeCost, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.249 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570521008, , RES_GET_PERSON_TYPES, 获取用户类型, 1, /getPersonTypeList, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.250 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570525726, 102004, RES_TRAINING_OFFLINE_ADD_USER_INIT, 线下培训新增人员初始化, 2, /trainingOffline/trainingOfflineAddUserInit, null, null, , null, 2022-09-01 20:42:31.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.259 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570525728, 102004, RES_TRAINING_OFFLINE_SAVE_USER_INFO, 线下培训报名新增人员保存, 2, /trainingOffline/saveUserInfo, null, null, , null, 2022-09-01 20:42:33.0, null, 2023-06-06 11:00:35.0, null]
2024-03-18 10:44:14.270 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570627938, 102004, RES_TRAINING_OFFLINE_ADVANCED, 报名统计, 1, /trainingOffline/trainingOfflineRegistrationAdvanced, null, null, , null, 2022-09-06 15:57:21.0, null, 2023-06-06 11:04:52.0, null]
2024-03-18 10:44:14.273 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570627939, 102004, RES_TRAINING_OFFLINE_SYS_LIST, 线下培训系统选中, 2, /trainingOffline/trainingOfflineSignUpSysList, null, null, , null, 2022-09-06 15:57:22.0, null, 2023-06-06 11:00:36.0, null]
2024-03-18 10:44:14.274 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613146852570627940, 102004, RES_TRAINING_OFFLINE_USER_LIST, 线下培训人工选中, 2, /trainingOffline/trainingOfflineSignUpUserList, null, null, , null, 2022-09-06 15:57:23.0, null, 2023-06-06 11:00:36.0, null]
2024-03-18 10:44:14.274 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [1613162475799260283, null, RES_EXCHSNGE_EXAM_INIT, 交易所培训考试初始化, 0, /exchangeExam/exchangeExamInit, null, null, , null, null, null, 2023-06-06 13:49:31.0, null]
2024-03-18 10:44:14.276 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [162, 160, RES_PARAMETER_QUERY, 系统参数设置_查询, 1, /parameter/ajaxQuery*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.277 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [16464648646464864846165, 748242623977938176, RES_ADD_SPECIAL_GRADE, 编辑级别, 2, /specialConfig/addSpecialGrade, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:56.0, null]
2024-03-18 10:44:14.278 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [165, 140, RES_RESOURCE_ITEM, 子资源信息, 1, /resource/itempop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.278 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [166, 34, RES_USERMANAGER_ROLEADD, 用户管理增加角色, 1, /userManager/addRole*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.279 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [167, 34, RES_USERMANAGER_ROLEDEL, 用户管理删除角色, 1, /userManager/delRole*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.279 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [169, 140, RES_RESOURCE_ITEMPOP, 新增子资源, 1, /resource/additempop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.280 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [170, 140, RES_RESOURCE_DELPOP, pop删除, 1, /resource/delectofpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.281 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [171, 140, RES_REVEAL_EDIT_ITEM, 编辑子项, 1, /resource/edititempop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.282 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [172, 140, RES_REVEAL_DEL_ITEM, 删除子项, 1, /resource/delectofpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.283 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [173, 145, RES_ROLE_SET, 权限设置, 1, /roleManager/queryRole*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.284 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [174, 145, RES_ROLE_ADD, 增加权限, 1, /roleManager/addRole*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.285 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [175, 145, RES_ROLE_DEL, 删除权限, 1, /roleManager/delRole*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.286 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [176, 140, RES_RESOURCE_CHK_NAME, 名字验证, 1, /resource/checkName*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.287 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [177, 140, RES_RESOURCE_CHK_URL, URL验证, 1, /resource/checkUrl*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.291 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [190, 160, RES_PARAMETER_EDIT_SAVE, 系统参数设置_编辑保存, 1, /parameter/parameterEditSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.292 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [192, 191, RES_CODE_QUERY, 业务代码设置_查询, 1, /code/ajaxQuery*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.293 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [193, 191, RES_CODE_EDIT_INIT, 业务代码设置_编辑初始化, 1, /code/codeEditInit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.294 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [194, 191, RES_CODE_EDIT_SAVE, 业务代码设置_编辑保存, 1, /code/codeEditSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.302 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [197, 118, RES_MENU_RESOURCE, 菜单资源, 1, /menu/menuresourcepop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.303 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199, 160, RES_PARAMETER_ADD_SAVE, 系统参数设置_新增保存, 1, /parameter/parameterAddSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.304 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199001, , RES_SYSTEM_ORG_MANAGER_INTI, 系统-机构管理初始化, 1, /orgmgr/orgInit, null, null, null, null, null, null, 2021-09-30 09:56:41.0, null]
2024-03-18 10:44:14.305 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199001001, 199001, RES_SYSTEM_ORG_GET_ALL_ORG, 系统-机构管理-获取全部机构, 1, /orgmgr/getAllOrg, null, null, null, null, null, null, 2021-10-14 10:42:18.0, null]
2024-03-18 10:44:14.305 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199001002, 199001, RES_SYSTEM_ORG_ADD_ORG, 系统-机构管理-新增机构机构, 1, /orgmgr/add, null, null, null, null, null, null, 2021-10-14 10:43:33.0, null]
2024-03-18 10:44:14.307 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199001003, 199001, RES_SYSTEM_ORG_ADD_SON_ORG, 系统-机构管理-新增子级机构, 1, /orgmgr/addb, null, null, null, null, null, null, 2021-10-14 10:42:18.0, null]
2024-03-18 10:44:14.316 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199001004, 199001, RES_SYSTEM_ORG_MODIFY_ORG, 系统-机构管理-修改机构, 1, /orgmgr/modify, null, null, null, null, null, null, 2021-10-14 10:54:08.0, null]
2024-03-18 10:44:14.327 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [199001005, 199001, RES_SYSTEM_ORG_DEL_ORG, 系统-机构管理-删除机构, 1, /orgmgr/del, null, null, null, null, null, null, 2021-10-14 10:54:13.0, null]
2024-03-18 10:44:14.328 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [2, , RES_MAIN, 首页, 0, /main*, null, null, null, null, null, 1, 2015-08-29 11:50:48.0, null]
2024-03-18 10:44:14.329 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [209, 191, RES_CODE_ADD_INIT, 业务代码设置_新增初始化, 1, /code/codeAddInit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.330 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [21013, 34, RES_DEPARTMENT_INIT, 系统管理-部门管理, 2, /department/departmentInit*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.331 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [21014, 34, RES_DEPARTMENT_QUERY_INIT, 系统管理-部门管理查询, 2, /department/query*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.338 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [21015, 34, RES_DEPARTMENT_POP_ROLE_INIT, 系统管理-选择角色, 2, /department/popRole*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.355 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [21016, 34, RES_DEPARTMENT_POP_ROLE_ADD, 系统管理-添加角色, 2, /department/addRole*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.361 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [21017, 34, RES_DEPARTMENT_POP_ROLE_DEL, 系统管理-删除角色, 2, /department/delRole*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.363 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [21018, 34, RES_SAVE_PERMISSION_RESOURCE, 系统管理-编辑门户网站资源, 2, /userManager/savePermissionResource*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.365 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [219, 191, RES_CODE_ADD_SAVE, 业务代码设置_新增保存, 1, /code/codeAddSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.366 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [228, 160, RES_PARAMETER_ADD_REMOTE_VALIDATE, 系统参数设置_新增后台校验, 1, /parameter/remoteValidate*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.368 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [229, 191, RES_CODE_ADD_REMOTE_VALIDATE, 业务代码设置_新增后台校验, 1, /code/remoteValidate*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.389 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [24882038364551729, 34, RES_USERMANAGER_RELEASELOCK, 解除用户锁定, 2, /userManager/releaseLock*, null, null, null, 1, 2017-01-08 11:37:29.0, 1, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.390 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27942207478671892, 9250280212976379142, RES_OPEN_CERTIFICATE, 开放证书, 2, /examMonitoring/openCertificate, null, null, null, 1, 2022-07-08 14:25:54.0, 1, 2023-06-06 15:42:04.0, null]
2024-03-18 10:44:14.394 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27942207478671894, 9250280212976379142, RES_DOWN_CERTIFICATE, 下载证书, 2, /examMonitoring/downCertificate, null, null, null, 1, 2022-07-08 14:25:54.0, 1, 2023-06-06 15:42:04.0, null]
2024-03-18 10:44:14.396 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27942207478671953, 9250280212976379142, RES_MAKE_CERTIFICATE, 制作证书, 2, /examMonitoring/makeCertificate, null, null, null, 1, 2022-07-08 14:25:54.0, 1, 2023-06-06 15:42:04.0, null]
2024-03-18 10:44:14.400 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27956949534466455, 748242623977984584, RES_SCH_PACK_SELECT_PACKAGE, 查询课程包信息, 2, /packManagement/getPackInfo, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 13:59:11.0, null]
2024-03-18 10:44:14.402 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27956949535290873, null, RES_CERTIFICATE_INIT, 证书管理初始化, 0, /certificateConfig/certificateInit, null, null, null, 1, 2022-11-10 17:36:58.0, 1, 2022-11-10 17:36:58.0, null]
2024-03-18 10:44:14.410 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27956949535304015, 27956949535290873, RES_QUERY_CERTIFICATE_LIST, 查询, 1, /certificateConfig/queryCertificateList, null, null, null, 1, 2022-11-08 16:53:20.0, 1, 2023-06-06 15:54:23.0, null]
2024-03-18 10:44:14.412 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27956949535305463, 27956949535290873, RES_VIEW_CERTIFICATE, 查看, 1, /certificateConfig/viewCertificate, null, null, null, 1, 2022-11-08 16:53:20.0, 1, 2023-06-06 15:54:41.0, null]
2024-03-18 10:44:14.413 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27985253637292544, 27956949535290873, RES_APPLY_CERTIFICATE, 审核证书照片更换, 2, /certificateConfig/applyCertificate, null, null, null, 1, 2022-11-08 16:53:20.0, 1, 2023-06-06 15:54:11.0, null]
2024-03-18 10:44:14.414 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27985253638038910, 748242625552706280, RES_INPUT_VALIDITY_TIME, 输入赠送时长界面初始化, 2, /membershipCardConfig/selectTimeInit, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:37.0, null]
2024-03-18 10:44:14.415 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [27994232987863917, 1613105796726824383, RES_EXPORT_COURSE, 导出, 1, /basicInformation/courseExport, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:52:05.0, null]
2024-03-18 10:44:14.417 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28017300485514975, 9250280212976379142, RES_MONITORING_EXPORT, 导出, 1, /examMonitoring/examMonitoringExport, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:43:02.0, null]
2024-03-18 10:44:14.418 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28017300487078331, 102004, RES_TRAINING_OFFLINE_BATCH_EXAMINE, 线下培训报名统计--批量审核, 2, /trainingOffline/batchExamine, null, null, , null, 2021-10-17 16:30:53.0, null, 2023-06-06 11:00:33.0, null]
2024-03-18 10:44:14.419 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28017300487134680, 102004, RES_TRAINING_OFFLINE_OFF_PUT_INIT, 线下培训人员顺延界面初始化, 2, /trainingOffline/offPutInit, null, null, , null, 2022-09-01 20:42:31.0, null, 2023-06-06 11:00:33.0, null]
2024-03-18 10:44:14.422 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28017300487136500, 102004, RES_TRAINING_OFFLINE_OFF_PUT_TRAIN, 线下培训报名人员顺延, 2, /trainingOffline/offPutTrain, null, null, , null, 2022-09-01 20:42:33.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.423 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28017300487137885, 102004, RES_TRAINING_OFFLINE_TRAINLIST, 获取线下培训高级证代列表, 2, /trainingOffline/getTrainingOfflineList, null, null, , null, 2022-09-01 20:42:33.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.424 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379571644446, 1613105796726745532, RES_SEND_SMS_LIST, 查询发送短信列表, 1, /sendSms/querySmsList, null, null, null, null, null, null, 2022-08-09 17:08:57.0, null]
2024-03-18 10:44:14.426 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379571646616, 102004, RES_TRAINING_OFFLINE_ADD_NEW_USER_INIT, 线下培训人员新增未报名用户界面初始化, 2, /trainingOffline/trainingOfflineAddNewUserInit, null, null, , null, 2022-09-01 20:42:31.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.428 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379571646622, 102004, RES_TRAINING_OFFLINE_ADD_NEW_USER, 线下培训人员新增未报名用户, 2, /trainingOffline/trainOfflineAddNewUser, null, null, , null, 2022-09-01 20:42:31.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.429 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379572234683, 102004, RES_TRAINING_OFFLINE_IMPORT_SUCCESS, 线下培训人员导入审核成功人员, 2, /trainingOffline/importSuccessPerson, null, null, , null, 2022-09-01 20:42:31.0, null, 2023-06-06 11:00:34.0, null]
2024-03-18 10:44:14.430 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379572616223, 748242623977938176, RES_RELEASE_SPECIAL_INFO, 删除, 1, /specialConfig/releaseSpecialInfo, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:54:45.0, null]
2024-03-18 10:44:14.431 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379572634480, 9250280212976379142, RES_LOGIN_EXAM, 登录考试系统, 2, /examApiConfig/loginToExam, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:14.432 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379573677893, 748214900903776660, RES_EXPORT_OFFLINE_EXAM_EXCEL_PERSON, 导出线下考试列表, 2, /examConfig/exportOfflineExamExcel, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:27.0, null]
2024-03-18 10:44:14.433 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379573678068, 748214900903776660, RES_IMPORT_OFFLINE_EXAM_EXCEL_PERSON, 导出线下考试列表, 2, /examConfig/importOfflineExamExcel, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:27.0, null]
2024-03-18 10:44:14.435 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28104379573865402, 748214900903776660, RES_DELETE_EXAM_RANK, 删除考试级别, 2, /examConfig/deleteExamRank, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:27.0, null]
2024-03-18 10:44:14.437 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [2811, 145, RES_ROLEMANAGER_SAVEROLERESOURCE, 保存角色资源信息, 2, /roleManager/saveRoleResource*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.443 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28121, 145, RES_ROLE_VIEW_POP, 角色查看, 1, /roleManager/roleViewpop*, null, null, , null, null, null, null, null]
2024-03-18 10:44:14.467 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28122, 145, RES_ROLE_VIEW_QUERY, 角色查看弹窗, 1, /roleManager/roleViewQuery*, null, null, , null, null, null, null, null]
2024-03-18 10:44:14.477 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754548, 1107442362268640762, RES_LIVE_COST, 费用查询, 1, /ebSchoolLiveInfo/liveCost, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-06-05 16:19:33.0, null]
2024-03-18 10:44:14.491 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754885, null, RES_COST_TEMPLATE_INIT, 价格模板页面初始化, 0, /costTemplate/pageInit, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-05-19 11:39:08.0, null]
2024-03-18 10:44:14.492 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754886, 28273744241754885, RES_ADD_COST_PAGE_INIT, 新增, 1, /costTemplate/addCostPageInit, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-06-06 15:56:54.0, null]
2024-03-18 10:44:14.493 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754887, 28273744241754885, RES_QUERY_COST_TEMPLATE_LIST, 查询, 1, /costTemplate/queryCostTemplate, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-06-06 15:56:48.0, null]
2024-03-18 10:44:14.495 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754888, 28273744241754885, RES_SELECT_COST_TEMPLATE_LIST_INIT, 选择价格模板列表初始化, 2, /costTemplate/costTemplateListInit, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-05-29 11:27:11.0, null]
2024-03-18 10:44:14.496 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754889, 28273744241754885, RES_SAVE_COST_TEMPLATE, 保存调价模板, 2, /costTemplate/saveTemplateCost, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-05-29 11:27:11.0, null]
2024-03-18 10:44:14.497 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754890, 28273744241754885, RES_DELETE_COST_TEMPLATE, 删除价格模板, 1, /costTemplate/deleteTemplateCost, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-05-29 11:27:11.0, null]
2024-03-18 10:44:14.500 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241754891, 28273744241754885, RES_GET_COST_TEMPLATE, 获取价格模板, 2, /costTemplate/getCostTemplate, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-05-29 11:27:11.0, null]
2024-03-18 10:44:14.501 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241759538, 1108003011071836963, RES_EDIT_REVIEW_INIT, 精彩回顾-新增/编辑, 1, /homepageConfig/editReviewInfo, null, null, null, null, 2022-08-25 16:20:14.0, null, 2023-06-05 14:07:47.0, null]
2024-03-18 10:44:14.502 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241780225, 28273744241754885, RES_SELECT_DATE_INIT, 选择到期日期初始化, 2, /costTemplate/selectDateInit, null, null, null, 1, 2023-05-14 11:23:13.0, 1, 2023-05-19 11:39:08.0, null]
2024-03-18 10:44:14.503 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28273744241780239, 28273744241754885, RES_SAVE_SELECT_TEMPLATE_COST, 保存已选择的价格模板, 2, /costTemplate/saveSelectTemplateCost, null, null, null, 2, 2023-05-14 11:23:13.0, 1, 2023-05-19 11:39:08.0, null]
2024-03-18 10:44:14.504 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [2828025391232145889, 1107442362268640893, RES_EB_SCHOOL_LECTURER_EX, 导出, 1, /ebSchoolLecturerInfo/lecturerInfoListExport, null, null, , 1, 2020-03-24 12:08:50.0, 1, 2023-08-15 17:47:44.0, null]
2024-03-18 10:44:14.505 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919015613, 9250280212976379142, RES_IMPORT_OFFLINE_EXAM_DATA, 导入线下考试数据, 2, /examMonitoring/importOfflineExam, null, null, null, 1, 2022-07-08 14:25:54.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:14.506 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919049814, 1108003011071836963, RES_BANNER_DELETE, 轮播设置-删除, 1, /homepageConfig/deleteBanner, null, null, null, null, null, null, 2023-06-05 11:11:45.0, null]
2024-03-18 10:44:14.507 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052637, 9250280212979015641, RES_SCH_DELETE, 删除, 1, /bunchPlanting/auditStatus, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:36:18.0, null]
2024-03-18 10:44:14.508 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052638, 9250280212979015641, RES_SCH_REPLY, 回复, 1, /bunchPlanting/replyCommentInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:36:10.0, null]
2024-03-18 10:44:14.509 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052639, 9250280212979015641, RES_SCH_REPLY_HISTROY, 回复历史, 1, /bunchPlanting/replyHistoryInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 17:36:12.0, null]
2024-03-18 10:44:14.509 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052941, 9250280212979015645, RES_SCH_OFFLINE_UPDATE_AUDIT_STATUS, 删除, 1, /bunchPlanting/auditStatus, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:56:18.0, null]
2024-03-18 10:44:14.510 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052942, 9250280212979015645, RES_SCH_OFFLINE_AUDIT_STATUS_INIT, 审核, 1, /bunchPlanting/auditStatusInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:56:19.0, null]
2024-03-18 10:44:14.511 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052943, 9250280212979015645, RES_SCH_OFFLINE_REPLY_STATUS_INIT, 回复, 1, /bunchPlanting/replyCommentInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:56:22.0, null]
2024-03-18 10:44:14.511 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052944, 9250280212979015645, RES_SCH_OFFLINE_REPLY_HISTROY_INIT, 回复历史, 1, /bunchPlanting/replyHistoryInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 17:56:26.0, null]
2024-03-18 10:44:14.513 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052945, 9250280212979015643, RES_SCH_LIVE_UPDATE_AUDIT_STATUS, 删除, 1, /bunchPlanting/auditStatus, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:49:00.0, null]
2024-03-18 10:44:14.517 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052946, 9250280212979015643, RES_SCH_LIVE_AUDIT_STATUS_INIT, 审核, 1, /bunchPlanting/auditStatusInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:48:11.0, null]
2024-03-18 10:44:14.518 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052947, 9250280212979015643, RES_SCH_LIVE_REPLY_STATUS_INIT, 回复, 1, /bunchPlanting/replyCommentInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:48:26.0, null]
2024-03-18 10:44:14.519 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919052948, 9250280212979015643, RES_SCH_LIVE_REPLY_HISTROY_INIT, 回复历史, 1, /bunchPlanting/replyHistoryInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 17:49:03.0, null]
2024-03-18 10:44:14.519 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070139, 1613105796726824383, RES_CHECK_COURSE, 审核, 1, /basicInformation/addCourseManageInit, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-06 14:15:17.0, null]
2024-03-18 10:44:14.520 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070157, 1107442362268640893, RES_EB_SCHOOL_LECTURER_EDIT, 新增/编辑, 1, /ebSchoolLecturerInfo/teacherInit, null, null, , 1, 2020-03-24 12:08:50.0, 1, 2023-06-06 14:34:49.0, null]
2024-03-18 10:44:14.522 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070238, 748214900903776660, RES_EXAM_MANGER, 题库管理, 1, /examConfig/examQuestionManger, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 15:50:11.0, null]
2024-03-18 10:44:14.524 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070257, 880, RES_QUESTIONNAIRE_ADD, 新增问卷, 1, /questionnaireInfo/add*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.527 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070258, 880, RES_QUESTIONNAIRE_RELEASE, 发布, 1, /questionnaireInfo/release*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.528 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070259, 880, RES_QUESTIONNAIRE_UPDATE, 修改, 1, /questionnaireInfo/update*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.529 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070260, 880, RES_QUESTIONNAIRE_EDIT, 编辑, 1, /questionnaireInfo/edit*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.530 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070261, 880, RES_QUESTIONNAIRE_REVIEW, 预览, 1, /questionnaireInfo/review*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.532 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919070262, 880, RES_QUESTIONNAIRE_DELETE, 删除, 1, /questionnaireInfo/delete*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.533 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919071123, 1107442362268640893, RES_EB_SCHOOL_LECTURER_SYN, 同步, 1, /ebSchoolLecturerInfo/synLecturerInfo, null, null, , 1, 2020-03-24 12:08:50.0, 1, 2023-08-15 17:39:34.0, null]
2024-03-18 10:44:14.536 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919071677, 880, RES_QUESTIONNAIRE_VIEW_RESULT, 查看结果, 1, /questionnaireInfo/viewResult*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2022-01-19 17:10:33.0, null]
2024-03-18 10:44:14.539 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919283262, , RES_SCHOOL_COURSE_DEMAND_MANAGE, 课程管理-点播管理, 0, /basicInformation/courseDemandManage, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-17 13:56:00.0, null]
2024-03-18 10:44:14.541 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919310305, , RES_SCHOOL_LIVE_MANAGE, 课程管理-直播设置, 0, /ebSchoolLiveInfo/liveManage, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-19 09:59:27.0, null]
2024-03-18 10:44:14.542 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919310306, , RES_SCHOOL_SPECIAL_COURSE_MANAGE, 课程管理-专题课管理, 0, /specialConfig/specialManageInit, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-19 10:23:01.0, null]
2024-03-18 10:44:14.544 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919312773, , RES_COMMENT_MANAGE, 动态信息管理-评论管理, 0, /offlineTraining/dynamicInformationCommentInit, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-19 15:20:27.0, null]
2024-03-18 10:44:14.545 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919312774, , RES_TRAIN_POST_MANAGE, 动态信息管理-培训动态, 0, /offlineTraining/trainPostInit, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-19 15:22:36.0, null]
2024-03-18 10:44:14.546 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919312841, null, RES_TRAIN_POST_MANAGE, 短信问卷-短信管理, 0, /sendSms/MsmManageInit, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-19 16:37:59.0, null]
2024-03-18 10:44:14.546 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919314185, , RES_USER_CHECK_MANAGE, 用户管理-用户审核, 0, /orderManage/userCheckInit, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-20 09:48:34.0, null]
2024-03-18 10:44:14.548 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919314186, , RES_USER_QUERY_MANAGE, 用户管理-用户查询, 0, /orderManage/userQueryInit, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-20 09:48:55.0, null]
2024-03-18 10:44:14.549 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [28280253919799963, 1613105796726745532, RES_SEND_SMS_BY_USER, 选择特定用户发送短信, 1, /sendSms/sendSmsByUser, null, null, null, null, null, null, 2023-07-19 17:43:01.0, null]
2024-03-18 10:44:14.550 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [3, , RES_FILE_UPLOAD, 文件上传, 1, /fileupload*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.551 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [301, 118, RES_MENU_NAME, 菜单名字, 1, /menu/selectName*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.551 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [305, 118, RES_RESOURCE_SLCT, 资源查询, 1, /menu/selectchdmenu*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.552 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [34, , RES_USERMANAGER_INIT, 用户管理初始化, 1, /userManager/userManagerInit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.552 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [35, 34, RES_USERMANAGER_QUERY, 用户管理查询, 1, /userManager/query*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.553 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [36, 34, RES_USERMANAGER_ADD, 用户管理新增, 1, /userManager/addUserpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.554 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [37, 34, RES_USERMANAGER_EDIT, 用户管理编辑, 1, /userManager/editUserpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.554 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [38, 34, RES_USERMANAGER_PASSWORD, 用户管理修改密码, 1, /userManager/changePasswordpop*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.555 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [39, 34, RES_USERMANAGER_DELETE, 用户管理删除, 1, /userManager/del*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.556 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [4, , RES_FILE_DOWNLOAD, 文件下载, 1, /filedownload*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.557 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [40, 34, RES_USERMANAGER_ADDSAVE, 用户管理新增保存, 1, /userManager/addSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.559 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [41, 34, RES_USERMANAGER_EDITSAVE, 用户管理编辑保存, 1, /userManager/editSave*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.561 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [42, 34, RES_USERMANAGER_PASSWORDSAVE, 用户管理修改密码保存, 2, /userManager/changePasswordSave*, null, null, null, null, null, null, 2023-06-26 09:54:24.0, null]
2024-03-18 10:44:14.562 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [************, 1108003011071836963, RES_BANNER_SAVE_MOVE_DETAIL, 轮播设置-上移/下移, 1, /homepageConfig/moveDetail, null, null, null, null, null, null, 2023-06-05 14:09:32.0, null]
2024-03-18 10:44:14.562 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [48964684684864684, 748242623977984584, RES_SCH_PACK_GET_COURSE_MAP, 查询课程信息, 2, /packManagement/getPackCourseMap, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 13:59:11.0, null]
2024-03-18 10:44:14.565 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [49, 34, RES_USERMANAGER_CHECK, 用户管理校验账号是否存在, 1, /userManager/remoteValidate*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.570 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [50, , RES_FILE_VIEW, 文件浏览, 1, /fileview*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.572 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [51, , RES_FILE_EDIT, 文件编辑, 1, /fileedit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.573 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [52, , RES_WEBSOCKET, 消息请求, 1, /websocket/**, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.574 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [6, , RES_FILE_TEMP_UPLOAD, 文件临时上传, 1, /filetempupload*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.576 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [600, 34, RES_USERMANAGER_UPDATEPWD, 用户管理密码修改, 1, /userManager/updatePersonPassword*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.577 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [601, 34, RES_USERMANAGER_PWDUPDATE, 用户管理密码修改保存, 1, /userManager/passwordUpdate*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.577 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [69, , RES_JOBSMANAGER_INIT, 机构岗位初始化, 1, /jobsManager/jobsManagerInit*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.580 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [7, , RES_FILE_DEL, 文件删除, 1, /filedel*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.581 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [745352190159280655, , RES_HOME_PIC_EDIT_SAVE, 登录图片编辑功能, 0, /home/<USER>
2024-03-18 10:44:14.582 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748112110776563479, 28280253919314186, RES_COURSE_PLAYBACK_RECORD_INIT, 课程播放查询, 1, /coursePlaybackRecord/coursePlaybackRecordInit, null, null, null, null, 2022-01-19 17:12:21.0, null, 2023-06-19 17:59:15.0, null]
2024-03-18 10:44:14.583 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748112110776736334, 28280253919314185, RES_USERCHANGE_INIT, 信息修改审核, 1, /trainUserChange/userChangeInit, null, null, null, null, null, null, 2023-06-19 17:01:13.0, null]
2024-03-18 10:44:14.585 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748112110777141619, 1107442362268640762, RES_EB_SCHOOL_SEND, 发送预约短信, 1, /ebSchoolLiveInfo/sendToNoAppointment, null, null, , 1, 2020-03-24 10:49:25.0, 1, 2023-06-05 16:20:04.0, null]
2024-03-18 10:44:14.586 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748214900903776660, null, RES_EXAM_INIT, 考试管理初始化, 0, /examConfig/examInit, null, null, null, null, 2022-05-19 09:39:21.0, null, 2022-05-19 09:39:21.0, null]
2024-03-18 10:44:14.587 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623977938176, 28280253919310306, RES_SPEIAL_MANAGE_INIT, 专题课设置, 1, /specialConfig/specialInit, null, null, null, null, 2022-05-19 09:39:21.0, null, 2023-06-19 11:19:59.0, null]
2024-03-18 10:44:14.588 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623977984584, 28280253919310306, RES_SCH_PACK_INIT, 打包管理, 1, /packManagement/packInit, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-19 11:20:01.0, null]
2024-03-18 10:44:14.589 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623982652858, 28280253919312841, RES_TRAIN_BLACK_LIST, 中上协-短信黑名单, 1, /blackList/blackListInit, null, null, null, null, null, null, 2023-06-19 16:24:27.0, null]
2024-03-18 10:44:14.589 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623982660599, 748242623982652858, RES_TRAIN_BLACK_LIST_QUERY, 查询, 1, /blackList/blackListQuery, null, null, null, null, null, null, 2023-06-06 15:26:41.0, null]
2024-03-18 10:44:14.590 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623982662897, 748242623982652858, RES_TRAIN_BLACK_LIST_DEL, 删除, 1, /blackList/blackListDel, null, null, null, null, null, null, 2023-06-06 15:27:02.0, null]
2024-03-18 10:44:14.591 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623982662950, 748242623982652858, RES_TRAIN_BLACK_LIST_ADD, 中上协-短信黑名单添加, 2, /blackList/blackListAdd, null, null, null, null, null, null, 2023-06-06 15:26:26.0, null]
2024-03-18 10:44:14.592 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242623982663237, 748242623982652858, RES_TRAIN_BLACK_LIST_ADD_INIT, 添加, 1, /blackList/blackListAddInit, null, null, null, null, null, null, 2023-06-06 15:26:54.0, null]
2024-03-18 10:44:14.593 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706280, null, RES_MEMBERSHIP_CARD_INIT, 会员卡初始化, 0, /membershipCardConfig/membershipCardInit, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2022-06-14 14:49:19.0, null]
2024-03-18 10:44:14.594 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706281, 748242625552706280, RES_QUERY_MEMBERSHIP_CARD_LIST, 查询, 1, /membershipCardConfig/queryMembershipCardList, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:56.0, null]
2024-03-18 10:44:14.594 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706282, 748242625552706280, RES_UPDATE_MEMBERSHIP_CARD_INFO, 发布/删除, 1, /membershipCardConfig/updateMembershipCardStatus, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:54:49.0, null]
2024-03-18 10:44:14.595 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706283, 748242625552706280, RES_QUERY_MEMBERSHIP_CARD_INFO, 查询会员卡信息, 2, /membershipCardConfig/queryMembershipCardInfo, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:37.0, null]
2024-03-18 10:44:14.596 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706284, 748242625552706280, RES_EDIT_MEMBERSHIP_CARD_INFO, 新建/编辑, 1, /membershipCardConfig/saveMembershipCardInfo, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:54:08.0, null]
2024-03-18 10:44:14.597 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706285, 748242625552706280, RES_SELECT_PARKCOURSE_LIST, 课程包列表初始化, 2, /membershipCardConfig/selectCoursePack, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:38.0, null]
2024-03-18 10:44:14.597 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706286, 748242625552706280, RES_SELECT_PERSON_LIST, 选择用户对象, 2, /membershipCardConfig/selectSendUser, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:38.0, null]
2024-03-18 10:44:14.606 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706287, 748242625552706280, RES_SEND_MEMBERSHIP_CARD, 赠送会员卡, 2, /membershipCardConfig/sendMembershipCard, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:38.0, null]
2024-03-18 10:44:14.610 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706288, 748242625552706280, RES_SHOW_CARD_HOLD_USER, 查看持有, 1, /membershipCardConfig/showCurrentCardHoldUser, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:54:21.0, null]
2024-03-18 10:44:14.612 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [748242625552706289, 748242625552706280, RES_SELECT_CARD_HOLD_USER, 获取会员卡持有用户列表, 2, /membershipCardConfig/getCurrentCardHoldUser, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 10:53:38.0, null]
2024-03-18 10:44:14.614 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [777001, , RES_MESSAGE_CENTER_INIT, 消息中心初始化, 2, /message/messageCenter*, null, null, , null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.614 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [8, , RES_OPEN_FILE_UPLOAD, 开放文件上传, 1, /openfileupload*, null, null, null, null, null, null, null, null]
2024-03-18 10:44:14.616 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [821000449167124879, 1107442362268640893, RES_SCH_TEACHER_ORG_INIT, 所属机构维护, 1, /ebSchoolLecturerInfo/orgList, null, null, , 1, 2020-03-24 12:08:50.0, 1, 2023-08-30 15:58:42.0, null]
2024-03-18 10:44:14.617 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [821271207361089241, 108004, RES_SCH_LIVE_TRAININIG_RECORDS_INIT, 参训记录初始化, 1, /liveSubscribe/trainingRecordsList, null, null, , null, null, null, 2024-03-04 17:42:11.0, null]
2024-03-18 10:44:14.618 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [821271207361089242, 108004, RES_SCH_LIVE_TRAININIG_RECORDS_EXPORT, 参训记录导出, 1, /liveSubscribe/trainingRecordsexport, null, null, null, null, 2024-03-04 17:25:09.0, null, 2024-03-04 17:26:50.0, null]
2024-03-18 10:44:14.619 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [880, null, RES_QUESTIONNAIRE_INIT, 短信问卷-调查问卷, 0, /questionnaireInfo/questionnaireInit*, null, null, null, 1, 2022-01-19 17:10:33.0, 1, 2023-06-19 16:24:11.0, null]
2024-03-18 10:44:14.620 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [8801, 8801, RES_COURSE_MANAGE_INIT, 易董学院课程初始化, 0, /courseOnDemand/courseOnDemandManageInit, null, null, null, 1, 2020-03-30 10:48:10.0, null, 2021-09-28 15:48:17.0, null]
2024-03-18 10:44:14.621 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [8802, , RES_SCH_VIDEO_COUNT, 易董学院视频统计初始化, 2, /schVideoCount/schVideoCountInit, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.623 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [8803, 8802, RES_GET_VIDEO_COUNT, 视频统计列表页查询, 2, /schVideoCount/getVideoCountList, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.627 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [8804, 8802, RES_SCH_COURSE_DETAILS, 视频统计课程详情列表页初始化, 2, /schVideoCount/schCourseDetails, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.629 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [8805, 8802, RES_GET_COURSE_DETAILS, 视频统计课程详情列表页查询, 2, /schVideoCount/getCourseDetailsList, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:14.630 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500001, , RES_EXPORT_TRAIN_MANAGER, 培训课程统计初始化, 0, /courseSpecial/exportTrainInit, null, null, null, null, 2023-07-11 10:01:33.0, null, 2023-08-03 09:51:22.0, null]
2024-03-18 10:44:14.631 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500002, , RES_EXPORT_REGISTER, 注册信息统计初始化, 0, /registerMember/getNums, null, null, null, null, 2023-07-11 10:04:52.0, null, 2023-07-17 15:37:09.0, null]
2024-03-18 10:44:14.632 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500003, 9251, RES_EXPORT_SUBJECT, 专题课程-观看记录, 1, /courseSpecial/getCourseSpecialList, null, null, null, null, 2023-07-11 10:06:22.0, null, 2023-07-27 09:30:57.0, null]
2024-03-18 10:44:14.635 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500004, 92500002, RES_EXPORT_REGISTER_NUMS, 注册信息-数量导出, 1, /registerMember/registerNumsExport, null, null, null, null, 2023-07-11 14:04:52.0, null, 2023-07-31 16:24:43.0, null]
2024-03-18 10:44:14.652 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500005, 92500002, RES_EXPORT_SELECT_LIST, 注册信息-查询, 1, /registerMember/getUserInfoList, null, null, null, null, 2023-07-24 10:23:25.0, null, 2023-08-10 09:54:01.0, null]
2024-03-18 10:44:14.656 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500006, 92500003, RES_EXPORT_SUBJECT_LIST, 专题课程-观看记录-查询, 1, /courseSpecial/getCourseSpecial, null, null, null, null, 2023-07-25 10:39:45.0, null, 2023-07-27 15:00:28.0, null]
2024-03-18 10:44:14.659 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500007, 9251, RES_EXPORT_NUMBER, 专题课程-培训统计, 1, /courseMemberNums/getCourseMemberNumsList, null, null, null, null, 2023-07-27 09:34:30.0, null, 2023-08-02 09:37:25.0, null]
2024-03-18 10:44:14.665 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500008, 92500007, RES_EXPORT_NUMBER_LIST, 专题课程-培训人数-查询, 1, /courseMemberNums/getCourseMemberNums, null, null, null, null, 2023-07-27 14:41:06.0, null, 2023-07-27 14:42:34.0, null]
2024-03-18 10:44:14.666 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500009, 92500002, RES_EXPORT_REGISTER_LIST, 注册信息-公司列表导出, 1, /registerMember/registerListExport, null, null, null, null, 2023-07-31 16:24:38.0, null, 2023-07-31 16:25:12.0, null]
2024-03-18 10:44:14.667 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500010, 92500003, RES_EXPORT_SUBJECT_EXLIST, 专题课程-观看记录-导出, 1, /courseSpecial/specialListExport, null, null, null, null, 2023-08-01 09:40:03.0, null, 2023-08-01 09:41:01.0, null]
2024-03-18 10:44:14.667 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500011, 9251, RES_EXPORT_PLAYBACK, 专题课程-视频统计, 1, /coursePlayback/getCoursePlaybackList, null, null, null, null, 2023-08-02 09:40:08.0, null, 2023-08-02 10:26:26.0, null]
2024-03-18 10:44:14.674 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500012, 92500011, RES_EXPORT_PLAYBACK_EXLIST, 专题课程-视频统计-导出, 1, /coursePlayback/courseListExport, null, null, null, null, 2023-08-02 09:44:28.0, null, 2023-08-02 10:41:36.0, null]
2024-03-18 10:44:14.675 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500013, 92500011, RES_EXPORT_PLAYBACK_LIST, 专题课程-视频统计-查询, 1, /coursePlayback/getCoursePlayback, null, null, null, null, 2023-08-02 09:45:30.0, null, 2023-08-02 10:26:02.0, null]
2024-03-18 10:44:14.676 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500014, 92500011, RES_EXPORT_PLAYBACK_GRADE, 获取专题培训级别类型, 2, /coursePlayback/getExportGradeTypeList, null, null, null, null, 2023-08-02 14:54:12.0, null, 2023-08-02 14:54:20.0, null]
2024-03-18 10:44:14.676 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500015, 92500001, RES_EXPORT_TRAIN_LIVE, 培训课程-直播统计, 1, /trainLive/getTrainLiveList, null, null, null, null, 2023-08-03 13:47:57.0, null, 2023-08-03 13:49:21.0, null]
2024-03-18 10:44:14.679 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500016, 92500015, RES_EXPORT_TRAIN_LIVE_LIST, 培训课程-直播统计-查询, 1, /trainLive/getTrainLive, null, null, null, null, 2023-08-03 13:49:40.0, null, 2023-08-03 13:58:12.0, null]
2024-03-18 10:44:14.680 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500017, 92500015, RES_EXPORT_TRAIN_LIVE_EXLIST, 培训课程-直播统计-导出, 1, /trainLive/trainListExport, null, null, null, null, 2023-08-03 13:50:36.0, null, 2023-08-03 16:09:13.0, null]
2024-03-18 10:44:14.682 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500018, 92500001, RES_EXPORT_TRAIN_LIVEDE, 培训课程-直播详细统计, 1, /trainLiveDetails/getTrainLiveDetailsList, null, null, null, null, 2023-08-04 11:30:49.0, null, 2023-08-04 11:31:37.0, null]
2024-03-18 10:44:14.688 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500019, 92500018, RES_EXPORT_TRAIN_LIVEDE_EXLIST, 培训课程-直播详细统计-导出, 1, /trainLiveDetails/trainListExport, null, null, null, null, 2023-08-04 11:32:25.0, null, 2023-08-04 14:06:46.0, null]
2024-03-18 10:44:14.690 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500020, 92500018, RES_EXPORT_TRAIN_LIVEDE_LIST, 培训课程-直播详细统计-查询, 1, /trainLiveDetails/getTrainLiveDetails, null, null, null, null, 2023-08-04 11:33:01.0, null, 2023-08-04 11:33:25.0, null]
2024-03-18 10:44:14.692 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500021, 92500001, RES_EXPORT_TRAIN_ONDEMAND, 培训课程-点播统计, 1, /onDemand/getOnDemandList, null, null, null, null, 2023-08-04 16:16:26.0, null, 2023-08-04 16:17:33.0, null]
2024-03-18 10:44:14.693 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500022, 92500021, RES_EXPORT_TRAIN_ONDEMAND_LIST, 培训课程-点播统计-查询, 1, /onDemand/getOnDemand, null, null, null, null, 2023-08-04 16:18:56.0, null, 2023-08-04 16:19:38.0, null]
2024-03-18 10:44:14.699 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500023, 92500021, RES_EXPORT_TRAIN_ONDEMAND_EXLIST, 培训课程-点播统计-导出, 1, /onDemand/onDemandListExport, null, null, null, null, 2023-08-04 16:19:53.0, null, 2023-08-07 14:00:03.0, null]
2024-03-18 10:44:14.705 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500024, 92500001, RES_EXPORT_TRAIN_ONDEMANDDE, 培训课程-点播详细统计, 1, /onDemandDetails/getOnDemandDetailsList, null, null, null, null, 2023-08-07 09:42:37.0, null, 2023-08-07 09:43:16.0, null]
2024-03-18 10:44:14.707 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500025, 92500024, RES_EXPORT_TRAIN_ONDEMANDDE_LIST, 培训课程-点播详细统计-查询, 1, /onDemandDetails/getOnDemandDetails, null, null, null, null, 2023-08-07 09:43:46.0, null, 2023-08-07 09:44:19.0, null]
2024-03-18 10:44:14.707 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500026, 92500024, RES_EXPORT_TRAIN_ONDEMANDDE_EXLIST, 培训课程-点播详细统计-导出, 1, /onDemandDetails/onDemandDetailsListExport, null, null, null, null, 2023-08-07 09:44:34.0, null, 2023-08-07 13:59:47.0, null]
2024-03-18 10:44:14.709 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500027, 92500007, RES_EXPORT_PNUMBER_LIST, 专题课程-培训通过人数-查询, 1, /courseMemberNums/getCoursePassNums, null, null, null, null, 2023-08-08 15:44:10.0, null, 2023-08-08 15:44:37.0, null]
2024-03-18 10:44:14.709 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500028, 92500007, RES_EXPORT_NUMBER_EXLIST, 专题课程-培训人数-导出, 1, /courseMemberNums/courseRenCiExport, null, null, null, null, 2023-08-09 09:44:26.0, null, 2023-08-09 10:01:34.0, null]
2024-03-18 10:44:14.711 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [92500029, 92500007, RES_EXPORT_PNUMBER_EXLIST, 专题课程-培训通过人数-导出, 1, /courseMemberNums/coursePassExport, null, null, null, null, 2023-08-09 09:45:16.0, null, 2023-08-09 10:07:39.0, null]
2024-03-18 10:44:14.712 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974968980, , RES_DATA, 资料页面初始化, 0, /dataConfig/dataInit, null, null, null, null, 2021-11-05 08:50:17.0, null, 2023-06-19 11:34:11.0, null]
2024-03-18 10:44:14.714 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974968981, 9250280212974968980, RES_DATA_LIST, 查询, 1, /dataConfig/trainDataInfoList, null, null, null, null, 2021-11-05 08:50:17.0, null, 2023-06-19 11:33:43.0, null]
2024-03-18 10:44:14.714 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974968982, 9250280212974968980, RES_DATA_ADD, 新增/编辑, 1, /dataConfig/trainDataAdd, null, null, null, null, 2021-11-05 08:50:17.0, null, 2023-06-19 11:45:12.0, null]
2024-03-18 10:44:14.716 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974968983, 9250280212974968980, RES_DATA_SAVE, 新增资料保存, 2, /dataConfig/saveTrainData, null, null, null, null, 2021-11-05 08:50:17.0, null, 2023-06-19 11:34:03.0, null]
2024-03-18 10:44:14.725 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974968984, 9250280212974968980, RES_DATA_STATUS, 删除/发布, 1, /dataConfig/getTrainDataStatus, null, null, null, null, 2021-11-05 08:50:17.0, null, 2023-06-19 11:44:47.0, null]
2024-03-18 10:44:14.728 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974977943, 1108264963677433802, RES_PLAYBACK_RECORD_LOOK, 查看, 1, /playbackRecord/playbackRecordLook, null, null, null, null, null, null, 2023-06-06 14:20:56.0, null]
2024-03-18 10:44:14.729 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212974995500, 1108264963677433802, RES_PLAYBACK_RECORD_LOOK_LIST, 学习记录查询, 2, /playbackRecord/playbackRecordLookList, null, null, null, null, 2021-11-30 14:00:11.0, null, 2023-06-06 14:18:47.0, null]
2024-03-18 10:44:14.730 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975026454, 1107442362300218388, RES_TRAIN_USER_MANAGER_SAVEINFO_UPDATE, 修改密码, 2, /trainUserManage/userInfoUpdate, null, null, null, null, 2021-12-06 17:47:33.0, null, 2023-06-06 15:23:29.0, null]
2024-03-18 10:44:14.731 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975217849, 1108264963677392456, RES_WEB_SAVEORG, 保存, 1, /webManage/saveOrgShow, null, null, null, null, null, null, 2023-06-06 14:16:36.0, null]
2024-03-18 10:44:14.771 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975223414, 1107442362300218388, RES_TRAIN_USER_MANAGE_HISTORY, 前端用户学习记录同步, 2, /trainUserManage/getHistory, null, null, null, null, 2022-01-06 17:06:00.0, null, 2023-06-06 15:21:27.0, null]
2024-03-18 10:44:14.774 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975297778, 9250280212974968980, RES_CHOOSE_RELATION_COURSE, 选择关联课程初始化, 2, /dataConfig/chooseCourseList, null, null, null, null, null, null, 2023-06-19 11:33:59.0, null]
2024-03-18 10:44:14.776 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975297783, 9250280212974968980, RES_GET_COURSE_NAME, 获取关联课程列表, 2, /dataConfig/getCourseNameList, null, null, null, null, null, null, 2023-06-19 11:33:59.0, null]
2024-03-18 10:44:14.777 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975297788, 9250280212974968980, RES_ADD_RELATION_COURSE, 添加关联课程, 2, /dataConfig/addRelationCourse, null, null, null, null, null, null, 2023-06-19 11:34:07.0, null]
2024-03-18 10:44:14.779 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975297794, 748112110776563479, RES_COURSE_PLAYBACK_RECORD_QUERY, 课程播放记录列表查询, 1, /coursePlaybackRecord/coursePlaybackRecordQuery, null, null, null, null, 2022-01-19 17:12:50.0, null, 2023-06-06 14:22:23.0, null]
2024-03-18 10:44:14.780 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975297795, 748112110776563479, RES_COURSE_PLAYBACK_RECORD_EXPORT, 课程播放记录导出, 1, /coursePlaybackRecord/courseExport, null, null, null, null, 2022-01-19 17:12:50.0, null, 2023-06-06 14:22:23.0, null]
2024-03-18 10:44:14.781 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975595327, 1107442362268640762, RES_SEND_MESSAGE_INFO, 订单详情, 2, /ebSchoolLiveInfo/getSendMessageInfo, null, null, null, 1, 2022-03-10 18:10:40.0, 1, 2023-06-05 16:21:10.0, null]
2024-03-18 10:44:14.782 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642421, null, RES_SUBSCRIBE_TRAIN_INIT, 交易所培训预约初始化, 0, /trainSubscribe/trainSubscribeInit, null, null, , null, null, null, 2023-06-06 13:43:10.0, null]
2024-03-18 10:44:14.783 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642555, 28280253919314186, RES_ORDER_DETAILS_INIT, 订单详情, 1, /orderDetails/orderDetailsInit, null, null, null, 1, null, 1, 2023-06-19 18:00:45.0, null]
2024-03-18 10:44:14.785 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642567, null, RES_EXCHANGE_TRAIN_INIT, 交易所培训初始化, 0, /exchangeTrain/exchangeTrainInit, null, null, , null, null, null, 2023-06-06 11:44:53.0, null]
2024-03-18 10:44:14.786 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642629, 1108003011071836963, RES_EDIT_COURSE_DELETE_TYPE, 热门专题-取消固定, 1, /homepageConfig/deleteCourseType, null, null, null, 1, 2022-03-18 17:05:32.0, 1, 2023-06-05 14:26:51.0, null]
2024-03-18 10:44:14.788 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642630, 1107442362268640762, RES_VIEW_MANAGE_RECORD, 短信日志, 1, /ebSchoolLiveInfo/viewMessageRecord, null, null, null, 1, 2022-03-18 17:05:32.0, 1, 2023-06-05 16:20:56.0, null]
2024-03-18 10:44:14.789 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642645, 108002, RES_SCH_LIVE_SUBSCRIBE_EXPORT, 导出, 1, /liveSubscribe/export, null, null, , null, null, null, 2023-06-05 16:36:42.0, null]
2024-03-18 10:44:14.791 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642650, 1107442362300218388, RES_SCH_USER_EXPORT, 导出, 1, /trainUserManage/export, null, null, , null, null, null, 2023-06-06 15:22:06.0, null]
2024-03-18 10:44:14.793 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642680, 9250280212975642689, RES_ORDER_INFO_VIEW, 订单详情, 1, /orderManage/viewOrderInfo, null, null, null, 1, 2022-03-18 17:10:11.0, 1, 2023-06-06 14:45:26.0, null]
2024-03-18 10:44:14.794 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642689, 28280253919314186, RES_ORDER_MANAGE, 订单查询, 1, /orderManage/orderManageInit, null, null, null, 1, 2022-03-18 17:10:33.0, 1, 2023-06-19 18:00:45.0, null]
2024-03-18 10:44:14.796 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642690, 9250280212975642689, RES_ORDER_QUERY, 查询, 1, /orderManage/orderQuery, null, null, null, 1, 2022-01-25 15:35:42.0, 1, 2023-06-06 14:43:19.0, null]
2024-03-18 10:44:14.800 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212975642691, 9250280212975642689, RES_ORDER_REFUND, 退款, 1, /orderManage/orderRefund, null, null, null, 1, 2022-01-25 15:35:42.0, 1, 2023-06-06 14:45:28.0, null]
2024-03-18 10:44:14.801 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150362, 748242623977984584, RES_SCH_PACK_LIST_INIT, 查询, 1, /packManagement/queryPackageList, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 13:59:36.0, null]
2024-03-18 10:44:14.802 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150363, 748242623977984584, RES_SCH_RELEASE_FLAG, 发布, 1, /packManagement/releasePack, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 14:00:57.0, null]
2024-03-18 10:44:14.803 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150364, 748242623977984584, RES_SCH_PACK_DELET_PACK, 删除, 1, /packManagement/deletePack, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 14:01:04.0, null]
2024-03-18 10:44:14.808 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150365, 748242623977984584, RES_SCH_PACK_EDIT_INIT, 新增/编辑, 1, /packManagement/editPackInit, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 14:00:24.0, null]
2024-03-18 10:44:14.809 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150366, 748242623977984584, RES_SCH_PACK_COURSE_INFO, 打包选择课程（多选）, 2, /packManagement/selectPackCourse, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 13:59:11.0, null]
2024-03-18 10:44:14.823 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150367, 748242623977984584, RES_SCH_PACK_SAVE_INFO, 保存&新建打包, 2, /packManagement/savePackInfo, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 13:59:11.0, null]
2024-03-18 10:44:14.824 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150368, 748242623977984584, RES_SCH_PACK_SELECT_ICOURSE, 查询课程信息, 2, /packManagement/getCourseInfo, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 13:59:12.0, null]
2024-03-18 10:44:14.825 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150369, 748214900903776660, RES_QUERY_EXAM_INFO, 查询, 1, /examConfig/queryExamInfoList, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 15:49:47.0, null]
2024-03-18 10:44:14.826 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150370, 748214900903776660, RES_ADD_EXAM, 新增考核/编辑, 1, /examConfig/addExamManageInit, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 15:52:22.0, null]
2024-03-18 10:44:14.827 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150371, 748214900903776660, RES_SAVE_EXAM, 考核列表, 2, /examConfig/saveExam, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 15:48:28.0, null]
2024-03-18 10:44:14.828 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150372, 748214900903776660, RES_UPDSTE_EXAM_STATUS, 删除, 1, /examConfig/updateExamStatus, null, null, null, null, 2022-05-19 09:39:41.0, null, 2023-06-06 15:52:57.0, null]
2024-03-18 10:44:14.829 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150373, 748214900903776660, RES_SELECT_PAPER_INFO, 试卷列表页, 2, /examConfig/selectPaperInfo, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:28.0, null]
2024-03-18 10:44:14.829 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150374, 748214900903776660, RES_GET_PAPER_LIST, 查询试卷列表, 2, /examConfig/getPaperList, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:28.0, null]
2024-03-18 10:44:14.830 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150375, 748214900903776660, RES_GET_COURSE_HOURSE, 查询业务类型学时, 2, /examConfig/getCourseHours, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 15:48:28.0, null]
2024-03-18 10:44:14.831 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150376, 748242623977938176, RES_QUERY_SPECIAL_INFO, 查询, 2, /specialConfig/querySpecialInfoList, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:52:16.0, null]
2024-03-18 10:44:14.832 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150377, 748242623977938176, RES_ADD_SPECIAL, 新增专题/编辑, 1, /specialConfig/addSpecial, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:53:13.0, null]
2024-03-18 10:44:14.833 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150378, 748242623977938176, RES_SPECIAL_DETAILED, 专题详情, 2, /specialConfig/selectSpecialDetailed, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.835 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150379, 748242623977938176, RES_SAVE_SPECIAL_INFO, 保存专题基本信息, 2, /specialConfig/saveSpecialInfo, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.836 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150380, 748242623977938176, RES_UPDATE_SPECIAL_INFO, 修改专题基本信息, 2, /specialConfig/updateSpecialInfo, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.838 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150381, 748242623977938176, RES_SAVE_SPECIAL_GRADE, 保存专题级别, 2, /specialConfig/saveSpecialGrade, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.841 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150382, 748242623977938176, RES_ADD_SPECIAL_TYPE, 添加专题分类, 2, /specialConfig/addSpecialType, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.843 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150383, 748242623977938176, RES_SAVE_SPECIAL_TYPE, 保存专题分类, 2, /specialConfig/saveSpecialType, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.844 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150384, 748242623977938176, RES_SELECT_EXAM_POP, 选择考试弹出页, 2, /specialConfig/selectExamPop, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.846 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150385, 748242623977938176, RES_SELECT_SPECIAL_EXAM, 选择考试, 2, /specialConfig/selectSpecialExam, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.847 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150386, 748242623977938176, RES_DELETE_SPECIAL_GRADE, 删除专题级别, 2, /specialConfig/deleteSpecialGrade, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:55.0, null]
2024-03-18 10:44:14.849 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150387, 748242623977938176, RES_DELETE_SPECIAL_TYPE, 删除专题分类, 2, /specialConfig/deleteSpecialType, null, null, null, null, 2022-05-19 09:39:42.0, null, 2023-06-06 13:51:56.0, null]
2024-03-18 10:44:14.851 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976150388, 1108003011071836963, RES_REFRESH_HOME, 刷新首页缓存, 1, /homepageConfig/refreshHome, null, null, null, null, 2022-05-19 09:39:43.0, null, 2023-06-05 10:58:33.0, null]
2024-03-18 10:44:14.853 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976311376, 748242623977938176, RES_SAVE_SPECIAL_PRICE, 保存专题级别收费, 2, /specialConfig/saveSpecialPrice, null, null, null, null, 2022-06-08 09:38:41.0, null, 2023-06-06 13:51:56.0, null]
2024-03-18 10:44:14.859 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976311378, 748242623977938176, RES_SPECIAL_PRICE, 专题级别收费设置, 2, /specialConfig/editSpecialPrice, null, null, null, null, 2022-06-08 09:39:13.0, null, 2023-06-06 13:51:56.0, null]
2024-03-18 10:44:14.861 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976312131, 748242623977938176, RES_SORT_SPECIAL_TYPE, 专题分类排序, 2, /specialConfig/sortSpecialType, null, null, null, null, 2022-06-08 15:52:57.0, null, 2023-06-06 13:51:56.0, null]
2024-03-18 10:44:14.862 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976379142, null, RES_EXAM_MONITORING_INIT, 考试监控初始化, 0, /examMonitoring/examMonitoringInit, null, null, null, 1, 2022-06-17 15:07:39.0, 1, 2022-06-17 15:07:39.0, null]
2024-03-18 10:44:14.863 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976379144, 9250280212976379142, RES_QUERY_EXAM_MONITORING_INFO, 查询, 1, /examMonitoring/queryExamMonitoringList, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:42:54.0, null]
2024-03-18 10:44:14.863 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976379145, 9250280212976379142, RES_SELECT_MONITOR, 查看认证, 1, /examMonitoring/selectMonitorInit, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:44:50.0, null]
2024-03-18 10:44:14.864 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976379146, 9250280212976379142, RES_TRAINING_LEVEL_MONITOR, 获取专题培训级别类型, 2, /examMonitoring/getExamGradeTypeList, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:14.865 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976379147, 9250280212976379142, RES_DEL_EXAM_RECORD, 删除, 1, /examMonitoring/updateExamMonitoringStatus, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:45:07.0, null]
2024-03-18 10:44:14.867 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976379148, 9250280212976379142, RES_UPDATE_VERIFICATION_STATUS, 更新人脸验证状态, 2, /examMonitoring/updateVerificationStatus, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:14.875 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976384808, 1107442362300218388, RES_USER_INFO_STATISTICS_EXPORT, 统计导出, 1, /trainUserManage/exportStatisticsData, null, null, null, 1, 2022-06-14 11:23:13.0, 1, 2023-06-06 15:22:14.0, null]
2024-03-18 10:44:14.877 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976474307, 9250280212975642689, RES_EXPORT_ORDER, 导出, 1, /orderManage/exportOrderTable, null, null, null, 1, 2022-06-27 13:56:04.0, 1, 2023-06-06 14:43:10.0, null]
2024-03-18 10:44:14.878 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976585305, 9250280212976379142, RES_SEND_CERTIFICATE, 下发证书, 2, /examMonitoring/sendCertificate, null, null, null, 1, 2022-07-08 14:25:54.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:14.878 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976585306, 9250280212976379142, RES_GET_USERTYPE, 获取考试用户, 2, /examMonitoring/getUserType, null, null, null, 1, 2022-07-08 14:25:54.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:14.888 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212976585752, 1107442362268640762, RES_EB_SCHOOL_SEND_NOT_WATCH, 发送提醒观看短信, 1, /ebSchoolLiveInfo/sendAppointmentNotWatch, null, null, null, 1, 2022-07-08 17:13:06.0, 1, 2023-06-05 16:20:18.0, null]
2024-03-18 10:44:14.889 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015560, 1613105796726824381, RES_SCHOOL_COURSE_TYPE_MAXSORT, 易董学院-课程类型树最大排序, 2, /courseType/getItemCategoryClassMaxSort, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 14:58:49.0, null]
2024-03-18 10:44:14.891 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015561, 1613105796726824381, RES_SCHOOL_COURSE_TYPE_BIDDEN, 禁用选中节点, 1, /courseType/courseTypeForbidden, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 15:00:13.0, null]
2024-03-18 10:44:14.915 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015562, 1613105796726824381, RES_SCHOOL_COURSE_TYPE_SAVE, 保存, 1, /courseType/courseTreeSave, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 14:59:59.0, null]
2024-03-18 10:44:14.916 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015563, 1613105796726824381, RES_SCHOOL_COURSE_TYPE, 易董学院-业务课程分类管理, 2, /courseType/courseTypeManage, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 14:58:49.0, null]
2024-03-18 10:44:14.918 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015564, 1613105796726824382, RES_FILE_OPEN, 易董学院-附件是否开放, 2, /schoolCourse/openFile, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 15:21:07.0, null]
2024-03-18 10:44:14.920 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015565, 1613105796726824382, RES_SCHOOL_lAWITEM_UPDATE, 易董学院-课程关联法规更改目录, 2, /schoolCourse/updateLawItemIds, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 15:21:07.0, null]
2024-03-18 10:44:14.921 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015566, 1613105796726824382, RES_SCHOOL_CASE_INFO_LIST, 易董学院-案例查询, 2, /schoolCourse/caseInfoListInit, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 15:21:07.0, null]
2024-03-18 10:44:14.922 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015567, 1613105796726824382, RES_SCHOOL_CAPITAL, 易董学院-选择案例, 2, /schoolCourse/capital, null, null, null, null, 2022-08-25 16:00:47.0, null, 2023-06-05 15:21:07.0, null]
2024-03-18 10:44:14.923 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015568, 1613105796726824382, RES_SCHOOL_CAPITAL_DEL, 易董学院-删除案例, 2, /schoolCourse/capitalDel, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:07.0, null]
2024-03-18 10:44:14.925 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015569, 1613105796726824382, RES_SCHOOL_COURSE_UPDATE, 易董学院-删除课程, 2, /schoolCourse/courseUpdate, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:07.0, null]
2024-03-18 10:44:14.943 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015570, 1613105796726824382, RES_SCHOOL_OPENFILEUPLOAD, 易董学院-上传文件, 2, /schoolCourse/openfileupload, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.946 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015571, 1613105796726824382, RES_SCHOOL_DEL_FILE, 易董学院-删除文件, 2, /schoolCourse/delFile, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.948 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015572, 1613105796726824382, RES_SCHOOL_CASE_SELECT_INIT, 易董学院-选择案例初始化, 2, /schoolCourse/caseSelectInit, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.949 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015573, 1613105796726824382, RES_SCHOOL_INSERT_LAW_ID, 易董学院-关联法律法规, 2, /schoolCourse/insertLawId, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.952 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015574, 1613105796726824382, RES_SCHOOL_DEL_COURSE_LAW, 易董学院-删除法律法规, 2, /schoolCourse/delCourseLaw, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.952 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015575, 1613105796726824382, RES_SCHOOL_INSERT_CASE_ID, 易董学院-关联违规案例, 2, /schoolCourse/insertCaseId, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.955 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015576, 1613105796726824382, RES_SCHOOL_DEL_COURSE_CASE, 易董学院-删除违规案例, 2, /schoolCourse/delCourseCase, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.956 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015577, 1613105796726824382, RES_SCHOOL_TREE_UPDATE, 易董学院-课程树保存, 2, /schoolCourse/treeUpdate, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:08.0, null]
2024-03-18 10:44:14.957 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015578, 1613105796726824382, RES_SCHOOL_VIDEO_MANAGE_INIT, 易董学院-课程视频选择初始化, 2, /schoolCourse/videoManageInit, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.959 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015579, 1613105796726824382, RES_SCHOOL_VIDEO_LIST_INIT, 易董学院-课程视频查询, 2, /schoolCourse/videoInfoListInit, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.960 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015580, 1613105796726824382, RES_SCHOOL_COURSE_REFRESH, 易董学院-课程树刷新, 2, /schoolCourse/refresh, null, null, null, null, 2022-08-25 16:00:48.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.963 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015581, 1613105796726824382, RES_SCHOOL_NODE_UPDATE, 易董学院-课程结点更新, 2, /schoolCourse/nodeUpdate, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.970 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015582, 1613105796726824382, RES_SCHOOL_COURSE_QUERY, 易董学院-课程查询, 2, /schoolCourse/queryRepCaseInfoList, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.982 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015583, 1613105796726824382, RES_SCHOOL_COURSE_ADD, 易董学院-课程新增, 2, /schoolCourse/addCourseManageInit, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.983 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015584, 1613105796726824382, RES_SCHOOL_COURSE_SAVE, 易董学院-课程保存, 2, /schoolCourse/courseSave, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.985 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015585, 1613105796726824382, RES_SCHOOL_COURSE_VIDEO, 易董学院-业务课程视频管理, 2, /schoolCourse/courseVideoManage, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:09.0, null]
2024-03-18 10:44:14.987 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015586, 1613105796726824382, RES_SCHOOL_COURSE_ESSENTIAL_EXPORT, 易董学院-业务课程基本信息导出, 2, /schoolCourse/exportCourse, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:14.989 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015587, 1613105796726824382, RES_SCHOOL_COURSE_UPDATE_FILE, 易董学院-业务课程附件展示, 2, /schoolCourse/updateFileDownloadOpen, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:14.991 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015588, 1613105796726824382, RES_SCHOOL_DEL_EVENT, 易董学院-事件删除, 2, /schoolCourse/deleteEvent, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:14.994 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015589, 1613105796726824382, RES_SCHOOL_DEL_COLUMN, 易董学院-专栏删除, 2, /schoolCourse/deleteColumn, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:14.996 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015590, 1613105796726824382, RES_SCHOOL_EVENT_DETAIL_INIT, 易董学院-事件编辑初始化, 2, /schoolCourse/schEventDetailInit, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:15.013 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015591, 1613105796726824382, RES_SCHOOL_EVENT_INFO_LIST, 易董学院-课程管理查询, 2, /schoolCourse/getSchEventInfoList, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:15.014 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015592, 1613105796726824382, RES_SCHOOL_EVENT_MANAGE, 易董学院-事件管理, 2, /schoolCourse/eventManage, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:15.016 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015593, 1613105796726824382, RES_SCHOOL_SAVE_SCHOOL_EVENT, 易董学院-事件信息保存, 2, /schoolCourse/saveSchoolEvent, null, null, null, null, 2022-08-25 16:00:49.0, null, 2023-06-05 15:21:10.0, null]
2024-03-18 10:44:15.017 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015594, 1613105796726824382, RES_SCHOOL_SAVE_SCHOOL_COLUMN, 易董学院-专栏信息保存, 2, /schoolCourse/saveSchoolColumn, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.018 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015595, 1613105796726824382, RES_SCHOOL_DEL_ASSOCIATED, 易董学院-删除专栏事件关联课程, 2, /schoolCourse/delAssociated, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.019 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015596, 1613105796726824382, RES_SCHOOL_COURSE_OF_RELA, 易董学院-查询已关联的课程, 2, /schoolCourse/courseOfRela, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.020 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015597, 1613105796726824382, RES_SCHOOL_COLUMN_RELA_COURSE, 易董学院-专栏关联课程, 2, /schoolCourse/schColumnRelaCourse, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.021 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015598, 1613105796726824382, RES_SCHOOL_COLUMN_DETAIL_INIT, 易董学院-专栏编辑初始化, 2, /schoolCourse/schColumnDetailInit, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.027 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015599, 1613105796726824382, RES_SCHOOL_COLUMN_INFO_LIST, 易董学院-专栏管理查询, 2, /schoolCourse/getSchColumnInfoList, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.038 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015600, 1613105796726824382, RES_SCHOOL_COLUMN_MANAGE, 易董学院-专栏管理, 2, /schoolCourse/columnManage, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.040 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015601, 1613105796726824382, RES_DELETE_SCH_COURSE_VIDEOMAP, 易董学院-专栏管理, 2, /schoolCourse/deleteSchCourseVideoMap, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:11.0, null]
2024-03-18 10:44:15.043 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015602, 1613105796726824382, RES_QUERY_IS_CONNECT, 易董学院-专栏管理, 2, /schoolCourse/queryIsConnect, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.045 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015603, 1613105796726824382, RES_QUERY_IS_CUT_VIDEO, 易董学院-查询相关视频是否切片, 2, /schoolCourse/queryIsCutVideo, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.062 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015604, 1613105796726824382, RES_COLUMN_DETAIL_INFO, 易董学院-获取专栏课程, 2, /schoolCourse/getColumnDetailInfo, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.066 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015605, 1613105796726824382, RES_SCH_COLUMN_VERSION_INIT, 易董学院-历史课程编辑页, 2, /schoolCourse/schColumnVersionInit, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.068 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015606, 1613105796726824382, RES_GET_COLUMN_DETAIL_VERSION_INFO, 易董学院-获取专栏课程, 2, /schoolCourse/getColumnDetailVersionInfo, null, null, null, null, 2022-08-25 16:00:50.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.070 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015607, 1613105796726824382, RES_SAVE_SCHOOL_COLUMN_DETAIL_VERSION, 易董学院-获取专栏历史课程信息, 2, /schoolCourse/saveSchoolColumnDetailVersion, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.071 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015608, 1613105796726824382, RES_GET_EVENT_DETAIL_INFO, 易董学院-获取事件课程, 2, /schoolCourse/getEventDetailInfo, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.078 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015609, 1613105796726824382, RES_REFRESH_EVENT_AND_COLUMN, 易董学院-刷新专栏和事件, 2, /schoolCourse/refreshEventAndColumn, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:21:12.0, null]
2024-03-18 10:44:15.079 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015610, 1613105796726824382, RES_CUT_VIDEO, 视频切片, 1, /video/cutVideo, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:34:29.0, null]
2024-03-18 10:44:15.110 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015611, 1613105796726824382, RES_SCHOOL_DELETE_VIDEO, 易董学院-删除视频, 2, /video/deleteVideo, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:21:13.0, null]
2024-03-18 10:44:15.112 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015612, 1613105796726824382, RES_SCHOOL_COURSE_VIDEO_INFO, 查询, 1, /video/getCourseVideoInfoList, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:23:10.0, null]
2024-03-18 10:44:15.113 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015613, 1613105796726824382, RES_GET_VIDEO_DURATION, 刷新视频时长, 1, /video/getVideoDuration, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:32:07.0, null]
2024-03-18 10:44:15.114 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015614, 1613105796726824382, RES_SCHOOL_UPDATE_VIDEO, 新增学时/删除/编辑, 1, /video/updateVideo, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:33:42.0, null]
2024-03-18 10:44:15.115 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015615, 1613105796726824382, RES_UPDATE_VIDEO_CUT_URL, 视频切片链接更新, 1, /video/updateVideoCutUrl, null, null, null, null, 2022-08-25 16:00:51.0, null, 2023-06-05 15:45:29.0, null]
2024-03-18 10:44:15.116 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015617, 1613105796726824383, RES_COURSE_QUERY_INFO, 查询, 1, /basicInformation/queryRepCaseInfoList, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:52:24.0, null]
2024-03-18 10:44:15.116 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015618, 1613105796726824383, RES_ADD_COURSE_INFO, 新增/编辑课程, 1, /basicInformation/addCourseManageInit, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:47:10.0, null]
2024-03-18 10:44:15.117 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015619, 1613105796726824383, RES_ADD_COURSE_SAVE_INFO, 中上协-新建/编辑课程保存, 2, /basicInformation/courseSave, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:31.0, null]
2024-03-18 10:44:15.133 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015620, 1613105796726824383, RES_RELEASE_COURSE, 中上协-发布/取消发布, 2, /basicInformation/courseRelease, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:31.0, null]
2024-03-18 10:44:15.136 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015621, 1613105796726824383, RES_VIDEO_INIT, 中上协-视频管理初始化, 2, /basicInformation/videoManageInit, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:31.0, null]
2024-03-18 10:44:15.144 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015622, 1613105796726824383, RES_DELETE_COURSE, 删除课程, 1, /basicInformation/deleteCourse, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:51:51.0, null]
2024-03-18 10:44:15.145 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015623, 1613105796726824383, RES_COURSE_QUERY_VIDEO, 中上协-课程关联视频查询视频, 2, /basicInformation/videoInfoListInit, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:31.0, null]
2024-03-18 10:44:15.146 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015624, 1613105796726824383, RES_COURSE_TREE_VIDEO, 中上协-课程关联视频章节, 2, /basicInformation/treeUpdate, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:15.148 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015625, 1613105796726824383, RES_COURSE_REFRESH_VIDEO, 中上协-撤销关联视频, 2, /basicInformation/refresh, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:15.149 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015626, 1613105796726824383, RES_COURSE_SAVE_VIDEO, 中上协-保存关联视频, 2, /basicInformation/nodeUpdate, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:15.157 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015627, 1613105796726824383, RES_COURSE_BACK_IMG, 上传课程f封面, 2, /basicInformation/uploadBackImg, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:15.159 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015628, 1613105796726824383, RES_COURSE_FILE, 上传课程附件, 2, /basicInformation/uploadCourseFile, null, null, null, null, 2022-08-25 16:00:52.0, null, 2023-06-05 14:42:32.0, null]
2024-03-18 10:44:15.160 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015629, 1613105796726824384, RES_SCHOOL_COURSE_SORTINIT, 易董学院-业务课程排序初始化, 2, /courseRanking/courseRankingManage, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 15:46:45.0, null]
2024-03-18 10:44:15.161 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015630, 1613105796726824384, RES_GET_COURSE_LIST, 查询, 1, /courseRanking/getSortList, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-20 17:51:01.0, null]
2024-03-18 10:44:15.162 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015631, 1613105796726824384, RES_EDIT_SORT_PAGE, 顺序调整, 1, /courseRanking/editSortPage, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 15:52:10.0, null]
2024-03-18 10:44:15.171 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015632, 1613105796726824384, RES_MOVE_INFO, 易董学院-上下排序, 2, /courseRanking/moveInfo, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 15:46:45.0, null]
2024-03-18 10:44:15.173 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015633, 1613105796726824384, RES_SAVE_SORT, 上移/下移, 1, /courseRanking/saveSort, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 15:51:31.0, null]
2024-03-18 10:44:15.174 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015634, 28280253919312773, RES_SCH_BUNCH_PLANT_INIT, 点播评论, 1, /bunchPlanting/bunchPlantingInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-19 15:27:18.0, null]
2024-03-18 10:44:15.176 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015635, 9250280212979015634, RES_SCH_UPDATE_AUDIT_STATUS, 删除, 1, /bunchPlanting/auditStatus, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:49:00.0, null]
2024-03-18 10:44:15.177 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015636, 9250280212979015634, RES_SCH_AUDIT_STATUS_INIT, 审核, 1, /bunchPlanting/auditStatusInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:48:11.0, null]
2024-03-18 10:44:15.178 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015637, 9250280212979015634, RES_SCH_BUNCH_PLANT_LIST, 查询, 1, /bunchPlanting/getBunchPlantingList, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:45:40.0, null]
2024-03-18 10:44:15.179 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015638, 9250280212979015634, RES_SCH_REPLY_STATUS_INIT, 回复, 1, /bunchPlanting/replyCommentInit, null, null, null, null, 2022-08-25 16:00:53.0, null, 2023-06-05 17:48:26.0, null]
2024-03-18 10:44:15.180 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015639, 9250280212979015634, RES_SCH_REPLY_HISTROY_INIT, 回复历史, 1, /bunchPlanting/replyHistoryInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 17:49:03.0, null]
2024-03-18 10:44:15.182 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015640, 9250280212979015634, RES_SCH_SAVE_REPLY_COMMENT, 保存回复, 2, /bunchPlanting/saveReplyComment, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 17:45:40.0, null]
2024-03-18 10:44:15.183 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015641, 28280253919312773, RES_SCH_FEED_BACK_INIT, 我要提问, 1, /feedBack/feedBackInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-19 15:27:43.0, null]
2024-03-18 10:44:15.184 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015642, 9250280212979015641, RES_SCH_GET_FEED_LIST, 获取意见反馈列表, 1, /feedBack/getFeedBackList, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 17:01:34.0, null]
2024-03-18 10:44:15.185 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015643, 28280253919312773, RES_SCH_LIVE_COMMENT_INIT, 直播评论, 1, /offlineTraining/liveCommentInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-19 15:27:34.0, null]
2024-03-18 10:44:15.186 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015644, 9250280212979015643, RES_SCH_LIVE_COMMENT_LIST, 查询, 1, /offlineTraining/getLiveCommentList, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-05 18:00:04.0, null]
2024-03-18 10:44:15.188 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015645, 28280253919312773, RES_SCH_OFF_LINE_INIT, 线下培训, 1, /offlineTraining/offlineTrainingInit, null, null, null, null, 2022-08-25 16:00:54.0, null, 2023-06-19 15:27:36.0, null]
2024-03-18 10:44:15.190 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015646, 9250280212979015645, RES_SCH_GET_LINE_INIT, 查询, 1, /offlineTraining/getOfflineTrainingList, null, null, null, null, 2022-08-25 16:00:55.0, null, 2023-06-05 18:00:31.0, null]
2024-03-18 10:44:15.191 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979015955, 1108003011071836963, RES_EDIT_NOTICE_INIT, 培训通知-新增/编辑, 1, /homepageConfig/editNoticeInfo, null, null, null, null, 2022-08-25 16:20:14.0, null, 2023-06-05 11:56:36.0, null]
2024-03-18 10:44:15.194 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979183944, 9250280212976379142, RES_EXAMINE_ABOPT, 审核考试, 2, /examMonitoring/examineAdopt, null, null, null, 1, 2022-09-08 15:08:54.0, 1, 2023-06-06 15:42:05.0, null]
2024-03-18 10:44:15.195 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431877, 9250280212975642567, RES_EXCHANGE_TRAIN_QUERY, 查询, 1, /exchangeTrain/getExchangeTrainList, null, null, , null, null, null, 2023-06-06 11:45:30.0, null]
2024-03-18 10:44:15.196 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431878, 9250280212975642567, RES_EXCHANGE_TRAIN_UPDTE, 删除, 1, /exchangeTrain/updateField, null, null, , null, null, null, 2023-06-06 13:42:14.0, null]
2024-03-18 10:44:15.202 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431879, 9250280212975642567, RES_EXCHANGE_ADD_INIT, 新增/编辑, 1, /exchangeTrain/exchangeTrainAddInit, null, null, , null, null, null, 2023-06-06 13:35:21.0, null]
2024-03-18 10:44:15.204 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431880, 9250280212975642567, RES_EXCHANGE_TRAIN_COURSE, 培训选择课程初始化, 2, /exchangeTrain/exchangeTrainCourse, null, null, , null, null, null, 2023-06-06 11:45:09.0, null]
2024-03-18 10:44:15.205 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431881, 9250280212975642567, RES_EXCHANGE_COURSE_QUERY, 选择课程直播, 2, /exchangeTrain/queryChooseCourse, null, null, , null, null, null, 2023-06-06 11:45:09.0, null]
2024-03-18 10:44:15.206 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431882, 9250280212975642567, RES_EXCHANGE_COURSE_INFO, 查询课程信息, 2, /exchangeTrain/getCourseInfo, null, null, , null, null, null, 2023-06-06 11:45:09.0, null]
2024-03-18 10:44:15.208 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431883, 9250280212975642567, RES_EXCHANGE_TRAIN_SAVE, 保存培训信息, 2, /exchangeTrain/saveExchangeTrainInfo, null, null, , null, null, null, 2023-06-06 11:45:09.0, null]
2024-03-18 10:44:15.216 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431884, 9250280212975642421, RES_SUBSCRIBE_TRAIN_QUERY, 查询, 1, /trainSubscribe/getTrainSubscribeList, null, null, , null, null, null, 2023-06-06 13:43:43.0, null]
2024-03-18 10:44:15.229 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431888, 1613162475799260283, RES_EXCHSNGE_QUERY_EXAM_LIST, 查询, 1, /exchangeExam/queryExchangeExamMonitoringList, null, null, , null, null, null, 2023-06-06 13:51:31.0, null]
2024-03-18 10:44:15.230 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431937, 9250280212975642555, RES_ORDER_DETAILS_QUERY, 查询, 1, /orderDetails/getOrderDetailsList, null, null, , null, null, null, 2023-06-06 14:48:14.0, null]
2024-03-18 10:44:15.231 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979431938, 9250280212975642555, RES_ORDER_DETAILS_EXPORT, 条件下导出, 1, /orderDetails/exportDetailsTable, null, null, , null, null, null, 2023-06-06 14:48:35.0, null]
2024-03-18 10:44:15.233 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979502489, 9250280212975642421, RES_SUBSCRIBE_TRAIN_EXAMINE, 审核, 1, /trainSubscribe/saveExamineInfo, null, null, , null, null, null, 2023-06-06 13:48:49.0, null]
2024-03-18 10:44:15.234 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9250280212979502490, 9250280212975642421, RES_SUBSCRIBE_TRAIN_EXPORT, 选择培训后导出, 1, /trainSubscribe/exportSubscribeInfo, null, null, , null, null, null, 2023-06-06 13:44:00.0, null]
2024-03-18 10:44:15.235 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9251, , RES_EXPORT_SUBJECT_MANAGER, 专题课程统计初始化, 0, /courseSpecial/exportCheckInit, null, null, null, null, 2023-07-27 09:29:54.0, null, 2023-07-27 09:46:49.0, null]
2024-03-18 10:44:15.236 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [96427552125502931, , RES_HOME_PIC_MGMT, 产品登录图管理, 0, /home/<USER>
2024-03-18 10:44:15.238 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [96427552125503818, , RES_HOME_PIC_SHOW, 首页图展示, 0, /home/<USER>
2024-03-18 10:44:15.239 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [96427552125503838, , RES_HOME_PIC_SAVE, 首页图保存, 0, /home/<USER>
2024-03-18 10:44:15.242 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [97189131112897634, , RES_LONGIN_GRAPHIC_MANAGE_LONGIN_GRAPHIC_MANAGE_IN, 登录页图文管理, 2, /longinGraphicManage/longinGraphicManageInit*, null, null, null, null, null, null, 2023-07-07 15:12:03.0, null]
2024-03-18 10:44:15.244 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} Result: [9921, 34, RES_USERMANAGER_LOCKUSER, 用户锁定, 2, /userManager/lockUser*, null, null, null, 1, 2017-01-08 11:37:29.0, 1, 2023-07-07 15:11:23.0, null]
2024-03-18 10:44:15.245 [DEBUG]  com.stock.core.dao.CommonDao.getAllResources debug - <==      Total: 634
2024-03-18 10:44:15.246 [DEBUG]  druid.sql.ResultSet resultSetLog - {conn-10001, pstmt-20001, rs-50001} closed
2024-03-18 10:44:15.247 [DEBUG]  druid.sql.Statement statementLog - {conn-10001, pstmt-20001} closed
2024-03-18 10:44:15.248 [DEBUG]  druid.sql.Connection connectionLog - {conn-10001} pool-recycle
2024-03-18 10:44:16.181 [WARN ]  org.springframework.security.config.http.DefaultFilterChainValidator checkForDuplicates - Possible error: Filters at position 11 and 12 are both instances of org.springframework.security.web.access.intercept.FilterSecurityInterceptor
2024-03-18 10:44:18.943 [INFO ]  com.stock.core.os.config.ObjectStorageConfig getObjectStorageClient - ObjectStorageClient use profile ===================> dev/test/sim :qingstor
2024-03-18 11:15:00.162 [DEBUG]  druid.sql.Connection connectionLog - {conn-10001} closed
