
select temp.commission as'所属辖区',sum(temp.num) as '注册人数',count(temp.company_name) as '公司家数',sum(temp.con) as '上市公司家数'
from (
select t1.belong_commission,
(select sc.code_name from sa_code sc where sc.code_no = "BELONG_COMMISSION" and sc.code_value = t1.belong_commission) as commission,
count(t1.id) as num,t1.company_name,
t1.person_type,
case when t1.person_type like '001%' then 1 else 0 end con
from sch_user t1
where
(t1.status = 'C' or t1.status = 'U')  
group by t1.belong_commission,t1.company_name
)temp
group by temp.belong_commission;

select temp.personType as'用户类型',sum(temp.num) as '人数',count(temp.company_name) as '公司家数'
from (
select t1.person_type,
(select sc.code_name from sa_code sc where sc.code_value=t1.person_type and sc.code_no='PERSON_TYPE') as personType,
count(t1.id) as num,t1.company_name
from sch_user t1
where
(t1.status = 'C' or t1.status = 'U')
group by t1.person_type,t1.company_name
)temp
group by temp.person_type