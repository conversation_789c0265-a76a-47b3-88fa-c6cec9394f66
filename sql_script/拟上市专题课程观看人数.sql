select sci.id,  sci.course_name ,IFNULL(temp.sum, 0)
from  sch_course_info sci
left join sch_course_type_detailed sctd on sctd.course_info_id = sci.id and sctd.course_type = 'custom001'
left join (
	select sci.id,  sci.course_name,count(1) sum
	from sch_watch_info swi
	left join sch_course_info sci on sci.id = swi.course_id
	left join sch_course_type_detailed sctd on sctd.course_info_id = sci.id and sctd.course_type = 'custom001'
	where sctd.course_relation_id = '9250280212976665477'
	group by sci.course_name
) temp on temp.id = sci.id
where sctd.course_relation_id = '9250280212976665477'
AND sci.org_id = '0001' AND sci.if_open = '1' AND sci.`status` = '1' AND sci.release_flag = '1'
group by sci.course_name;
