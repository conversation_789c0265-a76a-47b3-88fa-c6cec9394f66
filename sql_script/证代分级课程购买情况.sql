select sum(t.total_amount)
from capco_order_info t
where t.order_status = 'PAID' and t.create_time > '2022-07-30' and t.order_type = '4';
select count(1)
from capco_order_info t
where t.order_status = 'PAID' and t.create_time > '2022-07-30' and t.total_amount != '0'  and t.order_type = '4';
select date_format(t.create_time,'%Y-%m-%d'), sum(t.total_amount)
from capco_order_info t
where t.order_status = 'PAID' and t.create_time > '2022-07-30'  and t.order_type = '4'
group by date_format(t.create_time,'%Y-%m-%d');
select sum(temp.aa)
from (
select coi.total_amount aa
from capco_order_info coi
left join capco_order_detail cod on cod.order_id = coi.id
where coi.order_status = 'PAID' and coi.create_time >= '2022-10-01' and coi.create_time < '2022-12-01'  and (cod.ware_type = '2' or cod.ware_type = '4')
group by coi.id) temp;
