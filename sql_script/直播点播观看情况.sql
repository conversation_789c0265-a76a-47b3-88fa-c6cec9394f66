select sli.live_name as '直播名称',su.real_name as '姓名',su.org_name as '部门',su.job_name as '职务',
(select sc.code_name from sa_code sc where sc.code_value=su.person_type and sc.code_no='PERSON_TYPE') as '用户类型',
su.company_code as '公司代码',su.company_name as '公司名称',
case when sum(clwi.watch_time) <1 then 1 else ROUND(sum(clwi.watch_time)/60) end as '观看时长(分钟)',
(select sc.code_name from sa_code sc where sc.code_no = 'BELONG_COMMISSION' and su.belong_commission = sc.code_value) '辖区'
from capco_live_watch_info clwi
left join sch_live_info sli on sli.live_id=clwi.live_id
left join sch_user su on su.id = clwi.user_id
left join capco_train_member_unit mu on mu.company_code=su.company_code
where clwi.live_id in ('12782114556608762754','9250280212979087729','9250280212977854768','9250280212976665286','9250280212976752682','9250280212975659622')
and mu.belongs_plate in ('00','02','04','07','09') 
group by clwi.live_id,clwi.user_id 
order by sli.live_id,su.belong_commission,su.company_name;

select sci.course_name as '点播名称',su.real_name as '姓名',su.org_name as '部门',su.job_name as '职务',
(select sc.code_name from sa_code sc where sc.code_value=su.person_type and sc.code_no='PERSON_TYPE') as '用户类型',
su.company_code as '公司代码',su.company_name as '公司名称',
case when ROUND(ROUND(swi.video_time/60)/ROUND(sum(svi.video_time)/60),2 ) = 0.00
then concat(1,'%') else concat(ROUND(ROUND(swi.video_time/60)/ROUND(sum(svi.video_time)/60)*100,0 ),'%') end as '已学习百分比' ,
(select sc.code_name from sa_code sc where sc.code_no = 'BELONG_COMMISSION' and su.belong_commission = sc.code_value) '辖区'
from sch_watch_info swi
left join sch_user su on su.id=swi.user_id
left join capco_train_member_unit mu on mu.company_code=su.company_code
left join sch_course_info sci on sci.id=swi.course_id
where sci.id in ('9250280212975805273','9250280212975805256','9250280212975805237','9250280212975805200','9250280212976912712')
and mu.belongs_plate in ('00','02','04','07','09') 
order by swi.course_id,su.belong_commission,su.company_name

select temp.*
from (
select sli.live_name as '课程名称',su.real_name as '姓名',su.org_name as '部门',su.job_name as '职务',
(select sc.code_name from sa_code sc where sc.code_value=su.person_type and sc.code_no='PERSON_TYPE') as '用户类型',
su.company_code as '公司代码',su.company_name as '公司名称',
(select sc.code_name from sa_code sc where sc.code_no = 'BELONG_COMMISSION' and su.belong_commission = sc.code_value) '辖区'
from capco_live_hits clwi
left join sch_live_info sli on sli.live_id=clwi.live_id
left join sch_user su on su.id = clwi.user_id
where clwi.live_id ='12782114556609341885'
group by clwi.live_id,clwi.user_id
union all
select sci.course_name as '课程名称',su.real_name as '姓名',su.org_name as '部门',su.job_name as '职务',
(select sc.code_name from sa_code sc where sc.code_value=su.person_type and sc.code_no='PERSON_TYPE') as '用户类型',
su.company_code as '公司代码',su.company_name as '公司名称',
(select sc.code_name from sa_code sc where sc.code_no = 'BELONG_COMMISSION' and su.belong_commission = sc.code_value) '辖区'
from sch_watch_info swi
left join sch_user su on su.id=swi.user_id
left join capco_train_member_unit mu on mu.company_code=su.company_code
left join sch_course_info sci on sci.id=swi.course_id
where sci.id ='12782114556609331088'
)temp
group by temp.姓名

