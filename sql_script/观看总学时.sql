select a.course_name '课程名',a.real_name '姓名', a.company_code , a.company_name,a.video_time '视频时长', a.studyTime '学习时长',a.period '已获得学时'
from (

select temp.course_name,temp.real_name,temp.studyTime, sum(svi.video_time) video_time,  temp.company_code , temp.company_name,
		case when temp.studyTime * 0.95 >   sum(svi.video_time)
				then sum(scvm.period)  else 0 end  period

from
	(	select swid.course_id, sci.course_name, swid.user_id ,  su.real_name, su.company_code , su.company_name,  sum(swid.study_time) studyTime
		from sch_watch_info_detail swid
		left join sch_course_info sci on sci.id = swid.course_id
		left join sch_user su on su.id = swid.user_id
		where date_format(swid.create_time,'%Y-%m-%d') >= '2022-03-21' and date_format(swid.create_time,'%Y-%m-%d')  <= '2023-03-20'
		group by swid.course_id,swid.user_id
	) temp
left join sch_course_video_map scvm on scvm.biz_id = temp.course_id
left join sch_video_info svi on svi.id = scvm.video_id
where scvm.`status` = '1'
group by scvm.biz_id,temp.user_id )  a
