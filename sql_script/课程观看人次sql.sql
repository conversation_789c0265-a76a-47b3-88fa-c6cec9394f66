select temp2.live_name as '直播名称',
group_concat(distinct sct.item_name) as '业务类别',group_concat(distinct sti.teach_name) as '讲师',temp2.begin_time as '发布时间',
temp2.sum as '观看人次'
from (
select temp.live_id,temp.live_name ,count(temp.live_id) as sum,temp.begin_time
from(
select sli.live_name,sli.live_id,sli.begin_time
from sch_live_view_info clwi
left join sch_live_info sli on sli.live_id=clwi.live_id
where sli.rele_state='1' and sli.live_status='1' and clwi.operation_type='1'
group by clwi.live_id,clwi.user_id 
) temp
group by temp.live_id
) temp2
left join sch_course_type_detailed sctd on sctd.course_info_id=temp2.live_id
left join sch_course_type sct on sct.id=sctd.course_relation_id
left join sch_live_teach_rela sltr on sltr.live_id=temp2.live_id
left join sch_teach_info sti on sti.teach_id=sltr.teach_id
group by temp2.live_id
order by sct.item_name;

select temp.course_name as '课程名称',
group_concat(distinct sct.item_name) as '业务类别',group_concat(distinct sti.teach_name) as '讲师',
temp.release_time as '发布时间',
temp.num as '观看人次',sum(scvm.period) as '学时'
from (
select sci.id,sci.course_name,count(swi.course_id) as num,sci.release_time
from sch_watch_info swi
left join sch_course_info sci on sci.id=swi.course_id
where sci.`status`='1' and sci.release_flag='1'
group by swi.course_id
) temp 
left join sch_course_type_detailed sctd on sctd.course_info_id=temp.id and sctd.course_type='custom001'
left join sch_course_type sct on sct.id=sctd.course_relation_id 
left join sch_course_type_detailed sctd1 on sctd1.course_info_id=temp.id and sctd1.course_type='custom999'
left join sch_teach_info sti on sti.teach_id=sctd1.course_relation_id
left join sch_course_video_map scvm on scvm.biz_id=temp.id
left join sch_course_type_detailed sctd2 on sctd2.course_info_id=temp.id and sctd2.course_type='custom004'
group by temp.id
order by sct.item_name,temp.release_time